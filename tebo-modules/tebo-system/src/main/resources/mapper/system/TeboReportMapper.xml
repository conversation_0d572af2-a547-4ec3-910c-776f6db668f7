<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboReportMapper">


    <select id="selectPartnerReport" resultType="com.tebo.system.api.model.SystemPartnerReportVO">
        select 1 as partnerNum, sum(t.shopNum) shopNum,sum(t.accountNum) accountNum, sum(t.customerNum) customerNum
        from (
        select count(1) as partnerNum ,0 as shopNum, 0 as accountNum, 0 as customerNum from tebo_partner_info a
        union  all
        select 0 as partnerNum, count(1) as  shopNum, 0 as accountNum, 0 as customerNum from tebo_shop b
        where b.tenant_id = #{tenantId}
        and b.del_flag =0
        union  all
        select 0 as partnerNum, 0 as  shopNum,count(1) as  accountNum, 0 as customerNum from tebo_account c
        where c.tenant_id = #{tenantId}
        and c.del_flag =0
        union  all
        select 0 as partnerNum, 0 as  shopNum,0 as  accountNum,count(1) as customerNum from tebo_customer_service_record d
        where d.tenant_id = #{tenantId}
        ) as t;
    </select>
    <select id="selectPartnerRevenueReport" resultType="com.tebo.system.api.model.SystemPartnerReportVO">
        select  sum(t.todayRevenue) todayRevenue,sum(t.monthRevenue) monthRevenue, sum(t.totalRevenue) totalRevenue
        from (
        SELECT sum(a.amount) todayRevenue,0 monthRevenue, 0 totalRevenue FROM `pay_wechat_record` a
        where a.business_type in (1,10)
        and a.pay_status = 1
        and a.tenant_id = #{tenantId}
        and DATE(a.create_time) = CURDATE()
        union all
        SELECT 0 todayRevenue,sum(b.amount)  monthRevenue, 0 totalRevenue  FROM `pay_wechat_record` b
        where b.business_type in (1,10)
        and b.pay_status = 1
        and b.tenant_id = #{tenantId}
        and MONTH(b.create_time) = MONTH(CURDATE())
        union all
        SELECT 0 todayRevenue,0 monthRevenue, sum(c.amount)  totalRevenue FROM `pay_wechat_record` c
        where c.business_type in (1,10)
        and c.pay_status = 1
        and c.tenant_id = #{tenantId}
        )t
    </select>
    <select id="selectPartnerInfoReport" resultType="com.tebo.system.api.model.SystemPartnerReportVO">
        select 1 as partnerNum, sum(t.shopNum) shopNum,sum(t.accountNum) accountNum, sum(t.customerNum) customerNum
        from (
        select count(1) as partnerNum ,0 as shopNum, 0 as accountNum, 0 as customerNum from tebo_partner_info a
        union  all
        select 0 as partnerNum, count(1) as  shopNum, 0 as accountNum, 0 as customerNum from tebo_shop b
        where b.tenant_id = 0
        and b.del_flag =0
        union  all
        select 0 as partnerNum, 0 as  shopNum,count(1) as  accountNum, 0 as customerNum from tebo_account c
        where c.shop_id = #{shopId}
        and c.del_flag =0
        union  all
        select 0 as partnerNum, 0 as  shopNum,0 as  accountNum,count(1) as customerNum from tebo_customer_service_record d
        where d.shop_id = #{shopId}
        ) as t;
    </select>
    <select id="selectPartnerInfoRevenueReport" resultType="com.tebo.system.api.model.SystemPartnerReportVO">
        select  sum(t.todayRevenue) todayRevenue,sum(t.monthRevenue) monthRevenue, sum(t.totalRevenue) totalRevenue
        from (
        SELECT sum(a.amount) todayRevenue,0 monthRevenue, 0 totalRevenue FROM `pay_wechat_record` a
        where a.business_type in (1,10)
        and a.pay_status = 1
        and a.shopId = #{shopId}
        and DATE(a.create_time) = CURDATE()
        union all
        SELECT 0 todayRevenue,sum(b.amount)  monthRevenue, 0 totalRevenue  FROM `pay_wechat_record` b
        where b.business_type in (1,10)
        and b.pay_status = 1
        and b.shopId = #{shopId}
        and MONTH(b.create_time) = MONTH(CURDATE())
        union all
        SELECT 0 todayRevenue,0 monthRevenue, sum(c.amount)  totalRevenue FROM `pay_wechat_record` c
        where c.business_type in (1,10)
        and c.pay_status = 1
        and c.shopId = #{shopId}
        )t
    </select>
</mapper>
