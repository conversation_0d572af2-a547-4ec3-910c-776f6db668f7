<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboCustomerGiftRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboCustomerGiftRecordDO">
        <id column="id" property="id" />
        <result column="unionid" property="unionid" />
        <result column="nick_name" property="nickName" />
        <result column="phone_number" property="phoneNumber" />
        <result column="type" property="type" />
        <result column="goods_type" property="goodsType" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="district" property="district" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, unionid, nick_name, phone_number, `type`, goods_type, create_time, update_time, district
    </sql>

</mapper>
