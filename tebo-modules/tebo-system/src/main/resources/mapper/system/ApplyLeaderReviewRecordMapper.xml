<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.ApplyLeaderReviewRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.ApplyLeaderReviewRecordDO">
        <result column="id" property="id" />
        <result column="customer_id" property="customerId" />
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="real_name" property="realName" />
        <result column="phone" property="phone" />
        <result column="id_card" property="idCard" />
        <result column="id_card_front_url" property="idCardFrontUrl" />
        <result column="id_card_back_url" property="idCardBackUrl" />
        <result column="id_card_hold_url" property="idCardHoldUrl" />
        <result column="status" property="status" />
        <result column="reason" property="reason" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, customer_id, tenant_id, real_name, phone, id_card, id_card_front_url, id_card_back_url, id_card_hold_url, `status`, reason, create_time, update_time,update_by,tenant_name
    </sql>

    <select id="getApplyLeaderRecordInfo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_apply_record
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="customerId != null and customerId != ''">
                and customer_id = #{customerId}
            </if>
            order by create_time desc limit 1
        </where>
    </select>

    <select id="getApplyLeaderRecordList" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"></include> from tebo_apply_record
      <where>
        <if test="tenantId != null and tenantId != ''">
         and tenant_id = #{tenantId}
        </if>
        <if test="status != null and status != ''">
         and status = #{status}
        </if>
        <if test="id != null and id != ''">
          and id = #{id}
        </if>
        <if test="keyword != null and keyword != ''">
          and ((real_name like concat('%', #{keyword}, '%'))
          or (phone like concat('%', #{keyword}, '%'))
          or (id_card like concat('%', #{keyword}, '%'))
          or (tenant_name like concat('%', #{keyword}, '%'))
          )
        </if>

        <if test="auditStartTime != null and auditStartTime != ''"><!-- 开始时间检索 -->
        AND date_format(update_time,'%y%m%d') &gt;= date_format(#{auditStartTime},'%y%m%d')
        </if>
        <if test="auditEndTime != null and auditEndTime != ''"><!-- 结束时间检索 -->
            AND date_format(update_time,'%y%m%d') &lt;= date_format(#{auditEndTime},'%y%m%d')
        </if>

      </where>
        order by create_time desc
    </select>

    <update id="updateApplyLeaderStatus" parameterType="com.tebo.system.entity.ApplyLeaderReviewRecordDO">
        update tebo_apply_record
        <set>
         <if test="status != null and status != ''">status = #{status},</if>
         <if test="reason != null and reason != ''">reason = #{reason},</if>
         <if test="rate != null and rate != ''">rate = #{rate},</if>
        </set>
        where id = #{id}
    </update>

</mapper>
