<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboShopReviewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboShopReviewDO">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="applyment_id" property="applymentId" />
        <result column="business_code" property="businessCode" />
        <result column="sub_mchid" property="subMchid" />
        <result column="customer_subject" property="customerSubject" />
        <result column="pay_type" property="payType" />
        <result column="tebo_account_bank" property="teboAccountBank" />
        <result column="tebo_account_branch_bank" property="teboAccountBranchBank" />
        <result column="tebo_bank_branch_id" property="bankBranchId" />
        <result column="tebo_account_number" property="teboAccountNumber" />
        <result column="tebo_account_name" property="teboAccountName" />
        <result column="tebo_bank_address_code" property="teboBankAddressCode" />
        <result column="tebo_id_card_front" property="teboIdCardFront" />
        <result column="tebo_id_card_back" property="teboIdCardBack" />
        <result column="tebo_id_card" property="teboIdCard" />
        <result column="tebo_id_card_name" property="teboIdCardName" />
        <result column="tebo_id_card_period_begin" property="teboIdCardPeriodBegin" />
        <result column="tebo_id_card_period_end" property="teboIdCardPeriodEnd" />
        <result column="id_card_copy" property="idCardCopy" />
        <result column="id_card_national" property="idCardNational" />
        <result column="business_license" property="businessLicense" />
        <result column="wx_seller_name" property="wxSellerName" />
        <result column="business_license_code" property="businessLicenseCode" />
        <result column="legal_person" property="legalPerson" />
        <result column="license_address" property="licenseAddress" />
        <result column="register_capital" property="registerCapital" />
        <result column="review_status" property="reviewStatus" />
        <result column="wechat_review_status" property="wechatReviewStatus" />
        <result column="id_card_address" property="idCardAddress" />
        <result column="license_copy" property="licenseCopy" />
        <result column="del_flag" property="delFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="audit_time" property="auditTime" />
        <result column="note" property="note" />
        <result column="source" property="source" />
        <result column="certification_time" property="certificationTime" />
        <result column="sign_url" property="signUrl" />
        <result column="fail_reason" property="failReason" />
        <result column="establish_time" property="establishTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id,source,establish_time,tebo_note,business_code,sign_url,fail_reason,applyment_id,sub_mchid, customer_subject, pay_type, tebo_account_bank, tebo_account_branch_bank,tebo_bank_branch_id, tebo_account_number, tebo_account_name, tebo_bank_address_code, tebo_id_card_front, tebo_id_card_back, tebo_id_card, tebo_id_card_name, tebo_id_card_period_begin, tebo_id_card_period_end, tebo_id_card_address,license_copy,id_card_copy, id_card_national, business_license, wx_seller_name, business_license_code, legal_person, license_address, register_capital, review_status, wechat_review_status,del_flag, create_by, create_time, update_by, update_time,audit_time,certification_time
    </sql>

    <sql id="Base_shop_review_List">
       r.id, r.establish_time,r.source,r.shop_id,r.tebo_note,r.business_code,r.sign_url,r.fail_reason, r.applyment_id,r.sub_mchid, r.customer_subject, r.pay_type, r.tebo_account_bank, r.tebo_account_branch_bank,r.tebo_bank_branch_id, r.tebo_account_number, r.tebo_account_name, r.tebo_bank_address_code, r.tebo_id_card_front, r.tebo_id_card_back, r.tebo_id_card, r.tebo_id_card_name, r.tebo_id_card_period_begin, r.tebo_id_card_period_end, r.tebo_id_card_address,r.license_copy,r.id_card_copy, r.id_card_national, r.business_license, r.wx_seller_name, r.business_license_code, r.legal_person, r.license_address, r.register_capital, r.review_status,r.wechat_review_status, r.del_flag, r.create_by, r.create_time, r.update_by, r.update_time,r.audit_time,r.certification_time
    </sql>

    <select id="getTeboReviewList" resultMap="BaseResultMap">
     select distinct  <include refid="Base_shop_review_List"></include> from tebo_shop_review r
         inner join tebo_shop s on r.shop_id = s.id and s.del_flag = 0
        <where>
            <if test="shopName!= null and shopName!=''">
             and s.shop_name like concat('%', #{shopName}, '%')
            </if>
            <if test="businessManager!= null and businessManager!=''">
                and r.create_by like concat('%', #{businessManager}, '%')
            </if>
            <if test="wxSellerName!=null and wxSellerName!=''">
             and r.wx_seller_name like concat('%', #{wxSellerName}, '%')
            </if>
            <if test="shopBossName!=null and shopBossName!=''">
            and s.shop_boss_name like concat('%', #{shopBossName}, '%')
            </if>
            <if test="tenantName!=null and tenantName!=''">
             and s.tenant_name like concat('%', #{tenantName}, '%')
            </if>
            <if test="phoneNumber!=null and phoneNumber!= ''">
             and s.phone_number like concat('%', #{phoneNumber}, '%')
            </if>
            <if test="reviewStatus != null">
            and  r.review_status = #{reviewStatus}
            </if>
            <if test="shopId!=null">
            and  r.shop_id = #{shopId}
            </if>
            <if test="wechatReviewStatus!=null and wechatReviewStatus!=''">
            and  r.wechat_review_status = #{wechatReviewStatus}
            </if>
            <if test="auditTimeStartSecond!= null">
                and audit_time >= #{auditTimeStartSecond ,jdbcType=TIMESTAMP}
            </if>
            <if test="auditTimeEndSecond!= null">
                and #{auditTimeEndSecond,jdbcType=TIMESTAMP} > audit_time
            </if>
            <if test="shopIdList!= null and shopIdList.size() > 0">
                and shop_id in
                <foreach collection="shopIdList" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>

            </if>
        </where>
        order by r.update_time desc
    </select>

    <select id="getTeboShopReview" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_shop_review where shop_id = #{shopId}
    </select>
</mapper>
