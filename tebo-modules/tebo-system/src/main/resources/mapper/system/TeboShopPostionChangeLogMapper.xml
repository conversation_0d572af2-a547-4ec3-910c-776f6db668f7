<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboShopPostionChangeLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboShopPostionChangeLogDO">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="change_before" property="changeBefore" />
        <result column="change_after" property="changeAfter" />
        <result column="source" property="source" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, shop_name, change_before, change_after, `source`, create_time, update_time
    </sql>

</mapper>
