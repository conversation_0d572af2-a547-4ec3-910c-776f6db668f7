<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboPartnerInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboPartnerInfoDO">
        <id column="id" property="id" />
        <result column="sys_user_id" property="sysUserId" />
        <result column="partner_code" property="partnerCode" />
        <result column="partner_name" property="partnerName" />
        <result column="customer_type" property="customerType" />
        <result column="area" property="area" />
        <result column="area_code" property="areaCode" />
        <result column="area_name" property="areaName" />
        <result column="address" property="address" />
        <result column="link_man" property="linkMan" />
        <result column="link_phone" property="linkPhone" />
        <result column="area_manager_id" property="areaManagerId" />
        <result column="area_manager" property="areaManager" />
        <result column="remark" property="remark" />
        <result column="partner_type" property="partnerType" />
        <result column="pay_type" property="payType" />
        <result column="account_bank" property="accountBank" />
        <result column="account_number" property="accountNumber" />
        <result column="id_card_front" property="idCardFront" />
        <result column="id_card_back" property="idCardBack" />
        <result column="id_card" property="idCard" />
        <result column="business_license" property="businessLicense" />
        <result column="business_license_code" property="businessLicenseCode" />
        <result column="legal_person" property="legalPerson" />
        <result column="register_capital" property="registerCapital" />
        <result column="establish_time" property="establishTime" />
        <result column="status" property="status" />
        <result column="email" property="email" />
        <result column="account_branch_bank" property="accountBranchBank" />
        <result column="wx_seller_name" property="wxSellerName" />
        <result column="service_earnest_money" property="serviceEarnestMoney" />
        <result column="goods_earnest_money" property="goodsEarnestMoney" />
        <result column="home_service_fee" property="homeServiceFee" />
        <result column="pay_earnest_time" property="payEarnestTime" />


        <result column="del_flag" property="delFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="cid" property="cid" />
        <result column="mark" property="mark" />
    </resultMap>

    <resultMap id="PersonGroupCountVO" type="com.tebo.system.domain.vo.PersonGroupCountVO">
        <result property="businessAccount" column="businessAccount"/>
        <result property="count" column="count"/>
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , sys_user_id, partner_code, partner_name,customer_type, area, area_code, area_name, address, link_man, link_phone, area_manager_id, area_manager, remark, partner_type, pay_type, account_bank, account_number, id_card_front, id_card_back, id_card, business_license, business_license_code, legal_person, register_capital, establish_time, `status`, email,account_branch_bank,wx_seller_name,
        service_earnest_money,goods_earnest_money ,home_service_fee,  pay_earnest_time,     del_flag, create_by, create_time, update_by, update_time,cid,mark
    </sql>
    <update id="batchEnable">
        update tebo_partner_info set status = 1 where id in
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>

    </update>
    <update id="batchDisable">
        update tebo_partner_info set status = 0 where id in
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>

    </update>
    <update id="batchDelete">
        update tebo_partner_info set del_flag = 1 where id in
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </update>
    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tebo_partner_info
        <include refid="whereSql"/>
        order by create_time desc
    </select>
    <select id="selectByPartnerName" resultType="java.lang.Integer">
        select count(1) from tebo_partner_info
            where partner_name = #{partnerName}
            and del_flag = 0
           <if test="id!=null">
                and id != #{id}
            </if>
    </select>
    <select id="getPartnerByDistrictCode" resultType="com.tebo.system.entity.TeboPartnerInfoDO">
        select <include refid="Base_Column_List" />
            from tebo_partner_info
            where del_flag = 0
            and area_code = #{districtCode} limit 1
    </select>

    <select id="getPartnerByIdList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
        from tebo_partner_info
        <where>
            del_flag = 0
            <if test="idList!= null and idList.size() > 0">
                and id in
                <foreach collection="idList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>

    </select>
    <select id="selectByPartnerCode" resultType="java.lang.Integer">
        select count(1) from tebo_partner_info
        where partner_code = #{partnerCode}
        and del_flag = 0
        <if test="id!=null">
            and id != #{id}
        </if>
    </select>


    <resultMap id="TimeGroupCountVO" type="com.tebo.system.domain.vo.TimeGroupCountVO">
        <result property="dateStr" column="dateStr"/>
        <result property="count" column="count"/>
    </resultMap>



    <select id="groupByDay" resultMap="TimeGroupCountVO">

        SELECT DATE_FORMAT(create_time, '%Y年%m月%d日') AS dateStr,
        SUM(1) AS 'count'
        FROM tebo_partner_info
        <include refid="whereSql"/>
        GROUP BY dateStr
        ORDER BY dateStr

    </select>

    <select id="groupByMonth" resultMap="TimeGroupCountVO">

        SELECT DATE_FORMAT(create_time, '%Y年%m月') AS dateStr,
        SUM(1) AS 'count'
        FROM tebo_partner_info
        <include refid="whereSql"/>
        GROUP BY dateStr
        ORDER BY dateStr

    </select>


    <sql id="whereSql">
        where del_flag = 0
        <if test="keyword != null and keyword != ''">
            and ((partner_name like concat('%', #{keyword}, '%'))
            or (link_phone like concat('%', #{keyword}, '%'))
            or (link_phone like concat('%', #{keyword}, '%'))
            )
        </if>

        <if test="partnerNameLike!= null and partnerNameLike != ''">
            and partner_name like concat('%', #{partnerNameLike}, '%')
        </if>
        <if test="partnerCodeLike!= null and partnerCodeLike != ''">
            and partner_code like concat('%', #{partnerCodeLike}, '%')
        </if>
        <if test="phoneNumberLike!= null and phoneNumberLike != ''">
            and link_phone like concat('%', #{phoneNumberLike}, '%')
        </if>

        <if test="areaCode!= null and areaCode != ''">
            and area_code = #{areaCode}
        </if>

        <if test="idList!= null and idList.size() > 0">
            and id in
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>

        </if>
        <if test="status!= null">
            and status = #{status}
        </if>
        <if test="createTimeStartSecond!= null">
            and create_time >= #{createTimeStartSecond ,jdbcType=TIMESTAMP}
        </if>
        <if test="createTimeEndSecond!= null">
            and #{createTimeEndSecond ,jdbcType=TIMESTAMP} > create_time
        </if>
        <if test="areaStr!= null">
            and area_name like concat('%', #{areaStr}, '%')
        </if>
        <if test="areaName!= null and areaName != ''">
            and area_name = #{areaName}
        </if>
    </sql>




    <select id="groupByPerson" resultMap="PersonGroupCountVO">

        SELECT business_account AS businessAccount,
        SUM(1) AS 'count'
        FROM tebo_partner_info
        <include refid="whereSql"/>
        GROUP BY business_account


    </select>
</mapper>
