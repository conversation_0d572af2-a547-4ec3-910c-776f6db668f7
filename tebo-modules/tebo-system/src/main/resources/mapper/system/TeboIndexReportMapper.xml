<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboIndexReportMapper">


    <select id="platformRevenue" resultType="com.tebo.system.api.domain.view.TeboSystemIndexReportVO">
        SELECT
        sum( todayRevenue ) todayRevenue,
        sum( yesterdayRevenue ) yesterdayRevenue,
        sum( weekRevenue ) weekRevenue
        FROM
        (
        SELECT
        ifnull( sum( a.amount ), 0 ) todayRevenue,
        0 yesterdayRevenue,
        0 weekRevenue
        FROM
        `pay_wechat_record` a
        WHERE
        a.pay_status = 1
        AND DATE( a.create_time ) = CURDATE() UNION ALL
        SELECT
        0 todayRevenue,
        ifnull( sum( a.amount ), 0 ) yesterdayRevenue,
        0 weekRevenue
        FROM
        `pay_wechat_record` a
        WHERE
        a.pay_status = 1
        AND DATE( a.create_time ) = DATE_SUB( CURDATE(), INTERVAL 1 DAY ) UNION ALL
        SELECT
        0 todayRevenue,
        0 yesterdayRevenue,
        ifnull( sum( a.amount ), 0 ) weekRevenue
        FROM
        `pay_wechat_record` a
        WHERE
        a.pay_status = 1
        AND DATE( a.create_time ) >= DATE_SUB( CURDATE(), INTERVAL 7 DAY )
        ) t;
    </select>
    <select id="platformPartnerNum" resultType="com.tebo.system.api.domain.view.TeboSystemIndexReportVO">
        SELECT
        sum( todayPartnerNum ) todayPartnerNum,
        sum( yesterdayPartnerNum ) yesterdayPartnerNum,
        sum( monthPartnerNum ) monthPartnerNum,
        sum( totalPartnerNum ) totalPartnerNum
        FROM
        (
        SELECT
        count( 1 ) todayPartnerNum,
        0 yesterdayPartnerNum,
        0 monthPartnerNum,
        0 totalPartnerNum
        FROM
        tebo_partner_info
        WHERE
        DATE( create_time ) = CURDATE()
        AND del_flag = 0 UNION ALL
        SELECT
        0 todayPartnerNum,
        count( 1 ) yesterdayPartnerNum,
        0 monthPartnerNum,
        0 totalPartnerNum
        FROM
        tebo_partner_info
        WHERE
        DATE( create_time ) = DATE_SUB( CURDATE(), INTERVAL 1 DAY )
        AND del_flag = 0 UNION ALL
        SELECT
        0 todayPartnerNum,
        0 yesterdayPartnerNum,
        count( 1 ) monthPartnerNum,
        0 totalPartnerNum
        FROM
        tebo_partner_info
        WHERE
        YEAR(create_time) = YEAR(CURDATE())
        and MONTH( create_time ) >= MONTH( CURDATE())
        AND del_flag = 0 UNION ALL
        SELECT
        0 todayPartnerNum,
        0 yesterdayPartnerNum,
        0 monthPartnerNum,
        count( 1 ) totalPartnerNum
        FROM
        tebo_partner_info
        WHERE
        del_flag = 0
        ) t
    </select>
    <select id="platformShopNum" resultType="com.tebo.system.api.domain.view.TeboSystemIndexReportVO">
        SELECT
        sum( todayShopNum ) todayShopNum,
        sum( yesterdayShopNum ) yesterdayShopNum,
        sum( monthShopNum ) monthShopNum,
        sum( totalShopNum ) totalShopNum
        FROM
        (
        SELECT
        count( 1 ) todayShopNum,
        0 yesterdayShopNum,
        0 monthShopNum,
        0 totalShopNum
        FROM
        tebo_shop
        WHERE
        DATE( create_time ) =CURDATE()
        and del_flag =0
        UNION ALL
        SELECT
        0 todayShopNum,
        count( 1 ) yesterdayShopNum,
        0 monthShopNum,
        0 totalShopNum
        FROM
        tebo_shop
        WHERE
        DATE( create_time ) = DATE_SUB( CURDATE(), INTERVAL 1 DAY )
        and del_flag =0
        UNION ALL
        SELECT
        0 todayShopNum,
        0 yesterdayShopNum,
        count( 1 ) monthShopNum,
        0 totalShopNum
        FROM
        tebo_shop
        WHERE
        YEAR(create_time) = YEAR(CURDATE())
        and MONTH( create_time ) >= MONTH( CURDATE())
        and del_flag =0
        UNION ALL
        SELECT
        0 todayShopNum,
        0 yesterdayShopNum,
        0 monthShopNum,
        count( 1 ) totalShopNum
        FROM
        tebo_shop
        WHERE
        del_flag =0
        ) t
    </select>
    <select id="platformConsumerNum" resultType="com.tebo.system.api.domain.view.TeboSystemIndexReportVO">
        SELECT
        sum( todayConsumerNum ) todayConsumerNum,
        sum( yesterdayConsumerNum ) yesterdayConsumerNum,
        sum( monthConsumerNum ) monthConsumerNum,
        sum( totalConsumerNum ) totalConsumerNum
        FROM
        (
        SELECT
        count( 1 ) todayConsumerNum,
        0 yesterdayConsumerNum,
        0 monthConsumerNum,
        0 totalConsumerNum
        FROM
        tebo_customer
        WHERE
        DATE( create_time ) = CURDATE()

        UNION ALL
        SELECT
        0 todayConsumerNum,
        count( 1 ) yesterdayConsumerNum,
        0 monthConsumerNum,
        0 totalConsumerNum
        FROM
        tebo_customer
        WHERE
        DATE( create_time ) = DATE_SUB( CURDATE(), INTERVAL 1 DAY )
        UNION ALL
        SELECT
        0 todayConsumerNum,
        0 yesterdayConsumerNum,
        count( 1 ) monthConsumerNum,
        0 totalConsumerNum
        FROM
        tebo_customer
        WHERE
        YEAR(create_time) = YEAR(CURDATE())
        and MONTH( create_time ) >= MONTH( CURDATE())
        UNION ALL
        SELECT
        0 todayConsumerNum,
        0 yesterdayConsumerNum,
        0 monthConsumerNum,
        count( 1 ) totalConsumerNum
        FROM
        tebo_customer
        ) t
    </select>


</mapper>
