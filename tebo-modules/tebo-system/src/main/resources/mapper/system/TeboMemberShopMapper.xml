<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboMemberShopMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboMemberShopDO">
        <result column="id" property="id" />
        <result column="phone_number" property="phoneNumber" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, phone_number, create_time, update_time
    </sql>

    <select id="selectByPhoneNumber" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_member_shop
        where phone_number = #{phoneNumber}
    </select>
</mapper>
