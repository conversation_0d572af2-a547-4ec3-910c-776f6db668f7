<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboShopUserLinkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboShopUserLinkDO">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="sys_user_id" property="sysUserId"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , shop_id, sys_user_id, del_flag
    </sql>

    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        tebo_shop_user_link
        where
        del_flag = 0
        <if test="shopIdList!= null and shopIdList.size() > 0">
            and shop_id in
            <foreach collection="shopIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="userIdList!= null and userIdList.size() > 0">
            and sys_user_id in
            <foreach collection="userIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectByShopId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        tebo_shop_user_link
        where
        del_flag = 0
        and shop_id = #{shopId}
    </select>
    <select id="selectByUserId" resultType="java.lang.Long">
        select shop_id
        from tebo_shop_user_link
        where del_flag = 0
          and sys_user_id = #{userId}
    </select>

    <update id="deleteByParams">
        update tebo_shop_user_link
        set del_flag = 1
        where del_flag = 0
        <if test="shopIdList!= null and shopIdList.size() > 0">
            and shop_id in
            <foreach collection="shopIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="userIdList!= null and userIdList.size() > 0">
            and sys_user_id in
            <foreach collection="userIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </update>

</mapper>
