<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboCustomerSingRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboCustomerSingRecordDO">
        <id column="id" property="id" />
        <result column="unionid" property="unionid" />
        <result column="phone_number" property="phoneNumber" />
        <result column="record_time" property="recordTime" />
        <result column="record_date" property="recordDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, unionid, phone_number, record_time, record_date
    </sql>

</mapper>
