<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.pay.PayShopMerchantsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboShopPayMerchantsDO">
        <result column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="tenant_id" property="tenantId" />
        <result column="merchants_id" property="merchantsId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, shop_id, merchants_id
    </sql>
    <!-- 通过门店id查询商户号 -->
    <select id="getMerchantsIdByShopId" resultType="String">
        select merchants_id from pay_shop_merchants where shop_id = #{shopId}
    </select>

    <select id="getMerchantsByShopIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pay_shop_merchants
        where
        shop_id in
        <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
    </select>

    <select id="getMerchantsIdByTenantId" resultType="java.lang.String">
        select merchants_id from pay_shop_merchants where tenant_id = #{tenantId} and shop_id = 0
    </select>

    <update id="updatePayShopMerchants">
        update pay_shop_merchants
        set merchants_id = #{merchantsId}
        where shop_id = #{shopId}
        and tenant_id = #{tenantId}
    </update>
</mapper>
