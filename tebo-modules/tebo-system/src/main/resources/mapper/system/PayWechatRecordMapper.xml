<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.PayWechatRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.PayWechatRecordDO">
        <result column="id" property="id" />
        <result column="source" property="source" />
        <result column="business_type" property="businessType" />
        <result column="amount" property="amount" />
        <result column="order_no" property="orderNo" />
        <result column="description" property="description" />
        <result column="openId" property="openId" />
        <result column="shopId" property="shopId" />
        <result column="shopName" property="shopName" />
        <result column="pay_status" property="payStatus" />
        <result column="prePayId" property="prePayId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <resultMap id="payRecordMap" type="com.tebo.system.api.domain.view.PayWechatRecordVO">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="shopId" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="source" property="source" />
        <result column="business_type" property="businessType" />
        <result column="amount" property="amount" />
        <result column="order_no" property="orderNo" />
        <result column="description" property="description" />
        <result column="openId" property="openId" />
        <result column="channel" property="channel" />
        <result column="create_time" property="orderTime" />
        <result column="update_time" property="paymentTime" />
    </resultMap>

    <resultMap id="payRecordVoMap" type="com.tebo.system.api.domain.view.PayWechatVO">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="source" property="source" />
        <result column="business_type" property="businessType" />
        <result column="amount" property="amount" />
        <result column="order_no" property="orderNo" />
        <result column="description" property="description" />
        <result column="openId" property="openId" />
        <result column="transaction_id" property="transactionId" />
        <result column="create_time" property="orderTime" />
        <result column="update_time" property="paymentTime" />
    </resultMap>

    <resultMap id="reportShopDealVO" type="com.tebo.system.domain.vo.ReportShopDealVO">
        <result column="shop_name" property="shopName" />
        <result column="amountAll" property="amountAll" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, `source`, business_type, amount, channel, order_No, description, openId, shopId,shop_name, pay_status, prePayId, create_time, update_time,tenant_id, transaction_id
    </sql>
    <update id="updatePayStatus">
        update pay_wechat_record set pay_status = #{status} where order_no = #{orderNo}
    </update>
    <select id="selectOneByOrderNo" resultType="com.tebo.system.entity.PayWechatRecordDO">
        select <include refid="Base_Column_List"></include>
        from pay_wechat_record
        where order_no = #{orderNo} limit 1
    </select>
    <select id="getPayWechatRecordList" resultMap="payRecordMap">
        select <include refid="Base_Column_List"></include>
        from pay_wechat_record
        <where>
            pay_status = 1
            <if test="tenantId != null and tenantId != ''">
                AND tenant_id=#{tenantId}
            </if>
            <if test="shopId != null and shopId != ''">
                AND shopId = #{shopId}
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND order_No= #{orderNo}
            </if>
            <if test="source != null and source != ''">
                AND source= #{source}
            </if>
            <if test="businessType != null and businessType != ''">
                AND business_type= #{businessType}
            </if>
            <if test="amountStart != null">
                AND amount > #{amountStart}
            </if>
            <if test="amountEnd != null">
                AND amount &lt;= #{amountEnd}
            </if>
            <if test="channel != null">
                and channel = #{channel}
            </if>
            <if test="startPaymentTime != null and startPaymentTime != ''"><!-- 支付时间检索 -->
                AND date_format(update_time,'%y%m%d') &gt;= date_format(#{startPaymentTime},'%y%m%d')
            </if>
            <if test="endPaymentTime != null and endPaymentTime != ''"><!-- 支付时间检索 -->
                AND date_format(update_time,'%y%m%d') &lt;= date_format(#{endPaymentTime},'%y%m%d')
            </if>
            <if test="businessTypeList != null and businessTypeList.size() > 0">
                and business_type in
                <foreach collection="businessTypeList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

        </where>
        order by update_time desc
    </select>
    <select id="getPayWechatRecord" resultMap="payRecordVoMap" parameterType="com.tebo.system.api.domain.dto.PayWechatRecordQueryDTO">
        select <include refid="Base_Column_List"></include>
        from pay_wechat_record
        <where>
            <if test="tenantId != null and tenantId != ''">
                AND tenant_id=#{tenantId}
            </if>
            <if test="shopId != null and shopId != ''">
                AND shopId=#{shopId}
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND order_No= #{orderNo}
            </if>
            <if test="source != null and source != ''">
                AND source= #{source}
            </if>
            <if test="businessType != null and businessType != ''">
                AND business_type= #{businessType}
            </if>
            <if test="startPaymentTime != null and startPaymentTime != ''"><!-- 支付时间检索 -->
                AND date_format(update_time,'%y%m%d') &gt;= date_format(#{startPaymentTime},'%y%m%d')
            </if>
            <if test="endPaymentTime != null and endPaymentTime != ''"><!-- 支付时间检索 -->
                AND date_format(update_time,'%y%m%d') &lt;= date_format(#{endPaymentTime},'%y%m%d')
            </if>
        </where>
        limit 1
    </select>

    <select id="getTotalAmount" parameterType="com.tebo.system.api.domain.dto.PayWechatRecordQueryDTO" resultType="Integer">
        select sum(amount)
        from pay_wechat_record
        <where>
            pay_status = 1
            <if test="tenantId != null and tenantId != ''">
                AND tenant_id=#{tenantId}
            </if>
            <if test="shopId != null and shopId != ''">
                AND shopId = #{shopId}
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND order_No= #{orderNo}
            </if>
            <if test="source != null and source != ''">
                AND source= #{source}
            </if>
            <if test="businessType != null and businessType != ''">
                AND business_type= #{businessType}
            </if>
            <if test="amountStart != null">
                AND amount > #{amountStart}
            </if>
            <if test="amountEnd != null">
                AND amount &lt;= #{amountEnd}
            </if>
            <if test="channel != null">
                and channel = #{channel}
            </if>
            <if test="startPaymentTime != null and startPaymentTime != ''"><!-- 支付时间检索 -->
                AND date_format(update_time,'%y%m%d') &gt;= date_format(#{startPaymentTime},'%y%m%d')
            </if>
            <if test="endPaymentTime != null and endPaymentTime != ''"><!-- 支付时间检索 -->
                AND date_format(update_time,'%y%m%d') &lt;= date_format(#{endPaymentTime},'%y%m%d')
            </if>
            <if test="businessTypeList != null and businessTypeList.size() > 0">
                and business_type in
                <foreach collection="businessTypeList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getSupplierSubMchid" resultType="java.lang.String">
        select merchants_id
        from pay_shop_merchants
        where tenant_id = #{supplierCode}
    </select>

    <select id="getShopAmountTop100" resultMap="reportShopDealVO">
        select shop_name,shopId, sum(amount) as amountAll
        from pay_wechat_record where pay_status = 1
        <if test="startPaymentTime != null and startPaymentTime != ''"><!-- 支付时间检索 -->
            AND date_format(update_time,'%y%m%d') &gt;= date_format(#{startPaymentTime},'%y%m%d')
        </if>
        <if test="endPaymentTime != null and endPaymentTime != ''"><!-- 支付时间检索 -->
            AND date_format(update_time,'%y%m%d') &lt;= date_format(#{endPaymentTime},'%y%m%d')
        </if>
        group by shop_name order by amountAll desc limit 100
    </select>

        <select id="getShopAmountAll" resultMap="reportShopDealVO">
        select shop_name,shopId, sum(amount) as amountAll
        from pay_wechat_record where pay_status = 1
        <if test="startPaymentTime != null and startPaymentTime != ''"><!-- 支付时间检索 -->
            AND date_format(update_time,'%y%m%d') &gt;= date_format(#{startPaymentTime},'%y%m%d')
        </if>
        <if test="endPaymentTime != null and endPaymentTime != ''"><!-- 支付时间检索 -->
            AND date_format(update_time,'%y%m%d') &lt;= date_format(#{endPaymentTime},'%y%m%d')
        </if>
        group by shop_name order by amountAll desc
    </select>



    <select id="getSupplierSubMchid" resultType="java.lang.String">
        select merchants_id
        from pay_shop_merchants
        where tenant_id = #{supplierCode}
    </select>
    <select id="selectOrderNoForSupplier" resultType="java.lang.String">
        select  order_no from tebo_gift_pack_order
        where rice_verification_status = 1
        and pay_time &lt; '2025-03-12'
        and date(rice_verification_time) =  '2025-03-17'
        order by rice_verification_time desc
--         limit 1400, 200
    </select>
</mapper>
