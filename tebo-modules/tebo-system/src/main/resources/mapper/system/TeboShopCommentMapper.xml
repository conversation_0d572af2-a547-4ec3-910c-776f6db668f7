<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboShopCommentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboShopCommentDO">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="total_comment" property="totalComment"/>
        <result column="service_process" property="serviceProcess"/>
        <result column="professional_image" property="professionalImage"/>
        <result column="professional_competence" property="professionalCompetence"/>
        <result column="service_attitude" property="serviceAttitude"/>
        <result column="comment_content" property="commentContent"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , shop_id, total_comment, service_process, professional_image, professional_competence, service_attitude, comment_content,remark, create_by, create_time, update_by, update_time
    </sql>


    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tebo_shop_comment
        where 1=1
        <if test="shopIds != null and shopIds.size() > 0">
            and shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by create_time desc
    </select>

</mapper>
