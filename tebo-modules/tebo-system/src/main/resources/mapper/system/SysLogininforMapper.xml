<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.SysLogininforMapper">

	<resultMap type="com.tebo.system.api.domain.SysLogininfor" id="SysLogininforResult">
		<id     property="infoId"        column="info_id"           />
		<result property="userName"      column="user_name"         />
		<result property="status"        column="status"            />
		<result property="ipaddr"        column="ipaddr"            />
		<result property="msg"           column="msg"               />
		<result property="accessTime"    column="access_time"       />
		<result property="source"    column="source"       />
	</resultMap>

	<insert id="insertLogininfor" parameterType="com.tebo.system.api.domain.SysLogininfor">
		insert into sys_logininfor (user_name, status, ipaddr, msg, access_time, source)
		values (#{userName}, #{status}, #{ipaddr}, #{msg}, #{accessTime}, #{source})
	</insert>

	<select id="selectLogininforList" parameterType="com.tebo.system.api.domain.SysLogininfor" resultMap="SysLogininforResult">
		select info_id, user_name, ipaddr, status, msg, access_time, source from sys_logininfor
		<where>
			<if test="ipaddr != null and ipaddr != ''">
				AND ipaddr like concat('%', #{ipaddr}, '%')
			</if>
			<if test="status != null and status != ''">
				AND status = #{status}
			</if>
			<if test="source != null and source != ''">
				AND source = #{source}
			</if>
			<if test="userName != null and userName != ''">
				AND user_name like concat('%', #{userName}, '%')
			</if>
			<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
				AND access_time &gt;= #{params.beginTime}
			</if>
			<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
				AND access_time &lt;= #{params.endTime}
			</if>
		</where>
		order by info_id desc
	</select>

	<delete id="deleteLogininforByIds" parameterType="Long">
 		delete from sys_logininfor where info_id in
 		<foreach collection="array" item="infoId" open="(" separator="," close=")">
 			#{infoId}
        </foreach>
 	</delete>

    <update id="cleanLogininfor">
        truncate table sys_logininfor
    </update>

</mapper>
