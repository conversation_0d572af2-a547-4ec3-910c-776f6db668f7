<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboShopPostionChangeAccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboShopPostionChangeAccountDO">
        <id column="id" property="id" />
        <result column="phone_number" property="phoneNumber" />
        <result column="shop_name" property="shopName" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, phone_number, shop_name, `status`, create_time, update_time
    </sql>

</mapper>
