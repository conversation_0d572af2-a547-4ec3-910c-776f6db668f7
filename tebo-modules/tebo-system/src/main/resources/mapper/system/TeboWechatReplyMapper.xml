<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboWechatReplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboWechatReplyDO">
        <id column="id" property="id"/>
        <result column="ask_content" property="askContent"/>
        <result column="answer_content" property="answerContent"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , ask_content, answer_content, create_by, create_time, update_by, update_time
    </sql>


    <select id="getByAskContent" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tebo_wechat_reply
        where ask_content = #{askContent}
    </select>
</mapper>
