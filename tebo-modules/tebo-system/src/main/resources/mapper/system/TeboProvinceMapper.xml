<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboProvinceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboProvinceDO">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, `name`, `code`, create_time, update_time
    </sql>
    <select id="queryCityByProvinceId" resultType="com.tebo.system.entity.TeboCityDO">
        select id, name, code, province_id
        from tebo_city
        where province_id = #{provinceId}
    </select>
    <select id="queryDistrictByCityId" resultType="com.tebo.system.entity.TeboDistrictDO">
        select id, name, code, city_id
        from tebo_district
        where city_id = #{cityId}
    </select>
    <select id="queryProvinceByCode" resultType="java.lang.String">
        select name
        from tebo_province
        where code = #{code}
    </select>
    <select id="queryCityByCode" resultType="java.lang.String">
        select name
        from tebo_city
        where code = #{code}
    </select>
    <select id="queryDistrictByCode" resultType="java.lang.String">
        select name
        from tebo_district
        where code = #{code}
    </select>

    <select id="queryProvinceByName" resultType="com.tebo.system.entity.TeboProvinceDO">
        select id, name, code, create_time, update_time
        from tebo_province
        where name = #{name}
    </select>
    <insert id="batchInsertCity">
        insert into tebo_city (id, name, code, province_id)
        values
        <foreach collection="cityList" item="city" index="index" separator=",">
            (#{city.id}, #{city.name}, #{city.code}, #{city.provinceId})
        </foreach>
    </insert>
    <insert id="batchInsertDistrict">
        insert into tebo_district (id, name, code, city_id)
        values
        <foreach collection="districtList" item="district" index="index" separator=",">
            (#{district.id}, #{district.name}, #{district.code}, #{district.cityId})
        </foreach>
    </insert>
    <delete id="deleteCityByProvinceId">
        delete from tebo_city
        where province_id = #{provinceId}
    </delete>
    <delete id="batchDelDistrict">
        delete from tebo_district
        where code in
        <foreach collection="codeList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>


		<select id="getByName" resultMap="BaseResultMap">
			select
			<include refid="Base_Column_List"/>
			from tebo_province
			where name like concat('%', #{name}, '%') limit 1
	</select>
</mapper>
