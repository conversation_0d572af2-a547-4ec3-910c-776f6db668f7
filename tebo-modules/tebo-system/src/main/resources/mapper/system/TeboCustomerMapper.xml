<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.consumer.TeboCustomerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboCustomerDO">
        <id column="id" property="id" />
        <result column="unionid" property="unionid" />
        <result column="openid" property="openid" />
        <result column="nick_name" property="nickName" />
        <result column="user_type" property="userType" />
        <result column="phone_number" property="phoneNumber" />
        <result column="gender" property="gender" />
        <result column="avatar_url" property="avatarUrl" />
        <result column="country" property="country" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="address" property="address" />
        <result column="status" property="status" />
        <result column="wallet_id" property="walletId" />
        <result column="channel" property="channel" />
        <result column="del_flag" property="delFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, unionid, openid, nick_name, user_type, phone_number, gender, avatar_url, country, province, city, address, `status`, del_flag, create_by, create_time, update_by, update_time,leader_id,wallet_id,channel
    </sql>

    <select id="selectByUnionId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from tebo_customer
        where unionid = #{unionid} and del_flag = 0
    </select>

    <select id="selectByPhoneNumber" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from tebo_customer
        where phone_number = #{phoneNumber} and del_flag = 0 limit 1
    </select>
    <select id="list" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from tebo_customer
        where del_flag = 0
        <if test="hasPhone">
            and phone_number &lt;&gt; ''
        </if>
        <if test="unionId!= null  and unionId !=''">
            and unionid = #{unionId}
        </if>
        <if test="unionIds != null and unionIds.size() > 0">
            and unionid in
            <foreach collection="unionIds" item="tenantId" open="(" separator="," close=")">
                #{tenantId}
            </foreach>
        </if>
        order by create_time desc
    </select>

    <select id="selectByOpenId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from tebo_customer
        where openid = #{openId}
    </select>

    <select id="getPromotionNumber"  resultType="Integer">
        select count(id)
        from tebo_customer
        where leader_code = #{leaderCode}
    </select>

    <select id="selectByUnionIdList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_customer
        where del_flag = 0
        <if test="unionIdList != null and unionIdList.size() > 0">
            and unionid in
            <foreach collection="unionIdList" item="unionId" open="(" separator="," close=")">
                #{unionId}
            </foreach>
        </if>
    </select>
</mapper>
