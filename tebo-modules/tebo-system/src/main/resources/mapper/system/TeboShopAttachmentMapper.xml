<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboShopAttachmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboShopAttachmentDO">
        <result column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="store_qr_code" property="storeQrCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, store_qr_code, create_time, update_time
    </sql>

    <select id="selectByShopId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_shop_attachment
        where shop_id = #{shopId}
    </select>
</mapper>
