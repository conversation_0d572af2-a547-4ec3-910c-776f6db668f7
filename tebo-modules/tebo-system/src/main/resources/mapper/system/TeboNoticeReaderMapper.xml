<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboNoticeReaderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboNoticeReaderDO">
        <result column="id" property="id" />
        <result column="union_id" property="unionId" />
        <result column="notice_id" property="noticeId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, union_id, notice_id, create_time, update_time
    </sql>

    <select id="getNoticeReader" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_notice_reader
        <where>
            <if test="unionId!= null">
                and union_id = #{unionId}
            </if>
            <if test="noticeId!= null">
                and notice_id = #{noticeId}
            </if>
            <if test="accountId!= null">
                and account_id = #{accountId}
            </if>
           limit 1
        </where>
    </select>

</mapper>
