<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboShopMappingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboShopMappingDO">
        <id column="id" property="id" />
        <result column="tebo_shop_id" property="teboShopId" />
        <result column="dianpei_shop_id" property="dianpeiShopId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="authorization_time" property="authorizationTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tebo_shop_id, dianpei_shop_id, create_by, create_time, update_by, update_time, authorization_time, del_flag
    </sql>

</mapper>
