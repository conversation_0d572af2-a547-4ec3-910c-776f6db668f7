<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboNoticeCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboNoticeCategoryDO">
        <result column="id" property="id" />
        <result column="category_name" property="categoryName" />
        <result column="category_no" property="categoryNo" />
        <result column="show" property="show" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <resultMap id="noticeCount" type="com.tebo.system.domain.vo.notice.TeboNoticeNumber">
        <result column="notice_category_id" property="noticeCategoryId" />
        <result column="count" property="count" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, category_name, category_no, `show`, create_time, update_time, del_flag
    </sql>
    <select id="selectNoticeCategoryById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_notice_category where id = #{id}
    </select>

    <select id="selectNoticeCategoryList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_notice_category
    </select>

    <select id="getNoticeCount" resultMap="noticeCount">
        SELECT notice_category_id,count(1) as count FROM tebo_notice
        WHERE notice_category_id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY notice_category_id
    </select>

    <select id="countByCategoryName" resultType="Integer">
        select count(id) from tebo_notice_category where category_name = #{categoryName}
    </select>
</mapper>
