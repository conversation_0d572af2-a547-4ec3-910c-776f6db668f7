<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboRoleDO">
        <result column="id" property="id" />
        <result column="role_name" property="roleName" />
        <result column="role_key" property="roleKey" />
        <result column="create_time" property="createTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, role_name,role_key, create_time, del_flag
    </sql>

    <select id="getTeboAccountRoleList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_role
    </select>

    <select id="getTeboAccountRole" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_role where role_key = #{roleKey}
    </select>
</mapper>
