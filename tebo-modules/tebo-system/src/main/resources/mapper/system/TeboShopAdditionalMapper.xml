<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboShopAdditionalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboShopAdditionalDO">
        <result column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="lst_bond" property="lstBond" />
        <result column="lst_review_time" property="lstReviewTime" />
        <result column="lst_review_status" property="lstReviewStatus" />
        <result column="lst_status" property="lstStatus" />
        <result column="lst_shop_name" property="lstShopName" />
        <result column="lst_bond_time" property="lstBondTime" />
        <result column="service_type" property="serviceType" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>


    <resultMap id="shopResultMap" type="com.tebo.system.api.domain.view.TeboMerchantShopVO">
        <result column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="lst_status" property="lstStatus" />
        <result column="lst_review_status" property="lstReviewStatus" />
        <result column="lst_shop_name" property="lstShopName" />
        <result column="lst_review_time" property="lstReviewTime" />
        <result column="lst_bond" property="lstBond" />
        <result column="lst_bond_time" property="lstBondTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="shop_code" property="shopCode" />
        <result column="shop_type" property="shopType" />
        <result column="vip" property="vip" />
        <result column="shop_boss_name" property="shopBossName" />
        <result column="phone_number" property="phoneNumber" />
        <result column="tenant_name" property="tenantName" />
        <result column="shop_name" property="shopName" />
        <result column="alipay_name" property="alipayName" />
        <result column="alipay_account" property="alipayAccount" />
        <result column="address" property="address" />
        <result column="area_name" property="areaName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id,lst_status, lst_review_status,lst_shop_name,lst_bond,create_time,update_time,lst_review_time,service_type
    </sql>

    <sql id="shop_Column_List">
        a.id, a.shop_id,a.lst_status, a.lst_review_status,a.lst_shop_name,a.lst_bond,a.lst_bond_time,a.create_time,a.update_time,a.lst_review_time,a.service_type,
        s.shop_code,s.shop_type,s.vip,s.shop_boss_name,s.phone_number,s.tenant_name,s.shop_name,s.alipay_name ,s.alipay_account ,s.address,s.area_name
    </sql>

    <select id="getReviewList" resultMap="shopResultMap">
        select <include refid="shop_Column_List"></include> from tebo_shop_additional a
        left join tebo_shop s on a.shop_id = s.id
        <where>
            <if test="lstShopName!= null and lstShopName!='' ">
                and (a.lst_shop_name like concat('%', #{lstShopName}, '%')
                or s.shop_name like concat('%', #{lstShopName}, '%'))
            </if>
            <if test="districtName!= null and districtName!='' ">
                and s.area_name like concat('%', #{districtName}, '%')
            </if>
            <if test="phoneNumber!= null and phoneNumber!='' ">
                and s.phone_number like concat('%', #{phoneNumber}, '%')
            </if>
            <if test="reviewStatus!= null and reviewStatus != 3 ">
                and a.lst_review_status = #{reviewStatus}
            </if>
            <if test="lstStatus!= null">
                and a.lst_status = #{lstStatus}
            </if>
            <if test="reviewStatus!= null and reviewStatus == 3 ">
                and a.lst_review_status is null
            </if>
            <if test="payDeposit!= null and payDeposit == 1 ">
                and a.lst_bond > 0
            </if>
            <if test="payDeposit!= null and payDeposit == 2 ">
                and a.lst_bond  is null
            </if>
            <if test="createTimeStartSecond!= null">
                and a.create_time >= #{createTimeStartSecond ,jdbcType=TIMESTAMP}
            </if>
            <if test="createTimeEndSecond!= null">
                and #{createTimeEndSecond ,jdbcType=TIMESTAMP} > a.create_time
            </if>
            <if test="startPayTimeSecond!= null">
                and a.lst_bond_time >= #{startPayTimeSecond ,jdbcType=TIMESTAMP}
            </if>
            <if test="endPayTimeSecond!= null">
                and #{endPayTimeSecond ,jdbcType=TIMESTAMP} > a.lst_bond_time
            </if>
        </where>
        order by a.lst_status asc,a.create_time desc
    </select>

    <select id="getByShopId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_shop_additional
        where shop_id = #{shopId}
    </select>

    <select id="getLstShopList" resultType="com.tebo.system.api.domain.view.TeboMerchantShopVO">
        select <include refid="shop_Column_List"></include> from tebo_shop_additional a
        left join tebo_shop s on a.shop_id = s.id
        <where>
            lst_status = 1 and lst_review_status = 1
            <if test="lstShopName!= null and lstShopName!='' ">
                and a.lst_shop_name like concat('%', #{lstShopName}, '%')
            </if>
            <if test="shopName!= null and shopName!='' ">
                and s.shop_name like concat('%', #{shopName}, '%')
            </if>
        </where>
    </select>

    <select id="selectLstShopById" resultType="com.tebo.system.api.domain.view.TeboAdditionalShopVO">
        select <include refid="shop_Column_List"></include> from tebo_shop_additional a
        left join tebo_shop s on a.shop_id = s.id
        <where>
          a.shop_id = #{id}
        </where>
    </select>

    <select id="selectLstShopDetailById" resultType="com.tebo.system.api.domain.view.TeboMerchantShopVO">
        select <include refid="shop_Column_List"></include> from tebo_shop_additional a
        left join tebo_shop s on a.shop_id = s.id
        <where>
            a.shop_id = #{id}
        </where>
    </select>

    <select id="selectLstShopDetail"  resultType="com.tebo.system.api.domain.view.TeboMerchantShopVO">
        select <include refid="shop_Column_List"></include> from tebo_shop_additional a
        left join tebo_shop s on a.shop_id = s.id
        <where>
           a.shop_id = #{id}
        </where>
    </select>
</mapper>
