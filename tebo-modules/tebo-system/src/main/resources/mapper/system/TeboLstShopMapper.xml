<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboLstShopMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboLstShopDO">
        <id column="id" property="id"/>
        <result column="shop_code" property="shopCode"/>
        <result column="shop_qr_code" property="shopQrCode"/>
        <result column="shop_name" property="shopName"/>
        <result column="shop_type" property="shopType"/>
        <result column="shop_nature" property="shopNature"/>
        <result column="shop_brand" property="shopBrand"/>
        <result column="shop_boss_name" property="shopBossName"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="tenant_name" property="tenantName"/>
        <result column="area" property="area"/>
        <result column="area_code" property="areaCode"/>
        <result column="area_name" property="areaName"/>
        <result column="address" property="address"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="sales_phone" property="salesPhone"/>
        <result column="after_sale_phone" property="afterSalePhone"/>
        <result column="open_time" property="openTime"/>
        <result column="number_calling_service" property="numberCallingService"/>
        <result column="show_nearby_shop" property="showNearbyShop"/>
        <result column="customer_subject" property="customerSubject"/>
        <result column="pay_type" property="payType"/>
        <result column="account_bank" property="accountBank"/>
        <result column="account_number" property="accountNumber"/>
        <result column="id_card_front" property="idCardFront"/>
        <result column="id_card_back" property="idCardBack"/>
        <result column="id_card" property="idCard"/>
        <result column="business_license" property="businessLicense"/>
        <result column="business_license_code" property="businessLicenseCode"/>
        <result column="legal_person" property="legalPerson"/>
        <result column="register_capital" property="registerCapital"/>
        <result column="establish_time" property="establishTime"/>
        <result column="shop_pic" property="shopPic"/>
        <result column="remark" property="remark"/>
        <result column="cid" property="cid"/>
        <result column="status" property="status"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="shop_slogan" property="shopSlogan"/>
        <result column="promotion_video" property="promotionVideo"/>
        <result column="email" property="email"/>
        <result column="account_branch_bank" property="accountBranchBank"/>
        <result column="wx_seller_name" property="wxSellerName"/>
        <result column="goods_earnest_money" property="goodsEarnestMoney"/>
        <result column="service_earnest_money" property="serviceEarnestMoney"/>
        <result column="shop_star_level" property="shopStarLevel"/>
        <result column="shop_level" property="shopLevel"/>
        <result column="integration_coefficient" property="integrationCoefficient"/>
        <result column="pay_earnest_time" property="payEarnestTime"/>
        <result column="open_workbench" property="openWorkbench"/>
        <result column="open_mall" property="openMall"/>
        <result column="open_door" property="openDoor"/>
        <result column="home_service_fee" property="homeServiceFee"/>
        <result column="open_rescue" property="openRescue"/>
        <result column="id_card_copy" property="idCardCopy"/>
        <result column="id_card_national" property="idCardNational"/>
        <result column="license_copy" property="licenseCopy"/>
        <result column="id_card_period_begin" property="idCardPeriodBegin"/>
        <result column="id_card_period_end" property="idCardPeriodEnd"/>
        <result column="id_card_address" property="idCardAddress"/>
        <result column="note" property="note"/>
        <result column="license_address" property="licenseAddress"/>
        <result column="bank_address_code" property="bankAddressCode"/>
        <result column="service_scope" property="serviceScope"/>
        <result column="id_card_name" property="idCardName"/>
        <result column="bank_branch_id" property="bankBranchId"/>
        <result column="alipay_name" property="alipayName"/>
        <result column="alipay_account" property="alipayAccount"/>
        <result column="shop_source" property="shopSource"/>
        <result column="lst_shop_code" property="lstShopCode"/>
        <result column="transform_status" property="transformStatus"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="lst_shop_level" property="lstShopLevel"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , shop_code, shop_qr_code, shop_name, shop_type, shop_nature, shop_brand, shop_boss_name, phone_number, tenant_id, tenant_name,  area, area_code, area_name, address, longitude, latitude, sales_phone, after_sale_phone, open_time, number_calling_service, show_nearby_shop, customer_subject, pay_type, account_bank, account_number, id_card_front, id_card_back, id_card, business_license, business_license_code, legal_person, register_capital, establish_time, shop_pic, remark, cid, `status`, del_flag, create_by, create_time, update_by, update_time, shop_slogan, promotion_video, email, account_branch_bank, wx_seller_name, goods_earnest_money, service_earnest_money, shop_star_level, shop_level, integration_coefficient, pay_earnest_time, open_workbench, open_mall, open_door, home_service_fee, open_rescue, id_card_copy, id_card_national, license_copy, id_card_period_begin, id_card_period_end, id_card_address, note, license_address, bank_address_code, service_scope, id_card_name, bank_branch_id, alipay_name, alipay_account,
shop_source, lst_shop_code ,transform_status,supplier_name,lst_shop_level
    </sql>


    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tebo_lst_shop
        where del_flag = 0
        <if test="keyword != null and keyword != ''">
            and ((shop_name like concat('%', #{keyword}, '%'))
            or (shop_boss_name like concat('%', #{keyword}, '%'))
            or (phone_number like concat('%', #{keyword}, '%'))
            or (tenant_name like concat('%', #{keyword}, '%'))
            )
        </if>
        <if test="shopNameLike != null and shopNameLike!='' ">
            and shop_name like concat('%', #{shopNameLike}, '%')
        </if>
        <if test="tenantId != null">
            and tenant_id = #{tenantId}
        </if>
        <if test="showNearbyShop != null">
            and show_nearby_shop = #{showNearbyShop}
        </if>
        <if test="shopType!= null">
            and shop_type = #{shopType}
        </if>
        <if test="transformStatus!= null">
            and transform_status = #{transformStatus}
        </if>

        <if test="shopLevel!= null and shopLevel!='' ">
            and shop_level = #{shopLevel}
        </if>
        <if test="lstShopLevel!= null and lstShopLevel!='' ">
            and lst_shop_level = #{lstShopLevel}
        </if>
        <if test="areaLike!= null and areaLike!='' ">
            and area_name like concat('%', #{areaLike}, '%')
        </if>
        <if test="supplierNameLike!= null and supplierNameLike!='' ">
            and supplier_name like concat('%', #{supplierNameLike}, '%')
        </if>

        <if test="shopNature!= null">
            and shop_nature = #{shopNature}
        </if>

        <if test="shopNatureList != null and shopNatureList.size() > 0">
            and shop_nature in
            <foreach collection="shopNatureList" item="shopNature" open="(" separator="," close=")">
                #{shopNature}
            </foreach>
        </if>


        <if test="serviceScopeList != null and serviceScopeList.size() > 0">
            and
            <foreach collection="serviceScopeList" item="scope" open="(" separator=" or " close=")">
                service_scope like concat('%', #{scope}, '%')
            </foreach>
        </if>


        <if test="status!= null">
            and status = #{status}
        </if>
        <if test="openRescue!= null">
            and open_rescue = #{openRescue}
        </if>
        <if test="openMall!= null">
            and open_mall = #{openMall}
        </if>
        <if test="idList != null and idList.size() > 0">
            and id in
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="tenantIdList != null and tenantIdList.size() > 0">
            and tenant_id in
            <foreach collection="tenantIdList" item="tenantId" open="(" separator="," close=")">
                #{tenantId}
            </foreach>
        </if>
        <if test="createTimeStartSecond!= null">
            and create_time >= #{createTimeStartSecond ,jdbcType=TIMESTAMP}
        </if>
        <if test="createTimeEndSecond!= null">
            and #{createTimeEndSecond ,jdbcType=TIMESTAMP} > create_time
        </if>

        <if test="areaStr!= null">
            and area_name like concat('%', #{areaStr}, '%')
        </if>

        <if test="orderByColumn==null or orderByColumn ==''">
            order by create_time desc
        </if>

        <if test="orderByColumn!=null and orderByColumn!=''">
            order by #{orderByColumn}
        </if>
    </select>

</mapper>
