<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.PayWechatRefundMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.PayWechatRefundDO">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="sub_mchid" property="subMchid" />
        <result column="transaction_id" property="transactionId" />
        <result column="refund_no" property="refundNo" />
        <result column="amount" property="amount" />
        <result column="status" property="status" />
        <result column="source" property="source" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, sub_mchid, transaction_id, refund_no, amount, `status`, `source`, create_time, update_time
    </sql>

</mapper>
