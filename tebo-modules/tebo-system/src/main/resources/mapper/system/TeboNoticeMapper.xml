<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboNoticeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboNoticeDO">
        <result column="id" property="id" />
        <result column="title" property="title" />
        <result column="notice_category_id" property="noticeCategoryId" />
        <result column="topic_category" property="topicCategory" />
        <result column="content" property="content" />
        <result column="createBy" property="createBy" />
        <result column="pop_up_pic" property="popUpPic" />
        <result column="list_pic" property="listPic" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <resultMap id="categoryResultMap" type="com.tebo.system.domain.vo.notice.TeboNoticeVO">
        <result column="id" property="id" />
        <result column="title" property="title" />
        <result column="notice_category_id" property="noticeCategoryId" />
        <result column="topic_category" property="topicCategory" />
        <result column="content" property="content" />
        <result column="createBy" property="createBy" />
        <result column="pop_up_pic" property="popUpPic" />
        <result column="list_pic" property="listPic" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="category_name" property="noticeCategoryName" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, notice_category_id, topic_category, content,pop_up_pic,list_pic, createBy, create_time, update_time, del_flag
    </sql>

    <sql id="category_Column_List">
        n.id, title, notice_category_id, topic_category, content,pop_up_pic,list_pic,tenant_id, createBy, n.create_time, n.update_time, n.del_flag,c.category_name
    </sql>
    <select id="selectNoticeList" resultMap="categoryResultMap">
    select <include refid="category_Column_List"></include> from tebo_notice  n left join tebo_notice_category c on n.notice_category_id = c.id
    <where>
        <if test="title!= null">
            and n.title like concat('%', #{title}, '%')
        </if>
        <if test="tenantId!= null">
            and (n.tenant_id = #{tenantId} or n.tenant_id = 0)
        </if>
        <if test="noticeCategoryId!= null">
            and n.notice_category_id = #{noticeCategoryId}
        </if>
        <if test="topicCategory!= null">
            and n.topic_category = #{topicCategory}
        </if>
        <if test="topicCategoryList != null and topicCategoryList.size() > 0">
            and n.topic_category in
            <foreach collection="topicCategoryList" item="category" open="(" separator="," close=")">
                #{category}
            </foreach>
        </if>
        <if test="startUpdateTime!= null and startUpdateTime!= ''"><!-- 开始时间检索 -->
            AND date_format(n.update_time,'%y%m%d') &gt;= date_format(#{startUpdateTime},'%y%m%d')
        </if>
        <if test="endUpdateTime!= null and endUpdateTime!= ''"><!-- 结束时间检索 -->
            AND date_format(n.update_time,'%y%m%d') &lt;= date_format(#{endUpdateTime},'%y%m%d')
        </if>
    </where>
        order by n.update_time desc
    </select>

    <select id="selectNoticeById" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from tebo_notice where id = #{id}
    </select>

    <select id="getUpToDateNotice" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_notice
        <where>
        <if test="topicCategoryList != null and topicCategoryList.size() > 0">
            and topic_category in
            <foreach collection="topicCategoryList" item="category" open="(" separator="," close=")">
                #{category}
            </foreach>
        </if>
        <if test="tenantId!= null">
           and (tenant_id = #{tenantId} or tenant_id = 0)
        </if>
        </where>
        order by update_time desc limit 1
    </select>
</mapper>
