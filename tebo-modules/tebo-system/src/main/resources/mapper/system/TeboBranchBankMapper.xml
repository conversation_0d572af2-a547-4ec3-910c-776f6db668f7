<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboBranchBankMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboBranchBankDO">
        <id column="id" property="id" />
        <result column="branch_no" property="branchNo" />
        <result column="branch_name" property="branchName" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, branch_no, branch_name, create_by, create_time, update_by, update_time
    </sql>

    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tebo_branch_bank
        where 1=1
        <if test="nameLike!= null and nameLike!= ''">
            and branch_name like concat('%', #{nameLike}, '%')
        </if>
        <if test="branchNo!= null and branchNo!= ''">
            and branch_no = #{branchNo}
        </if>
        <if test="branchNos != null and branchNos.size() > 0">
            and branch_no in
            <foreach collection="branchNos" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
