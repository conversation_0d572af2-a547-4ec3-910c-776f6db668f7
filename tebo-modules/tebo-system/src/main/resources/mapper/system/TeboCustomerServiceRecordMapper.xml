<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.consumer.TeboCustomerServiceRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboCustomerServiceRecordDO">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="unionid" property="unionid" />
        <result column="nick_name" property="nickName" />
        <result column="phone_number" property="phoneNumber" />
        <result column="empower_time" property="empowerTime" />
        <result column="last_time" property="lastTime" />
        <result column="last_type" property="lastType" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, shop_id, shop_name, unionid, nick_name, phone_number, empower_time, last_time, last_type, create_by, create_time, update_by, update_time, del_flag
    </sql>
    <select id="selectConsumerRecordInfo" resultType="com.tebo.system.entity.TeboCustomerServiceRecordDO">
        select <include refid="Base_Column_List" />
        from tebo_customer_service_record
        where unionid = #{unionid}
          and shop_id = #{shopId}
          and del_flag = 0
    </select>
    <select id="selectRecordList" resultType="com.tebo.system.entity.TeboCustomerServiceRecordDO">
        select
        <include refid="Base_Column_List"/>
        from tebo_customer_service_record
        where del_flag = 0
        <if test="tenantId != null and tenantId != 0">
            and tenant_id = #{tenantId}
        </if>
        <if test="shopId != null and shopId != ''">
            and shop_id = #{shopId}
        </if>
        <if test="shopIdList!= null and shopIdList.size() > 0">
            and shop_id in
            <foreach collection="shopIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="phoneNumber != null and phoneNumber != ''">
            and phone_number = #{phoneNumber}
        </if>
        <if test="shopName != null and shopName != ''">
            and shop_name like concat('%', #{shopName}, '%')
        </if>
        <if test="lastTimeStartSecond!= null">
            and last_time >= #{lastTimeStartSecond ,jdbcType=TIMESTAMP}
        </if>
        <if test="lastTimeEndSecond!= null">
            and #{lastTimeEndSecond ,jdbcType=TIMESTAMP} > last_time
        </if>
        and del_flag = 0
        order by last_time desc
    </select>

</mapper>
