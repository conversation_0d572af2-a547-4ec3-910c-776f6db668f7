<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboWechatUserSubscribeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboWechatUserSubscribeDO">
        <id column="id" property="id" />
        <result column="open_id" property="openId" />
        <result column="official_account" property="officialAccount" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, open_id, official_account, `status`,unionid, create_by, create_time, update_by, update_time
    </sql>



    <insert id="batchInsert">
        insert into tebo_wechat_user_subscribe ( id, open_id, official_account, `status`,unionid, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.openId}, #{item.officialAccount}, #{item.status}, #{item.unionid}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>
    <select id="getCustomerUnionIdList" resultType="java.lang.String">
        select distinct union_id from tebo_rescue.tebo_coupon_customer
        where status = 2
        and pack_id in (1839262600894873600,1850046332442509312)
    </select>


</mapper>
