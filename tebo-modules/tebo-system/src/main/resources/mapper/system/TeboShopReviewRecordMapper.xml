<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboShopReviewRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboShopReviewRecordDO">
        <result column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="review_status" property="reviewStatus" />
        <result column="wechat_review_status" property="wechatReviewStatus" />
        <result column="fail_reason" property="failReason" />
        <result column="type" property="type" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, type,review_status,wechat_review_status, fail_reason, create_time, update_time,update_by
    </sql>

    <select id="getTeboReviewRecordList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_shop_review_record
        where shop_id = #{shopId}
        <if test="type!= null">
            and type = #{type}
        </if>
        <if test="wechatReviewStatus!= null">
            and wechat_review_status = #{wechatReviewStatus}
        </if>
        <if test="failReason!= null">
            and fail_reason = #{failReason}
        </if>
        order by create_time desc
    </select>

    <select id="getNewReviewRecord" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_shop_review_record
        where shop_id = #{shopId} and review_status = 2
        order by create_time desc limit 1
    </select>
</mapper>
