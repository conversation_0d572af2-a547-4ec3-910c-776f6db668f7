<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboShopPicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboShopPicDO">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="pic_url" property="picUrl"/>
        <result column="pic_type" property="picType"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , shop_id, pic_url, pic_type, create_by, create_time, update_by, update_time
    </sql>


    <select id="listByShopId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tebo_shop_pic
        where
        shop_id = #{shopId}
    </select>

    <select id="listByShopIds" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List"/>
        from tebo_shop_pic
        where
        shop_id in
        <foreach item="shopId" collection="shopIds" open="(" separator="," close=")">
            #{shopId}
        </foreach>
    </select>
</mapper>
