<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboShopMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboShopDO">
        <id column="id" property="id" />
        <result column="shop_code" property="shopCode" />
        <result column="shop_qr_code" property="shopQrCode" />
        <result column="shop_name" property="shopName" />
        <result column="shop_type" property="shopType" />
        <result column="shop_nature" property="shopNature" />
        <result column="shop_brand" property="shopBrand" />
        <result column="shop_boss_name" property="shopBossName" />
        <result column="phone_number" property="phoneNumber" />
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="area" property="area" />
        <result column="area_code" property="areaCode" />
        <result column="area_name" property="areaName" />
        <result column="address" property="address" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="sales_phone" property="salesPhone" />
        <result column="after_sale_phone" property="afterSalePhone" />
        <result column="open_time" property="openTime" />
        <result column="number_calling_service" property="numberCallingService" />
        <result column="show_nearby_shop" property="showNearbyShop" />
        <result column="customer_subject" property="customerSubject" />
        <result column="pay_type" property="payType" />
        <result column="account_bank" property="accountBank" />
        <result column="account_number" property="accountNumber" />
        <result column="id_card_front" property="idCardFront" />
        <result column="id_card_back" property="idCardBack" />
        <result column="id_card" property="idCard" />
        <result column="id_card_name" property="idCardName" />
        <result column="business_license" property="businessLicense" />
        <result column="business_license_code" property="businessLicenseCode" />
        <result column="legal_person" property="legalPerson" />
        <result column="register_capital" property="registerCapital" />
        <result column="establish_time" property="establishTime" />
        <result column="shop_pic" property="shopPic" />
        <result column="cid" property="cid" />
        <result column="email" property="email" />
        <result column="account_branch_bank" property="accountBranchBank" />
        <result column="wx_seller_name" property="wxSellerName" />
        <result column="remark" property="remark"/>
        <result column="status" property="status" />
        <result column="shop_star_level" property="shopStarLevel" />
        <result column="shop_level" property="shopLevel" />
        <result column="integration_coefficient" property="integrationCoefficient" />
        <result column="service_earnest_money" property="serviceEarnestMoney" />
        <result column="goods_earnest_money" property="goodsEarnestMoney" />
        <result column="pay_earnest_time" property="payEarnestTime" />

        <result column="open_workbench" property="openWorkbench" />
        <result column="open_mall" property="openMall" />
        <result column="open_door" property="openDoor" />
        <result column="open_rescue" property="openRescue" />
        <result column="home_service_fee" property="homeServiceFee" />
        <result column="service_scope" property="serviceScope" />
        <result column="shop_source" property="shopSource" />
        <result column="open_third_order" property="openThirdOrder" />
        <result column="business_account" property="businessAccount" />

        <result column="id_card_copy" property="idCardCopy" />
        <result column="id_card_national" property="idCardNational" />
        <result column="license_copy" property="licenseCopy" />
        <result column="id_card_period_begin" property="idCardPeriodBegin" />
        <result column="id_card_period_end" property="idCardPeriodEnd" />
        <result column="id_card_address" property="idCardAddress" />
        <result column="note" property="note" />
        <result column="license_address" property="licenseAddress" />
        <result column="bank_address_code" property="bankAddressCode" />
        <result column="alipay_name" property="alipayName" />
        <result column="alipay_account" property="alipayAccount" />
        <result column="lst_shop_code" property="lstShopCode" />


        <result column="del_flag" property="delFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="vip" property="vip" />
        <result column="wallet_id" property="walletId" />
        <result column="cloud_shop_id" property="cloudShopId" />

        <result column="tebo_note" property="teboNote" />
        <result column="tebo_id_card" property="teboIdCard" />
        <result column="tebo_id_card_name" property="teboIdCardName" />
        <result column="tebo_account_bank" property="teboAccountBank" />
        <result column="tebo_account_number" property="teboAccountNumber" />
        <result column="tebo_account_branch_bank" property="teboAccountBranchBank" />
        <result column="tebo_id_card_front" property="teboIdCardFront" />
        <result column="tebo_id_card_back" property="teboIdCardBack" />
        <result column="tebo_bank_address_code" property="teboBankAddressCode" />
        <result column="tebo_id_card_period_begin" property="teboIdCardPeriodBegin" />
        <result column="tebo_id_card_period_end" property="teboIdCardPeriodEnd" />
        <result column="tebo_id_card_address" property="teboIdCardAddress" />
        <result column="shop_sap_code" property="shopSapCode"></result>
    </resultMap>

    <resultMap id="tenantIdCountMap" type="com.tebo.system.domain.vo.shop.TeboShopNumVO" >
        <result property="tenantId" column="tenant_id" />
        <result property="num" column="num" />
    </resultMap>

    <resultMap id="TimeGroupCountVO" type="com.tebo.system.domain.vo.TimeGroupCountVO">
        <result property="dateStr" column="dateStr"/>
        <result property="count" column="count"/>
    </resultMap>

    <resultMap id="PersonGroupCountVO" type="com.tebo.system.domain.vo.PersonGroupCountVO">
        <result property="businessAccount" column="businessAccount"/>
        <result property="count" column="count"/>
    </resultMap>

    <resultMap id="ShopDataProvVO" type="com.tebo.system.domain.vo.TeboShopDataProvVO">
        <result property="prov" column="prov"/>
        <result property="customerCount" column="customerCount"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_code, shop_qr_code, shop_name, shop_type, shop_nature, shop_brand, shop_boss_name, phone_number, tenant_id, tenant_name, area, area_code, area_name, address, longitude, latitude, sales_phone, after_sale_phone, open_time, number_calling_service, show_nearby_shop, customer_subject, pay_type, account_bank, account_number, id_card_front, id_card_back, id_card, business_license, business_license_code, legal_person, register_capital, establish_time, shop_pic, cid, remark, `status`, email,account_branch_bank,
        wx_seller_name, pay_earnest_time, open_workbench, open_mall, open_door, open_rescue, service_earnest_money, goods_earnest_money,home_service_fee,  shop_star_level, shop_level, integration_coefficient, service_scope ,del_flag, create_by, create_time, update_by, update_time,id_card_copy,id_card_national,license_copy,id_card_period_begin,id_card_period_end,id_card_address,note,license_address,bank_address_code,id_card_name ,alipay_name,alipay_account  ,shop_source,lst_shop_code,vip,wallet_id,cloud_shop_id,
        tebo_account_bank,tebo_account_number,tebo_id_card_front,tebo_id_card_back,tebo_id_card,tebo_id_card_name,
        tebo_account_branch_bank,tebo_id_card_period_begin,tebo_id_card_period_end,tebo_id_card_address,tebo_note,tebo_bank_address_code

    </sql>
    <update id="updateStatus">
        update tebo_shop set `status` = #{status} where id in
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </update>
    <update id="batchDelete">
        update tebo_shop set del_flag = 1 where id in
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </update>

    <select id="groupByDay" resultMap="TimeGroupCountVO">

        SELECT DATE_FORMAT(create_time, '%Y年%m月%d日') AS dateStr,
               SUM(1) AS 'count'
        FROM tebo_shop
        where del_flag = 0
        <include refid="shopWhereSql"/>
        GROUP BY dateStr
        ORDER BY dateStr

    </select>

    <select id="groupByMonth" resultMap="TimeGroupCountVO">

        SELECT DATE_FORMAT(create_time, '%Y年%m月') AS dateStr,
               SUM(1) AS 'count'
        FROM tebo_shop
        where del_flag = 0
        <include refid="shopWhereSql"/>
        GROUP BY dateStr
        ORDER BY dateStr

    </select>


    <select id="groupByPerson" resultMap="PersonGroupCountVO">

        SELECT business_account AS businessAccount,
               SUM(1) AS 'count'
        FROM tebo_shop
        where del_flag = 0 and business_account is not null
        <include refid="shopWhereSql"/>
        GROUP BY business_account
    </select>

    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tebo_shop
        where del_flag = 0
        and shop_source = 1
        <include refid="shopWhereSql"/>
        <if test="orderByColumn!=null and orderByColumn!=''">
            order by #{orderByColumn}
        </if>
        <if test="orderByColumn==null or orderByColumn ==''">
            order by create_time desc
        </if>
    </select>

    <select id="rescueNearbyStore" resultType="com.tebo.system.entity.TeboShopDO">
        select
        <include refid="Base_Column_List"/>
        ,ST_DISTANCE_SPHERE(
        POINT(#{lgt}, #{lnt}),
        POINT(longitude, latitude)
        ) AS distance
        from tebo_shop
        where del_flag = 0 and status = 1 and open_rescue = 1 and open_door = 1
        and longitude is not null and latitude is not null and tenant_id != 1739804744320286720
        order by distance asc
        limit 20
    </select>

    <select id="nearbyStore" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tebo_shop
        where del_flag = 0 and longitude is not null and latitude is not null and tenant_id != 1739804744320286720
        <include refid="shopWhereSql"/>
        <if test="orderByColumn!=null and orderByColumn!=''">
            order by #{orderByColumn}
        </if>
        <if test="orderByColumn==null or orderByColumn ==''">
            order by create_time desc
        </if>

    </select>
    <select id="getShopListForPc" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tebo_shop
        where del_flag = 0
        <include refid="shopWhereSql"/>
        <if test="orderByColumn!=null and orderByColumn!=''">
            order by #{orderByColumn}
        </if>
        <if test="orderByColumn==null or orderByColumn ==''">
            order by create_time desc
        </if>
    </select>
    <sql id="shopWhereSql">
        <if test="keyword != null and keyword != ''">
            and ((shop_name like concat('%', #{keyword}, '%'))
            or (shop_boss_name like concat('%', #{keyword}, '%'))
            or (phone_number like concat('%', #{keyword}, '%'))
            or (tenant_name like concat('%', #{keyword}, '%'))
            )
        </if>
        <if test="shopNameLike != null and shopNameLike!='' ">
            and shop_name like concat('%', #{shopNameLike}, '%')
        </if>
        <if test="tenantId != null">
            and tenant_id = #{tenantId}
        </if>
        <if test="showNearbyShop != null">
            and show_nearby_shop = #{showNearbyShop}
        </if>
        <if test="shopType!= null">
            and shop_type = #{shopType}
        </if>
        <if test="lstShopCode!= null and lstShopCode!='' ">
            and lst_shop_code = #{lstShopCode}
        </if>
        <if test="shopLevel!= null and shopLevel!='' ">
            and shop_level = #{shopLevel}
        </if>
        <if test="shopCode!= null and shopCode!='' ">
            and shop_code = #{shopCode}
        </if>
        <if test="businessAccount!= null and businessAccount!='' ">
            and business_account = #{businessAccount}
        </if>
        <if test="areaLike!= null and areaLike!='' ">
            and area_name like concat('%', #{areaLike}, '%')
        </if>
        <if test="shopBossNameLike!= null and shopBossNameLike!='' ">
            and shop_boss_name like concat('%', #{shopBossNameLike}, '%')
        </if>
        <if test="phoneNumberLike!= null and phoneNumberLike!='' ">
            and phone_number like concat('%', #{phoneNumberLike}, '%')
        </if>
        <if test="shopNature!= null">
            and shop_nature = #{shopNature}
        </if>
        <if test="shopSource!= null">
            and shop_source = #{shopSource}
        </if>

        <if test="shopNatureList != null and shopNatureList.size() > 0">
            and shop_nature in
            <foreach collection="shopNatureList" item="shopNature" open="(" separator="," close=")">
                #{shopNature}
            </foreach>
        </if>

        <if test="shopTypeList != null and shopTypeList.size() > 0">
            and shop_type in
            <foreach collection="shopTypeList" item="shopType" open="(" separator="," close=")">
                #{shopType}
            </foreach>
        </if>


        <if test="serviceScopeList != null and serviceScopeList.size() > 0">
            and
            <foreach collection="serviceScopeList" item="scope" open="(" separator=" or " close=")">
                service_scope like concat('%', #{scope}, '%')
            </foreach>
        </if>


        <if test="status!= null">
            and status = #{status}
        </if>
        <if test="openRescue!= null">
            and open_rescue = #{openRescue}
        </if>
        <if test="openMall!= null">
            and open_mall = #{openMall}
        </if>
        <if test="idList != null and idList.size() > 0">
            and id in
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="tenantIdList != null and tenantIdList.size() > 0">
            and tenant_id in
            <foreach collection="tenantIdList" item="tenantId" open="(" separator="," close=")">
                #{tenantId}
            </foreach>
        </if>
        <if test="createTimeStartSecond!= null">
            and create_time >= #{createTimeStartSecond ,jdbcType=TIMESTAMP}
        </if>
        <if test="createTimeEndSecond!= null">
            and #{createTimeEndSecond ,jdbcType=TIMESTAMP} > create_time
        </if>

        <if test="areaStr!= null">
            and area_name like concat('%', #{areaStr}, '%')
        </if>

        <if test="phoneNumber!= null">
            and phone_number = #{phoneNumber}
        </if>

        <if test="vip!= null">
            and vip = #{vip}
        </if>

    </sql>

    <select id="selectShopCountByPartnerId" resultMap="tenantIdCountMap">
        select tenant_id,count(1) as num
            from tebo_shop
            where del_flag = 0
            <if test="tenantIdList != null and tenantIdList.size() > 0" >
                and tenant_id in
                <foreach collection="tenantIdList" item="tenantId" open="(" separator="," close=")">
                    #{tenantId}
                </foreach>
            </if>
        group by tenant_id
    </select>
    <select id="selectCountByPartnerId" resultType="java.lang.Integer">
        select count(1) from tebo_shop
            where del_flag = 0
             and tenant_id in
             <foreach collection="idList" item="tenantId" open="(" separator="," close=")">
                #{tenantId}
            </foreach>

    </select>

    <select id="getByCid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tebo_shop
        where del_flag = 0
        and cid =  #{cid} order by update_time desc limit 1
    </select>


    <select id="countByBusinessLicenseCode"  resultType="java.lang.Integer">
        select count(*)
        from tebo_shop
        where del_flag = 0
          and business_license_code = #{businessLicenseCode}
    </select>

    <select id="listMarkShop" resultType="com.tebo.system.entity.TeboShopDO">
        SELECT
        ts.*
        FROM
        tebo_customer_shop_mark csm
        LEFT JOIN tebo_shop ts ON csm.shop_id = ts.id
        WHERE
        ts.`status` = 1
        AND customer_id = #{customerId}
        ORDER BY
        csm.create_time DESC
    </select>

    <update id="updateTeboShop" >
        update tebo_shop
        <set>
            <if test="latitude!= null">
                latitude = #{latitude},
            </if>
            <if test="longitude!= null">
                longitude = #{longitude},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getShopByPhoneNumber" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>    from tebo_shop
       where phone_number = #{phoneNumber} and del_flag = 0  limit 1
    </select>

    <select id="selectByNewTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>    from tebo_shop
        where new_tenant_id = #{newTenantId} limit 1
    </select>
    <select id="listNew" resultType="com.tebo.system.entity.TeboShopDO">
        select
        <include refid="Base_Column_List"/>
        ,ST_DISTANCE_SPHERE(
        POINT(#{lgt}, #{lnt}),
        POINT(longitude, latitude)
        ) AS distance
        from tebo_shop
        where del_flag = 0
        and shop_source in (1,2)
        <include refid="shopWhereSql"/>
        order by distance asc
        limit 20
    </select>

    <select id="nearbyShopList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        ,ST_DISTANCE_SPHERE(
        POINT(#{lgt}, #{lnt}),
        POINT(longitude, latitude)
        ) AS distance
        from tebo_shop
        where del_flag = 0 and status = 1 and (open_mall = 1 or vip = 1)
        <include refid="shopWhereSql"/>
        and tenant_id != 1739804744320286720 and  longitude is not null and latitude is not null and longitude != "" and latitude != ""
        order by distance asc
        limit 50
    </select>

    <select id="getHomePageMallShopByDistrictCodeList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        ,ST_DISTANCE_SPHERE(
        POINT(#{lgt}, #{lnt}),
        POINT(longitude, latitude)
        ) AS distance
        from tebo_shop
        where del_flag = 0 and (open_mall = 1 or vip = 1)
        <include refid="shopWhereSql"/>
        and tenant_id != 1739804744320286720 and  longitude is not null and latitude is not null and longitude != "" and latitude != ""
        order by distance asc
        limit 50
    </select>
    <select id="listNewOne" resultType="com.tebo.system.entity.TeboShopDO">
        select
        <include refid="Base_Column_List"/>
        ,ST_DISTANCE_SPHERE(
        POINT(#{lgt}, #{lnt}),
        POINT(longitude, latitude)
        ) AS distance
        from tebo_shop
        where del_flag = 0
        and shop_source in (1,2) and longitude is not null and latitude is not null
        <include refid="shopWhereSql"/>
        order by distance asc
        limit 1
    </select>

    <select id="listForRescue" resultType="com.tebo.system.entity.TeboShopDO">
        select
        <include refid="Base_Column_List"/>
        ,ST_DISTANCE_SPHERE(
        POINT(#{lgt}, #{lnt}),
        POINT(longitude, latitude)
        ) AS distance
        from tebo_shop
        where del_flag = 0 and status = 1 and tenant_id != 1739804744320286720
        and shop_type in (1,2)
        <include refid="shopWhereSql"/>
        order by distance asc
        limit 1
    </select>

    <select id="getPartnerWalletId" resultType="Long">
        SELECT s.wallet_id FROM tebo_shop s LEFT JOIN partner_sale_shop_detail d
        ON s.id = d.tebo_shop_id WHERE d.buy_shop_id = #{cloudShopId}
    </select>
    <select id="getDistanceByShopId" resultType="java.lang.String">
        SELECT
            area
        FROM
            tebo_partner_info
        WHERE
            id = (SELECT tenant_id FROM tebo_shop WHERE id = #{shopId})
    </select>

    <select id="getSapCode" resultType="com.tebo.system.entity.TeboShopDO">
        select id,shop_sap_code,tenant_id from tebo_shop where id = #{shopId}
    </select>
    <select id="getAllTenantSapCode" resultType="com.tebo.system.entity.TeboShopDO">
        select id,shop_sap_code,area_name, alipay_account from tebo_shop
        where shop_sap_code in
        <foreach collection="sapCodeList" item="sapCode" open="(" separator="," close=")">
            #{sapCode}
        </foreach>
    </select>
    <select id="selectUnionIdById" resultType="java.lang.String">
        select union_id from tebo_shop
        where id = #{id}
        limit 1
    </select>
</mapper>
