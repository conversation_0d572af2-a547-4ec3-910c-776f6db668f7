<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.SysOperLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.SysOperLogDO">
        <id column="id" property="id" />
        <result column="oper_model" property="operModel" />
        <result column="oper_desc" property="operDesc" />
        <result column="oper_type" property="operType" />
        <result column="oper_ip" property="operIp" />
        <result column="remark" property="remark" />
        <result column="oper_time" property="operTime" />
        <result column="oper_name" property="operName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, oper_model, oper_desc, oper_type, oper_ip, remark, oper_time, oper_name
    </sql>

</mapper>
