<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.LstShopReviewRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.LstShopReviewRecordDO">
        <result column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="review_status" property="reviewStatus" />
        <result column="reviewer" property="reviewer" />
        <result column="reason" property="reason" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id,reviewer, review_status, reason, create_time, update_time
    </sql>

    <select id="getReviewRecordList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from lst_shop_review_record
        where shop_id = #{shopId} order by create_time desc
    </select>

    <select id="getRecentReviewRecord" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from lst_shop_review_record
        where shop_id = #{shopId}  order by create_time desc limit 1
    </select>
</mapper>
