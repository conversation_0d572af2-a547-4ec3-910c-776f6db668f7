<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboAccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboAccountDO">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="shop_id" property="shopId" />
        <result column="account_name" property="accountName" />
        <result column="user_name" property="userName" />
        <result column="password" property="password" />
        <result column="phone_number" property="phoneNumber" />
        <result column="phone_number_wechat" property="phoneNumberWechat" />
        <result column="sex" property="sex" />
        <result column="avatar" property="avatar" />
        <result column="status" property="status" />
        <result column="online" property="online" />
        <result column="role_id" property="roleId" />
        <result column="del_flag" property="delFlag" />
        <result column="login_date" property="loginDate" />
        <result column="unionid" property="unionid" />
        <result column="open_id" property="openId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
        <result column="type" property="type" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, shop_id,role_id, account_name, user_name, `password`, phone_number, phone_number_wechat, sex, avatar, `status`, online, del_flag, login_date, create_by, create_time, update_by, update_time, remark,open_rescue,unionid,open_id,type
    </sql>
    <sql id="Base_account_List">
        a.id, tenant_id, shop_id, account_name, user_name, `password`, phone_number, phone_number_wechat, sex, avatar, `status`, online, a.del_flag, login_date, create_by, a.create_time, update_by, a.update_time, remark,open_rescue,tr.role_name,tr.role_key
    </sql>
    <sql id="Base_Column_List_join">
        a.id, a.tenant_id, a.shop_id, a.account_name, a.user_name, a.`password`, a.phone_number, a.phone_number_wechat, a.sex, a.avatar, a.`status`, a.online, a.del_flag, a.login_date, a.create_by, a.create_time, a.update_by, a.update_time, a.remark,a.open_rescue,a.role_id,a.type
    </sql>
    <delete id="batchDelAccountByShopId">
        delete from tebo_account
        where shop_id = #{shopId}
    </delete>
    <select id="countByUserNameAndPhone" resultType="java.lang.Integer">
        select count(*)
        from tebo_account
        where user_name = #{userName} or phone_number = #{phoneNumber}
    </select>

    <select id="countByUserName" resultType="java.lang.Integer">
        select count(*)
        from tebo_account
        where user_name = #{userName}
    </select>
    <select id="countByShopId" resultType="java.lang.Integer">
        select count(*)
        from tebo_account

        where shop_id = #{shopId} and  del_flag = 0
    </select>


    <select id="selectUserByUserName" resultType="com.tebo.system.entity.TeboAccountDO">
        select
        <include refid="Base_Column_List" />
        from tebo_account
        where user_name = #{userName} and role_id = 2
    </select>

    <select id="getAccountInfoByRoleId"  resultType="com.tebo.system.entity.TeboAccountDO">
        select
        <include refid="Base_Column_List" />
        from tebo_account
        where role_id in (1,5) and user_name = #{userName}
    </select>

    <select id="selectAccountByUserName"  resultType="com.tebo.system.entity.TeboAccountDO">
        select
        <include refid="Base_Column_List" />
        from tebo_account
        where role_id = 4  and user_name = #{userName}
    </select>
    <select id="getAccountList" resultType="com.tebo.system.domain.view.TeboAccountVO">
        select
        <include refid="Base_account_List"/>
        from tebo_account a left join  tebo_role  tr on a.role_id = tr.id
        where a.del_flag = 0
        <if test="tenantId!= null and tenantId!= ''">
            and a.tenant_id = #{tenantId}
        </if>
        <if test="shopId!= null and shopId!= ''">
            and a.shop_id = #{shopId}
        </if>
        <if test="phoneNumber!= null and phoneNumber!= ''">
            and a.phone_number = #{phoneNumber}
        </if>
        <if test="userName!= null and userName!= ''">
            and a.user_name = #{userName}
        </if>
        <if test="roleId!= null and roleId!= ''">
            and a.role_id = #{roleId}
        </if>
        <if test="createTimeStartSecond!= null">
            and a.create_time >= #{createTimeStartSecond ,jdbcType=TIMESTAMP}
        </if>
        <if test="createTimeEndSecond!= null">
            and #{createTimeEndSecond ,jdbcType=TIMESTAMP} > a.create_time
        </if>
        <if test="ids!= null and ids.size() > 0">
            and a.id in
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by a.create_time desc
    </select>

    <select id="selectAccountList" resultType="com.tebo.system.entity.TeboAccountDO">
        select
        <include refid="Base_Column_List_join"/>
        from tebo_account a left join tebo_shop s
        on a.shop_id = s.id where a.del_flag = 0
        <if test="tenantId!= null and tenantId!= ''">
            and a.tenant_id = #{tenantId}
        </if>
        <if test="shopId!= null and shopId!= ''">
            and a.shop_id = #{shopId}
        </if>
        <if test="phoneNumber!= null and phoneNumber!= ''">
            and a.phone_number = #{phoneNumber}
        </if>
        <if test="type!= null and type!= ''">
            and a.type = #{type}
        </if>
        <if test="roleId!= null and roleId!= ''">
            and a.role_id = #{roleId}
        </if>
        <if test="userName!= null and userName!= ''">
            and a.user_name = #{userName}
        </if>
        <if test="accountName!= null and accountName!= ''">
            and a.account_name like concat('%', #{accountName}, '%')
        </if>
        <if test="shopName!= null and shopName!= ''">
            and s.shop_name  like concat('%', #{shopName}, '%')
        </if>
        <if test="createTimeStartSecond!= null">
            and a.create_time >= #{createTimeStartSecond ,jdbcType=TIMESTAMP}
        </if>
        <if test="createTimeEndSecond!= null">
            and #{createTimeEndSecond ,jdbcType=TIMESTAMP} > a.create_time
        </if>
        <if test="ids!= null and ids.size() > 0">
            and a.id in
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by a.create_time desc

    </select>

    <select id="getAccountInfoListByShopId" resultType="com.tebo.system.entity.TeboAccountDO">
        select
        <include refid="Base_Column_List" />
        from tebo_account
        where del_flag = 0 and shop_id = #{shopId}
    </select>
    <select id="selectUserByPhone" resultType="com.tebo.system.domain.view.TeboAccountVO">
        select
        a.*
        from tebo_account a
        where a.del_flag = 0
        and a.phone_number = #{phoneNumber}
        limit 1
    </select>
    <select id="selectUserByPhoneAndRole" resultType="com.tebo.system.domain.view.TeboAccountVO">
        select a.*
        from tebo_account a
        where a.del_flag = 0 and a.phone_number = #{phoneNumber} and shop_id is not null and role_id in (1,5)
        limit 1
    </select>

    <update id="updateAccountPasswd" >
        update tebo_account set password = #{password} where tenant_id = #{tenantId} and type = 2
    </update>

    <select id="notActivePartner" resultType="com.tebo.system.domain.view.TeboAccountVO">
        SELECT <include refid="Base_Column_List"></include> FROM `tebo_account`
        where role_id = 4
          and tenant_id not in
            (
                  select new_tenant_id from tebo_shop
                  where new_tenant_id != ''
            )
    </select>

    <select id="getOpenRescueShopList" resultType="String">
       select distinct(shop_id) from tebo_account where del_flag = 0 and open_rescue = 1
        <if test="ids!= null and ids.size() > 0">
            and shop_id in
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
