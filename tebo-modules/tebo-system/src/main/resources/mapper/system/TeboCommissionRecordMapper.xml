<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboCommissionRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboCommissionRecordDO">
        <result column="id" property="id" />
        <result column="leader_id" property="leaderId" />
        <result column="tenant_id" property="tenantId" />
        <result column="source" property="source" />
        <result column="order_no" property="orderNo" />
        <result column="order_amount" property="orderAmount" />
        <result column="rate" property="rate" />
        <result column="commission_amount" property="commissionAmount" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, leader_id, tenant_id, `source`, order_no, order_amount, rate, commission_amount, create_time, update_time
    </sql>

    <select id="getTeboCommissionRecordList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_commission_record
        <where>
            <if test="leaderId != null and leaderId != ''">leader_id = #{leaderId}</if>
        </where>
    </select>

    <select id="getTeboCommissionRecordByOrderNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_commission_record
       where order_no = #{orderNo} limit 1
    </select>
</mapper>
