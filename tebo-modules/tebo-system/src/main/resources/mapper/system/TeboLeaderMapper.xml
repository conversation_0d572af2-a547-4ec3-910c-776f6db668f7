<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.system.mapper.TeboLeaderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.system.entity.TeboLeaderDO">
        <result column="id" property="id" />
        <result column="customer_id" property="customerId" />
        <result column="real_name" property="realName" />
        <result column="phone" property="phone" />
        <result column="id_card" property="idCard" />
        <result column="id_card_front_url" property="idCardFrontUrl" />
        <result column="id_card_back_url" property="idCardBackUrl" />
        <result column="id_card_hold_url" property="idCardHoldUrl" />
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="status" property="status" />
        <result column="rate" property="rate" />
        <result column="leader_code" property="leaderCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, customer_id, real_name, phone, id_card, id_card_front_url, id_card_back_url, id_card_hold_url, tenant_id, `status`, rate,leader_code,tenant_name
    </sql>
    <select id="getTeboLeaderInfo" resultType="com.tebo.system.entity.TeboLeaderDO">
        select <include refid="Base_Column_List"></include> from tebo_leader
        <where>
            <if test="customerId!= null">
                and customer_id = #{customerId}
            </if>
            <if test="leaderId!= null">
                and id = #{leaderId}
            </if>
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="leaderCode!= null">
                and leader_code = #{leaderCode}
            </if>
        </where>
        limit 1
    </select>

   <update id="updateTeboLeaderStatus">
       update tebo_leader set status = #{status} where id = #{leaderId}
   </update>

    <update id="batchUpdateCommissionRate">
        update tebo_leader set rate = #{rate} where id in
        <foreach collection="leaderIdList" item="leaderId" open="(" separator="," close=")">
            #{leaderId}
        </foreach>
    </update>
    <select id="getTeboLeaderList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_leader
        <where>
            <if test="tenantId!= null">
                and tenant_id = #{tenantId}
            </if>
            <if test="keyword != null and keyword != ''">
                and ((real_name like concat('%', #{keyword}, '%'))
                or (phone like concat('%', #{keyword}, '%'))
                or (id_card like concat('%', #{keyword}, '%'))
                or (tenant_name like concat('%', #{keyword}, '%'))
                )
            </if>
        </where>
    </select>
</mapper>
