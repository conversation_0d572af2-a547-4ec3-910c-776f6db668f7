CREATE TABLE `tebo_customer`
(
    `id`           bigint      NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `unionid`      varchar(64)  DEFAULT NULL COMMENT 'unionid',
    `openid`       varchar(64)  DEFAULT NULL COMMENT 'openid',
    `nick_name`    varchar(30) NOT NULL COMMENT '用户昵称',
    `user_type`    tinyint      DEFAULT NULL COMMENT '会员类型',
    `phone_number` varchar(11)  DEFAULT '' COMMENT '手机号码',
    `gender`       tinyint      DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
    `avatar_url`   varchar(100) DEFAULT '' COMMENT '头像地址',
    `country`      varchar(10)  DEFAULT '' COMMENT '国家',
    `province`     varchar(10)  DEFAULT '' COMMENT '省份/直辖市',
    `city`         varchar(10)  DEFAULT '' COMMENT '城市',
    `address`      varchar(200) DEFAULT '' COMMENT '详细地址',
    `status`       tinyint(1) DEFAULT 0 COMMENT '帐号状态（0正常 1停用）',
    `del_flag`     tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    `create_by`    varchar(64)  DEFAULT '' COMMENT '创建者',
    `create_time`  datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`    varchar(64)  DEFAULT '' COMMENT '更新者',
    `update_time`  datetime     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='C端用户信息表';

CREATE TABLE `tebo_partner_info`
(
    `id`                    bigint       NOT NULL AUTO_INCREMENT COMMENT '唯一主键',
    `sys_user_id`           bigint       NOT NULL COMMENT '关联账号表sys_user的id',
    `partner_code`          varchar(64)  NOT NULL DEFAULT '' COMMENT '合伙人编码',
    `partner_name`          varchar(64)  NOT NULL DEFAULT '' COMMENT '合伙人名称',
    `customer_type`         tinyint(1) NOT NULL DEFAULT 0 COMMENT '客户类型（0天能客户 1非天能客户）',
    `province_name`         varchar(64)  NOT NULL DEFAULT '' COMMENT '省份名称',
    `city_name`             varchar(64)  NOT NULL DEFAULT '' COMMENT '城市名称',
    `district_name`         varchar(64)  NOT NULL DEFAULT '' COMMENT '区县名称',
    `address`               varchar(200) NOT NULL DEFAULT '' COMMENT '客户地址',
    `link_man`              varchar(64)  NOT NULL DEFAULT '' COMMENT '联系人',
    `link_phone`            varchar(64)  NOT NULL DEFAULT '' COMMENT '联系电话',
    `area_manager_id`       bigint COMMENT '区域负责人id',
    `area_manager`          varchar(64)  NOT NULL DEFAULT '' COMMENT '区域负责人',
    `remark`                varchar(300) NOT NULL DEFAULT '' COMMENT '备注',
    `partner_type`          tinyint(1) NOT NULL DEFAULT 0 COMMENT '合伙人类型（0个体 1企业）',
    `pay_type`              tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付方式（0微信支付 1聚合支付）',
    `account_bank`          varchar(64)  NOT NULL DEFAULT '' COMMENT '开户银行',
    `account_number`        varchar(64)  NOT NULL DEFAULT '' COMMENT '银行账号',
    `id_card_front`         varchar(64)  NOT NULL DEFAULT '' COMMENT '身份证正面',
    `id_card_back`          varchar(64)  NOT NULL DEFAULT '' COMMENT '身份证反面',
    `id_card`               varchar(64)  NOT NULL DEFAULT '' COMMENT '身份证号码',
    `business_license`      varchar(64)  NOT NULL DEFAULT '' COMMENT '营业执照',
    `business_license_code` varchar(64)  NOT NULL DEFAULT '' COMMENT '营业执照编号',
    `legal_person`          varchar(64)  NOT NULL DEFAULT '' COMMENT '法人姓名',
    `register_capital`      varchar(64)  NOT NULL DEFAULT '' COMMENT '注册资本',
    `establish_time`        datetime COMMENT '成立时间',
    `status`                tinyint(1) NOT NULL DEFAULT 0 COMMENT '合伙人状态（0正常 1停用）',
    `del_flag`              tinyint               DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    `create_by`             varchar(64)           DEFAULT '' COMMENT '创建者',
    `create_time`           datetime              DEFAULT NULL COMMENT '创建时间',
    `update_by`             varchar(64)           DEFAULT '' COMMENT '更新者',
    `update_time`           datetime              DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='合伙人信息表';


CREATE TABLE `tebo_shop`
(
    `id`                     bigint       NOT NULL AUTO_INCREMENT COMMENT '唯一主键',
    `shop_code`              varchar(64)  NOT NULL DEFAULT '' COMMENT '店铺编码',
    `shop_qr_code`           varchar(256) NOT NULL DEFAULT '' COMMENT '门店取号二维码',
    `shop_name`              varchar(64)  NOT NULL DEFAULT '' COMMENT '店铺名称',
    `shop_type`              tinyint(1) NOT NULL DEFAULT 0 COMMENT '0:旗舰店 1:标准店 2:社区店',
    `shop_nature`            tinyint(1) NOT NULL DEFAULT 0 COMMENT '0:销售 1:服务 2:销售+服务',
    `shop_brand`             varchar(64)  NOT NULL DEFAULT '' COMMENT '品牌',
    `shop_boss_name`         varchar(64)  NOT NULL DEFAULT '' COMMENT '门店老板姓名',
    `phone_number`           varchar(64)  NOT NULL DEFAULT '' COMMENT '联系电话',
    `tenant_id`              bigint       NOT NULL COMMENT '合伙人id',
    `tenant_name`            varchar(64)  NOT NULL DEFAULT '' COMMENT '合伙人名称',
    `provinceName`           varchar(64)  NOT NULL DEFAULT '' COMMENT '省份',
    `cityName`               varchar(64)  NOT NULL DEFAULT '' COMMENT '城市',
    `districtName`           varchar(64)  NOT NULL DEFAULT '' COMMENT '区县',
    `address`                varchar(64)  NOT NULL DEFAULT '' COMMENT '地址',
    `longitude`              varchar(64)  NOT NULL DEFAULT '' COMMENT '经度',
    `latitude`               varchar(64)  NOT NULL DEFAULT '' COMMENT '纬度',
    `sales_phone`            varchar(64)           DEFAULT '' COMMENT '销售电话',
    `after_sale_phone`       varchar(64)           DEFAULT '' COMMENT '售后电话',
    `open_time`              varchar(64)           DEFAULT NULL COMMENT '营业时间',
    `number_calling_service` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否开通叫号服务0:否 1:是',
    `show_nearby_shop`       tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否展示附近店铺0:否 1:是',
    `customer_subject`       tinyint(1) NOT NULL DEFAULT 0 COMMENT '客户主体（0个体 1企业）',
    `pay_type`               tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付方式（0微信支付 1聚合支付）',
    `account_bank`           varchar(64)  NOT NULL DEFAULT '' COMMENT '开户银行',
    `account_number`         varchar(64)  NOT NULL DEFAULT '' COMMENT '银行账号',
    `id_card_front`          varchar(64)  NOT NULL DEFAULT '' COMMENT '身份证正面',
    `id_card_back`           varchar(64)  NOT NULL DEFAULT '' COMMENT '身份证反面',
    `id_card`                varchar(64)  NOT NULL DEFAULT '' COMMENT '身份证号码',
    `business_license`       varchar(64)  NOT NULL DEFAULT '' COMMENT '营业执照',
    `business_license_code`  varchar(64)  NOT NULL DEFAULT '' COMMENT '营业执照编号',
    `legal_person`           varchar(64)  NOT NULL DEFAULT '' COMMENT '法人姓名',
    `register_capital`       varchar(64)  NOT NULL DEFAULT '' COMMENT '注册资本',
    `establish_time`         datetime COMMENT '成立时间',
    `shop_pic`               varchar(64)  NOT NULL DEFAULT '' COMMENT '店铺图片',
    `status`                 tinyint(1) NOT NULL DEFAULT 0 COMMENT '门店状态（0正常 1停用）',
    `del_flag`               tinyint               DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    `create_by`              varchar(64)           DEFAULT '' COMMENT '创建者',
    `create_time`            datetime              DEFAULT NULL COMMENT '创建时间',
    `update_by`              varchar(64)           DEFAULT '' COMMENT '更新者',
    `update_time`            datetime              DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='门店信息表';

CREATE TABLE `tebo_shop_user_link`
(
    `id`          bigint NOT NULL AUTO_INCREMENT COMMENT '唯一主键',
    `shop_id`     bigint NOT NULL COMMENT '门店id',
    `sys_user_id` bigint NOT NULL COMMENT '系统用户id',
    `del_flag`    tinyint DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='门店账号关联表';