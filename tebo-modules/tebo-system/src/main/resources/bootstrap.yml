# Tomcat
server:
  port: 9211

# Spring
spring:
  application:
    # 应用名称
    name: tebo-system

  # 最大上传文件大小
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 200MB
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: @nacos.server.address@
        namespace: @nacos.server.namespace@
        group: tebo
        username: nacos
        password: Tn!@#$1234
      config:
        # 配置中心地址
        server-addr: @nacos.server.address@
        namespace: @nacos.server.namespace@
        group: tebo
        username: nacos
        password: Tn!@#$1234
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

sms:
  config:
    signName: 泰博出行
    accessKeyId: LTAI5tCzrrTRrtKpTcW47SXF
    accessKeySecret: ******************************
    defaultConnectTimeout: 10000
    defaultReadTimeout: 10000
    #维修师傅接单模板
    pendingOrderTemplate: SMS_465265038

merchant:
  applicationStatusUrl: https://api.mch.weixin.qq.com/v3/applyment4sub/applyment/applyment_id/
  applicationUrl: https://api.mch.weixin.qq.com/v3/applyment4sub/applyment/
  settlementUrl: https://api.mch.weixin.qq.com/v3/apply4sub/sub_merchants/%s/settlement
  updateSettlementUrl: https://api.mch.weixin.qq.com/v3/apply4sub/sub_merchants/%s/modify-settlement
  uploadImageUrl: https://api.mch.weixin.qq.com/v3/merchant/media/upload
  wechatpaySerial: 7AB0E110FDC98FE52CEF7F005D493093976F4EBA
  authorizationHeader: WECHATPAY2-SHA256-RSA2048
aliyun:
  ocr:
    accessKeyId: LTAI4Fi5yeu5tvMSLq1s1p9x
    accessKeySecret: ******************************
tebo:
  teboProdBaseUrl: https://newtsl.tntab.cn/prod-api
