package com.tebo.system.domain.vo.wallet;

import lombok.Data;

import java.io.Serializable;

/**
 *
 */
@Data
public class TeboWalletRecordVO implements Serializable {
    /**
     * 流水id
     */
    private Long id;

    /**
     * 钱包id
     */
    private Long walletId;

    /**
     * 类型 1:收入 2:支出(不传查全部)
     */
    private Integer recordType;

    /**
     * 1 佣金 10 充值 11 缴纳履约保证金 12退履约保证金 13奖励 14核销 20提现 除上述描述的，其他的暂归于佣金
     */
    private Integer flowType;

    /**
     * 流水描述
     */
    private String recordDescription;

    /**
     * 金额
     */
    private Integer amount;

    /**
     * 金额，以元为单位
     */
    private String amountStr;
    /**
     * 关联业务id
     */
    private Long bizId;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 状态 1 冻结 2 解冻
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String failDesc;
}