package com.tebo.system.manager.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tebo.common.core.constant.CacheConstants;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.util.xml.XMLUtil;
import com.tebo.system.config.WxMerchantConfig;
import com.tebo.system.config.WxPushConfig;
import com.tebo.system.domain.so.WechatBonusSO;
import com.tebo.system.domain.vo.WxSubscribeUserResultVO;
import com.tebo.system.manager.WeChatManger;
import com.tebo.system.util.WxPayUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.net.ssl.SSLContext;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URL;
import java.security.KeyStore;
import java.util.*;
import java.util.concurrent.TimeUnit;


@Service
@Slf4j
public class WeChatMangerImpl implements WeChatManger {

    @Resource
    private RedisService redisService;


    @Resource
    private WxPushConfig wxPushConfig;

    @Value("${official_account.appId}")
    private String appId;
    @Value("${official_account.secret}")
    private String secret;

//    // key为商户平台设置的密钥key
//    @Value("${wx.merchant.payKey}")
//    private String payKey;
//
//    //**********   微信支付分配的商户号
//    @Value("${wx.merchant.mchid}")
//    private String mchid;
//
//    @Value("${wx.merchant.appid}")
//    private String appid;
//
//
//    //商家在申请微信支付成功后，收到的相应邮件后，可以按照指引下载API证书，也可以按照以下路径下载：微信商户平台(pay.weixin.qq.com)-->账户中心-->账户设置-->API安全
//    @Value("${wx.merchant.certificatePath}")
//    private String certificatePath;

    @Autowired
    private WxMerchantConfig wxMerchantConfig;

    @Override
    public String getAppletAccessToken() {

//        String appid = wxPushTokenSO.getAppid();
//        String secret = wxPushTokenSO.getSecret();
//        Assert.notNull(appid, "推送小程序appid不能为空");
//        Assert.notNull(secret, "推送小程序秘钥不能为空");
        // 因为微信有限制次数跟有效期，所以需要存在redis中
        String token = redisService.getCacheObject(CacheConstants.WECHAT_MINI_ACCESS_TOKEN);
        // token失效或者为空
        if (StringUtils.isEmpty(token)) {
            HashMap<String, Object> map = new HashMap<>(3);
            map.put("appid", appId); // 小程序appid
            map.put("secret", secret); // 小程序secret
            map.put("grant_type", "client_credential");
            //  发送Http的post请求
            String resultStr = HttpUtil.post(wxPushConfig.getTokenUrl(), JSON.toJSONString(map));

            log.info("获取小程序accessToken返回：" + resultStr);
            if (StringUtils.isEmpty(resultStr) || !resultStr.contains("access_token")) {
                // 获取token失败再获取一次
                resultStr = HttpUtil.post(wxPushConfig.getTokenUrl(), JSON.toJSONString(map));
                if (StringUtils.isEmpty(resultStr) || !resultStr.contains("access_token")) {
                    throw new GlobalException("获取小程序accessToken失败");
                }
            }
            JSONObject resultJson = JSON.parseObject(resultStr);
            token = resultJson.getString("access_token");
            //默认2h
            String expiresIn = resultJson.getString("expires_in");
            long expireLong = Long.parseLong(expiresIn);
            if (expireLong > 100L) {
                redisService.setCacheObject(CacheConstants.WECHAT_MINI_ACCESS_TOKEN, token,
                        expireLong - 100, TimeUnit.SECONDS);
            }

        }
        return token;
    }

    @Override
    public String getUnionidByOpenId(String openId) {

        String unionid = redisService.getCacheObject(CacheConstants.WECHAT_OPENID_UNIONID + openId);
        if (StringUtils.isNotEmpty(unionid)) {
            return unionid;
        }
        Map<String, Object> paramMap = new HashMap<>();

        paramMap.put("access_token", getAppletAccessToken());
        paramMap.put("openid", openId);
        String resultStr = HttpUtil.get(wxPushConfig.getUserInfoUrl(), paramMap);
//        log.info("通过openid获取Unionid，返回用户信息：{}", resultStr);
        JSONObject jsonObject = JSON.parseObject(resultStr);
        String errcode = jsonObject.getString("errcode");
        if (StringUtils.isNotEmpty(errcode)) {
            log.error("通过openid获取Unionid失败," + errcode + jsonObject.getString("errmsg"));
            throw new ServiceException("通过openid获取Unionid失败");
        }
        unionid = jsonObject.getString("unionid");
        redisService.setCacheObject(CacheConstants.WECHAT_OPENID_UNIONID + openId, unionid, 1L, TimeUnit.HOURS);
        return unionid;
    }

    @Override
    public List<String> getAppletUsers() {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("access_token", getAppletAccessToken());
        paramMap.put("next_openid", "");
        String resultStr = HttpUtil.get(wxPushConfig.getSubscribeUserUrl(), paramMap);
        JSONObject jsonObject = JSON.parseObject(resultStr);
        String errcode = jsonObject.getString("errcode");
        if (StringUtils.isNotEmpty(errcode)) {
            log.error("获取服务号的关注用户失败," + errcode + jsonObject.getString("errmsg"));
            throw new ServiceException("获取服务号的关注用户失败");
        }
        log.info("获取服务号的关注用户，total：{},count:{}", jsonObject.getString("total"), jsonObject.getString("count"));
        WxSubscribeUserResultVO.WxSubscribeUser data = jsonObject.getObject("data", WxSubscribeUserResultVO.WxSubscribeUser.class);
        List<String> openidList = data.getOpenid();
        if (CollectionUtil.isEmpty(openidList)) {
            log.info("本次获取的openid数组为空");
            return new ArrayList<>();
        }
        return openidList;
    }

    @Override
    public void awardBonus(WechatBonusSO wechatBonusSO) {

        String amount = wechatBonusSO.getAmount();
        Assert.notEmpty(amount, "必须指定红包金额");
        Assert.notEmpty(wechatBonusSO.getOpenId(), "必须指定openid");

        //设置参数
        String partner_trade_no = create_nonce_str();
        String nonce_str = create_nonce_str();
        SortedMap<String, String> parameters = new TreeMap<>();
        //申请商户号的appid或商户号绑定的appid
        parameters.put("mch_appid", wxMerchantConfig.getAppid());
        //微信支付分配的商户号
        parameters.put("mchid", wxMerchantConfig.getMchid());
        parameters.put("partner_trade_no", partner_trade_no);
        parameters.put("nonce_str", nonce_str);
        parameters.put("openid", wechatBonusSO.getOpenId());
        parameters.put("amount", amount);
        parameters.put("check_name", "NO_CHECK");
        parameters.put("desc", "泰博出行转账");
        //创建签名
//        key为商户平台设置的密钥key
        String mySign = WxPayUtil.createSign(parameters, wxMerchantConfig.getPayKey());
        parameters.put("sign", mySign);
        String xml = null;
        try {
            xml = XMLUtil.mapToXml(parameters);
        } catch (Exception e) {
            log.error("转化xml异常", e);
        }
//发送post请求
        String res = null;
        try {
            res = postHttps("https://api.mch.weixin.qq.com/mmpaymkttransfers/promotion/transfers", new String(xml.getBytes(), "ISO8859-1"), wxMerchantConfig.getMchid(), wxMerchantConfig.getCertificatePath());
        } catch (Exception e) {
            log.error("微信发红包接口失败", e);
            throw new ServiceException("微信发红包失败，请联系管理员");
        }
        log.info("{红包发送结果}" + res);


    }


    //创建随机字符串
    private String create_nonce_str() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }


    public String postHttps(String url, String xml, String mchId, String certPath) throws Exception {
        String res = "";
        KeyStore keyStore = KeyStore.getInstance("PKCS12");

        URL resource = getClass().getClassLoader().getResource(certPath);
        String path = resource.getPath();
        File file = new File(path);
        FileInputStream instream = new FileInputStream(file);

//        ClassPathResource classPathResource = new ClassPathResource(certPath);
//        InputStream instream = classPathResource.getInputStream();
//        FileInputStream instream = new FileInputStream(new File("E:\\project\\tebo-back\\tebo-modules\\tebo-system\\src\\main\\resources\\apiclient_cert.p12"));
//        FileInputStream instream = new FileInputStream(new File("E:\\project\\tebo-back\\tebo-modules\\tebo-system\\src\\main\\resources\\apiclient_cert.p12"));
//        FileInputStream instream = new FileInputStream(new File(certPath));

        try {
            keyStore.load(instream, mchId.toCharArray());
        } finally {
            instream.close();
        }
        // Trust own CA and all self-signed certs
        SSLContext sslcontext = SSLContexts.custom()
                .loadKeyMaterial(keyStore, mchId.toCharArray())
                .build();
        // Allow TLSv1 protocol only
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
                sslcontext,
                new String[]{"TLSv1", "TLSv1.2"},
                null,
                SSLConnectionSocketFactory.BROWSER_COMPATIBLE_HOSTNAME_VERIFIER);
        CloseableHttpClient httpclient = HttpClients.custom().setSSLSocketFactory(sslsf).build();
        try {
            HttpPost httppost = new HttpPost(url);
            log.info("executing request" + httppost.getRequestLine());
            httppost.setEntity(new StringEntity(xml));
            httppost.setHeader("Content-Type", "text/xml");
            CloseableHttpResponse response = httpclient.execute(httppost);
            try {
                HttpEntity entity = response.getEntity();
                log.info("----------------------------------------");
                log.info("" + response.getStatusLine());
                if (entity != null) {
                    log.info("Response content length: " + entity.getContentLength());
                    res = EntityUtils.toString(entity);
                }
                EntityUtils.consume(entity);
            } finally {
                response.close();
            }
        } finally {
            httpclient.close();
        }
        return res;
    }
}
