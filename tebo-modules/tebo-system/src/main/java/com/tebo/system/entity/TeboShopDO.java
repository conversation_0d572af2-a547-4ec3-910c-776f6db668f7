package com.tebo.system.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 门店信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Getter
@Setter
@TableName("tebo_shop")
public class TeboShopDO extends Model<TeboShopDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺编码
     */
    @TableField("shop_code")
    private String shopCode;

    /**
     * 门店取号二维码
     */
    @TableField("shop_qr_code")
    private String shopQrCode;


    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 0:旗舰店 1:标准店 2:社区店
     */
    @TableField("shop_type")
    private Integer shopType;

    /**
     * 门店性质
     * 0:销售 1:服务 2:销售+服务
     */
    @TableField("shop_nature")
    private Integer shopNature;

    /**
     * 品牌
     */
    @TableField("shop_brand")
    private String shopBrand;

    /**
     * 门店老板姓名
     */
    @TableField("shop_boss_name")
    private String shopBossName;

    /**
     * 联系电话
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 合伙人id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 合伙人名称
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 区域。省市区编码，以,分隔
     */
    @TableField("area")
    private String area;


    /**
     * 区县code
     */
    @TableField("area_code")
    private String areaCode;

    /**
     * 省-市-区
     */
    @TableField("area_name")
    private String areaName;


    /**
     * 地址
     */
    @TableField("address")
    private String address;

    /**
     * 经度
     */
    @TableField("longitude")
    private String longitude;

    /**
     * 纬度
     */
    @TableField("latitude")
    private String latitude;

    /**
     * 销售电话
     */
    @TableField("sales_phone")
    private String salesPhone;

    /**
     * 售后电话
     */
    @TableField("after_sale_phone")
    private String afterSalePhone;

    /**
     * 营业时间
     */
    @TableField("open_time")
    private String openTime;

    /**
     * 是否开通叫号服务0:否 1:是
     */
    @TableField("number_calling_service")
    private Integer numberCallingService;

    /**
     * 是否展示附近店铺0:否 1:是
     */
    @TableField("show_nearby_shop")
    private Integer showNearbyShop;

    /**
     * 客户主体（0个体 1企业）
     */
    @TableField("customer_subject")
    private Integer customerSubject;

    /**
     * 支付方式（0微信支付 1聚合支付）
     */
    @TableField("pay_type")
    private Integer payType;

    /**
     * 开户银行
     */
    @TableField("account_bank")
    private String accountBank;

    /**
     * 泰博出行-开户银行
     */
    @TableField("tebo_account_bank")
    private String teboAccountBank;

    /**
     * 银行账号
     */
    @TableField("account_number")
    private String accountNumber;

    /**
     * 泰博出行-银行账号
     */
    @TableField("tebo_account_number")
    private String teboAccountNumber;

    /**
     * 身份证正面
     */
    @TableField("id_card_front")
    private String idCardFront;

    /**
     * 泰博出行-身份证正面
     */
    @TableField("tebo_id_card_front")
    private String teboIdCardFront;

    /**
     * 身份证反面
     */
    @TableField("id_card_back")
    private String idCardBack;

    /**
     * 泰博出行-身份证反面
     */
    @TableField("tebo_id_card_back")
    private String teboIdCardBack;

    /**
     * 身份证号码
     */
    @TableField("id_card")
    private String idCard;

    /**
     * 泰博出行-身份证号码
     */
    @TableField("tebo_id_card")
    private String teboIdCard;

    /**
     * 身份证名称
     */
    @TableField("id_card_name")
    private String idCardName;

    /**
     * 泰博出行-身份证名称
     */
    @TableField("tebo_id_card_name")
    private String teboIdCardName;
    /**
     * 营业执照
     */
    @TableField("business_license")
    private String businessLicense;

    /**
     * 营业执照编号
     */
    @TableField("business_license_code")
    private String businessLicenseCode;

    /**
     * 法人姓名
     */
    @TableField("legal_person")
    private String legalPerson;

    /**
     * 注册资本
     */
    @TableField("register_capital")
    private String registerCapital;

    /**
     * 成立时间
     */
    @TableField(value = "establish_time",updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime establishTime;

    /**
     * 店铺图片  门头照片
     */
    @TableField("shop_pic")
    private String shopPic;

    @TableField("cid")
    private String cid;

    @TableField("remark")
    private String remark;

    /**
     * 门店状态（0停用 1启用）
     */
    @TableField("`status`")
    private Integer status;


    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField("del_flag")
    private Integer delFlag;



    /**
     * 创建者
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 店铺标语
     */
    @TableField("shop_slogan")
    private String shopSlogan;


    /**
     * 宣传视频url
     */
    @TableField("promotion_video")
    private String promotionVideo;


    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 开户支行
     */
    @TableField("account_branch_bank")
    private String accountBranchBank;

    /**
     * 泰博出行-开户支行
     */
    @TableField("tebo_account_branch_bank")
    private String teboAccountBranchBank;

    /**
     * 微信平台商家简称
     *
     * @return
     */
    @TableField("wx_seller_name")
    private String wxSellerName;

    /**
     * 门店星级
     *
     * @return
     */
    @TableField("shop_star_level")
    private String shopStarLevel;

    /**
     * 门店等级
     *
     * @return
     */
    @TableField("shop_level")
    private String shopLevel;

    /**
     * 门店积分系数
     *
     * @return
     */
    @TableField("integration_coefficient")
    private BigDecimal integrationCoefficient;

    /**
     * 服务保证金
     */
    @TableField("service_earnest_money")
    private Integer serviceEarnestMoney;


    /**
     * 商品保证金
     */
    @TableField("goods_earnest_money")
    private Integer goodsEarnestMoney;


    /**
     * 缴纳保证金时间
     */
    @TableField("pay_earnest_time")
    private LocalDateTime payEarnestTime;


    /**
     * 开通工作台
     */
    @TableField("open_workbench")
    private Boolean openWorkbench;


    /**
     * 开通商城
     */
    @TableField("open_mall")
    private Boolean openMall;


    /**
     * 开通上门服务
     */
    @TableField("open_door")
    private Boolean openDoor;


    /**
     * 开通救援服务
     */
    @TableField("open_rescue")
    private Boolean openRescue;

    /**
     * 是否支持三方订单
     */
    @TableField("open_third_order")
    private Boolean openThirdOrder;

    /**
     * 业务人员账号
     */
    @TableField("business_account")
    private String businessAccount;


    /**
     * 上门服务费
     */
    @TableField("home_service_fee")
    private Integer homeServiceFee;


    /**
     * 服务范围
     */
    @TableField("service_scope")
    private String serviceScope;

    /**
     * 身份证正面照片MediaId
     */
    @TableField("id_card_copy")
    private String idCardCopy;

    /**
     * 身份证国徽MediaId
     */
    @TableField("id_card_national")
    private String idCardNational;

    /**
     * 营业执照的mediaId
     */
    @TableField("license_copy")
    private String licenseCopy;

    /**
     * 身份证开始时间
     */
    @TableField("id_card_period_begin")
    private String idCardPeriodBegin;

    /**
     * 泰博出行-身份证开始时间
     */
    @TableField("tebo_id_card_period_begin")
    private String teboIdCardPeriodBegin;

    /**
     * 身份证结束时间
     */
    @TableField("id_card_period_end")
    private String idCardPeriodEnd;

    /**
     * 泰博出行-身份证结束时间
     */
    @TableField("tebo_id_card_period_end")
    private String teboIdCardPeriodEnd;

    /**
     * 身份证地址
     */
    @TableField("id_card_address")
    private String idCardAddress;

    /**
     * 泰博出行-身份证地址
     */
    @TableField("tebo_id_card_address")
    private String teboIdCardAddress;

    /**
     * 银行支行区域地址
     */
    @TableField("note")
    private String note;

    /**
     * 泰博出行-银行支行区域地址
     */
    @TableField("tebo_note")
    private String teboNote;

    /**
     * 营业执照地址
     */
    @TableField("license_address")
    private String licenseAddress;

    /**
     * 开户银行省市编码
     */
    @TableField("bank_address_code")
    private String bankAddressCode;

    /**
     * 泰博出行-开户银行省市编码
     */
    @TableField("tebo_bank_address_code")
    private String teboBankAddressCode;


    /**
     * 支付宝名称
     */
    @TableField("alipay_name")
    private String alipayName;

    /**
     * 支付宝账号
     */
    @TableField("alipay_account")
    private String alipayAccount;


    /**
     * 门店来源 1泰博出行 2零售通
     */
    @TableField("shop_source")
    private Integer shopSource;


    /**
     * 零售通门店code
     */
    @TableField("lst_shop_code")
    private String lstShopCode;

    /**
     * 是否vip门店 0:非vip 1:vip
     */
    @TableField("vip")
    private Integer vip;

    /**
     * 钱包id
     */
    @TableField("wallet_id")
    private Long walletId;

    /**
     * 云门店id
     */
    @TableField("cloud_shop_id")
    private String cloudShopId;

    /**
     * 进件手机号
     */
    @TableField("phone_number_bank")
    private String phoneNumberBank;

    /**
     * 淘宝账号
     */
    @TableField("tb_account")
    private String tbAccount;

    /**
     * 门店sap编码
     */
    @TableField("shop_sap_code")
    private String shopSapCode;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
