package com.tebo.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.domain.so.ReportChartBaseSO;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.util.DataUtils;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.mall.api.RemoteGoodsService;
import com.tebo.mall.api.domain.dto.TeboGoodsQueryDTO;
import com.tebo.mall.api.domain.dto.TeboPlatGoodsQueryDTO;
import com.tebo.mall.api.domain.dto.TeboPlatGoodsShopQueryDTO;
import com.tebo.mall.api.domain.view.TeboGoodsVO;
import com.tebo.mall.api.domain.view.TeboPlatGoodsGroupVO;
import com.tebo.system.api.domain.dto.PayWechatRecordQueryDTO;
import com.tebo.system.domain.dto.TeboPartnerQueryDTO;
import com.tebo.system.domain.dto.TeboShopQueryDTO;
import com.tebo.system.domain.so.ReportChartPersonGroupSO;
import com.tebo.system.domain.vo.*;
import com.tebo.system.domain.vo.excel.*;
import com.tebo.system.entity.TeboPartnerInfoDO;
import com.tebo.system.entity.TeboShopDO;
import com.tebo.system.enums.ShopTypeEnum;
import com.tebo.system.enums.TeboShopNatureEnum;
import com.tebo.system.manager.TeboPartnerInfoManger;
import com.tebo.system.manager.TeboShopManager;
import com.tebo.system.mapper.PayWechatRecordMapper;
import com.tebo.system.mapper.TeboPartnerInfoMapper;
import com.tebo.system.mapper.TeboShopMapper;
import com.tebo.system.service.IReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ReportServiceImpl implements IReportService {

    @Autowired
    private TeboShopManager shopManager;

    @Autowired
    private TeboShopMapper shopMapper;

    @Autowired
    private TeboPartnerInfoManger partnerInfoManger;

    @Autowired
    private TeboPartnerInfoMapper partnerInfoMapper;

    @Autowired
    private RemoteGoodsService remoteGoodsService;

    @Autowired
    private PayWechatRecordMapper payWechatRecordMapper;


    @Override
    public List<TeboBussinessChartVO> businessLineChart(ReportChartBaseSO reportChartBaseSO) {

        List<TeboShopChartVO> reportShopData = shopManager.getReportShopData(reportChartBaseSO);
        List<TeboPartnerChartVO> reportPartnerData = partnerInfoManger.getReportPartnerData(reportChartBaseSO);


        if (CollectionUtil.isEmpty(reportShopData)) {
            return new ArrayList<>();
        }
        Map<String, TeboPartnerChartVO> stringTeboPartnerChartVOMap = DataUtils.listToMap(reportPartnerData, TeboPartnerChartVO::getDateStr);

        List<TeboBussinessChartVO> bussinessChartVOS = new ArrayList<>();
        for (TeboShopChartVO reportShopDatum : reportShopData) {
            TeboBussinessChartVO bussinessChartVO = new TeboBussinessChartVO();
            BeanConvert.copy(reportShopDatum, bussinessChartVO);
            TeboPartnerChartVO partnerChartVO = stringTeboPartnerChartVOMap.get(reportShopDatum.getDateStr());
            if (Objects.nonNull(partnerChartVO)) {
                bussinessChartVO.setPartnerCount(partnerChartVO.getPartnerCount());
            }

            bussinessChartVOS.add(bussinessChartVO);
        }

        return bussinessChartVOS;
    }

    @Override
    public void businessExport(HttpServletResponse response, ReportChartBaseSO reportChartBaseSO) {

        List<TeboBussinessChartVO> bussinessChartVOS = this.businessLineChart(reportChartBaseSO);

        List<ChartBussinessExcelVO> bussinessFormExcelVOList = BeanConvert.copyList(bussinessChartVOS, ChartBussinessExcelVO::new);


        // 将数据写入流中
        try {
            EasyExcel.write(response.getOutputStream(), ChartBussinessExcelVO.class).sheet("业务新增").doWrite(bussinessFormExcelVOList);
        } catch (IOException e) {
            log.error("导出文件异常", e);
            throw new ServiceException("导出文件异常,请联系管理员");
        }
    }

    @Override
    public List<ReportChartPersonGroupVO> businessPersonChart(ReportChartPersonGroupSO reportChartPersonGroupSO) {
        List<ReportChartPersonGroupVO> resultList = new ArrayList<>();

        TeboShopQueryDTO shopQueryDTO = new TeboShopQueryDTO();
        shopQueryDTO.setCreateTimeStartSecond(reportChartPersonGroupSO.getTimeStart().atStartOfDay());
        shopQueryDTO.setCreateTimeEndSecond(reportChartPersonGroupSO.getTimeEnd().plusDays(1).atStartOfDay());
        shopQueryDTO.setBusinessAccount(reportChartPersonGroupSO.getBusinessAccount());
        List<ReportChartPersonGroupVO> shopDataGroupPerson = shopManager.getReportShopDataGroupPerson(shopQueryDTO);
        Map<String, ReportChartPersonGroupVO> shopGroupMap = DataUtils.listToMap(shopDataGroupPerson, ReportChartPersonGroupVO::getBusinessAccount);

        TeboPartnerQueryDTO partnerQueryDTO = new TeboPartnerQueryDTO();
        partnerQueryDTO.setCreateTimeStartSecond(reportChartPersonGroupSO.getTimeStart().atStartOfDay());
        partnerQueryDTO.setCreateTimeEndSecond(reportChartPersonGroupSO.getTimeEnd().plusDays(1).atStartOfDay());

//
//        List<PersonGroupCountVO> personGroupCountVOS = partnerInfoManger.groupByPerson(partnerQueryDTO);
//        Map<String, PersonGroupCountVO> partnerGroup = DataUtils.listToMap(personGroupCountVOS, PersonGroupCountVO::getBusinessAccount);


        Set<String> accountSet = new HashSet<>();
        accountSet.addAll(shopDataGroupPerson.stream().map(ReportChartPersonGroupVO::getBusinessAccount).collect(Collectors.toSet()));
//        accountSet.addAll(personGroupCountVOS.stream().map(PersonGroupCountVO::getBusinessAccount).collect(Collectors.toSet()));

        for (String account : accountSet) {
//            PersonGroupCountVO personGroupCountVO = partnerGroup.get(account);
            ReportChartPersonGroupVO shopCountVO = shopGroupMap.get(account);
            ReportChartPersonGroupVO result = new ReportChartPersonGroupVO();
            result.setBusinessAccount(account);
            result.setBusinessAccountName(shopCountVO.getBusinessAccountName());
//            if (Objects.nonNull(personGroupCountVO)) {
//                result.setPartnerCount(personGroupCountVO.getCount());
//            }
            if (Objects.nonNull(shopCountVO)) {
                result.setStandardCount(shopCountVO.getStandardCount());
                result.setCooperateCount(shopCountVO.getCooperateCount());
                result.setExperienceCenterCount(shopCountVO.getExperienceCenterCount());
            }
//todo 业务人员名字
            resultList.add(result);
        }


        return resultList;
    }

    @Override
    public void businessPersonExport(HttpServletResponse response, ReportChartPersonGroupSO reportChartPersonGroupSO) {
        List<ReportChartPersonGroupVO> chartPersonGroupVOS = this.businessPersonChart(reportChartPersonGroupSO);
        List<ChartBussinessPersonExcelVO> bussinessPersonExcelVOS = BeanConvert.copyList(chartPersonGroupVOS, ChartBussinessPersonExcelVO::new);


        // 将数据写入流中
        try {
            EasyExcel.write(response.getOutputStream(), ChartBussinessPersonExcelVO.class).sheet("业务新增").doWrite(bussinessPersonExcelVOS);
        } catch (IOException e) {
            log.error("导出文件异常", e);
            throw new ServiceException("导出文件异常,请联系管理员");
        }


    }

    @Override
    public List<TeboPartnerFormVO> partnerFormDataList(TeboPartnerQueryDTO partnerQueryDTO) {
        partnerQueryDTO.setStatus(1);
        List<TeboPartnerInfoDO> list = partnerInfoMapper.list(partnerQueryDTO);
        List<TeboPartnerFormVO> partnerFormVOS = new ArrayList<>();

        TeboShopQueryDTO teboShopQueryDTO = new TeboShopQueryDTO();
        teboShopQueryDTO.setStatus(1);
        teboShopQueryDTO.setCreateTimeStartSecond(partnerQueryDTO.getCreateTimeStartSecond());
        teboShopQueryDTO.setCreateTimeEndSecond(partnerQueryDTO.getCreateTimeEndSecond());
        List<TeboShopDO> teboShopDOS = shopMapper.list(teboShopQueryDTO);
        Map<Long, List<TeboShopDO>> shopGroup = DataUtils.listToGroup(teboShopDOS, TeboShopDO::getTenantId);

        TeboPlatGoodsQueryDTO platGoodsQueryDTO = new TeboPlatGoodsQueryDTO();
        platGoodsQueryDTO.setCreateTimeStart(partnerQueryDTO.getCreateTimeStart());
        platGoodsQueryDTO.setCreateTimeEnd(partnerQueryDTO.getCreateTimeEnd());
        List<TeboPlatGoodsGroupVO> goodsGroupVOS = remoteGoodsService.platGoodsPartnerGroup(platGoodsQueryDTO).getData();
        //合伙人分组
        Map<Long, TeboPlatGoodsGroupVO> goodsGroupVOMap = DataUtils.listToMap(goodsGroupVOS, TeboPlatGoodsGroupVO::getTenantId);


        //根据查出来的合伙人，补充统计信息
        for (TeboPartnerInfoDO teboPartnerInfoDO : list) {
            TeboPartnerFormVO partnerFormVO = new TeboPartnerFormVO();
            partnerFormVO.setPartnerCode(teboPartnerInfoDO.getPartnerCode());
            partnerFormVO.setPartnerName(teboPartnerInfoDO.getPartnerName());
            partnerFormVO.setPhoneNumber(teboPartnerInfoDO.getLinkPhone());
            //各个门店
            List<TeboShopDO> shopDOSByPartner = shopGroup.get(teboPartnerInfoDO.getId());
            if (CollectionUtil.isNotEmpty(shopDOSByPartner)) {
                Integer strandCount = (int) shopDOSByPartner.stream().filter(e -> {
                    Integer shopType = e.getShopType();
                    if (ShopTypeEnum.STANDARD.getCode() == shopType
                    ) {
                        return true;
                    }
                    return false;
                }).count();
                Integer ecCount = (int) shopDOSByPartner.stream().filter(e -> {
                    Integer shopType = e.getShopType();
                    if (ShopTypeEnum.EXPERIENCE_CENTER.getCode() == shopType
                    ) {
                        return true;
                    }
                    return false;
                }).count();
                Integer coopCount = (int) shopDOSByPartner.stream().filter(e -> {
                    Integer shopType = e.getShopType();
                    if (ShopTypeEnum.COOPERATE.getCode() == shopType
                    ) {
                        return true;
                    }
                    return false;
                }).count();

                partnerFormVO.setCooperateCount(coopCount);
                partnerFormVO.setExperienceCenterCount(ecCount);
                partnerFormVO.setStandardCount(strandCount);


            }
            TeboPlatGoodsGroupVO platGoodsGroupVO = goodsGroupVOMap.get(teboPartnerInfoDO.getId());
            partnerFormVO.setPlatGoodsCount(Objects.isNull(platGoodsGroupVO) ? 0 : platGoodsGroupVO.getCount());

//            partnerFormVO.setSelfGoodsCount();
            partnerFormVOS.add(partnerFormVO);

        }


//        List<TeboPartnerFormVO> partnerDataForm = partnerInfoManger.getPartnerDataForm(partnerQueryDTO);


        return partnerFormVOS;
    }

    @Override
    public void partnerExport(HttpServletResponse response, TeboPartnerQueryDTO queryDTO) {


        Integer pageNum = 1;
        queryDTO.setPageSize(200);


        List<TeboPartnerFormVO> partnerFormVOSAll = new ArrayList<>();

        while (true) {
            queryDTO.setPageNum(pageNum);
            Page page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
            List<TeboPartnerFormVO> partnerFormVOS = this.partnerFormDataList(queryDTO);
            if (CollectionUtil.isNotEmpty(partnerFormVOS)) {
                partnerFormVOSAll.addAll(partnerFormVOS);
                pageNum++;

            } else {
                break;
            }


        }

        List<ChartPartnerExcelVO> personExcelVOS = BeanConvert.copyList(partnerFormVOSAll, ChartPartnerExcelVO::new);


        // 将数据写入流中
        try {
            EasyExcel.write(response.getOutputStream(), ChartPartnerExcelVO.class).sheet("业务新增").doWrite(personExcelVOS);
        } catch (IOException e) {
            log.error("导出文件异常", e);
            throw new ServiceException("导出文件异常,请联系管理员");
        }

    }

    @Override
    public void shopExport(HttpServletResponse response, TeboShopQueryDTO queryDTO) {


        Integer pageNum = 1;
        queryDTO.setPageSize(200);


        List<TeboShopFormVO> shopFormVOSAll = new ArrayList<>();

        while (true) {
            queryDTO.setPageNum(pageNum);
            Page page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
            List<TeboShopFormVO> teboShopFormVOS = this.shopFormDataList(queryDTO);
            if (CollectionUtil.isNotEmpty(teboShopFormVOS)) {
                shopFormVOSAll.addAll(teboShopFormVOS);
                pageNum++;

            } else {
                break;
            }


        }

        List<ChartShopExcelVO> personExcelVOS = BeanConvert.copyList(shopFormVOSAll, ChartShopExcelVO::new);


        // 将数据写入流中
        try {
            EasyExcel.write(response.getOutputStream(), ChartShopExcelVO.class).sheet("业务新增").doWrite(personExcelVOS);
        } catch (IOException e) {
            log.error("导出文件异常", e);
            throw new ServiceException("导出文件异常,请联系管理员");
        }


    }

    @Override
    public TeboPartnerFormVO partnerFormChartAll(TeboPartnerQueryDTO queryDTO) {
        Integer pageNum = 1;
        queryDTO.setPageSize(200);


        List<TeboPartnerFormVO> partnerFormVOSAll = new ArrayList<>();

        while (true) {
            queryDTO.setPageNum(pageNum);
            Page page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
            List<TeboPartnerFormVO> partnerFormVOS = this.partnerFormDataList(queryDTO);
            if (CollectionUtil.isNotEmpty(partnerFormVOS)) {
                partnerFormVOSAll.addAll(partnerFormVOS);
                pageNum++;

            } else {
                break;
            }


        }
        Integer standardCount = partnerFormVOSAll.stream().map(TeboPartnerFormVO::getStandardCount).reduce(0, Integer::sum);
        Integer coopCount = partnerFormVOSAll.stream().map(TeboPartnerFormVO::getCooperateCount).reduce(0, Integer::sum);
        Integer ecCount = partnerFormVOSAll.stream().map(TeboPartnerFormVO::getExperienceCenterCount).reduce(0, Integer::sum);
        Integer pgCount = partnerFormVOSAll.stream().map(TeboPartnerFormVO::getPlatGoodsCount).reduce(0, Integer::sum);

        TeboPartnerFormVO partnerFormVO = new TeboPartnerFormVO();
        partnerFormVO.setStandardCount(standardCount);
        partnerFormVO.setCooperateCount(coopCount);
        partnerFormVO.setExperienceCenterCount(ecCount);
        partnerFormVO.setPlatGoodsCount(pgCount);

        return partnerFormVO;
    }

    @Override
    public List<TeboShopFormVO> shopFormDataList(TeboShopQueryDTO shopQueryDTO) {
        shopQueryDTO.setStatus(1);
        List<TeboShopDO> shopDOS = shopMapper.list(shopQueryDTO);
        if (CollectionUtil.isEmpty(shopDOS)) {
            return new ArrayList<>();
        }
        Map<Long, TeboShopDO> shopDOMap = DataUtils.listToMap(shopDOS, TeboShopDO::getId);

        List<TeboGoodsVO> goodsVOS = new ArrayList<>();
        DataUtils.splitHandle(shopDOS.stream().map(TeboShopDO::getId).collect(Collectors.toList()), temp -> {
            TeboGoodsQueryDTO goodsQueryDTO = new TeboGoodsQueryDTO();
            goodsQueryDTO.setShopIdList(temp);
            goodsQueryDTO.setReviewStatus(2);
            goodsQueryDTO.setStatus(1);
            List<TeboGoodsVO> teboGoodsVOS = remoteGoodsService.listGoodsSingle(goodsQueryDTO).getData();
//            List<TeboGoodsVO> teboGoodsVOS = remoteGoodsService.getPlatGoodsByShop(platGoodsShopQueryDTO).getData();
            if (CollectionUtil.isNotEmpty(teboGoodsVOS)) {
                goodsVOS.addAll(teboGoodsVOS);
            }

        }, 200);


        Map<Long, List<TeboGoodsVO>> goodsShopGroup = DataUtils.listToGroup(goodsVOS, TeboGoodsVO::getShopId);

        List<TeboShopFormVO> shopFormVOS = BeanConvert.copyList(shopDOS, TeboShopFormVO::new);
        for (TeboShopFormVO shopFormVO : shopFormVOS) {
            TeboShopDO shopDO = shopDOMap.get(shopFormVO.getId());
            shopFormVO.setPartnerName(shopDO.getTenantName());
            Integer shopNature = shopDO.getShopNature();
            TeboShopNatureEnum shopNatureEnum = TeboShopNatureEnum.getMsgByCode(shopNature);
            if (Objects.nonNull(shopNatureEnum)) {
                shopFormVO.setShopNatureName(shopNatureEnum.getMsg());
            }
            Integer shopType = shopFormVO.getShopType();
            if (Objects.nonNull(shopType)) {
                ShopTypeEnum anEnum = ShopTypeEnum.getEnum(shopType);
                if (Objects.nonNull(anEnum)) {
                    shopFormVO.setShopTypeName(anEnum.getMsg());
                }
            }

            List<TeboGoodsVO> teboGoodsVOS = goodsShopGroup.get(shopFormVO.getId());
            if (CollectionUtil.isNotEmpty(teboGoodsVOS)) {
                long platCount = teboGoodsVOS.stream().filter(e -> e.getProductType() == 2).count();
                long selfCount = teboGoodsVOS.stream().filter(e -> e.getProductType() == 1).count();
                shopFormVO.setPlatGoodsCount((int) platCount);
                shopFormVO.setSelfGoodsCount((int) selfCount);
            }

//            shopFormVO.setPartnerGoodsCount();
//            shopFormVO.setPlatGoodsCount(CollectionUtil.isEmpty(teboGoodsVOS) ? 0 : teboGoodsVOS.size());

        }


        return shopFormVOS;
    }

    @Override
    public TeboShopFormVO shopFormChartAll(TeboShopQueryDTO queryDTO) {


        Integer pageNum = 1;
        queryDTO.setPageSize(300);


        List<TeboShopFormVO> teboShopFormVOS = new ArrayList<>();

        while (true) {
            queryDTO.setPageNum(pageNum);
            Page page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
            List<TeboShopFormVO> shopFormVOS = this.shopFormDataList(queryDTO);
            if (CollectionUtil.isNotEmpty(shopFormVOS)) {
                teboShopFormVOS.addAll(shopFormVOS);
                pageNum++;

            } else {
                break;
            }


        }
        Integer platGoodsCount = teboShopFormVOS.stream().map(TeboShopFormVO::getPlatGoodsCount).reduce(0, Integer::sum);
        Integer selfGoodsCount = teboShopFormVOS.stream().map(TeboShopFormVO::getSelfGoodsCount).reduce(0, Integer::sum);
        //todo 合伙人授权商品数量

        TeboShopFormVO shopFormVO = new TeboShopFormVO();
        shopFormVO.setPlatGoodsCount(platGoodsCount);
        shopFormVO.setSelfGoodsCount(selfGoodsCount);
        return shopFormVO;
    }

    @Override
    public List<TeboShopDataProvVO> shopDataProvGroup(ReportChartBaseSO reportChartBaseSO) {


        TeboShopQueryDTO shopQueryDTO = new TeboShopQueryDTO();
        shopQueryDTO.setCreateTimeStartSecond(reportChartBaseSO.getTimeStart().atStartOfDay());
        shopQueryDTO.setCreateTimeEndSecond(reportChartBaseSO.getTimeEnd().plusDays(1).atStartOfDay());
        shopQueryDTO.setStatus(1);

        List<TeboShopDO> list = shopMapper.list(shopQueryDTO);


        Map<String, List<TeboShopDO>> shopProvGroup = DataUtils.listToGroup(list, (e) -> {
            String areaName = e.getAreaName();
            String[] split = areaName.split("-");
            String prov = split[0];

            return prov;
        });

        List<TeboShopDataProvVO> provVOList = new ArrayList<>();
        for (String prov : shopProvGroup.keySet()) {
            TeboShopDataProvVO shopDataProvVO = new TeboShopDataProvVO();
            shopDataProvVO.setProv(prov);
            int size = shopProvGroup.get(prov).size();
            shopDataProvVO.setCustomerCount(size);
            BigDecimal allSizeDecimal = BigDecimal.valueOf(list.size());
            BigDecimal provSizeDecimal = BigDecimal.valueOf(size * 100);
            BigDecimal divide = provSizeDecimal.divide(allSizeDecimal, 2, RoundingMode.HALF_UP);
            shopDataProvVO.setRate(divide.toEngineeringString() + "%");
            provVOList.add(shopDataProvVO);

        }
        List<TeboShopDataProvVO> dataProvVOS = provVOList.stream().sorted(Comparator.comparing(TeboShopDataProvVO::getCustomerCount, Comparator.reverseOrder())).collect(Collectors.toList());
        for (int i = 0; i < dataProvVOS.size(); i++) {
            TeboShopDataProvVO shopDataProvVO = dataProvVOS.get(i);
            shopDataProvVO.setSort(i + 1);
        }
        return dataProvVOS;
    }

    @Override
    public List<ReportShopDealVO> shopDataDealTop10(PayWechatRecordQueryDTO payWechatRecordQueryDTO) {
        List<ReportShopDealVO> resultList = new ArrayList<>();
        List<ReportShopDealVO> shopAmountTop100 = payWechatRecordMapper.getShopAmountTop100(payWechatRecordQueryDTO);
        List<Long> shopIds = shopAmountTop100.stream().map(ReportShopDealVO::getShopId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(shopIds)) {
            TeboShopQueryDTO shopQueryDTO = new TeboShopQueryDTO();
            shopQueryDTO.setIdList(shopIds);
            List<TeboShopDO> shopDOS = shopMapper.list(shopQueryDTO);
            Map<Long, TeboShopDO> shopDOMap = DataUtils.listToMap(shopDOS, TeboShopDO::getId);
            int sort = 1;
            for (ReportShopDealVO reportShopDealVO : shopAmountTop100) {
                if (sort > 10) {
                    continue;
                }
                reportShopDealVO.setSort(sort);


                TeboShopDO shopDO = shopDOMap.get(reportShopDealVO.getShopId());
                if (Objects.nonNull(shopDO)) {
                    if (1 != shopDO.getStatus()) {
                        continue;
                    }
                    Integer shopType = shopDO.getShopType();
                    reportShopDealVO.setShopType(shopType);
                    reportShopDealVO.setShopName(shopDO.getShopName());
                    if (Objects.nonNull(shopType)) {
                        ShopTypeEnum anEnum = ShopTypeEnum.getEnum(shopType);
                        if (Objects.nonNull(anEnum)) {
                            reportShopDealVO.setShopTypeName(anEnum.getMsg());
                        }
                    }
                    sort++;
                    resultList.add(reportShopDealVO);
                }
            }
        }

        return resultList;
    }

    @Override
    public void shopDataDealExport(PayWechatRecordQueryDTO payWechatRecordQueryDTO, HttpServletResponse response) {
        List<ReportShopDealVO> shopAmountAll = payWechatRecordMapper.getShopAmountAll(payWechatRecordQueryDTO);
        List<Long> shopIds = shopAmountAll.stream().map(ReportShopDealVO::getShopId).collect(Collectors.toSet()).stream().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(shopIds)) {
            List<TeboShopDO> shopDOS = new ArrayList<>();
            DataUtils.splitHandle(shopIds, split -> {
                TeboShopQueryDTO shopQueryDTO = new TeboShopQueryDTO();
                shopQueryDTO.setIdList(shopIds);
                shopDOS.addAll(shopMapper.list(shopQueryDTO));
            }, 200);

            Map<Long, TeboShopDO> shopDOMap = DataUtils.listToMap(shopDOS, TeboShopDO::getId);
            int sort = 1;
            for (ReportShopDealVO reportShopDealVO : shopAmountAll) {
                reportShopDealVO.setSort(sort);
                sort++;
                TeboShopDO shopDO = shopDOMap.get(reportShopDealVO.getShopId());
                if (Objects.nonNull(shopDO)) {
                    Integer shopType = shopDO.getShopType();
                    reportShopDealVO.setShopType(shopType);
                    reportShopDealVO.setStatus(shopDO.getStatus());
                    if (Objects.nonNull(shopType)) {
                        ShopTypeEnum anEnum = ShopTypeEnum.getEnum(shopType);
                        if (Objects.nonNull(anEnum)) {
                            reportShopDealVO.setShopTypeName(anEnum.getMsg());
                        }
                    }

                }
            }
        }

        List<ReportShopDealVO> collect = shopAmountAll.stream().filter(e -> e.getStatus() != null && 1 == e.getStatus()).collect(Collectors.toList());
        List<ReportShopDealExcelVO> personExcelVOS = BeanConvert.copyList(collect, ReportShopDealExcelVO::new);

        // 将数据写入流中
        try {
            EasyExcel.write(response.getOutputStream(), ReportShopDealExcelVO.class).sheet("门店成交").doWrite(personExcelVOS);
        } catch (IOException e) {
            log.error("导出文件异常", e);
            throw new ServiceException("导出文件异常,请联系管理员");
        }
    }

    @Override
    public ReportShopDealAllVO shopDealTopAll(PayWechatRecordQueryDTO payWechatRecordQueryDTO) {
        Integer totalAmount = payWechatRecordMapper.getTotalAmount(payWechatRecordQueryDTO);
        ReportShopDealAllVO reportShopDealAllVO = new ReportShopDealAllVO();
        reportShopDealAllVO.setAmountAll(totalAmount);
        return reportShopDealAllVO;
    }
}
