package com.tebo.system.mapper;

import com.tebo.system.api.domain.dto.ApplyLeaderReviewRecordDTO;
import com.tebo.system.entity.ApplyLeaderReviewRecordDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 团长申请审核表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-08
 */
@Mapper
public interface ApplyLeaderReviewRecordMapper extends TeboBaseMapper<ApplyLeaderReviewRecordDO> {
    ApplyLeaderReviewRecordDO getApplyLeaderRecordInfo(ApplyLeaderReviewRecordDTO applyLeaderReviewRecordDTO);
    List<ApplyLeaderReviewRecordDO> getApplyLeaderRecordList(ApplyLeaderReviewRecordDTO applyLeaderReviewRecordDTO);

    Integer updateApplyLeaderStatus(ApplyLeaderReviewRecordDTO applyLeaderReviewRecordDTO);
}
