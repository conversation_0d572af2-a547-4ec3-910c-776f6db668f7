package com.tebo.system.manager.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tebo.system.domain.so.BranchSO;
import com.tebo.system.entity.TeboBranchBankDO;
import com.tebo.system.manager.TeboBranchBankManger;
import com.tebo.system.mapper.TeboBranchBankMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class TeboBranchBankMangerImpl extends ServiceImpl<TeboBranchBankMapper, TeboBranchBankDO> implements TeboBranchBankManger {

    @Autowired
    private TeboBranchBankMapper branchBankMapper;


    @Override
    public List<TeboBranchBankDO> list(BranchSO branchSO) {
        return branchBankMapper.list(branchSO);
    }
}
