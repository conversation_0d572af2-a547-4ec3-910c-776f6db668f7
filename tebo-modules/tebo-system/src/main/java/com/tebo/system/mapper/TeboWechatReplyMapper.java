package com.tebo.system.mapper;

import com.tebo.system.entity.TeboWechatReplyDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 微信问答表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@Mapper
public interface TeboWechatReplyMapper extends TeboBaseMapper<TeboWechatReplyDO> {

    List<TeboWechatReplyDO> getByAskContent(@Param("askContent") String askContent);
}
