package com.tebo.system.domain.vo.shop;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 零售通门店接单审核记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Data
public class LstShopReviewRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 审核状态 0:待审核 1:审核通过 2:审核驳回
     */
    private Integer reviewStatus;

    /**
     * 失败原因
     */
    private String reason;

    /**
     * 审核人
     */
    private String reviewer;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
}
