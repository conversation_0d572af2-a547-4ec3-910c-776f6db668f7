package com.tebo.system.service.impl;

import com.tebo.system.domain.so.ALiYuYinPushSO;
import com.tebo.system.manager.AliManger;
import com.tebo.system.service.IAliService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AliServiceImpl implements IAliService {

    @Autowired
    private AliManger aliManger;

    @Override
    public Boolean push(ALiYuYinPushSO aLiYuYinPushSO) {
        return aliManger.pushYuYin(aLiYuYinPushSO);
    }

}
