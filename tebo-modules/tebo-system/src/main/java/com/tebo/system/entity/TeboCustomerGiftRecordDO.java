package com.tebo.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * C端用户久久卡埋点表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
@Getter
@Setter
@TableName("tebo_customer_gift_record")
public class TeboCustomerGiftRecordDO extends Model<TeboCustomerGiftRecordDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 用户unionid
     */
    @TableField("unionid")
    private String unionid;

    /**
     * 用户昵称
     */
    @TableField("nick_name")
    private String nickName;

    /**
     * 手机号码
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 1点击分享2点击购买
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 1钜惠2常规
     */
    @TableField("goods_type")
    private Integer goodsType;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 区域
     */
    @TableField("district")
    private String district;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
