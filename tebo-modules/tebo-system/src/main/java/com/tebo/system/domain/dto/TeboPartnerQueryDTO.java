package com.tebo.system.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tebo.common.core.web.page.PageDomain;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author：zhengmk
 * @Date 2023/12/11 9:26
 */
@Data
public class TeboPartnerQueryDTO extends PageDomain implements Serializable{

    // 查询内容
    private String keyword;

    /**
     * 合伙人账号模糊查询
     */
    private String userName;


    private String partnerNameLike;
    private String partnerCodeLike;


    /**
     * 联系电话
     */
    private String phoneNumberLike;



    /**
     * 区域code
     */
    private String areaCode;

    // id列表
    private List<Long> idList;

    // 状态
    private Integer status;

    /**
     * 行政区域
     */
    private String areaStr;
    private String areaName;



    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate createTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate createTimeEnd;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTimeStartSecond;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTimeEndSecond;
    /**
     * 时间维度
     */
    private  String timeType;

}
