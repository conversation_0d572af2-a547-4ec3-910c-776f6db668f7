package com.tebo.system.controller.shop;

import com.github.pagehelper.PageHelper;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.system.domain.dto.TeboShopQueryDTO;
import com.tebo.system.domain.vo.shop.TeboShopListVO;
import com.tebo.system.service.IShopService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author：zhengmk
 * @Date 2023/12/13 13:51
 */
@RestController
@RequestMapping("/customer/shop")
public class TeboCustomerShopController extends BaseController {

    @Resource
    private IShopService shopService;

    @PostMapping("/page")
    public TableDataInfo page(@RequestBody TeboShopQueryDTO queryDTO) {
        if(queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            throw new IllegalArgumentException("分页参数不能为空");
        }
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<TeboShopListVO> list = shopService.list(queryDTO);
        return getDataTable(list);
    }

}
