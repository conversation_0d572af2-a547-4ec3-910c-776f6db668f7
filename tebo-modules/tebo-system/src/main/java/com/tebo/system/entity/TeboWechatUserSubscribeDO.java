package com.tebo.system.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 公众号用户绑定记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-27
 */
@Data
@TableName("tebo_wechat_user_subscribe")
public class TeboWechatUserSubscribeDO extends Model<TeboWechatUserSubscribeDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 公众号用户openid
     */
    @TableField("open_id")
    private String openId;

    @TableField("unionid")
    private String unionid;

    /**
     * 服务号
     */
    @TableField("official_account")
    private String officialAccount;

    /**
     * subscribe或者unsubscribe
     */
    @TableField("`status`")
    private String status;

    /**
     * 创建者
     */
    @TableField(value = "create_by",fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by",fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
