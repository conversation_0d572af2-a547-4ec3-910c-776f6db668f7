package com.tebo.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.enums.ShopStarLevelIntegralEnum;
import com.tebo.common.core.enums.WeChatOfficialAccountEnum;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.core.utils.excel.ExcelUtil;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.security.utils.SecurityUtils;
import com.tebo.common.util.DataUtils;
import com.tebo.common.util.applet.AppletRequestUrl;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.http.HttpTool;
import com.tebo.common.util.number.CodeGenerator;
import com.tebo.common.util.number.DistanceUtil;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.common.util.time.DateUtil;
import com.tebo.mall.api.RemoteGoodsService;
import com.tebo.mall.api.domain.dto.TeboGoodsQueryDTO;
import com.tebo.rescue.api.RemoteLstShopService;
import com.tebo.rescue.api.domain.dto.ShopCustomerQueryDTO;
import com.tebo.rescue.api.domain.dto.lst.ShopDto;
import com.tebo.rescue.api.domain.view.LstShopVo;
import com.tebo.system.api.RemoteAreaService;
import com.tebo.system.api.domain.SysUser;
import com.tebo.system.api.domain.dto.TeboShopQueryParamDTO;
import com.tebo.system.api.domain.view.TeboAreaVO;
import com.tebo.system.api.domain.view.TeboMerchantShopVO;
import com.tebo.system.api.model.TeboShop;
import com.tebo.system.config.WxPushConfig;
import com.tebo.system.domain.dto.*;
import com.tebo.system.domain.dto.consumer.TeboConsumerRecordQueryDTO;
import com.tebo.system.domain.dto.merchant.TeboShopReviewQueryDTO;
import com.tebo.system.domain.so.BranchSO;
import com.tebo.system.domain.so.ShopAliUpdateSO;
import com.tebo.system.domain.so.WxPushSO;
import com.tebo.system.domain.view.TeboCityVO;
import com.tebo.system.domain.view.TeboDistrictVO;
import com.tebo.system.domain.view.TeboProvinceVO;
import com.tebo.system.domain.vo.consumer.TeboConsumerVO;
import com.tebo.system.domain.vo.consumer.TeboCustomerServiceRecordVO;
import com.tebo.system.domain.vo.excel.ShopExportVO;
import com.tebo.system.domain.vo.openApi.OpenApiShopVO;
import com.tebo.system.domain.vo.shop.AppletShopDetailVO;
import com.tebo.system.domain.vo.shop.ShopSapCodeVO;
import com.tebo.system.domain.vo.shop.TeboShopListVO;
import com.tebo.system.domain.vo.shop.TeboShopVO;
import com.tebo.system.entity.*;
import com.tebo.system.enums.*;
import com.tebo.system.manager.*;
import com.tebo.system.manager.consumer.TeboConsumerManager;
import com.tebo.system.manager.merchant.TeboShopReviewManager;
import com.tebo.system.mapper.*;
import com.tebo.system.mapper.pay.PayShopMerchantsMapper;
import com.tebo.system.service.*;
import com.tebo.system.service.consumer.TeboConsumerMarkService;
import com.tebo.system.service.merchant.TeboShopReviewService;
import com.tebo.system.service.push.IWxPushService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author：zhengmk
 * @Date 2023/12/12 10:33
 */
@Slf4j
@Service
public class ShopService implements IShopService {

    @Resource
    private RedisService redisService;

    @Resource
    private IFileService fileService;

    @Resource
    private RemoteGoodsService remoteGoodsService;

    @Resource
    private RemoteAreaService remoteAreaService;

    @Resource
    private TeboShopMapper teboShopMapper;

    @Resource
    private TeboShopAdditionalMapper shopAdditionalMapper;

    @Resource
    private LstShopReviewRecordMapper lstShopReviewRecordMapper;

    @Autowired
    private TeboShopManager shopManager;

    @Autowired
    private TeboLstShopManger lstShopManger;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private TeboShopUserLinkMapper teboShopUserLinkMapper;

    @Resource
    private TeboPartnerInfoMapper teboPartnerInfoMapper;

    @Autowired
    private IAreaService areaService;

    @Autowired
    private ISysUserService userService;
    @Resource
    private TeboBaseService teboBaseService;
    @Resource
    private TeboAccountManger teboAccountManger;
    @Resource
    private TeboConsumerManager teboConsumerManager;
    @Resource
    private PayShopMerchantsMapper payShopMerchantsMapper;

    @Autowired
    private TeboBranchBankManger branchBankManger;

    @Resource
    private TeboShopAttachmentManager shopAttachmentManager;

    @Resource
    private ITeboConsumerService teboConsumerService;
    @Resource
    private TeboConsumerMarkService teboConsumerMarkService;

    @Autowired
    private HttpServletRequest request;

    @Resource
    private TeboRoleMapper teboRoleMapper;

    @Resource
    private TeboAccountMapper teboAccountMapper;

    @Autowired
    private RemoteLstShopService remoteLstShopService;

    @Resource
    private TeboMemberShopManager teboMemberShopManager;

    @Resource
    private TeboShopPostionChangeLogMapper teboShopPostionChangeLogMapper;

    @Value("${applet.appId}")
    private String appId;

    @Value("${applet.secret}")
    private String secret;


    @Resource
    private WxPushConfig wxPushConfig;

    @Value("${applet.qrcode.page}")
    private String qrCodePage;
    @Value("${wx.template_id.shop_regist}")
    private String regist;
    @Value("${applet.appId}")
    private String appid;

    /**
     * 门店二维码
     */
    @Value("${applet.qrcode.shopQrCodePage}")
    private String shopQrCodePage;

    @Autowired
    private TeboShopPicManger shopPicManger;

    @Resource
    private TeboShopReviewManager teboShopReviewManager;


    @Autowired
    private TeboShopCommentManger shopCommentManger;

    //服务保证金
    private final int serverFee = 50000;
    @Resource
    private TeboShopReviewService teboShopReviewService;

    @Override
    public List<TeboShopListVO> list(TeboShopQueryDTO queryDTO) {
        if (queryDTO == null) {
            throw new GlobalException("参数不能为空");
        }
        List<TeboShopDO> list = teboShopMapper.list(queryDTO);
        if (list.isEmpty()) {
            return Collections.emptyList();
        }
        List<Long> shopIdList = list.stream().map(TeboShopDO::getId).collect(Collectors.toList());
        TeboShopReviewQueryDTO reviewQueryDTO = new TeboShopReviewQueryDTO();
        reviewQueryDTO.setShopIdList(shopIdList);
        List<TeboShopReviewDO> reviewList = teboShopReviewManager.getTeboReviewList(reviewQueryDTO);
        Map<Long, TeboShopReviewDO> reviewDOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(reviewList)) {
            reviewDOMap = DataUtils.listToMap(reviewList, TeboShopReviewDO::getShopId);
        }


        ShopCustomerQueryDTO shopCustomerQueryDTO = new ShopCustomerQueryDTO();
        shopCustomerQueryDTO.setShopIdList(shopIdList);
        TeboConsumerRecordQueryDTO query = new TeboConsumerRecordQueryDTO();
        query.setShopIdList(shopIdList);
        List<TeboCustomerServiceRecordVO> customerList = teboConsumerManager.selectServiceRecordList(query);
        Map<Long, List<TeboCustomerServiceRecordVO>> shopCustomerNum = customerList.stream().collect(Collectors.groupingBy(TeboCustomerServiceRecordVO::getShopId));
        List<TeboShopListVO> resList = BeanConvert.copyList(list, TeboShopListVO::new);
        Map<Long, List<SysUser>> userMap = getUserNameByShopId(shopIdList);
        Map<Long, TeboShopReviewDO> finalReviewDOMap = reviewDOMap;
        resList.forEach(item -> {
            item.setCustomerNum(CollectionUtils.isEmpty(shopCustomerNum.get(item.getId())) ? 0 : shopCustomerNum.get(item.getId()).size());
            int goodsEarnestMoney = Objects.isNull(item.getGoodsEarnestMoney()) ? 0 : item.getGoodsEarnestMoney();
            int serviceEarnestMoney = Objects.isNull(item.getServiceEarnestMoney()) ? 0 : item.getServiceEarnestMoney();
            item.setEarnestMoney(goodsEarnestMoney + serviceEarnestMoney);

            if (CollectionUtil.isNotEmpty(finalReviewDOMap)) {
                TeboShopReviewDO teboShopReviewDO = finalReviewDOMap.get(item.getId());
                if (!ObjectUtils.isEmpty(teboShopReviewDO) && (teboShopReviewDO.getReviewStatus() == 1 || teboShopReviewDO.getReviewStatus() == 0)) {
                    item.setReviewId(teboShopReviewDO.getId());
                }
            }
            if (!CollectionUtils.isEmpty(userMap.get(item.getId()))) {
                List<SysUser> sysUserList = userMap.get(item.getId());
                item.setUserName(sysUserList.get(0).getUserName());
            }
        });
        return resList;
    }

    /**
     * 修车救援-附近门店
     */
    @Override
    public List<TeboShopListVO> rescueNearbyStore(TeboShopQueryDTO queryDTO) {
        if (queryDTO == null) {
            throw new GlobalException("参数不能为空");
        }
        List<TeboShopDO> list = teboShopMapper.rescueNearbyStore(queryDTO);
        if (list.isEmpty()) {
            return Collections.emptyList();
        }
        List<Long> shopIdList = list.stream().map(TeboShopDO::getId).collect(Collectors.toList());
        List<Long> openRescueShopIdList = teboAccountManger.getOpenRescueShopList(shopIdList);
        ShopCustomerQueryDTO shopCustomerQueryDTO = new ShopCustomerQueryDTO();
        shopCustomerQueryDTO.setShopIdList(shopIdList);
        TeboConsumerRecordQueryDTO query = new TeboConsumerRecordQueryDTO();
        query.setShopIdList(shopIdList);
        List<TeboCustomerServiceRecordVO> customerList = teboConsumerManager.selectServiceRecordList(query);
        Map<Long, List<TeboCustomerServiceRecordVO>> shopCustomerNum = customerList.stream().collect(Collectors.groupingBy(TeboCustomerServiceRecordVO::getShopId));
        List<TeboShopListVO> resList = BeanConvert.copyList(list, TeboShopListVO::new);
        Map<Long, List<SysUser>> userMap = getUserNameByShopId(shopIdList);
        List<TeboShopListVO> finalResult = new ArrayList<>();
        resList.forEach(item -> {
            if (CollectionUtil.isNotEmpty(openRescueShopIdList) && openRescueShopIdList.contains(item.getId().toString())){
                item.setCustomerNum(CollectionUtils.isEmpty(shopCustomerNum.get(item.getId())) ? 0 : shopCustomerNum.get(item.getId()).size());
                int goodsEarnestMoney = Objects.isNull(item.getGoodsEarnestMoney()) ? 0 : item.getGoodsEarnestMoney();
                int serviceEarnestMoney = Objects.isNull(item.getServiceEarnestMoney()) ? 0 : item.getServiceEarnestMoney();
                item.setEarnestMoney(goodsEarnestMoney + serviceEarnestMoney);
                if (!CollectionUtils.isEmpty(userMap.get(item.getId()))) {
                    List<SysUser> sysUserList = userMap.get(item.getId());
                    item.setUserName(sysUserList.get(0).getUserName());
                }
                finalResult.add(item);
            }

        });
        return finalResult;
    }
    @Override
    public List<TeboShopListVO> nearbyStore(TeboShopQueryDTO queryDTO) {
        if (queryDTO == null) {
            throw new GlobalException("参数不能为空");
        }
        List<TeboShopDO> list = teboShopMapper.nearbyStore(queryDTO);
        if (list.isEmpty()) {
            return Collections.emptyList();
        }
        List<Long> shopIdList = list.stream().map(TeboShopDO::getId).collect(Collectors.toList());
        TeboShopReviewQueryDTO reviewQueryDTO = new TeboShopReviewQueryDTO();
        reviewQueryDTO.setShopIdList(shopIdList);
        ShopCustomerQueryDTO shopCustomerQueryDTO = new ShopCustomerQueryDTO();
        shopCustomerQueryDTO.setShopIdList(shopIdList);
        TeboConsumerRecordQueryDTO query = new TeboConsumerRecordQueryDTO();
        query.setShopIdList(shopIdList);
        List<TeboCustomerServiceRecordVO> customerList = teboConsumerManager.selectServiceRecordList(query);
        Map<Long, List<TeboCustomerServiceRecordVO>> shopCustomerNum = customerList.stream().collect(Collectors.groupingBy(TeboCustomerServiceRecordVO::getShopId));
        List<TeboShopListVO> resList = BeanConvert.copyList(list, TeboShopListVO::new);
        Map<Long, List<SysUser>> userMap = getUserNameByShopId(shopIdList);
        resList.forEach(item -> {
            item.setCustomerNum(CollectionUtils.isEmpty(shopCustomerNum.get(item.getId())) ? 0 : shopCustomerNum.get(item.getId()).size());
            int goodsEarnestMoney = Objects.isNull(item.getGoodsEarnestMoney()) ? 0 : item.getGoodsEarnestMoney();
            int serviceEarnestMoney = Objects.isNull(item.getServiceEarnestMoney()) ? 0 : item.getServiceEarnestMoney();
            item.setEarnestMoney(goodsEarnestMoney + serviceEarnestMoney);
            if (!CollectionUtils.isEmpty(userMap.get(item.getId()))) {
                List<SysUser> sysUserList = userMap.get(item.getId());
                item.setUserName(sysUserList.get(0).getUserName());
            }
        });
        return resList;
    }

    public List<TeboShopListVO> getShopListForPc(TeboShopQueryDTO queryDTO){
        if (queryDTO == null) {
            throw new GlobalException("参数不能为空");
        }
        List<TeboShopDO> list = teboShopMapper.getShopListForPc(queryDTO);
        if (list.isEmpty()) {
            return Collections.emptyList();
        }
        List<Long> shopIdList = list.stream().map(TeboShopDO::getId).collect(Collectors.toList());
        TeboShopReviewQueryDTO reviewQueryDTO = new TeboShopReviewQueryDTO();
        reviewQueryDTO.setShopIdList(shopIdList);
        List<TeboShopReviewDO> reviewList = teboShopReviewManager.getTeboReviewList(reviewQueryDTO);
        Map<Long, TeboShopReviewDO> reviewDOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(reviewList)) {
            reviewDOMap = DataUtils.listToMap(reviewList, TeboShopReviewDO::getShopId);
        }
        ShopCustomerQueryDTO shopCustomerQueryDTO = new ShopCustomerQueryDTO();
        shopCustomerQueryDTO.setShopIdList(shopIdList);
        TeboConsumerRecordQueryDTO query = new TeboConsumerRecordQueryDTO();
        query.setShopIdList(shopIdList);
        List<TeboCustomerServiceRecordVO> customerList = teboConsumerManager.selectServiceRecordList(query);
        Map<Long, List<TeboCustomerServiceRecordVO>> shopCustomerNum = customerList.stream().collect(Collectors.groupingBy(TeboCustomerServiceRecordVO::getShopId));
        List<TeboShopListVO> resList = BeanConvert.copyList(list, TeboShopListVO::new);
        Map<Long, List<SysUser>> userMap = getUserNameByShopId(shopIdList);
        Map<Long, TeboShopReviewDO> finalReviewDOMap = reviewDOMap;
        resList.forEach(item -> {
            item.setCustomerNum(CollectionUtils.isEmpty(shopCustomerNum.get(item.getId())) ? 0 : shopCustomerNum.get(item.getId()).size());
            int goodsEarnestMoney = Objects.isNull(item.getGoodsEarnestMoney()) ? 0 : item.getGoodsEarnestMoney();
            int serviceEarnestMoney = Objects.isNull(item.getServiceEarnestMoney()) ? 0 : item.getServiceEarnestMoney();
            item.setEarnestMoney(goodsEarnestMoney + serviceEarnestMoney);

            if (CollectionUtil.isNotEmpty(finalReviewDOMap)) {
                TeboShopReviewDO teboShopReviewDO = finalReviewDOMap.get(item.getId());
                if (!ObjectUtils.isEmpty(teboShopReviewDO) && (teboShopReviewDO.getReviewStatus() == 1 || teboShopReviewDO.getReviewStatus() == 0)) {
                    item.setReviewId(teboShopReviewDO.getId());
                }
            }
            if (!CollectionUtils.isEmpty(userMap.get(item.getId()))) {
                List<SysUser> sysUserList = userMap.get(item.getId());
                item.setUserName(sysUserList.get(0).getUserName());
            }
        });
        return resList;
    }
    public List<TeboShopListVO> getShopList(TeboShopQueryParamDTO queryDTO){
        TeboShopQueryDTO teboShopQueryDTO = new TeboShopQueryDTO();
        BeanConvert.copy(queryDTO,teboShopQueryDTO);
        return list(teboShopQueryDTO);
    }

    // 通过门店id查询账号
    private Map<Long, List<SysUser>> getUserNameByShopId(List<Long> shopIds) {
        List<SysUser> sysUsers = sysUserMapper.selectUserByShopId(shopIds);
        Map<Long, List<SysUser>> userNameMap = sysUsers.stream().collect(Collectors.groupingBy(SysUser::getShopId));
        return userNameMap;
    }

    @Override
    public void export(HttpServletResponse response, TeboShopQueryDTO queryDTO) {
        List<TeboShopListVO> shopListVOS = list(queryDTO);


        List<ShopExportVO> shopExportVOS = getShopExportVOList(shopListVOS);


        ExcelUtil.exportExcelToResponse(response, shopExportVOS, ShopExportVO.class, "门店列表");

    }

    private List<ShopExportVO> getShopExportVOList(List<TeboShopListVO> shopListVOS) {
        List<ShopExportVO> shopExportVOS = BeanConvert.copyList(shopListVOS, ShopExportVO::new);
        List<TeboBranchBankDO> branchBankDOS = new ArrayList<>();
        Map<String, TeboBranchBankDO> bankDOMap = new HashMap<>();
        //支行编码
        List<String> branchCodes = shopExportVOS.stream().map(ShopExportVO::getAccountBranchBank).filter(StringUtils::isNotEmpty).collect(Collectors.toSet()).stream().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(branchCodes)) {

            DataUtils.splitHandle(branchCodes, list -> {

                BranchSO branchSO = new BranchSO();
                branchSO.setBranchNos(list);
                branchBankDOS.addAll(branchBankManger.list(branchSO));

            }, 200);
            bankDOMap = DataUtils.listToMap(branchBankDOS, TeboBranchBankDO::getBranchNo);
        }
        List<Long> shopIds = shopListVOS.stream().map(TeboShopListVO::getId).collect(Collectors.toSet()).stream().collect(Collectors.toList());
        List<TeboShopPayMerchantsDO> shopPayMerchantsDOList = new ArrayList<>();
        DataUtils.splitHandle(shopIds, (split) -> {
            if (CollectionUtil.isNotEmpty(split)) {
                List<TeboShopPayMerchantsDO> merchants = payShopMerchantsMapper.getMerchantsByShopIds(split);
                shopPayMerchantsDOList.addAll(merchants);
            }

        }, 200);
        Map<Long, List<TeboShopPayMerchantsDO>> merchantByShopId = DataUtils.listToGroup(shopPayMerchantsDOList, TeboShopPayMerchantsDO::getShopId);

        for (ShopExportVO shopExportVO : shopExportVOS) {
            String accountBranchBank = shopExportVO.getAccountBranchBank();
            if (StringUtils.isNotEmpty(accountBranchBank)) {
                TeboBranchBankDO branchBankDO = bankDOMap.get(accountBranchBank);
                if (Objects.nonNull(branchBankDO)) {
                    shopExportVO.setAccountBranchBankName(branchBankDO.getBranchName());
                } else {
                    shopExportVO.setAccountBranchBankName(shopExportVO.getAccountBranchBank());
                }
            }

            Integer shopNature = shopExportVO.getShopNature();
            if (Objects.nonNull(shopNature)) {
                TeboShopNatureEnum shopNatureEnum = TeboShopNatureEnum.getMsgByCode(shopNature);
                if (Objects.nonNull(shopNatureEnum)) {
                    shopExportVO.setShopNatureName(shopNatureEnum.getMsg());
                }
            }
//            Integer status = shopExportVO.getStatus();
//            if (Objects.nonNull(status)) {
//                if (0 == status) {
//                    shopExportVO.setStatusName("正常");
//                } else if (1 == status) {
//                    shopExportVO.setStatusName("停用");
//                }
//            }
            Integer shopType = shopExportVO.getShopType();
            if (Objects.nonNull(shopType)) {
                ShopTypeEnum typeEnum = ShopTypeEnum.getEnum(shopType);
                if (Objects.nonNull(typeEnum)) {
                    shopExportVO.setShopTypeName(typeEnum.getMsg());
                }
            }
            Boolean openMall = shopExportVO.getOpenMall();
            if (Objects.nonNull(openMall)) {
                if (Boolean.TRUE.equals(openMall)) {
                    shopExportVO.setOpenMallName("是");
                } else {
                    shopExportVO.setOpenMallName("否");
                }
            }
            Boolean openWorkbench = shopExportVO.getOpenWorkbench();
            if (Boolean.TRUE.equals(openWorkbench)) {
                shopExportVO.setOpenWorkbenchName("是");
            } else {
                shopExportVO.setOpenWorkbenchName("否");
            }
            Boolean openDoor = shopExportVO.getOpenDoor();
            if (Objects.nonNull(openDoor)) {
                if (Boolean.TRUE.equals(openDoor)) {
                    shopExportVO.setOpenDoorName("是");
                } else {
                    shopExportVO.setOpenDoorName("否");
                }
            }

            Boolean openRescue = shopExportVO.getOpenRescue();
            if (Objects.nonNull(openRescue)) {
                if (Boolean.TRUE.equals(openRescue)) {
                    shopExportVO.setOpenRescueName("是");
                } else {
                    shopExportVO.setOpenRescueName("否");
                }
            }

            Integer showNearbyShop = shopExportVO.getShowNearbyShop();
            if (Objects.nonNull(showNearbyShop) && 1 == (showNearbyShop)) {
                shopExportVO.setShowNearbyShopName("是");
            } else {
                shopExportVO.setShowNearbyShopName("否");
            }

            Integer customerSubject = shopExportVO.getCustomerSubject();
            if (Objects.nonNull(customerSubject) && 1 == (customerSubject)) {
                shopExportVO.setCustomerSubjectName("企业");
            } else if (0 == customerSubject) {
                shopExportVO.setCustomerSubjectName("个体");
            }
            String serviceScope = shopExportVO.getServiceScope();
            if (com.alibaba.cloud.commons.lang.StringUtils.isNotBlank(serviceScope)) {

                String[] split = serviceScope.split(",");
                StringBuilder stringBuilder = new StringBuilder();

                for (String s : split) {
                    ShopServiceScopeEnum anEnum = ShopServiceScopeEnum.getEnum(Integer.parseInt(s));
                    if (Objects.nonNull(anEnum)) {
                        stringBuilder.append(anEnum.getMsg() + " ");
                    }

                }

                shopExportVO.setServiceScopeName(stringBuilder.toString());
            }

            List<TeboShopPayMerchantsDO> merchantsDOS = merchantByShopId.get(shopExportVO.getId());
            if (CollectionUtil.isNotEmpty(merchantsDOS)) {
                shopExportVO.setWechatPayNumber(merchantsDOS.get(0).getMerchantsId());
            }

        }
        return shopExportVOS;
    }

    @Override
    public TeboShopVO selectShopById(Long id) {
        if (id == null) {
            throw new GlobalException("参数不能为空");
        }
        TeboShopDO shopDO = teboShopMapper.selectById(id);
        if (shopDO != null) {
            TeboShopVO vo = new TeboShopVO();
            BeanConvert.copy(shopDO, vo);
            // 用户信息填充
            fillUserInfo(vo);
            Integer serviceEarnestMoney = Objects.isNull(shopDO.getServiceEarnestMoney()) ? 0 : shopDO.getServiceEarnestMoney();
            Integer goodsEarnestMoney = Objects.isNull(shopDO.getGoodsEarnestMoney()) ? 0 : shopDO.getGoodsEarnestMoney();
            vo.setEarnestMoney(serviceEarnestMoney + goodsEarnestMoney);
            Integer count = remoteGoodsService.countPlatGoodsByShopId(id).getData();
            vo.setHasAuthorizedProduct(!Objects.isNull(count) && count > 0);
            vo.setAuthorizedProductCount(count);

            String accountBranchBank = vo.getTeboAccountBranchBank();
            if (StringUtils.isNotEmpty(accountBranchBank)) {
                //匹配到支行名字
                BranchSO branchSO = new BranchSO();
                branchSO.setBranchNo(accountBranchBank);
                List<TeboBranchBankDO> list = branchBankManger.list(branchSO);
                if (!CollectionUtils.isEmpty(list)) {
                    vo.setAccountBranchBankName(list.get(0).getBranchName());
                }
            }

            List<TeboShopPicDO> teboShopPicDOS = shopPicManger.picListByShopId(id);
            if (CollectionUtil.isNotEmpty(teboShopPicDOS)) {
                vo.setShopPicList(teboShopPicDOS.stream().map(TeboShopPicDO::getPicUrl).collect(Collectors.toList()));
            }
            String serviceScope = shopDO.getServiceScope();
            List<Integer> serviceScopeList = new ArrayList<>();
            if (StringUtils.isNotEmpty(serviceScope)) {
                String[] split = serviceScope.split(",");

                for (String s : split) {
                    ShopServiceScopeEnum anEnum = ShopServiceScopeEnum.getEnum(Integer.parseInt(s));
                    if (Objects.nonNull(anEnum)) {
                        int i = Integer.parseInt(s);
                        serviceScopeList.add(i);
                    }

                }
            }
            vo.setServiceScopeList(serviceScopeList);
            TeboMerchantShopVO teboMerchantShopVO = shopAdditionalMapper.selectLstShopDetailById(id);
            if (!ObjectUtils.isEmpty(teboMerchantShopVO)){
                vo.setLstBond(MoneyUtil.fenToYuan(teboMerchantShopVO.getLstBond()));
                vo.setLstStatus(teboMerchantShopVO.getLstStatus());
                vo.setLstReviewStatus(teboMerchantShopVO.getLstReviewStatus());
                vo.setLstReviewTime(teboMerchantShopVO.getLstReviewTime());
                vo.setLstShopName(teboMerchantShopVO.getLstShopName());
                vo.setLstShopCode(teboMerchantShopVO.getLstShopCode());
                vo.setLstBondTime(teboMerchantShopVO.getLstBondTime());
                vo.setServiceType(teboMerchantShopVO.getServiceType());
                if(ObjectUtil.isNotEmpty(teboMerchantShopVO.getLstBond()) && teboMerchantShopVO.getLstBond() > 0){
                    vo.setPayDeposit(true);
                }
                LstShopReviewRecordDO lstShopReviewRecordDO = lstShopReviewRecordMapper.getRecentReviewRecord(teboMerchantShopVO.getShopId());
                if (!ObjectUtils.isEmpty(lstShopReviewRecordDO)){
                    vo.setReason(lstShopReviewRecordDO.getReason());
                }
            }
            return vo;
        }
        throw new GlobalException("未查询到门店信息");
    }

    @Override
    public TeboShopVO selectByIdForPC(Long id) {
        TeboShopVO vo = selectShopById(id);
        // 判断是否位置校准过
        LambdaQueryWrapper<TeboShopPostionChangeLogDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeboShopPostionChangeLogDO::getShopId, id);
        queryWrapper.ne(TeboShopPostionChangeLogDO::getChangeAfter, "0,0");
        // 不存在位置较准记录，不返回门店经纬度
        if (teboShopPostionChangeLogMapper.selectCount(queryWrapper) < 1) {
            vo.setLatitude("");
            vo.setLongitude("");
        }
        return vo;
    }

    @Override
    public String selectInfoById(Long id) {
        return teboShopMapper.getDistanceByShopId(id);
    }

    @Override
    public TeboShop getShopInfoByCode(String shopCode) {
        if (StringUtils.isEmpty(shopCode)) {
            throw new ServiceException("门店编码不能为空");
        }
        TeboShopQueryDTO shopQueryDTO = new TeboShopQueryDTO();
        shopQueryDTO.setShopCode(shopCode);

        List<TeboShopDO> list = teboShopMapper.list(shopQueryDTO);
        if (CollectionUtil.isEmpty(list)){
            return null;
        }
        TeboShopDO shopDO = list.get(0);

        return BeanConvert.copy(shopDO,new TeboShop());
    }

    private void fillUserInfo(TeboShopVO vo) {
        TeboShopUserLinkDO teboShopUserLinkDO = teboShopUserLinkMapper.selectByShopId(vo.getId());
        if (Objects.nonNull(teboShopUserLinkDO)) {
            SysUser sysUser = sysUserMapper.selectUserById(teboShopUserLinkDO.getSysUserId());
            vo.setUserName(sysUser.getUserName());
            vo.setNickName(sysUser.getNickName());


        }

        TeboPartnerInfoDO teboPartnerInfoDO = teboPartnerInfoMapper.selectById(vo.getTenantId());
        if (Objects.nonNull(teboPartnerInfoDO)) {
            vo.setTenantName(teboPartnerInfoDO.getPartnerName());
            Integer serviceEarnestMoney = Objects.isNull(teboPartnerInfoDO.getServiceEarnestMoney()) ? 0 : teboPartnerInfoDO.getServiceEarnestMoney();
            Integer goodsEarnestMoney = Objects.isNull(teboPartnerInfoDO.getGoodsEarnestMoney()) ? 0 : teboPartnerInfoDO.getGoodsEarnestMoney();
            vo.setEarnestMoney(serviceEarnestMoney + goodsEarnestMoney);

        }

        String merchantsId = payShopMerchantsMapper.getMerchantsIdByShopId(Long.valueOf(vo.getId()));
        if (!ObjectUtils.isEmpty(merchantsId)) {
            vo.setWechatPayNumber(merchantsId);
        }

    }

    @Transactional
    @Override
    public Boolean addShop(TeboShopDTO shopDTO) throws Exception {
        if (shopDTO == null) {
            throw new GlobalException("参数不能为空");
        }
        Assert.notNull(shopDTO.getShopLevel(), "门店等级不能为空");
        Assert.notNull(shopDTO.getShopStarLevel(), "门店星级不能为空");
        Assert.notNull(shopDTO.getServiceScopeList(), "门店服务范围不能为空");
        Assert.notNull(shopDTO.getShopPic(), "门店主图不能为空");
        Assert.notEmpty(shopDTO.getShopPicList(), "门店轮播图不能为空");
//        Assert.notNull(shopDTO.getIntegrationCoefficient(),"门店积分系数不能为空");
//        Assert.notNull(shopDTO.getAccountBranchBank(), "开户支行不能为空");

        setIntegerByShopLevel(shopDTO);
        checkShopName(shopDTO);

        TeboShopDO teboShopDO = new TeboShopDO();

        BeanUtils.copyProperties(shopDTO, teboShopDO);
        TeboPartnerInfoDO partnerInfoDO = teboPartnerInfoMapper.selectById(shopDTO.getTenantId());
        teboShopDO.setTenantName(partnerInfoDO.getPartnerName());
//        teboShopDO.setServiceEarnestMoney(partnerInfoDO.getServiceEarnestMoney());
        teboShopDO.setHomeServiceFee(partnerInfoDO.getHomeServiceFee());
        // 设置区域
        setArea(teboShopDO);
        teboShopDO.setId(SnowFlakeUtil.nextId());
        teboShopDO.setShopCode(CodeGenerator.generateGysCode());
        if (shopDTO.getEstablishTime() != null) {
            teboShopDO.setEstablishTime(DateUtil.dateToLocalDateTime(shopDTO.getEstablishTime()));
        }
        teboShopDO.setDelFlag(0);
        teboShopDO.setCreateBy(SecurityUtils.getUsername());
        teboShopDO.setCreateBy(SecurityUtils.getUsername());
        teboShopDO.setCreateTime(LocalDateTime.now());
        teboShopDO.setUpdateTime(LocalDateTime.now());
        generateQrCode(teboShopDO);
        TeboMemberShopDO teboMemberShopDO = teboMemberShopManager.selectByPhoneNumber(teboShopDO.getPhoneNumber());
        if (ObjectUtil.isNotEmpty(teboMemberShopDO)){
            teboShopDO.setVip(1);
        }
        handleServiceScope(shopDTO, teboShopDO);
        teboShopDO.setLatitude("");
        teboShopDO.setLongitude("");
        int x = teboShopMapper.insert(teboShopDO);

        List<String> shopPicList = shopDTO.getShopPicList();
        if (CollectionUtil.isNotEmpty(shopPicList)) {
            for (String url : shopPicList) {
                TeboShopPicDO shopPicDO = new TeboShopPicDO();
                shopPicDO.setId(SnowFlakeUtil.nextId());
                shopPicDO.setShopId(teboShopDO.getId());
                shopPicDO.setPicType(TeboShopPicTypeEnum.MENTOU.getCode());
                shopPicDO.setPicUrl(url);
                shopPicManger.save(shopPicDO);
            }
        }


        if (x > 0) {

            Long userId = insertSysUser(shopDTO);
            // 生成门店用户关联关系记录
            TeboShopUserLinkDO shopUserLinkDO = new TeboShopUserLinkDO();
            shopUserLinkDO.setId(SnowFlakeUtil.nextId());
            shopUserLinkDO.setSysUserId(userId);
            shopUserLinkDO.setDelFlag(0);
            shopUserLinkDO.setShopId(teboShopDO.getId());
            if (teboShopUserLinkMapper.insert(shopUserLinkDO) > 0) {
                teboBaseService.initShopRole(userId, teboShopDO.getTenantId());
                // 维护微信商户平台收款账号
                teboBaseService.resetMerchantsId(teboShopDO.getTenantId(), teboShopDO.getId(), shopDTO.getWechatPayNumber());
                log.info("新建门店");
                /**
                 * 创建老板
                 */
                shopDTO.setId(teboShopDO.getId());
                teboAccountInsert(shopDTO, teboShopDO.getTenantId());
                pushWx(teboShopDO);
                return true;
            }

        }
        throw new GlobalException("创建门店信息失败");
    }

    private void teboAccountInsert(TeboShopDTO teboShopDTO, Long tenantId) {
        TeboAccountDO teboAccountDO = new TeboAccountDO();
        teboAccountDO.setId(SnowFlakeUtil.nextId());
        teboAccountDO.setTenantId(tenantId);
        teboAccountDO.setShopId(teboShopDTO.getId());
        teboAccountDO.setAccountName(teboShopDTO.getShopBossName());
        teboAccountDO.setUserName(teboShopDTO.getUserName());
        teboAccountDO.setPassword(SecurityUtils.encryptPassword(teboShopDTO.getPassword()));
        teboAccountDO.setPhoneNumber(teboShopDTO.getPhoneNumber());
        teboAccountDO.setType(1);
        TeboRoleDO teboRoleDO = new TeboRoleDO();
        /**
         * shopType 0:合作店 1:加盟店 2:体验中心
         */
        if (teboShopDTO.getShopType() == 0) {
            teboRoleDO = teboRoleMapper.getTeboAccountRole(TeboAccountRoleEnum.STANDARD_BOSS.getKey());
        } else {
            teboRoleDO = teboRoleMapper.getTeboAccountRole(TeboAccountRoleEnum.BOSS.getKey());
        }
        if (!ObjectUtils.isEmpty(teboRoleDO)) {
            teboAccountDO.setRoleId(teboRoleDO.getId());
        }
        teboAccountMapper.insert(teboAccountDO);
    }

    /**
     * 处理服务范围
     *
     * @param shopDTO
     * @param teboShopDO
     */
    private void handleServiceScope(TeboShopDTO shopDTO, TeboShopDO teboShopDO) {
        List<Integer> serviceScopeList = shopDTO.getServiceScopeList();
        if (CollectionUtil.isEmpty(serviceScopeList)) {
            return;
        }
        for (Integer scope : serviceScopeList) {
            ShopServiceScopeEnum anEnum = ShopServiceScopeEnum.getEnum(scope);
            if (Objects.isNull(anEnum)) {
                throw new ServiceException("服务范围不符合规范，请联系管理员");
            }
        }
        String join = CollectionUtil.join(serviceScopeList, ",");
        teboShopDO.setServiceScope(join);
    }


    /**
     * 判断同一个合伙人下有没有重名的店铺
     *
     * @param shopDTO
     */
    private void checkShopName(TeboShopDTO shopDTO) {
        TeboShopQueryDTO queryDTO = new TeboShopQueryDTO();
        queryDTO.setTenantId(shopDTO.getTenantId());
        List<TeboShopDO> list = teboShopMapper.list(queryDTO);
        if (CollectionUtil.isNotEmpty(list)) {
            List<TeboShopDO> collect = list.stream().filter(e ->
                    e.getShopName().equals(shopDTO.getShopName()) && !e.getId().equals(shopDTO.getId())
            ).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                throw new ServiceException("相通合伙人下存在重名的店铺");
            }
        }
    }

    /**
     * 推送微信
     *
     * @param teboShopDO
     */
    private void pushWx(TeboShopDO teboShopDO) {

        WxPushSO wxPushDTO = new WxPushSO();
        wxPushDTO.setAppid(appid);
        //张凡
        wxPushDTO.setUnionid("ozRHL6VQXGp7aJbI3bQmh28fnBbA");
        wxPushDTO.setTemplate_id(regist);
        HashMap<String, Object> pushData = new HashMap<>();
        JSONObject character_thing13 = new JSONObject();
        // 公司名称
        character_thing13.put("value", teboShopDO.getShopName());

        pushData.put("thing13", character_thing13);

        JSONObject character_time8 = new JSONObject();
        // 公司名称
        character_time8.put("value", cn.hutool.core.date.DateUtil.now());

        pushData.put("time8", character_time8);

        wxPushDTO.setData(pushData);
        wxPushDTO.setOfficialAccount(WeChatOfficialAccountEnum.teboTravel.getOfficialAccountName());

      //  wxPushService.sendMessage(wxPushDTO);
    }

    private void setArea(TeboShopDO teboShopDO) {
        String[] areaCodeArr = teboShopDO.getArea().split(",");
        teboShopDO.setAreaCode(areaCodeArr[2]);
        TeboAreaVO areaVO = remoteAreaService.selectAreaByCode(teboShopDO.getArea()).getData();
        teboShopDO.setAreaName(areaVO.getProvince() + "-" + areaVO.getCity() + "-" + areaVO.getDistrict());
    }

    /**
     * 生成取号二维码
     */
    public void generateQrCode(TeboShopDO teboShopDO) {
        String accessToken = redisService.getCacheObject("accessToken");
        if (StringUtils.isEmpty(accessToken)) {
            HashMap<String, Object> map = new HashMap<>(3);
            map.put("appid", appId); // 小程序appid
            map.put("secret", secret); // 小程序secret
            map.put("grant_type", "client_credential");
            //  发送Http的post请求
            String result = HttpUtil.post(wxPushConfig.getTokenUrl(), com.alibaba.fastjson.JSON.toJSONString(map));
            if (StringUtils.isEmpty(result)) {
                return;
            }
            JSONObject jsonObject = JSONObject.parseObject(result);
            accessToken = jsonObject.getString("access_token");
            Long expiresIn = jsonObject.getLong("expires_in");
            redisService.setCacheObject("accessToken", accessToken, expiresIn, TimeUnit.SECONDS);
        }
        String generateCodeUrl = AppletRequestUrl.GENERATE_QR_CODE + accessToken;
        JSONObject params = new JSONObject();
        params.put("page", qrCodePage);
        params.put("scene", String.valueOf(teboShopDO.getId()));
        params.put("env_version", "release");
        params.put("check_path", false);
        InputStream in = HttpTool.sendPostReturnInputStream(generateCodeUrl, JSON.toJSONString(params));
        String qrCodeUrl = fileService.upload(in, "tebo/shop/shop" + teboShopDO.getId() + ".png");
        teboShopDO.setShopQrCode(qrCodeUrl);
    }

    private Long insertSysUser(TeboShopDTO shopDTO) {

        SysUser user = new SysUser();
        Long id = SnowFlakeUtil.nextId();
        user.setUserId(id);
        user.setTenantId(shopDTO.getTenantId());
        user.setUserName(shopDTO.getUserName());
        user.setPhonenumber(shopDTO.getPhoneNumber());
        user.setNickName(shopDTO.getNickName());
        user.setPassword(SecurityUtils.encryptPassword(shopDTO.getPassword()));
        user.setDelFlag("0");
        user.setCreateBy(SecurityUtils.getUsername());
        user.setCreateBy(SecurityUtils.getUsername());
        user.setCreateTime(new Date());
        user.setUserType(1);
        user.setUpdateTime(new Date());
        if (!userService.checkUserNameUnique(user)) {
            throw new GlobalException("用户已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user)) {
            throw new GlobalException("手机号已存在");
        }
        if (sysUserMapper.insertUser(user) > 0) {
            return id;
        }
        throw new ServiceException("新建用户失败");
    }

    private Boolean hasBuildUserParams(TeboShopDTO shopDTO) {
        if (Objects.isNull(shopDTO.getTenantId())) {
            return false;
        }
        if (StringUtils.isEmpty(shopDTO.getUserName())) {
            return false;
        }
        if (StringUtils.isEmpty(shopDTO.getPhoneNumber())) {
            return false;
        }

        if (StringUtils.isEmpty(shopDTO.getNickName())) {
            return false;
        }

        if (StringUtils.isEmpty(shopDTO.getPassword())) {
            return false;
        }
        return true;
    }

    @Transactional
    @Override
    public Boolean updateShop(TeboShopDTO shopDTO) throws Exception {
        if (shopDTO == null || shopDTO.getId() == null) {
            throw new GlobalException("参数为空或id不存在");
        }
        // Assert.notNull(shopDTO.getServiceScopeList(), "门店服务范围不能为空");
        // Assert.notNull(shopDTO.getShopPic(), "门店主图不能为空");
        // Assert.notEmpty(shopDTO.getShopPicList(), "门店轮播图不能为空");


        checkShopName(shopDTO);

        setIntegerByShopLevel(shopDTO);


        TeboShopDO teboShopDO = new TeboShopDO();
        String lat = teboShopDO.getLatitude();
        String lon = teboShopDO.getLongitude();
        BeanConvert.copy(shopDTO, teboShopDO);
        if (teboShopDO.getArea() != null) {
            setArea(teboShopDO);
        }
        if (shopDTO.getEstablishTime() != null) {
            teboShopDO.setEstablishTime(DateUtil.dateToLocalDateTime(shopDTO.getEstablishTime()));
        }
        teboShopDO.setUpdateTime(LocalDateTime.now());
        // 维护微信商户平台收款账号
        teboBaseService.resetMerchantsId(teboShopDO.getTenantId(), teboShopDO.getId(), shopDTO.getWechatPayNumber());


        shopPicManger.removeByShopId(shopDTO.getId());
        List<String> shopPicList = shopDTO.getShopPicList();
        if (CollectionUtil.isNotEmpty(shopPicList)) {
            for (String url : shopPicList) {
                TeboShopPicDO shopPicDO = new TeboShopPicDO();
                shopPicDO.setId(SnowFlakeUtil.nextId());
                shopPicDO.setShopId(teboShopDO.getId());
                shopPicDO.setPicType(TeboShopPicTypeEnum.MENTOU.getCode());
                shopPicDO.setPicUrl(url);
                shopPicManger.save(shopPicDO);
            }
        }

        handleServiceScope(shopDTO, teboShopDO);

        /**
         * 同步到门店审核表
         */
        /*
        TeboShopReviewDTO teboShopReviewDTO = new TeboShopReviewDTO();
        BeanConvert.copy(shopDTO,teboShopReviewDTO);
        teboShopReviewDTO.setShopId(teboShopDO.getId());
        teboShopReviewService.perfectTeboShop(teboShopReviewDTO);

         */
        teboShopDO.setLatitude(lat);
        teboShopDO.setLongitude(lon);
        return teboShopMapper.updateById(teboShopDO) > 0;

//        Boolean hasBuildUserParams = hasBuildUserParams(shopDTO);
//
//        //当前门店没有建立过用户的话，新建用户
//        if (hasBuildUserParams) {
//            TeboShopUserLinkQueryDTO linkQueryDTO = new TeboShopUserLinkQueryDTO();
//            linkQueryDTO.setShopIdList(Collections.singletonList(shopDTO.getId()));
//            List<TeboShopUserLinkDO> userLinkDOS = teboShopUserLinkMapper.list(linkQueryDTO);
//            if (CollectionUtil.isNotEmpty(userLinkDOS)) {
//                Long userId = insertSysUser(shopDTO);
//                // 生成门店用户关联关系记录
//                TeboShopUserLinkDO shopUserLinkDO = new TeboShopUserLinkDO();
//                shopUserLinkDO.setId(SnowFlakeUtil.nextId());
//                shopUserLinkDO.setSysUserId(userId);
//                shopUserLinkDO.setDelFlag(0);
//                shopUserLinkDO.setShopId(teboShopDO.getId());
//                if (teboShopUserLinkMapper.insert(shopUserLinkDO) > 0) {
//                    teboBaseService.initShopRole(userId, teboShopDO.getTenantId());
//                    // 维护微信商户平台收款账号
//                    teboBaseService.resetMerchantsId(teboShopDO.getTenantId(), teboShopDO.getId(), shopDTO.getWechatPayNumber());
//                    log.info("新建门店");
//                    /**
//                     * 创建老板
//                     */
//                    shopDTO.setId(teboShopDO.getId());
//                    teboAccountInsert(shopDTO, teboShopDO.getTenantId());
//                    pushWx(teboShopDO);
//                    return true;
//                }
//            }
//
//
//        }
//
//        return true;
    }
    @Transactional
    @Override
    public Boolean reviewEditShop(TeboShopDTO shopDTO) throws Exception{
        updateShop(shopDTO);
        teboShopReviewService.submitForReview(shopDTO);
        return true;
    }

   public  String updateShopQrCode(TeboShopDTO shopDTO){
        TeboShopDO teboShopDO = new TeboShopDO();
        BeanConvert.copy(shopDTO,teboShopDO);
        generateQrCode(teboShopDO);
       teboShopMapper.updateById(teboShopDO);
       return teboShopDO.getShopQrCode();
   }

    private void setIntegerByShopLevel(TeboShopDTO shopDTO) {
        //积分由门店星级带出
        String shopStarLevel = shopDTO.getShopStarLevel();
        if (Objects.nonNull(shopStarLevel)) {
            ShopStarLevelIntegralEnum shopStarLevelIntegralByLevel = ShopStarLevelIntegralEnum.getShopStarLevelIntegralByLevel(shopStarLevel);
            if (Objects.isNull(shopStarLevelIntegralByLevel)) {
                throw new ServiceException("门店星级不规范");
            }
            BigDecimal integrationCoefficient = shopStarLevelIntegralByLevel.getIntegrationCoefficient();
            shopDTO.setIntegrationCoefficient(integrationCoefficient);
        }
    }


    @Transactional
    @Override
    public Boolean enableOrDisableShop(TeboShopStatusChangeDTO shopStatusChangeDTO) {
        if (shopStatusChangeDTO == null || shopStatusChangeDTO.getIdList() == null
                || shopStatusChangeDTO.getIdList().isEmpty() || shopStatusChangeDTO.getStatus() == null) {
            throw new GlobalException("参数有误");
        }
        boolean x = teboShopMapper.updateStatus(shopStatusChangeDTO) > 0;
        if (x) {
            TeboShopUserLinkQueryDTO queryDTO = new TeboShopUserLinkQueryDTO();
            queryDTO.setShopIdList(shopStatusChangeDTO.getIdList());
            List<Long> userIdList = teboShopUserLinkMapper.list(queryDTO)
                    .stream().map(TeboShopUserLinkDO::getSysUserId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(userIdList)) {
                String status = shopStatusChangeDTO.getStatus() == 1 ? "0" : "1";
                return sysUserMapper.changeStatusByUserIdList(userIdList, status) > 0;
            }
            return true;

        }
        return false;
    }

    @Transactional
    @Override
    public Boolean deleteShop(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new GlobalException("参数不能为空");
        }
        TeboGoodsQueryDTO teboGoodsQueryDTO = new TeboGoodsQueryDTO();
        teboGoodsQueryDTO.setShopIdList(ids);
        R<Integer> result = remoteGoodsService.countGoods(teboGoodsQueryDTO);
        if (result.getCode() != 200) {
            throw new GlobalException(result.getMsg());
        }
        if (result.getCode() == 200 && !ObjectUtils.isEmpty(result.getData()) && result.getData() > 0) {
            throw new GlobalException("门店下有商品,不能删除");
        }
        for (Long id : ids) {
            Integer integer = teboAccountManger.countByShopId(id);
            TeboShopDO teboShopDO = teboShopMapper.selectById(id);
            if (ObjectUtil.isNotEmpty(teboShopDO) && teboShopDO.getShopSource() ==3){
                throw new ServiceException(teboShopDO.getShopName()+"是云门店,不能删除");
            }
            if (Objects.nonNull(integer) && integer > 0) {
                throw new ServiceException("店铺下存在未删除的师傅，无法删除店铺");
            }
        }


        int x = teboShopMapper.batchDelete(ids);
        if (x > 0) {
            TeboShopUserLinkQueryDTO queryDTO = new TeboShopUserLinkQueryDTO();
            queryDTO.setShopIdList(ids);
            List<Long> userIdList = teboShopUserLinkMapper.list(queryDTO).stream()
                    .map(TeboShopUserLinkDO::getSysUserId).collect(Collectors.toList());
            if (!queryDTO.getShopIdList().isEmpty() || !queryDTO.getUserIdList().isEmpty()) {
                teboShopUserLinkMapper.deleteByParams(queryDTO);
            }
            if (!userIdList.isEmpty()) {
                sysUserMapper.deleteUserByIds(userIdList.toArray(new Long[0]));
            }
//            // // 根据门店id 查找师傅账号并删除(目前门店不能批量删除)
//            ids.stream().forEach(id -> {
//                teboAccountManger.batchDelAccountByShopId(id);
//            });
        }
        return true;
    }

    @Override
    public Boolean resetPassword(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new GlobalException("参数不能为空");
        }
        TeboShopUserLinkQueryDTO queryDTO = new TeboShopUserLinkQueryDTO();
        queryDTO.setShopIdList(ids);
        List<Long> sysIdList = teboShopUserLinkMapper.list(queryDTO).stream()
                .map(TeboShopUserLinkDO::getSysUserId).collect(Collectors.toList());
        List<SysUser> sysUsers = sysUserMapper.selectUserByIds(sysIdList);
        Map<Long, SysUser> sysUserMap = DataUtils.listToMap(sysUsers, SysUser::getUserId);
        for (Long sysId : sysIdList) {
            SysUser user = sysUserMap.get(sysId);
            if (Objects.isNull(user)) {
                continue;
            }
            SysUser update = new SysUser();
            update.setUserId(sysId);
            update.setPassword(SecurityUtils.encryptPassword(user.getUserName()));
            sysUserMapper.updateUser(update);
        }

        return true;
    }

    @Override
    public Boolean resetPassword(TeboShopDTO shopDTO) {

        Assert.notNull(shopDTO.getId(), "门店id不能为空");
        Assert.notNull(shopDTO.getPassword(), "密码不能为空");

        TeboShopUserLinkQueryDTO queryDTO = new TeboShopUserLinkQueryDTO();
        queryDTO.setShopIdList(Collections.singletonList(shopDTO.getId()));
        List<Long> sysIdList = teboShopUserLinkMapper.list(queryDTO).stream()
                .map(TeboShopUserLinkDO::getSysUserId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(sysIdList)) {
            throw new ServiceException("获取门店用户失败");
        }
        Long sysUserId = sysIdList.get(0);
        SysUser user = new SysUser();
        user.setUserId(sysUserId);
        user.setPassword(SecurityUtils.encryptPassword(shopDTO.getPassword()));
        sysUserMapper.updateUser(user);

        return true;
    }

    @Override
    public void brushComment(ShopBrushCommentDTO shopBrushCommentDTO) {
        Assert.notEmpty(shopBrushCommentDTO.getShopIds(), "未指定门店");
        Assert.notNull(shopBrushCommentDTO.getCount(), "未指定数量");
        ShopBrushCommentDTO.BrushCommentDTO brushCommentDTO = shopBrushCommentDTO.getBrushCommentDTO();
//        Assert.notNull(brushCommentDTO.getTotalComment(), "未指定整体服务");
//        Assert.notNull(brushCommentDTO.getServiceProcess(), "未指定服务过程");
//        Assert.notNull(brushCommentDTO.getProfessionalImage(), "未指定专业形象");
//        Assert.notNull(brushCommentDTO.getProfessionalCompetence(), "未指定专业能力");
//        Assert.notNull(brushCommentDTO.getServiceAttitude(), "未指定服务态度");

        List<TeboShopCommentDO> saveList = new ArrayList<>();

        for (Long shopId : shopBrushCommentDTO.getShopIds()) {
            for (int i = 0; i < shopBrushCommentDTO.getCount(); i++) {
                TeboShopCommentDO shopCommentDO = new TeboShopCommentDO();
                shopCommentDO.setId(SnowFlakeUtil.nextId());
                shopCommentDO.setShopId(shopId);
                shopCommentDO.setTotalComment(10);
                shopCommentDO.setServiceAttitude(10);
                shopCommentDO.setProfessionalCompetence(10);
                shopCommentDO.setProfessionalImage(10);
                shopCommentDO.setServiceProcess(10);
                saveList.add(shopCommentDO);


            }
        }


        DataUtils.splitHandle(saveList, splitList -> {
            shopCommentManger.saveBatch(splitList);
        }, 200);

    }

    @Override
    public void initPic() {
        List<TeboShopDO> shopDOS = new ArrayList<>();
        //找到每个门店的第一个照片
        List<TeboShopPicDO> list = shopPicManger.list();
        Map<Long, List<TeboShopPicDO>> listToGroup = DataUtils.listToGroup(list, TeboShopPicDO::getShopId);
        for (Long shopId : listToGroup.keySet()) {
            List<TeboShopPicDO> teboShopPicDOS = listToGroup.get(shopId).stream().sorted(Comparator.comparing(TeboShopPicDO::getCreateTime)).collect(Collectors.toList());
            TeboShopPicDO shopPicDO = teboShopPicDOS.get(0);
            TeboShopDO update = new TeboShopDO();
            update.setId(shopId);
            update.setShopPic(shopPicDO.getPicUrl());
            shopDOS.add(update);
        }
        DataUtils.splitHandle(shopDOS, (dos) -> {
            shopManager.updateBatchById(dos);
        }, 200);

    }


    @Override
    public void payEarnestMoney(PayEarnestMoneyDTO partnerInfoDTO) {
        String earnestMoney = partnerInfoDTO.getEarnestMoney();
        Assert.notNull(earnestMoney, "保证金不能为空");
        Assert.notNull(partnerInfoDTO.getShopId(), "门店不能为空");
        Integer earnestMoneyFen = MoneyUtil.yuanToFen(earnestMoney);
        TeboShopDO teboShopDO = teboShopMapper.selectById(partnerInfoDTO.getShopId());
        if (Objects.isNull(teboShopDO)) {
            throw new ServiceException("获取门店失败");
        }
        TeboShopDO update = new TeboShopDO();
        update.setId(partnerInfoDTO.getShopId());
        //经产品确认，目前采用直接覆盖而不是累加
        if (serverFee >= earnestMoneyFen) {
            update.setServiceEarnestMoney(earnestMoneyFen);
            update.setGoodsEarnestMoney(0);
        } else {
            //
            update.setServiceEarnestMoney(serverFee);
            update.setGoodsEarnestMoney(earnestMoneyFen - serverFee);
        }

        update.setPayEarnestTime(LocalDateTime.now());
        teboShopMapper.updateById(update);

    }

    @Override
    public Long selectShopIdByUserId(Long userId) {
        return teboShopUserLinkMapper.selectByUserId(userId);
    }

    @Override
    public Boolean refreshQrCode(TeboShopQueryDTO queryDTO) {
        List<TeboShopDO> shopDOList = teboShopMapper.list(queryDTO);
        shopDOList.forEach(shop -> {
            generateQrCode(shop);
            teboShopMapper.updateById(shop);
        });
        return null;
    }

    @Override
    public TeboShopVO getShopCode(Long shopId) {
        TeboShopVO teboShopVO = selectShopById(shopId);
        TeboShopAttachmentDO teboShopAttachmentDO = shopAttachmentManager.selectByShopId(shopId);
        if (ObjectUtil.isNotEmpty(teboShopAttachmentDO) && StringUtils.isNotEmpty(teboShopAttachmentDO.getStoreQrCode())) {
            teboShopVO.setStoreQrCode(teboShopAttachmentDO.getStoreQrCode());
        } else {
            /**
             * 生成门店二维码
             */
            String accessToken = redisService.getCacheObject("accessToken");
            if (StringUtils.isEmpty(accessToken)) {
                HashMap<String, Object> map = new HashMap<>(3);
                map.put("appid", appId); // 小程序appid
                map.put("secret", secret); // 小程序secret
                map.put("grant_type", "client_credential");
                //  发送Http的post请求
                String result = HttpUtil.post(wxPushConfig.getTokenUrl(), com.alibaba.fastjson.JSON.toJSONString(map));
                if (StringUtils.isEmpty(result)) {
                    return teboShopVO;
                }
                JSONObject jsonObject = JSONObject.parseObject(result);
                accessToken = jsonObject.getString("access_token");
                Long expiresIn = jsonObject.getLong("expires_in");
                redisService.setCacheObject("accessToken", accessToken, expiresIn, TimeUnit.SECONDS);
            }
            String generateCodeUrl = AppletRequestUrl.GENERATE_QR_CODE + accessToken;
            JSONObject params = new JSONObject();
            params.put("page", shopQrCodePage);
            params.put("scene", String.valueOf(shopId));
            params.put("env_version", "release");
            params.put("check_path", false);
            InputStream in = HttpTool.sendPostReturnInputStream(generateCodeUrl, JSON.toJSONString(params));
            String orderCodeUrl = fileService.upload(in, "tebo/shop/shopQrCodePage" + shopId + ".png");
            /**
             * 更新门店二维码
             */
            if (ObjectUtil.isEmpty(teboShopAttachmentDO)) {
                TeboShopAttachmentDO insert = new TeboShopAttachmentDO();
                insert.setShopId(shopId);
                insert.setStoreQrCode(orderCodeUrl);
                shopAttachmentManager.insert(insert);
            } else {
                teboShopAttachmentDO.setStoreQrCode(orderCodeUrl);
                shopAttachmentManager.update(teboShopAttachmentDO);
            }
            teboShopVO.setStoreQrCode(orderCodeUrl);
        }
        return teboShopVO;
    }

    @Override
    public TeboShopVO getByCid(String cid) {
        Assert.notEmpty(cid, "cid不能为空");
        TeboShopDO shopDO = teboShopMapper.getByCid(cid);
        if (Objects.isNull(shopDO)) {
            return null;
        }
        TeboShopVO vo = new TeboShopVO();
        BeanConvert.copy(shopDO, vo);
        // 用户信息填充
        fillUserInfo(vo);
        return vo;
    }

    @Override
    public void batchSetHomeServiceFee(HomeServiceFeeSetDTO homeServiceFeeSetDTO) {

        Assert.notNull(homeServiceFeeSetDTO.getIds(), "未指定店铺");
        Assert.notEmpty(homeServiceFeeSetDTO.getHomeServiceFee(), "未指定服务费");
        Integer yuanToFen = MoneyUtil.yuanToFen(homeServiceFeeSetDTO.getHomeServiceFee());
        List<TeboShopDO> updateList = new ArrayList<>();
        for (Long id : homeServiceFeeSetDTO.getIds()) {
            TeboShopDO update = new TeboShopDO();
            update.setId(id);
            update.setHomeServiceFee(yuanToFen);
            updateList.add(update);
        }
        for (TeboShopDO teboShopDO : updateList) {
            teboShopMapper.updateById(teboShopDO);
        }

    }

    @Override
    public List<String> shopPicList(Long shopId) {
        List<TeboShopPicDO> picDOS = shopPicManger.picListByShopId(shopId);
        if (CollectionUtils.isEmpty(picDOS)) {
            return new ArrayList<>();
        }
        return picDOS.stream().map(TeboShopPicDO::getPicUrl).collect(Collectors.toList());
    }

    @Override
    public AppletShopDetailVO appletDetail(NearbyShopQueryDTO nearbyShopQueryDTO) {

        Assert.notNull(nearbyShopQueryDTO.getId(), "未指定门店");
//        Assert.notNull(nearbyShopQueryDTO.getLatitude(), "未指定纬度");
//        Assert.notNull(nearbyShopQueryDTO.getLongitude(), "未指定经度");


        AppletShopDetailVO detailVO = new AppletShopDetailVO();
        TeboShopDO teboShopDO = teboShopMapper.selectById(nearbyShopQueryDTO.getId());
        if (Objects.isNull(teboShopDO)) {
            return detailVO;
        }
        BeanConvert.copy(teboShopDO, detailVO);
//        detailVO.setDistance(DistanceUtil.getDistance(detailVO.getLongitude(), detailVO.getLatitude(), nearbyShopQueryDTO.getLongitude(), nearbyShopQueryDTO.getLatitude()));
        String openTime = detailVO.getOpenTime();
//写死
        detailVO.setOpenDate("周一至周日");
        if (StringUtils.isNotEmpty(openTime)) {
            String[] split = openTime.split("-");
            if (split.length > 1) {
                String begin = split[0];
                String end = split[1];
                if (begin.contains(":") && end.contains(":")) {
                    String beginHm = begin.replace(":", "").trim();
                    String endHm = end.replace(":", "").trim();
                    //现在的时、分
                    String nowHm = cn.hutool.core.date.DateUtil.format(new Date(), "HHmm");

                    try {
                        int nowInt = Integer.parseInt(nowHm);
                        if (Integer.parseInt(beginHm) <= nowInt && Integer.parseInt(endHm) >= nowInt) {
                            detailVO.setBusinessStatus("营业中");
                        } else {
                            detailVO.setBusinessStatus("休息");
                        }
                    } catch (NumberFormatException e) {
                        log.error("营业时间转化异常", e);

                    }
                }


            }
        }
        TeboShopCommentQueryDTO shopCommentQueryDTO = new TeboShopCommentQueryDTO();
        shopCommentQueryDTO.setShopIds(Collections.singletonList(nearbyShopQueryDTO.getId()));
        List<TeboShopCommentDO> commentDOS = shopCommentManger.list(shopCommentQueryDTO);
        if (CollectionUtil.isNotEmpty(commentDOS)) {
            detailVO.setCommentCount(commentDOS.size());
        }
        TeboConsumerVO consumerVO = teboConsumerService.selectConsumerInfo(request);
        if (ObjectUtil.isEmpty(consumerVO)) {
            detailVO.setMark(2);
        } else {
            detailVO.setMark(teboConsumerMarkService.countMark(consumerVO.getId(), teboShopDO.getId()));
        }
        TeboShopVO shopResult = getShopCode(nearbyShopQueryDTO.getId());
        if (ObjectUtil.isNotEmpty(shopResult)){
            detailVO.setShopQrCode(shopResult.getStoreQrCode());
        }
        return detailVO;
    }

    @Override
    public void initLingShouTong() {
        log.info("零售通门店开始同步");
        //获取全量零售通门店
        List<ShopDto> lstShopVos = remoteLstShopService.getShopList().getData();
        if (CollectionUtil.isEmpty(lstShopVos)) {
            log.info("零售通门店为空");
            return;
        }
        List<ShopDto> enableLstShops = lstShopVos.stream().filter(e -> 1 == e.getApplyStatus() && "0".equals(e.getDeleteFlag()) && 1 == (e.getEnableStatus()))
                .collect(Collectors.toList());

        //不满足启用规则的零售通门店
        List<ShopDto> unEnableLstShopList = lstShopVos.stream().filter(e -> 1 != e.getApplyStatus() || "1".equals(e.getDeleteFlag()) || 0 == (e.getEnableStatus()))
                .collect(Collectors.toList());

        //全量泰博出行门店
        List<TeboShopDO> teboShopDOS = teboShopMapper.list(new TeboShopQueryDTO());
        //门店 营业执照分组
        Map<String, List<TeboShopDO>> shopByLicenseMap = DataUtils.listToGroup(teboShopDOS, TeboShopDO::getBusinessLicenseCode);
        //门店 零售通门店编码分组
//        teboShopDOS.stream().filter(e->StringUtils.isNotEmpty(e.getLstShopCode())).collect(Collectors.toList());
        List<TeboShopDO> hasLstShopDOS = teboShopDOS.stream().filter(e -> StringUtils.isNotEmpty(e.getLstShopCode())).collect(Collectors.toList());
        Map<String, List<TeboShopDO>> shopGroupByLstCode = DataUtils.listToGroup(hasLstShopDOS, TeboShopDO::getLstShopCode);

        List<TeboLstShopDO> addList = new ArrayList<>();
        List<TeboShopDO> updateList = new ArrayList<>();
        //需要关闭的门店
        List<TeboShopDO> unEnableUpdateList = new ArrayList<>();

        List<TeboPartnerInfoDO> partnerInfoDOList = teboPartnerInfoMapper.list(new TeboPartnerQueryDTO());
        Map<String, List<TeboPartnerInfoDO>> parentAreaGroup = DataUtils.listToGroup(partnerInfoDOList, TeboPartnerInfoDO::getAreaName);

        //和零售通有绑定的泰博出行门店

        Map<String, List<TeboShopDO>> lstCodeShopGroup = DataUtils.listToGroup(hasLstShopDOS, TeboShopDO::getLstShopCode);


        List<TeboProvinceVO> provinceVOS = areaService.queryList();
        Map<String, TeboProvinceVO> provinceVOMap = DataUtils.listToMap(provinceVOS, TeboProvinceVO::getName);

        //        更新零售通门店表
        List<TeboLstShopDO> allLstShop = lstShopManger.list(new TeboShopQueryDTO());
        Map<String, List<TeboLstShopDO>> lstShopDOGroup = DataUtils.listToGroup(allLstShop, TeboLstShopDO::getLstShopCode);
        List<TeboLstShopDO> updateLstShopList = new ArrayList<>();


        //匹配规则
        //泰博出行与零售通融合时门店匹配规则：以营业执照编码作为识别码，当同一营业执照下门店名称不同时，要以手机号和身份证做辅助验证，
        // 如果信息相同，则认定为泰博出行门店和零售通门店为同一门店，数据要进行合并，反之泰博出行需要新增一条门店数据。

        //先用不满足启用规则的筛选一遍，关闭掉匹配的门店
        for (ShopDto sltShopDto : unEnableLstShopList) {
            List<TeboShopDO> matedShops = lstCodeShopGroup.get(sltShopDto.getShopCode());
            if (CollectionUtil.isNotEmpty(matedShops)) {
                for (TeboShopDO matedShop : matedShops) {
                    TeboShopDO update = new TeboShopDO();
                    update.setId(matedShop.getId());
                    update.setUpdateTime(LocalDateTime.now());
                    update.setShopSource(ShopSourceEnum.TEBO.getCode());

                    unEnableUpdateList.add(update);
                }

            }
            //这些关闭的，把我们零售通表的数据也删掉
            List<TeboLstShopDO> lstShopDOS = lstShopDOGroup.get(sltShopDto.getShopCode());
            if (CollectionUtil.isNotEmpty(lstShopDOS)) {
                for (TeboLstShopDO lstShopDO : lstShopDOS) {
                    TeboLstShopDO update = new TeboLstShopDO();
                    update.setId(lstShopDO.getId());
                    update.setDelFlag(1);
                    update.setUpdateTime(LocalDateTime.now());
                    updateLstShopList.add(update);
                }

            }

        }


        for (ShopDto lstShopVo : enableLstShops) {

            TeboShopDO matedShop = getMatedShop(shopByLicenseMap, lstShopVo);
            if (Objects.nonNull(matedShop)) {
                TeboShopDO update = convertLstShopToTeboUpdate(lstShopVo, matedShop);
                updateList.add(update);
            } else {
                //身份证+电话+营业执照 没有满足的 且 门店表中零售通门店code也没有对应的
                // 再看看零售通门店表是不是也没
                List<TeboLstShopDO> lstShopDOS = lstShopDOGroup.get(lstShopVo.getShopCode());
                List<TeboShopDO> shopDOS = shopGroupByLstCode.get(lstShopVo.getShopCode());

                if (CollectionUtil.isEmpty(lstShopDOS) && CollectionUtil.isEmpty(shopDOS)) {
                    TeboLstShopDO save = convertLstShopToTeboSave(lstShopVo, parentAreaGroup, provinceVOMap);
                    addList.add(save);
                }

            }


        }
        DataUtils.splitHandle(addList, split -> {
            lstShopManger.saveBatch(split);

        }, 500);


        DataUtils.splitHandle(unEnableUpdateList, split -> {
            shopManager.updateBatchById(split);
        }, 300);


        DataUtils.splitHandle(updateList, split -> {
            shopManager.updateBatchById(split);
        }, 300);

        DataUtils.splitHandle(updateLstShopList, split -> {
            lstShopManger.updateBatchById(split);
        }, 300);

        log.info("零售通门店同步结束");
    }

    @Override
    public void updateShopAli(ShopAliUpdateSO shopAliUpdateSO) {
        Assert.notNull(shopAliUpdateSO.getAlipayAccount(), "支付宝账号不能为空");
        Assert.notNull(shopAliUpdateSO.getAlipayName(), "支付宝名称不能为空");
        Assert.notNull(shopAliUpdateSO.getShopId(), "门店不能为空");
        TeboShopDO shopDO = new TeboShopDO();
        shopDO.setId(shopAliUpdateSO.getShopId());
        shopDO.setAlipayName(shopAliUpdateSO.getAlipayName());
        shopDO.setAlipayAccount(shopAliUpdateSO.getAlipayAccount());
        shopManager.updateById(shopDO);

    }

    @Override
    @Transactional
    public void memberShop(MemberShopDTO memberShopDTO) {
        TeboShopQueryDTO shopQueryDTO = new TeboShopQueryDTO();
        shopQueryDTO.setPhoneNumber(memberShopDTO.getPhoneNumber());
        List<TeboShopDO> list = teboShopMapper.list(shopQueryDTO);
        TeboMemberShopDO teboMemberShopDO = new TeboMemberShopDO();
        teboMemberShopDO.setPhoneNumber(memberShopDTO.getPhoneNumber());
        teboMemberShopDO.setId(SnowFlakeUtil.nextId());
        if (CollectionUtil.isNotEmpty(list)){
            list.forEach(item ->{
                TeboShopDO teboShopDO = item;
                teboShopDO.setVip(1);
                teboShopMapper.updateById(teboShopDO);
            });
        }
        TeboMemberShopDO update = teboMemberShopManager.selectByPhoneNumber(memberShopDTO.getPhoneNumber());
        if (ObjectUtil.isNotEmpty(update)){
            update.setUpdateTime(LocalDateTime.now());
            teboMemberShopManager.update(update);
        }else {
            teboMemberShopManager.insert(teboMemberShopDO);
        }
    }

    @Override
    public void updateShopCoordinate(TeboShopDTO teboShopDTO) {
        // 经纬度为 "0,0"，直接忽略，不更新
        if (ObjectUtils.isEmpty(teboShopDTO.getLongitude()) || ObjectUtils.nullSafeEquals(teboShopDTO.getLongitude(),"0")){
            return;
        }
        if (ObjectUtils.isEmpty(teboShopDTO.getLatitude()) || ObjectUtils.nullSafeEquals(teboShopDTO.getLatitude(),"0")){
            return;
        }
        TeboShopDO teboShopDO = teboShopMapper.selectById(teboShopDTO.getId());
        teboShopDO.setId(teboShopDTO.getId());
        teboShopDO.setLatitude(teboShopDTO.getLatitude());
        teboShopDO.setLongitude(teboShopDTO.getLongitude());
        shopManager.updateTeboShop(teboShopDO);
        recordLog(teboShopDO.getId(),teboShopDO.getShopName(),teboShopDO.getLongitude()+","+teboShopDO.getLatitude(),teboShopDTO.getLongitude()+","+teboShopDTO.getLatitude());
    }


    private void recordLog(Long shopId,String shopName, String before,String after) {
        TeboShopPostionChangeLogDO teboShopPostionChangeLogDO = new TeboShopPostionChangeLogDO();
        teboShopPostionChangeLogDO.setId(SnowFlakeUtil.nextId());
        teboShopPostionChangeLogDO.setShopId(shopId);
        teboShopPostionChangeLogDO.setShopName(shopName);
        teboShopPostionChangeLogDO.setChangeBefore(before);
        teboShopPostionChangeLogDO.setChangeAfter(after);
        teboShopPostionChangeLogDO.setSource(1);
        teboShopPostionChangeLogDO.setCreateTime(LocalDateTime.now());
        teboShopPostionChangeLogDO.setUpdateTime(LocalDateTime.now());
        teboShopPostionChangeLogMapper.insert(teboShopPostionChangeLogDO);
//        log.info("记录门店坐标变更日志");
    }

    /**
     * 返回泰博出行对应门店
     *
     * @return
     */
    private TeboShopDO getMatedShop(Map<String, List<TeboShopDO>> shopByLicenseMap, ShopDto lstShopVo) {
        //执照
        String socialCreditCode = lstShopVo.getSocialCreditCode();
        if (StringUtils.isNotEmpty(socialCreditCode)) {
            List<TeboShopDO> shopDOS = new ArrayList<>();
            //兼容大小写
            List<TeboShopDO> upperCase = shopByLicenseMap.get(socialCreditCode.toUpperCase());
            List<TeboShopDO> lowCase = shopByLicenseMap.get(socialCreditCode.toLowerCase());
            if (CollectionUtil.isNotEmpty(upperCase)) {
                shopDOS.addAll(upperCase);
            }
            if (CollectionUtil.isNotEmpty(lowCase)) {
                shopDOS.addAll(lowCase);
            }
            if (CollectionUtil.isNotEmpty(shopDOS)) {
                //如果多条，先匹配已经有零售通code 的
                shopDOS = shopDOS.stream().sorted(Comparator.comparing(TeboShopDO::getLstShopCode, Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
                for (TeboShopDO shopDO : shopDOS) {
                    String phoneNumber = shopDO.getPhoneNumber();
                    String idCard = shopDO.getIdCard();
                    //手机号一致
                    if (StringUtils.equals(phoneNumber, lstShopVo.getKeeperTel()) && StringUtils.equals(idCard, lstShopVo.getIdCrd())) {


                        return shopDO;
                    }
                }
                //循环完了，没有匹配成功的话，新增
                return null;


            } else {
                //没有执照对应的门店
                return null;
            }

        } else {
            //营业执照字段为空
            return null;
        }
    }

    /**
     * 转化零售通门店
     *
     * @param lstShopVo
     * @return
     */
    private TeboLstShopDO convertLstShopToTeboSave(ShopDto lstShopVo, Map<String, List<TeboPartnerInfoDO>> parentAreaGroup, Map<String, TeboProvinceVO> provinceVOMap) {
        TeboLstShopDO save = new TeboLstShopDO();
        save.setId(SnowFlakeUtil.nextId());
//        save.setShopCode(CodeGenerator.generateGysCode());
        save.setShopName(lstShopVo.getShopName());
//        save.setShopType(ShopTypeEnum.LST.getCode());
        save.setShopBossName(lstShopVo.getKeeperName());
        save.setPhoneNumber(lstShopVo.getKeeperTel());
        save.setLstShopCode(lstShopVo.getShopCode());
        //默认关闭
        save.setStatus(0);


        save.setBusinessLicenseCode(lstShopVo.getSocialCreditCode());

        save.setBusinessLicense(lstShopVo.getBusinessLicense());
        String province = lstShopVo.getProvince();
        String city = lstShopVo.getCity();
        String area = lstShopVo.getArea();
        if (StringUtils.isNotEmpty(province)) {
            StringBuilder stringBuilder = new StringBuilder();
            StringBuilder stringBuilderCode = new StringBuilder();
            stringBuilder.append(province);
            TeboProvinceVO provinceVO = provinceVOMap.get(province);
            if (Objects.nonNull(provinceVO)) {
                buildArea(save, city, area, stringBuilderCode, provinceVO);
            }
            if (StringUtils.isNotEmpty(city)) {
                stringBuilder.append("-").append(city);

            }
            if (StringUtils.isNotEmpty(area)) {
                stringBuilder.append("-").append(area);
            }
            String areaName = stringBuilder.toString();
            save.setAreaName(areaName);
            save.setArea(stringBuilderCode.toString());
            //设值合伙人

            List<TeboPartnerInfoDO> partnerInfoDOList = parentAreaGroup.get(areaName);
            if (CollectionUtil.isNotEmpty(partnerInfoDOList) && parentAreaGroup.size() == 1) {
                TeboPartnerInfoDO teboPartnerInfoDO = partnerInfoDOList.get(0);
                save.setTenantId(teboPartnerInfoDO.getId());
                save.setTenantName(teboPartnerInfoDO.getPartnerName());
            }
        }

        save.setShopSource(ShopSourceEnum.LST.getCode());
        save.setAddress(lstShopVo.getReceiveAddress());
        save.setIdCardFront(lstShopVo.getIdCardFront());
        save.setIdCard(lstShopVo.getIdCrd());
        save.setShopPic(lstShopVo.getShopHeadPhoto());
        save.setSalesPhone(lstShopVo.getKeeperTel());
        save.setIdCardBack(lstShopVo.getIdCardFrontFan());
        save.setAlipayAccount(lstShopVo.getAlipayAccount());
        save.setAlipayName(lstShopVo.getAlipayName());

        save.setOpenWorkbench(false);
        save.setOpenMall(false);
        save.setOpenDoor(false);
        save.setOpenRescue(false);
        save.setShowNearbyShop(0);
        save.setTransformStatus(LstShopTransformStatusEnum.UN_TRANSFORM.getCode());
        save.setSupplierName(lstShopVo.getSupplierName());
        save.setLstShopLevel(lstShopVo.getShopLevel());

        save.setCreateTime(LocalDateTime.now());
        save.setUpdateTime(LocalDateTime.now());
        return save;
    }


    /**
     * 设值地区code
     *
     * @param save
     * @param city
     * @param area
     * @param stringBuilderCode
     * @param provinceVO
     */
    private void buildArea(TeboLstShopDO save, String city, String area, StringBuilder stringBuilderCode, TeboProvinceVO provinceVO) {
        stringBuilderCode.append(provinceVO.getCode());
        if (StringUtils.isNotEmpty(city)) {
            List<TeboCityVO> cityVOS = provinceVO.getChild();
            List<TeboCityVO> teboCityVOS = cityVOS.stream().filter(e -> e.getName().equals(city)).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(teboCityVOS)) {
                TeboCityVO cityVO = teboCityVOS.get(0);
                stringBuilderCode.append(",").append(cityVO.getCode());
                if (StringUtils.isNotEmpty(area)) {
                    List<TeboDistrictVO> districtVOList = cityVO.getChild().stream().filter(e -> e.getName().equals(area)).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(districtVOList)) {
                        TeboDistrictVO districtVO = districtVOList.get(0);
                        stringBuilderCode.append(",").append(districtVO.getCode());
                        save.setAreaCode(districtVO.getCode());
                    }

                }
            }

        }
    }


    /**
     * 转化零售通门店
     *
     * @param lstShopVo
     * @param shopDO
     * @return
     */
    private TeboShopDO convertLstShopToTeboUpdate(ShopDto lstShopVo, TeboShopDO shopDO) {
        TeboShopDO update = new TeboShopDO();
        update.setId(shopDO.getId());

        update.setShopSource(ShopSourceEnum.LST.getCode());
        if (StringUtils.isNotEmpty(shopDO.getAlipayAccount())) {
            update.setAlipayAccount(lstShopVo.getAlipayAccount());
        }
        if (StringUtils.isNotEmpty(shopDO.getAlipayName())) {
            update.setAlipayName(lstShopVo.getAlipayName());
        }
        update.setLstShopCode(lstShopVo.getShopCode());

        update.setUpdateTime(LocalDateTime.now());
        return update;
    }


    @Override
    public OpenApiShopVO getOpenApiShop(Long id) {
        if (Objects.isNull(id)) {
            throw new ServiceException("id不能为空");
        }
        TeboShopDO teboShopDO = teboShopMapper.selectById(id);
        if (Objects.isNull(teboShopDO)) {
            return null;
        }
        OpenApiShopVO openApiShopVO = new OpenApiShopVO();
        BeanConvert.copy(teboShopDO, openApiShopVO);

        TeboShopUserLinkDO teboShopUserLinkDO = teboShopUserLinkMapper.selectByShopId(id.toString());
        SysUser sysUser = sysUserMapper.selectUserById(teboShopUserLinkDO.getSysUserId());
        openApiShopVO.setUserName(sysUser.getUserName());
        openApiShopVO.setNickName(sysUser.getNickName());


        Long tenantId = teboShopDO.getTenantId();
        List<TeboPartnerInfoDO> partnerByIdList = teboPartnerInfoMapper.getPartnerByIdList(Collections.singletonList(tenantId));
        if (CollectionUtil.isNotEmpty(partnerByIdList)) {
            TeboPartnerInfoDO teboPartnerInfoDO = partnerByIdList.get(0);
            openApiShopVO.setPartnerId(teboPartnerInfoDO.getId());
            openApiShopVO.setPartnerCode(teboPartnerInfoDO.getPartnerCode());
            openApiShopVO.setPartnerAddress(teboPartnerInfoDO.getAddress());
            openApiShopVO.setPartnerAreaName(teboPartnerInfoDO.getAreaName());


            SysUser partnerSysUser = new SysUser();
            partnerSysUser.setTenantId(teboPartnerInfoDO.getId());
            partnerSysUser.setUserType(0);
            List<SysUser> sysUsers = sysUserMapper.selectUserList(partnerSysUser);
            if (CollectionUtil.isNotEmpty(sysUsers)) {
                openApiShopVO.setPartnerUserName(sysUsers.get(0).getUserName());
                openApiShopVO.setPartnerNickName(sysUsers.get(0).getNickName());
                openApiShopVO.setPartnerPhone(sysUsers.get(0).getPhonenumber());

            }

        }
        return openApiShopVO;
    }

    /**
     * 根据手机号查询门店
     * @param phoneNumber
     * @return
     */
    @Override
    public TeboShop getShopByPhoneNumber(String phoneNumber){
        TeboShopDO teboShopDO = teboShopMapper.getShopByPhoneNumber(phoneNumber);
        if (ObjectUtil.isEmpty(teboShopDO)){
            return null;
        }
        TeboShop teboShop = new TeboShop();
        BeanConvert.copy(teboShopDO,teboShop);
        return teboShop;
    }
    @Override
    public TeboShop selectByNewTenantId(Long newTenantId) {
        TeboShopDO teboShopDO = teboShopMapper.selectByNewTenantId(newTenantId);
        if (ObjectUtil.isEmpty(teboShopDO)) {
            return null;
        }
        TeboShop teboShop = new TeboShop();
        BeanConvert.copy(teboShopDO, teboShop);
        return teboShop;
    }

    @Override
    public Long getPartnerWalletId(String shopId) {
        return teboShopMapper.getPartnerWalletId(shopId);
    }

    @Override
    public ShopSapCodeVO getSapCode(Long shopId) {
        TeboShopDO teboShopDO = teboShopMapper.getSapCode(shopId);
        if (ObjectUtil.isEmpty(teboShopDO)){
            return null;
        }
       ShopSapCodeVO shopSapCodeVO = new ShopSapCodeVO();
       shopSapCodeVO.setShopSapCode(teboShopDO.getShopSapCode());
       SysUser sysUser = sysUserMapper.selectUserByTenantId(teboShopDO.getTenantId());
       if (ObjectUtil.isNotEmpty(sysUser)){
           shopSapCodeVO.setTenantSapCode(sysUser.getUserName());
       }
        return shopSapCodeVO;
    }
}
