package com.tebo.system.mapper;

import java.util.List;

import com.tebo.system.api.domain.dto.TeboSysUserDTO;
import org.apache.ibatis.annotations.Param;
import com.tebo.system.api.domain.SysUser;

/**
 * 用户表 数据层
 *
 * <AUTHOR>
 */
public interface SysUserMapper
{
    /**
     * 根据条件分页查询用户列表
     *
     * @param sysUser 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUserList(SysUser sysUser);

    /**
     * 根据条件分页查询已配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectAllocatedList(SysUser user);

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUnallocatedList(SysUser user);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByUserName(String userName);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUser selectUserById(Long userId);

    public List<SysUser> selectUserByIds(@Param("userIds") List<Long> userIds);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int insertUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUser(SysUser user);


    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    public int updateUserAvatar(@Param("userName") String userName, @Param("avatar") String avatar);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    public int resetUserPwd(@Param("userName") String userName, @Param("password") String password);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteUserById(Long userId);

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    public int deleteUserByIds(Long[] userIds);

    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    public SysUser checkUserNameUnique(String userName);

    /**
     * 校验手机号码是否唯一
     *
     * @param phonenumber 手机号码
     * @return 结果
     */
    public SysUser checkPhoneUnique(String phonenumber);

    /**
     * 校验email是否唯一
     *
     * @param email 用户邮箱
     * @return 结果
     */
    public SysUser checkEmailUnique(String email);

    /**
     * 删除合伙人下的所有用户
     */
    void deleteByPartnerId(@Param("tenantIdList") List<Long> tenantIdList);

    /**
     * 修改指定合伙人下用户状态
     */
    void changeStatusByPartnerIdList(@Param("tenantIdList") List<Long> ids, @Param("status") String status);

    int batchResetPasswordByIds(@Param("idList") List<Long> sysIdList, @Param("password") String password);

    int changeStatusByUserIdList(@Param("ids") List<Long> ids, @Param("status") String status);

    List<SysUser> selectUserByShopId(@Param("shopIds") List<Long> shopIds);

    /**
     * 零售通用户查询
     * @param userName
     * @return
     */
    SysUser selectLstUserByUserName(String userName);

    /**
     *
     */
    List<SysUser> getUserInfoByUserIdList(TeboSysUserDTO teboSysUserDTO);
    public SysUser selectUserByTenantId(Long tenantId);

    public List<String> getAllTenantSapCode();

    /**
     * 根据账号查询租户id
     * @param userName
     * @return
     */
    public List<Long> selectByUserName(@Param("userName") String userName);

}
