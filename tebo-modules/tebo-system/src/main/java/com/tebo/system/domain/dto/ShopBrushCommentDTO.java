package com.tebo.system.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.List;

/**
 * 门店
 */
@Data
public class ShopBrushCommentDTO {

    /**
     * 评价次数
     */
    private Long count;

    private List<Long> shopIds;

    /**
     * 评价内容
     */
    private BrushCommentDTO brushCommentDTO;

    @Data
    public static class BrushCommentDTO{

        /**
         * 整体服务0-10
         */
        private Integer totalComment;

        /**
         * 服务过程0-10
         */
        private Integer serviceProcess;

        /**
         * 专业形象评价0-10
         */
        private Integer professionalImage;

        /**
         * 专业能力评价0-10
         */
        private Integer professionalCompetence;

        /**
         * 服务态度评价0-10
         */
        private Integer serviceAttitude;

    }


}
