package com.tebo.system.service.consumer;

import com.tebo.system.domain.vo.shop.TeboShopListVO;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024/7/22 17:13
 * @Desc :
 */
public interface TeboConsumerMarkService {

    void mark(Long shopId, Long customerId);

    void cancelMark(Long shopId, Long customerId);
    List<TeboShopListVO> markList(Long customerId);

    Integer countMark(Long customerId, Long shopId);
}
