package com.tebo.system.domain.vo.merchant;

import cn.hutool.core.util.ObjectUtil;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.system.enums.TeboMerchantStatusEnum;
import com.tebo.system.enums.TeboShopReviewStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class TeboShopReviewVO implements Serializable {
    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 门店审核状态
     */
    private Integer reviewStatus;

    /**
     * 微信审核状态
     */
    private String wechatReviewStatus;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 微信认证时间
     */
    private LocalDateTime certificationTime ;

    /**
     * 申请单号
     */
    private String applymentId;

    /**
     * 商户名称
     */
    private String wxSellerName;

    /**
     * 商户号
     */
    private String subMchid;

    /**
     * 门店老板
     */
    private String shopBossName;

    /**
     * 联系电话
     */
    private String phoneNumber;

    /**
     * 当前合伙人
     */
    private String tenantName;

    /**
     * 营业执照编码
     */
    private String businessLicenseCode;

    /**
     * 二维码
     * @return
     */
    private String signUrl;

    /**
     * 失败原因
     * @return
     */
   private String failReason;

    public String getWechatReviewStatus(){
        if (StringUtils.isNotEmpty(wechatReviewStatus)){
            return TeboMerchantStatusEnum.getStatusMsgByCode(wechatReviewStatus);
        }
        return "";
    }

    public String getReviewStatus(){
        if (ObjectUtil.isNotEmpty(reviewStatus)){
            return TeboShopReviewStatusEnum.getEnum(reviewStatus);
        }
        return "";
    }

}