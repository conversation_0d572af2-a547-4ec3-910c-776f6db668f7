package com.tebo.system.task;

import cn.hutool.core.util.ObjectUtil;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.system.constant.TeboSystemCommonCacheConstant;
import com.tebo.system.entity.TeboShopReviewDO;
import com.tebo.system.enums.TeboMerchantStatusEnum;
import com.tebo.system.manager.merchant.TeboShopReviewManager;
import com.tebo.system.redisson.queue.RedisDelayQueueHandle;
import com.tebo.system.service.merchant.TeboShopReviewService;
import com.tebo.system.util.DistributedLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 微信进件申请商户号定期更新申请单状态
 */
@Slf4j
//@Component
public class TeboMerchantApplyStatusTask implements RedisDelayQueueHandle<Map> {
    @Resource
    private TeboShopReviewManager teboShopReviewManager;
    @Resource
    private TeboShopReviewService teboShopReviewService;

    @Resource
    private DistributedLock distributedLock;
    @Override
    public void execute(Map map) {
        if (!distributedLock.tryLock(TeboSystemCommonCacheConstant.getScheduledTaskPrefix("teboMerchantApplyStatusTask:execute"), 10, 30)) {
            return;
        }
        log.info("微信商户号超时延迟消息:{}", map);
        Object shopId = (Object)map.get("shopId");
        if (ObjectUtil.isEmpty(shopId)){
           return;
        }
        List<TeboShopReviewDO> list = new ArrayList();
        TeboShopReviewDO teboShopReviewDO = teboShopReviewManager.getTeboShopReview(Long.parseLong(shopId.toString()));
        list.add(teboShopReviewDO);
        teboShopReviewService.updateTeboShopWechatReviewStatus(list);
    }

    /**
     * 兜底方案
     * 每半小时执行一次，更新
     */
    @Scheduled(cron = "0 0/30 * * * *")
    public void mallOrderCancelTask(){
        if (!distributedLock.tryLock(TeboSystemCommonCacheConstant.getScheduledTaskPrefix("teboMerchantApplyStatusTask:mallOrderCancelTask"), 10, 30)) {
            return;
        }
        List<TeboShopReviewDO> reviewList = teboShopReviewManager.getTeboReviewList(null);
        List<TeboShopReviewDO> list = reviewList.stream().filter(item ->!TeboMerchantStatusEnum.FINISHED.getKey().equals(item.getWechatReviewStatus())).collect(Collectors.toList());
        list = list.stream().filter(item -> StringUtils.isNotEmpty(item.getApplymentId())).collect(Collectors.toList());
        teboShopReviewService.updateTeboShopWechatReviewStatus(list);
    }
}
