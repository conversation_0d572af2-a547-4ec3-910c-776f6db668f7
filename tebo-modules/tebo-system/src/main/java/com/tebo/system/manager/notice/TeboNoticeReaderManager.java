package com.tebo.system.manager.notice;

import com.tebo.system.domain.dto.notice.TeboNoticeReaderDTO;
import com.tebo.system.entity.TeboNoticeReaderDO;

public interface TeboNoticeReaderManager {

    /**
     * 查询是否已阅读
     */
    public TeboNoticeReaderDO getNoticeReader(TeboNoticeReaderDTO teboNoticeReaderDTO);

    /**
     * 插入公告已读
     */
    public int insert(TeboNoticeReaderDTO teboNoticeReaderDTO);
}