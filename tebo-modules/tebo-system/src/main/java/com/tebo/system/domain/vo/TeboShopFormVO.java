package com.tebo.system.domain.vo;

import lombok.Data;

/**
 * 门店数据 报表
 */
@Data
public class TeboShopFormVO {

    private Long id;

    private String shopName;


    /**
     * 门店老板姓名
     */
    private String shopBossName;

    /**
     * 门店类型 0:旗舰店 1:标准店 2:社区店
     */
    private Integer shopType;
    private String shopTypeName;

    /**
     * 合伙人
     */
    private String partnerName;

    /**
     * 联系电话
     */
    private String phoneNumber;

    /**
     * 门店性质
     */
    private String shopNatureName;

    /**
     * 平台授权商品数量
     */
    private Integer platGoodsCount = 0;
    /**
     * 合伙人授权商品数量
     */
    private Integer partnerGoodsCount = 0;


    /**
     * 自有商品数量
     */
    private Integer selfGoodsCount = 0;
}
