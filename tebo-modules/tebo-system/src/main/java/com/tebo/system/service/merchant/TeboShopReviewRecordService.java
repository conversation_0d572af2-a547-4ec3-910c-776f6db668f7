package com.tebo.system.service.merchant;

import com.tebo.system.domain.dto.merchant.TeboShopReviewRecordQueryDTO;
import com.tebo.system.domain.vo.merchant.TeboShopReviewRecordVO;

import java.util.List;

/**
 * 门店审核记录
 */
public interface TeboShopReviewRecordService {
    /**
     * 审核记录列表
     *
     */
    List<TeboShopReviewRecordVO> getTeboShopReviewRecordList(TeboShopReviewRecordQueryDTO queryDTO);

}