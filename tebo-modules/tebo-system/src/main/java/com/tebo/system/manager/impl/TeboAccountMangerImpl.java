package com.tebo.system.manager.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.tebo.system.domain.dto.account.TeboAccountQueryDTO;
import com.tebo.system.domain.dto.account.TeboAccountUpdateDTO;
import com.tebo.system.entity.TeboAccountDO;
import com.tebo.system.manager.TeboAccountManger;
import com.tebo.system.mapper.TeboAccountMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class TeboAccountMangerImpl implements TeboAccountManger {

    @Autowired
    private TeboAccountMapper teboAccountMapper;



    @Override
    public Boolean changeRescueStatus(TeboAccountUpdateDTO teboAccountUpdateDTO) {
        if (Objects.isNull(teboAccountUpdateDTO.getId())) {
            return false;
        }
        TeboAccountDO update = new TeboAccountDO();
        update.setId(teboAccountUpdateDTO.getId());
        update.setOpenRescue(teboAccountUpdateDTO.getOpen());

        //todo 构建维修师傅的缓存方法,对外部系统暴露缓存查询
        teboAccountMapper.updateById(update);
        return true;
    }

    @Override
    public int batchDelAccountByShopId(Long shopId) {
        return teboAccountMapper.batchDelAccountByShopId(shopId);
    }

    @Override
    public Integer countByShopId(Long shopId) {
        int i = teboAccountMapper.countByShopId(shopId);
        return i;
    }

    @Override
    public List<TeboAccountDO> getByIds(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)){
            return new ArrayList<>();
        }
        TeboAccountQueryDTO query  = new TeboAccountQueryDTO();
        query.setIds(ids);
        return teboAccountMapper.selectAccountList(query);
    }
    @Override
    public List<Long> getOpenRescueShopList(List<Long> ids){
        return teboAccountMapper.getOpenRescueShopList(ids);
    }
}
