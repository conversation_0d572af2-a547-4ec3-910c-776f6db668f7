package com.tebo.system.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.utils.excel.ExcelUtil;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.common.log.annotation.Log;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.system.api.domain.SysUser;
import com.tebo.system.domain.dto.*;
import com.tebo.system.domain.vo.TeboPartnerExportVO;
import com.tebo.system.domain.vo.TeboPartnerInfoListVO;
import com.tebo.system.domain.vo.TeboPartnerInfoVO;
import com.tebo.system.mapper.SysUserMapper;
import com.tebo.system.service.IPartnerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author：zhengmk
 * @Date 2023/12/11 8:56
 */
@RestController
@RequestMapping("/partner")
public class TeboPartnerController extends BaseController {

    @Autowired
    private IPartnerService partnerService;
    @Resource
    private SysUserMapper sysUserMapper;

    @PostMapping("/page")
    public TableDataInfo page(@RequestBody TeboPartnerQueryDTO queryDTO) {
        if(queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            throw new IllegalArgumentException("分页参数不能为空");
        }
        if (!ObjectUtils.isEmpty(queryDTO.getUserName())) {
            // 需要模糊查询合伙人账号
            List<Long> idList = sysUserMapper.selectByUserName(queryDTO.getUserName());
            queryDTO.setIdList(idList);
        }
        handleTime(queryDTO);
        Page page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<TeboPartnerInfoListVO> list = partnerService.list(queryDTO);
        return getDataTable(list,page);
    }


    /**
     * 处理时间范围
     * @param queryDTO
     */
    private static void handleTime(TeboPartnerQueryDTO queryDTO) {
        if (Objects.nonNull(queryDTO.getCreateTimeStart())) {
            queryDTO.setCreateTimeStartSecond(queryDTO.getCreateTimeStart().atStartOfDay());
        }
        if (Objects.nonNull(queryDTO.getCreateTimeEnd())) {
            queryDTO.setCreateTimeEndSecond(queryDTO.getCreateTimeEnd().plusDays(1).atStartOfDay());
        }
    }


    @PostMapping("/export")
    public void export(HttpServletResponse response, TeboPartnerQueryDTO queryDTO) {
        List<TeboPartnerInfoListVO> list = partnerService.list(queryDTO);
        ExcelUtil<TeboPartnerExportVO> util = new ExcelUtil<>(TeboPartnerExportVO.class);
        util.exportExcel(response, BeanConvert.copyList(list, TeboPartnerExportVO::new), "合伙人信息表");
    }

    @Log(operModel = 1, operDesc = "新增合伙人", operType = 1)
    @PostMapping("/addPartner")
    public R<Boolean> addPartner(@RequestBody TeboPartnerInfoDTO partnerInfoDTO) {
        return R.ok(partnerService.addPartner(partnerInfoDTO));
    }

    @Log(operModel = 1, operDesc = "编辑合伙人", operType = 1)
    @PostMapping("/updatePartner")
    public R<Boolean> updatePartner(@RequestBody TeboPartnerInfoDTO partnerInfoDTO) {
        return R.ok(partnerService.updatePartner(partnerInfoDTO));
    }

    /**
     * 保证金缴纳
     *
     * @return
     */
    @PostMapping("/payEarnestMoney")
    public R payEarnestMoney(@RequestBody PayEarnestMoneyDTO partnerInfoDTO) {
        partnerService.payEarnestMoney(partnerInfoDTO);
        return R.ok();
    }

    /**
     * 批量设置上门服务费
     *
     * @return
     */
    @PostMapping("/batchSetHomeServiceFee")
    public R batchSetHomeServiceFee(@RequestBody HomeServiceFeeSetDTO homeServiceFeeSetDTO) {
        partnerService.batchSetHomeServiceFee(homeServiceFeeSetDTO);
        return R.ok();
    }



    @GetMapping("/disable/{ids}")
    public R<Boolean> disable(@PathVariable("ids") List<Long> ids) {
        return R.ok(partnerService.disable(ids));
    }

    @GetMapping("/enable/{ids}")
    public R<Boolean> enable(@PathVariable("ids") List<Long> ids) {
        return R.ok(partnerService.enable(ids));
    }

    @GetMapping("/selectById/{id}")
    public R<TeboPartnerInfoVO> selectById(@PathVariable("id") Long id) {
        return R.ok(partnerService.getPartnerById(id));
    }


    @GetMapping("/deleteByIds/{ids}")
    public R<Boolean> deleteByIds(@PathVariable("ids") List<Long> ids) {
        return R.ok(partnerService.deleteByIds(ids));
    }

    @GetMapping("/selectCurrentUserPartner")
    public R<List<TeboPartnerInfoListVO>> selectByUserId() {
        return R.ok(partnerService.selectCurrentUserPartner());
    }

    @GetMapping("/new/selectCurrentUserPartner")
    public R<List<TeboPartnerInfoListVO>> selectCurrentUserPartner() {
        return R.ok(partnerService.selectCurrentUserPartnerNew());
    }

    @GetMapping("/resetPassword/{ids}")
    public R<Boolean> resetPassword(@PathVariable("ids") List<Long> ids) {
        return R.ok(partnerService.resetPassword(ids));
    }

    @PostMapping("/resetPassword")
    public R<Boolean> resetPassword(@RequestBody TeboPartnerInfoDTO partnerInfoDTO) {
        return R.ok(partnerService.resetPassword(partnerInfoDTO));
    }

    /**
     * 提交加盟信息
     * @param create
     * @return
     */
    @PostMapping("/addMsgInfo")
    public R<Boolean> addMsgInfo(@RequestBody TeboFranchiseeInfoCreateDTO create) {
        return R.ok(partnerService.addMsgInfo(create));
    }

    /**
     * 零售通根据地址信息获取合伙人信息(白名单）
     *
     * @param address
     * @return
     */
    @PostMapping("/lst/getPartnerByAddress")
    public R<TeboPartnerInfoVO> getPartnerByAddress(@RequestBody Map<String, Object> address) {
        return R.ok(partnerService.getPartnerByAddress(address));
    }


}
