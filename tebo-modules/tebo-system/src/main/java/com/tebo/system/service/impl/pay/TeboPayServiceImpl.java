package com.tebo.system.service.impl.pay;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tebo.common.core.enums.PaySourceEnum;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.HttpServletUtils;
import com.tebo.common.core.utils.SpringUtils;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.core.utils.excel.ExcelUtil;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.security.util.OnlineUserUtil;
import com.tebo.common.util.applet.AppletRequestUrl;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.http.HttpTool;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.mall.api.RemoteMallOrderWechatPayBackService;
import com.tebo.mall.api.domain.dto.TeboMallOrderDTO;
import com.tebo.mall.api.enums.MallOrderChannelEnum;
import com.tebo.rescue.api.RemoteWechatPayBackService;
import com.tebo.rescue.api.domain.dto.GiftPackOrderAfterPayDTO;
import com.tebo.rescue.api.domain.dto.ServiceOrderAfterPayDTO;
import com.tebo.system.api.domain.dto.PayOrderDTO;
import com.tebo.system.api.domain.dto.PayWechatRecordQueryDTO;
import com.tebo.system.api.domain.view.PayWechatRecordVO;
import com.tebo.system.api.domain.view.PayWechatVO;
import com.tebo.system.api.domain.view.WechatPrepayResponse;
import com.tebo.system.config.WxPayConfig;
import com.tebo.system.config.WxPushConfig;
import com.tebo.system.domain.vo.excel.PayWechatRecordExcelVO;
import com.tebo.system.entity.PayWechatRecordDO;
import com.tebo.system.entity.PayWechatRefundDO;
import com.tebo.system.mapper.PayWechatRecordMapper;
import com.tebo.system.mapper.PayWechatRefundMapper;
import com.tebo.system.mapper.pay.PayShopMerchantsMapper;
import com.tebo.system.redisson.queue.RedisDelayQueueUtil;
import com.tebo.system.service.IFileService;
import com.tebo.system.service.ITeboCommissionRecordService;
import com.tebo.system.service.pay.TeboPayService;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.partnerpayments.jsapi.JsapiServiceExtension;
import com.wechat.pay.java.service.partnerpayments.jsapi.model.*;
import com.wechat.pay.java.service.partnerpayments.jsapi.model.Amount;
import com.wechat.pay.java.service.profitsharing.ProfitsharingService;
import com.wechat.pay.java.service.profitsharing.model.CreateOrderReceiver;
import com.wechat.pay.java.service.profitsharing.model.CreateOrderRequest;
import com.wechat.pay.java.service.profitsharing.model.OrdersEntity;
import com.wechat.pay.java.service.profitsharing.model.UnfreezeOrderRequest;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.*;
import java.util.concurrent.TimeUnit;
import com.tebo.system.redisson.queue.RedisDelayQueueEnum;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR> ZhangFan
 * @date : 2023/12/23 9:06
 * @Desc :
 */
@Slf4j
@Service
public class TeboPayServiceImpl implements TeboPayService {
    @Resource
    private WxPayConfig wxPayConfig;
    @Resource
    private RSAAutoCertificateConfig rsaAutoCertificateConfig;
    @Resource
    private PayShopMerchantsMapper payShopMerchantsMapper;
    @Resource
    private PayWechatRecordMapper payWechatRecordMapper;
    @Resource
    private RemoteWechatPayBackService remoteWechatPayBackService;
    @Resource
    private ITeboCommissionRecordService commissionRecordService;
    @Resource
    private RemoteMallOrderWechatPayBackService remoteMallOrderWechatPayBackService;
    @Resource
    private RedisService redisService;

    @Value("${applet.appId}")
    private String appId;

    @Value("${applet.secret}")
    private String secret;

    @Resource
    private WxPushConfig wxPushConfig;

    @Value("${applet.qrcode.page}")
    private String qrCodePage;
    @Resource
    private IFileService fileService;
    @Resource
    private PayWechatRefundMapper payWechatRefundMapper;
    @Resource
    private RedisDelayQueueUtil redisDelayQueueUtil;
    /**
     * 云门店支付成功回到地址
     */
    @Value("${wx.pay.cloudShopPayNotifyUrl}")
    private String cloudShopPayNotifyUrl;

    @Override
    public WechatPrepayResponse wechatPrePay(PayOrderDTO order) {
        /**
         * 为了支付隔离环境，这里需要拼接环境标识
         */
        order.setOrderNo(wxPayConfig.getEnvPay() + order.getOrderNo());
        if (StringUtils.isEmpty(order.getOrderNo())) {
            throw new ServiceException("订单号不能为空");
        }
        // 工单支付需带上门店id
        if (order.getReceiver().equals(1) && ObjectUtils.isEmpty(order.getShopId())) {
            throw new ServiceException("门店id不能为空");
        }
        WechatPrepayResponse result = new WechatPrepayResponse();
        //请求微信支付相关配置
        JsapiServiceExtension service = new JsapiServiceExtension.Builder()
                .config(rsaAutoCertificateConfig)
                .signType("RSA") // 不填默认为RSA
                .build();
        try {
            PrepayRequest request = buildPrepayRequest(order);
            // 会员权益礼包收款到 收款方
            if (order.getReceiver() == 3) {
                request.setSubMchid("1688458872");
            }
            log.info("请求预支付下单，请求参数：{}", JSONObject.toJSONString(request));
            // 调用预下单接口
            PrepayWithRequestPaymentResponse response = service.prepayWithRequestPayment(request, PaySourceEnum.getPaySourceValue(order.getSource()));
            log.info("订单【{}】发起预支付成功，返回信息：{}", order.getOrderNo(), response);
            BeanConvert.copy(response, result);
            PayWechatRecordDO recordDO = payWechatRecordMapper.selectOneByOrderNo(order.getOrderNo().substring(4));
            //不存在则插入，存在则更新时间
            if (ObjectUtils.isEmpty(recordDO)){
                //微信支付记录
                PayWechatRecordDO payWechatRecordDO = new PayWechatRecordDO();
                BeanConvert.copy(order, payWechatRecordDO);
                payWechatRecordDO.setOrderNo(order.getOrderNo().substring(4));
                payWechatRecordDO.setPrePayId(response.getPackageVal().replace("prepay_id=",""));
                payWechatRecordDO.setTenantId(order.getTenantId());
                payWechatRecordDO.setChannel(order.getChannel());
                if(order.getChannel() == MallOrderChannelEnum.WARRANTY_EXTENSION.getCode()){
                    payWechatRecordDO.setChannel(MallOrderChannelEnum.NORMAL.getCode());
                }
                payWechatRecordMapper.insert(payWechatRecordDO);
            }else {
                payWechatRecordMapper.updateById(recordDO);
            }
            if (!ObjectUtils.isEmpty(order.getUniqueId())) {
                result.setUniqueId(order.getUniqueId());
            }
            return result;
        } catch (Exception e) { // 服务返回成功，返回体类型不合法，或者解析返回体失败
            log.error("微信下单发送HTTP请求失败", e);
            throw new ServiceException("商户收款账号未配置!");
        }
    }

    /**
     * 微信支付回调
     * @param request
     * @return
     */
    @Override
    public String payNotify(HttpServletRequest request) {
        log.info("payNotify 微信支付回调开始");
        // 请求头Wechatpay-Signature
        String signature = request.getHeader("Wechatpay-Signature");
        // 请求头Wechatpay-nonce
        String nonce = request.getHeader("Wechatpay-Nonce");
        // 请求头Wechatpay-Timestamp
        String timestamp = request.getHeader("Wechatpay-Timestamp");
        // 微信支付证书序列号
        String serial = request.getHeader("Wechatpay-Serial");
        // 签名方式
        String signType = request.getHeader("Wechatpay-Signature-Type");
        // 构造 RequestParam
        RequestParam requestParam = null;
        try {
            requestParam = new RequestParam.Builder()
                    .serialNumber(serial)
                    .nonce(nonce)
                    .signature(signature)
                    .timestamp(timestamp)
                    .signType(signType)
                    .body(HttpServletUtils.getRequestBody(request))
                    .build();
        } catch (IOException e) {
            log.error("支付回调失败",e);
            throw new RuntimeException(e);
        }
        log.info("payNotify 支付回调");
        // 初始化 NotificationParser
        NotificationParser parser = new NotificationParser(rsaAutoCertificateConfig);
        // 以支付通知回调为例，验签、解密并转换成 Transaction
        Transaction transaction = parser.parse(requestParam, Transaction.class);
        log.info("验签成功！-支付回调结果：{},参数: {}", transaction.toString(),JSONObject.toJSONString(requestParam));
        Map<String, String> returnMap = new HashMap<>(2);
        returnMap.put("code", "FAIL");
        returnMap.put("message", "失败");
        // 截掉环境前缀，获取真实订单号
        String orderNo = transaction.getOutTradeNo().substring(4);
        if (Transaction.TradeStateEnum.SUCCESS != transaction.getTradeState()) {
            log.info("内部订单号【{}】,微信支付订单号【{}】支付未成功", orderNo, transaction.getTransactionId());
            payWechatRecordMapper.updatePayStatus(orderNo,2);
            return JSONObject.toJSONString(returnMap);
        }
        // 回调业务
        returnMap.put("code", "SUCCESS");
        returnMap.put("message", "成功");
        Boolean alreadyUpdatePaymentStatus =  updatePaymentSuccessStatus(orderNo,transaction.getTransactionId());
        if (alreadyUpdatePaymentStatus){
            return JSONObject.toJSONString(returnMap);
        }
        PayWechatRecordDO payWechatRecordDO = payWechatRecordMapper.selectOneByOrderNo(orderNo);
        log.info("payNotify payWechatRecordDO {}", payWechatRecordDO);
        Integer type = payWechatRecordDO.getBusinessType();
        if (type == 1) {
            log.info("payNotify type{}", type);
            remoteWechatPayBackService.payOrderAfterNotify(new ServiceOrderAfterPayDTO(orderNo));
        }
        if (type == 2) {
            log.info("payNotify type{}", type);
            remoteWechatPayBackService.payGiftPackAfterNotify(new GiftPackOrderAfterPayDTO(orderNo));
        }
        /**
         * 保费
         */
        if (type == 9) {
            log.info("payNotify type{}", type);
            remoteMallOrderWechatPayBackService.payWarrantyExtensionOrderAfterNotify(new TeboMallOrderDTO(orderNo));
        }
        /**
         * 商城订单
         */
        if (type == 10) {
            log.info("payNotify type{}", type);
            remoteMallOrderWechatPayBackService.payMallOrderAfterNotify(new TeboMallOrderDTO(orderNo));
        }

        if (type == 20) {
            log.info("payNotify type{}", type);
            remoteWechatPayBackService.payRescueFeeAfterNotify(new ServiceOrderAfterPayDTO(orderNo));
        }
        // 云门店支付回调
        if (type == 101) {
            log.info("payNotify type{}", type);
            payCloudShopAfterNotify(new ServiceOrderAfterPayDTO(orderNo));
        }
        log.info("payNotify success");
        return JSONObject.toJSONString(returnMap);
    }
    /**
     * 根据查询条件 查询微信支付流水记录
     * @param
     * @return
     */
    @Override
    public List<PayWechatRecordVO> getPayWechatRecordList(PayWechatRecordQueryDTO wechatRecordQuery) {
        log.info("payWechatRecordQueryDTO:{}",JSONObject.toJSONString(wechatRecordQuery));
        List<PayWechatRecordVO> list = payWechatRecordMapper.getPayWechatRecordList(wechatRecordQuery);
        if (CollectionUtil.isEmpty(list)){
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public void payWechatRecordExport(HttpServletResponse response,PayWechatRecordQueryDTO payWechatRecordQueryDTO) {
        List<PayWechatRecordVO> payWechatRecordList = getPayWechatRecordList(payWechatRecordQueryDTO);
        List<PayWechatRecordExcelVO> excelVOS = BeanConvert.copyList(payWechatRecordList, PayWechatRecordExcelVO::new);
        for (PayWechatRecordExcelVO excelVO : excelVOS) {
            Integer channel = excelVO.getChannel();
            if (Objects.nonNull(channel)){
                MallOrderChannelEnum mallOrderStatus = MallOrderChannelEnum.getMallOrderStatus(channel);
                if (Objects.nonNull(mallOrderStatus)){
                    excelVO.setChannelName(mallOrderStatus.getMsg());
                }
            }
        }
        ExcelUtil.exportExcelToResponse(response, excelVOS, PayWechatRecordExcelVO.class, "财务流水");
    }

    @Override
    public PayWechatVO getPayWechatRecord(PayWechatRecordQueryDTO payWechatRecordQueryDTO) {
        return payWechatRecordMapper.getPayWechatRecord(payWechatRecordQueryDTO);
    }

    @Override
    public Boolean updatePaymentSuccessStatus(String orderNo, String transactionId) {
        log.info("updatePaymentSuccessStatus,orderNo:{}",orderNo);
        PayWechatRecordDO payWechatRecordDO = payWechatRecordMapper.selectOneByOrderNo(orderNo);
        if (!ObjectUtils.isEmpty(payWechatRecordDO) && 1 == payWechatRecordDO.getPayStatus()){
            log.info("已支付成功，不需要再次支付，订单信息：{}",JSONObject.toJSONString(payWechatRecordDO));
            return true;
        }
        payWechatRecordDO.setPayStatus(1);
        payWechatRecordDO.setTransactionId(transactionId);
        payWechatRecordMapper.updateById(payWechatRecordDO);
        /**
         * 插入分佣表
         */
        PayOrderDTO payOrderDTO = new PayOrderDTO();
        BeanConvert.copy(payWechatRecordDO,payOrderDTO);
        commissionRecordService.insertTeboCommissionRecord(payOrderDTO);
        return false;
    }

    private PrepayRequest buildPrepayRequest(PayOrderDTO order) {
        PrepayRequest request = new PrepayRequest();
        request.setSpAppid(PaySourceEnum.getPaySourceValue(order.getSource()));
        request.setSpMchid(wxPayConfig.getMerchantId());
        request.setDescription(order.getDescription());
        request.setOutTradeNo(order.getOrderNo());
        request.setNotifyUrl(wxPayConfig.getPayNotifyUrl());
        // 原有逻辑，判断到收款账户
        if (order.getChannel() == MallOrderChannelEnum.NORMAL.getCode() || order.getChannel() == MallOrderChannelEnum.WARRANTY_EXTENSION.getCode() || ObjectUtils.isEmpty(order.getChannel())) {
            // 子商户id,通过门店id映射子商户id
            if (order.getReceiver() == 1) {
                request.setSubMchid(getMerchantsIdByShopId(order.getShopId()));
            }
            if (order.getReceiver()== 2) {
                request.setSubMchid(getMerchantsIdByTenantId(order.getTenantId()));
            }
        }
        // 如果指定了特定渠道，指定到收款账户
        if (order.getChannel() == MallOrderChannelEnum.WARCRAFT.getCode()) {
            // 收款到驴充充账户
            request.setSubMchid(MallOrderChannelEnum.WARCRAFT.getSubMchid());
        }
        if (order.getBusinessType() == 9) {
            // 收款到上海誉好数据技术有限公司上饶分公司账户
            request.setSubMchid(MallOrderChannelEnum.WARRANTY_EXTENSION.getSubMchid());
        }
        if (ObjectUtils.isEmpty(request.getSubMchid())) {
            throw new ServiceException("收款账号未配置，无法支付!");
        }
        Amount amount = new Amount();
        amount.setTotal(order.getAmount());
        request.setAmount(amount);
        // 用户信息
        Payer payer = new Payer();
        payer.setSpOpenid(order.getOpenId());
        request.setPayer(payer);
//        // 分账设置
//        SettleInfo settleInfo = new SettleInfo();
//        settleInfo.setProfitSharing(true);
//        request.setSettleInfo(settleInfo);
        return request;
    }

    private String getMerchantsIdByShopId(Long shopId) {
        return payShopMerchantsMapper.getMerchantsIdByShopId(shopId);
    }
    private String getMerchantsIdByTenantId(Long tenantId) {
        return payShopMerchantsMapper.getMerchantsIdByTenantId(tenantId);
    }

    public OrdersEntity profitSharingCreate(String outOrderNo) {
        ProfitsharingService service = new ProfitsharingService.Builder()
                .config(rsaAutoCertificateConfig)
                .build();
//        // 测试分账
//        CreateOrderRequest request = new CreateOrderRequest();
//        // 根据供应商去匹配
//        request.setSubMchid("**********");
//        // 泰博出行
//        request.setAppid("wx42e36eb3f3dd94fe");
        // 天能云商城
//        request.setAppid("wx62366beef2f4025b");
        PayWechatRecordDO recordDO = payWechatRecordMapper.selectOneByOrderNo(outOrderNo);
        if (ObjectUtils.isEmpty(recordDO)) {
            throw new RuntimeException("订单不存在");
        }
//        return null;
//        request.setTransactionId(recordDO.getTransactionId());
//        request.setOutOrderNo(recordDO.getOrderNo());
//        List<CreateOrderReceiver> receivers = new ArrayList<>();
//        CreateOrderReceiver receiver = new CreateOrderReceiver();
//        receiver.setType("PERSONAL_OPENID");
//        // 韦乐乐
//        receiver.setAccount("otOeh6-nxzVIrb8ubw7LG5kq8CeA");
//        // 张凡
////        receiver.setAccount("otOeh64SkSJi8dJYhaqM5GOGLjr8");
//        receiver.setAmount(1L);
//        receiver.setDescription("返利");
//        receivers.add(receiver);
//        request.setReceivers(receivers);
//        request.setUnfreezeUnsplit(true);
//        return service.createOrder(request);
        UnfreezeOrderRequest unfreezeOrderRequest = new UnfreezeOrderRequest();
        unfreezeOrderRequest.setTransactionId(recordDO.getTransactionId());
        unfreezeOrderRequest.setOutOrderNo(recordDO.getOrderNo());
        unfreezeOrderRequest.setDescription("解冻");
        // 根据供应商去匹配
        String subMchid = payWechatRecordMapper.getSupplierSubMchid(String.valueOf(recordDO.getTenantId()));
//        unfreezeOrderRequest.setSubMchid("**********");
        unfreezeOrderRequest.setSubMchid(subMchid);
        return service.unfreezeOrder(unfreezeOrderRequest);
    }

    /**
     * 生成订单二维码
     */
    public String generateOrderCode(Long orderId,Integer source) {
        String accessToken = redisService.getCacheObject("accessToken");
        if (StringUtils.isEmpty(accessToken)) {
            HashMap<String, Object> map = new HashMap<>(3);
            map.put("appid", appId); // 小程序appid
            map.put("secret", secret); // 小程序secret
            map.put("grant_type", "client_credential");
            //  发送Http的post请求
            String result = HttpUtil.post(wxPushConfig.getTokenUrl(), com.alibaba.fastjson.JSON.toJSONString(map));
            if (StringUtils.isEmpty(result)) {
                return "";
            }
            JSONObject jsonObject = JSONObject.parseObject(result);
            accessToken = jsonObject.getString("access_token");
            Long expiresIn = jsonObject.getLong("expires_in");
            redisService.setCacheObject("accessToken", accessToken, expiresIn, TimeUnit.SECONDS);
        }
        String generateCodeUrl = AppletRequestUrl.GENERATE_QR_CODE + accessToken;
        JSONObject params = new JSONObject();
        params.put("page", "pages/emptyPay/index");
        params.put("scene", String.valueOf(orderId)+"_"+source);
        params.put("env_version", "release");
        params.put("check_path", false);
      //  params.put("source", source);
        InputStream in = HttpTool.sendPostReturnInputStream(generateCodeUrl, JSON.toJSONString(params));
        String orderCodeUrl = fileService.upload(in, "tebo/rescue/order" + orderId + ".png");
        return orderCodeUrl;
    }

    /**
     *
     * @param orderId
     * @return
     */
    public String generateGroupPurchasePayOrderCode(@PathVariable("orderId") Long orderId){
        String accessToken = redisService.getCacheObject("accessToken");
        if (StringUtils.isEmpty(accessToken)) {
            HashMap<String, Object> map = new HashMap<>(3);
            map.put("appid", appId); // 小程序appid
            map.put("secret", secret); // 小程序secret
            map.put("grant_type", "client_credential");
            //  发送Http的post请求
            String result = HttpUtil.post(wxPushConfig.getTokenUrl(), com.alibaba.fastjson.JSON.toJSONString(map));
            if (StringUtils.isEmpty(result)) {
                return "";
            }
            JSONObject jsonObject = JSONObject.parseObject(result);
            accessToken = jsonObject.getString("access_token");
            Long expiresIn = jsonObject.getLong("expires_in");
            redisService.setCacheObject("accessToken", accessToken, expiresIn, TimeUnit.SECONDS);
        }
        String generateCodeUrl = AppletRequestUrl.GENERATE_QR_CODE + accessToken;
        JSONObject params = new JSONObject();
        params.put("page", "pages/emptyPay/index");
        params.put("scene", String.valueOf(orderId));
        params.put("env_version", "release");
        params.put("check_path", false);
        //  params.put("source", source);
        InputStream in = HttpTool.sendPostReturnInputStream(generateCodeUrl, JSON.toJSONString(params));
        String orderCodeUrl = fileService.upload(in, "tebo/rescue/group/payOrder" + orderId + ".png");
        return orderCodeUrl;
    }

    @Override
    public String generateMerchantCode(String orderNo) {
        String accessToken = redisService.getCacheObject("accessToken");
        if (StringUtils.isEmpty(accessToken)) {
            HashMap<String, Object> map = new HashMap<>(3);
            map.put("appid", appId); // 小程序appid
            map.put("secret", secret); // 小程序secret
            map.put("grant_type", "client_credential");
            //  发送Http的post请求
            String result = HttpUtil.post(wxPushConfig.getTokenUrl(), com.alibaba.fastjson.JSON.toJSONString(map));
            if (StringUtils.isEmpty(result)) {
                return "";
            }
            JSONObject jsonObject = JSONObject.parseObject(result);
            accessToken = jsonObject.getString("access_token");
            Long expiresIn = jsonObject.getLong("expires_in");
            redisService.setCacheObject("accessToken", accessToken, expiresIn, TimeUnit.SECONDS);
        }
        String generateCodeUrl = AppletRequestUrl.GENERATE_QR_CODE + accessToken;
        JSONObject params = new JSONObject();
        params.put("page", "pages/emptyPay/index");
        params.put("scene", String.valueOf(orderNo));
        params.put("env_version", "release");
        params.put("check_path", false);
        //  params.put("source", source);
        InputStream in = HttpTool.sendPostReturnInputStream(generateCodeUrl, JSON.toJSONString(params));
        String orderCodeUrl = fileService.upload(in, "tebo/merchant/payOrder" + orderNo + ".png");
        return orderCodeUrl;
    }

    /**
     * 云店预支付
     * @param order
     * @return
     */
    @Override
    public WechatPrepayResponse wechatPrePayForCloudShop(PayOrderDTO order) {
        /**
         * 为了支付隔离环境，这里需要拼接环境标识
         */
        order.setOrderNo(wxPayConfig.getEnvPay() + order.getOrderNo());
        if (StringUtils.isEmpty(order.getOrderNo())) {
            throw new ServiceException("订单号不能为空");
        }
        WechatPrepayResponse result = new WechatPrepayResponse();
        //请求微信支付相关配置
        JsapiServiceExtension service = new JsapiServiceExtension.Builder()
                .config(rsaAutoCertificateConfig)
                .signType("RSA") // 不填默认为RSA
                .build();
        try {
            PrepayRequest request = buildPrepayRequestForCloudShop(order);
            String supplierCode = order.getSupplierCode();
            String subMchid = payWechatRecordMapper.getSupplierSubMchid(supplierCode);
            if (ObjectUtils.isEmpty(subMchid)) {
                throw new ServiceException("供应商支付账号未配置");
            }
            if (!"1688458872".equals(subMchid)) {
                // 供货商付款，冻结资金（如果是会员卡就不能冻结）
                SettleInfo settleInfo = new SettleInfo();
                settleInfo.setProfitSharing(true);
                request.setSettleInfo(settleInfo);
            }
            request.setSubMchid(subMchid);
            log.info("预支付下单，请求参数：{}", JSONObject.toJSONString(request));
            // 调用预下单接口
            PrepayWithRequestPaymentResponse response = service.prepayWithRequestPayment(request, PaySourceEnum.getPaySourceValue(order.getSource()));
            log.info("订单【{}】预支付成功，返回信息：{}", order.getOrderNo(), response);
            BeanConvert.copy(response, result);
            PayWechatRecordDO recordDO = payWechatRecordMapper.selectOneByOrderNo(order.getOrderNo().substring(4));
            //不存在则插入，存在则更新时间
            if (ObjectUtils.isEmpty(recordDO)){
                //微信支付记录
                PayWechatRecordDO payWechatRecordDO = new PayWechatRecordDO();
                BeanConvert.copy(order, payWechatRecordDO);
                payWechatRecordDO.setOrderNo(order.getOrderNo().substring(4));
                payWechatRecordDO.setPrePayId(response.getPackageVal().replace("prepay_id=",""));
                payWechatRecordDO.setTenantId(Long.valueOf(supplierCode));
                payWechatRecordDO.setChannel(MallOrderChannelEnum.CLOUD_SHOP.getCode());
                payWechatRecordMapper.insert(payWechatRecordDO);
            }else {
                payWechatRecordMapper.updateById(recordDO);
            }
            if (!ObjectUtils.isEmpty(order.getUniqueId())) {
                result.setUniqueId(order.getUniqueId());
            }
            return result;
        } catch (Exception e) { // 服务返回成功，返回体类型不合法，或者解析返回体失败
            log.error("微信下单发送HTTP请求失败", e);
            throw new ServiceException("商户收款失败,请联系管理员");
        }
    }

    @Override
    public void payCloudShopAfterNotify(ServiceOrderAfterPayDTO payDTO) {
        String url = cloudShopPayNotifyUrl;
        String res = HttpTool.sendGet(url + "/app/tnpointwechatnotify?orderCode=" + payDTO.getOrderNo());
//        String res = HttpTool.sendGet("https://newtsl.tntab.cn/prod-api/api/order/app/tnpointwechatnotify?orderCode=1286009744930242560");
        log.info("payCloudShopAfterNotify npointwechatnotify {}", res);
        JSONObject resObj = JSONObject.parseObject(res);
        String data = resObj.getString("code");
        if ("R-00000".equals(data)) {
            // 调用成功，更新流水状态
            updatePaymentSuccessStatus(payDTO.getOrderNo(),"");
        }

    }

    @Override
    public Boolean refundCloudShop(String orderNo) {
        CreateRequest request = new CreateRequest();
        // 子商户
        request.setSubMchid("1688458872");
        PayWechatRecordDO recordDO = payWechatRecordMapper.selectOneByOrderNo(orderNo);
        if (ObjectUtils.isEmpty(recordDO)) {
            return false;
        }
        // 微信支付回调唯一标识
        request.setTransactionId(recordDO.getTransactionId());
        // 商户退款单号
        request.setOutRefundNo(wxPayConfig.getEnvPay() + recordDO.getOrderNo());
        request.setReason("取消认领，进行退款");
        AmountReq amountReq = new AmountReq();
        // 原订单金额
        amountReq.setTotal(Long.valueOf(recordDO.getAmount()));
        //  退款金额
        amountReq.setRefund(Long.valueOf(recordDO.getAmount()));
        amountReq.setCurrency("CNY");
        request.setAmount(amountReq);
        log.info("refundCloudShop request{}", request);
        RefundService service = new RefundService.Builder()
                .config(rsaAutoCertificateConfig)
                .build();
        Refund refund = service.create(request);
        log.info("refundCloudShop refund {}", refund);
//        return refund.getStatus().equals("SUCCESS");
        // 记录退款状态
        insertRefundDO(recordDO, request.getOutRefundNo());
        return true;
    }

    @Override
    public void refundCloudShopTask(Long id) {
        PayWechatRefundDO refundDO = payWechatRefundMapper.selectById(id);
        if (ObjectUtils.isEmpty(refundDO)) {
            return;
        }
        // 查询退款单状态
        RefundService service = new RefundService.Builder()
                .config(rsaAutoCertificateConfig)
                .build();
        QueryByOutRefundNoRequest request = new QueryByOutRefundNoRequest();
        request.setOutRefundNo(refundDO.getRefundNo());
        request.setSubMchid("1688458872");
        Refund refund = service.queryByOutRefundNo(request);
        log.info("queryByOutRefundNo res {}", refund);
        refundDO.setStatus(2);
        if (refund.getStatus().equals(Status.SUCCESS)) {
            refundDO.setStatus(10);
        }else if (refund.getStatus().equals(Status.ABNORMAL) || refund.getStatus().equals(Status.CLOSED)) {
            refundDO.setStatus(3);
        }
        payWechatRefundMapper.updateById(refundDO);
    }

    @Override
    public void refundCloudShopTask() {
        QueryWrapper<PayWechatRefundDO> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().in(PayWechatRefundDO::getStatus, 1, 2);
        queryWrapper.lambda().between(PayWechatRefundDO::getCreateTime, LocalDate.now().atStartOfDay(), LocalDateTime.now());
        List<PayWechatRefundDO> list = payWechatRefundMapper.selectList(queryWrapper);
        list.stream().forEach(item ->{
            refundCloudShopTask(item.getId());
        });
    }

    public void insertRefundDO(PayWechatRecordDO recordDO, String refundNo) {
        PayWechatRefundDO refundDO = new PayWechatRefundDO();
        refundDO.setOrderNo(recordDO.getOrderNo());
        refundDO.setSubMchid("1688458872");
        refundDO.setTransactionId(recordDO.getTransactionId());
        refundDO.setRefundNo(refundNo);
        refundDO.setAmount(recordDO.getAmount());
        refundDO.setStatus(1);
        refundDO.setSource(1);
        payWechatRefundMapper.insert(refundDO);
        sendMQ(refundDO.getId());
    }

    public void sendMQ(Long id) {
        Map<String, String> param = new HashMap<>();
        param.put("id", String.valueOf(id));
        redisDelayQueueUtil.addDelayQueue(param, 1, TimeUnit.MINUTES, RedisDelayQueueEnum.TEBO_MERCHANT_REFUND_TASK.getCode());
    }
    /**
     * 云店支付
     * @param order
     * @return
     */
    private PrepayRequest buildPrepayRequestForCloudShop(PayOrderDTO order) {
        PrepayRequest request = new PrepayRequest();
        request.setSpAppid(PaySourceEnum.TB.getValues());
        request.setSpMchid(wxPayConfig.getMerchantId());
        request.setDescription(order.getDescription());
        request.setOutTradeNo(order.getOrderNo());
        request.setNotifyUrl(wxPayConfig.getPayNotifyUrl());
        // 原有逻辑，收款到智联账户
        request.setSubMchid("**********");
        Amount amount = new Amount();
        amount.setTotal(order.getAmount());
        request.setAmount(amount);
        // 用户信息
        Payer payer = new Payer();
        payer.setSpOpenid(order.getOpenId());
        request.setPayer(payer);
        return request;
    }

}
