package com.tebo.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.tebo.common.redis.constant.TeboSystemCacheConstant;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.http.HttpTool;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.common.util.position.AutoNaviUrlConstant;
import com.tebo.common.util.position.domain.AutoNaviAreaDTO;
import com.tebo.system.api.domain.view.TeboAreaVO;
import com.tebo.system.domain.view.TeboCityVO;
import com.tebo.system.domain.view.TeboDistrictVO;
import com.tebo.system.domain.view.TeboProvinceVO;
import com.tebo.system.entity.TeboCityDO;
import com.tebo.system.entity.TeboDistrictDO;
import com.tebo.system.entity.TeboProvinceDO;
import com.tebo.system.mapper.TeboCityMapper;
import com.tebo.system.mapper.TeboDistrictMapper;
import com.tebo.system.mapper.TeboProvinceMapper;
import com.tebo.system.service.IAreaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ZhangFan
 * @date : 2023/12/11 10:53
 * @Desc :
 */
@Slf4j
@Service
public class AreaServiceImpl implements IAreaService {

    @Resource
    private RedisService redisService;
    @Resource
    private TeboProvinceMapper teboProvinceMapper;

    @Resource
    private TeboCityMapper teboCityMapper;

    @Resource
    private TeboDistrictMapper teboDistrictMapper;

    @Override
    public List<TeboProvinceVO> queryList() {
        List<TeboProvinceVO> provinceDTOList = redisService.getCacheObject(TeboSystemCacheConstant.TEBO_SYSTEM_AUTO_NAVI_NATIONAL_ADDRESS);
        if (provinceDTOList != null && !provinceDTOList.isEmpty()) {
            return provinceDTOList;
        }
        provinceDTOList = new ArrayList<>();
        // 查询所有省信息集合
        List<TeboProvinceDO> provinceList = teboProvinceMapper.selectList(null);
        // 校验省信息集合是否为空
        if (CollectionUtils.isEmpty(provinceList)) {
            log.info("queryAllProvinces return empty...");
            return provinceDTOList;
        }
        provinceDTOList = BeanConvert.copyList(provinceList, TeboProvinceVO::new);
        // 设置省下属市信息集合
        provinceDTOList.stream()
                .forEach(provinceDTO -> provinceDTO.setChild(this.queryCityByProvinceId(provinceDTO.getId())));

        // 设置市下属区信息集合
        provinceDTOList.stream()
                .forEach(provinceDTO -> provinceDTO.getChild()
                        .forEach(cityDTO -> cityDTO.setChild(this.queryDistrictByCityId(cityDTO.getId()))));
        redisService.setCacheObject(TeboSystemCacheConstant.TEBO_SYSTEM_AUTO_NAVI_NATIONAL_ADDRESS, provinceDTOList);
        return provinceDTOList;
    }

    @Override
    public TeboAreaVO selectAreaByCode(String areaCode) {
        TeboAreaVO areaVO = new TeboAreaVO();
        if (areaCode.contains(",")) {
            // 包含省市区的编码
            String[] areaCodeArr = areaCode.split(",");

            areaVO.setProvince(teboProvinceMapper.queryProvinceByCode(areaCodeArr[0]));
            areaVO.setCity(teboProvinceMapper.queryCityByCode(areaCodeArr[1]));
            areaVO.setDistrict(teboProvinceMapper.queryDistrictByCode(areaCodeArr[2]));
        } else {
            // TODO
        }
        return areaVO;
    }

    @Override
    public TeboAreaVO provinceCityInfo(String districtName) {
        Assert.notNull(districtName, "三级地址不能为空");
        TeboDistrictDO districtDO = teboDistrictMapper.getByName(districtName);
        if (Objects.isNull(districtDO)) {
            return null;
        }
        TeboAreaVO areaVO = new TeboAreaVO();
        areaVO.setDistrictCode(districtDO.getCode());
        areaVO.setDistrict(districtDO.getName());

        TeboCityDO teboCityDO = teboCityMapper.selectById(districtDO.getCityId());
        if (Objects.isNull(teboCityDO)){
            return areaVO;
        }
        areaVO.setCity(teboCityDO.getName());
        areaVO.setCityCode(teboCityDO.getCode());
        TeboProvinceDO teboProvinceDO = teboProvinceMapper.selectById(teboCityDO.getProvinceId());
        if (Objects.isNull(teboProvinceDO)){
            return areaVO;
        }
        areaVO.setProvince(teboProvinceDO.getName());
        areaVO.setProvinceCode(teboProvinceDO.getCode());

        return areaVO;
    }

    @Override
    public Boolean migrateArea(String provinceName) {
        String result = HttpTool.sendGet(AutoNaviUrlConstant.WEATHER_FORECAST_URL, "key=" + AutoNaviUrlConstant.newKey + "&keywords=" + provinceName + "&subdistrict=2&extensions=base");

        JSONObject jsonObject = JSONObject.parseObject(result);
        if (jsonObject.getInteger("status") == 0) {
            System.out.println("失败");
        }
        // 获取省信息
        JSONArray platformArray = JSON.parseArray(jsonObject.get("districts").toString());
        JSONObject province = platformArray.getJSONObject(0);
        AutoNaviAreaDTO provinceDTO = JSON.parseObject(province.toJSONString(), AutoNaviAreaDTO.class);
        log.info("provinceDTO: {}", provinceDTO);
        if (!provinceName.equals(provinceDTO.getName())) {
            throw new RuntimeException("省份信息不存在");
        }
        // 查找省份信息
        TeboProvinceDO provinceDO = teboProvinceMapper.queryProvinceByName(provinceName);
        if (Objects.isNull(provinceDO)) {
            throw new RuntimeException("省份信息不存在");
        }
        Integer provinceId = provinceDO.getId();
        // TODO 清除城市信息
        teboProvinceMapper.deleteCityByProvinceId(provinceId);
        List<AutoNaviAreaDTO> cityList = provinceDTO.getDistricts();
        List<TeboCityDO> cityDOList = new ArrayList<>();
        List<TeboDistrictDO> districtDOList = new ArrayList<>();
        cityList.stream().forEach(city -> {
            TeboCityDO teboCityDO = new TeboCityDO();
            Long cityId = SnowFlakeUtil.nextId();
            teboCityDO.setId(cityId);
            teboCityDO.setProvinceId(provinceId);
            teboCityDO.setCode(city.getAdcode());
            teboCityDO.setName(city.getName());
            cityDOList.add(teboCityDO);
            // 区县信息
            city.getDistricts().stream().forEach(area -> {
                TeboDistrictDO teboDistrictDO = new TeboDistrictDO();
                Long districtId = SnowFlakeUtil.nextId();
                teboDistrictDO.setId(districtId);
                teboDistrictDO.setCityId(cityId);
                teboDistrictDO.setCode(area.getAdcode());
                teboDistrictDO.setName(area.getName());
                districtDOList.add(teboDistrictDO);
            });
        });
        teboProvinceMapper.batchDelDistrict(districtDOList.stream().map(TeboDistrictDO::getCode).collect(Collectors.toList()));
        teboProvinceMapper.batchInsertCity(cityDOList);
        teboProvinceMapper.batchInsertDistrict(districtDOList);
        redisService.deleteObject(TeboSystemCacheConstant.TEBO_SYSTEM_AUTO_NAVI_NATIONAL_ADDRESS);
        return true;
    }

    private List<TeboCityVO> queryCityByProvinceId(Integer provinceId) {
        List<TeboCityDO> cityList = teboProvinceMapper.queryCityByProvinceId(provinceId);
        return BeanConvert.copyList(cityList, TeboCityVO::new);
    }

    private List<TeboDistrictVO> queryDistrictByCityId(Long cityId) {
        List<TeboDistrictDO> districtList = teboProvinceMapper.queryDistrictByCityId(cityId);
        return BeanConvert.copyList(districtList, TeboDistrictVO::new);
    }
}
