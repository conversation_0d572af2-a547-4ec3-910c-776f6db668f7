package com.tebo.system.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 门店商品
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Getter
@Setter
@TableName("tebo_shop_pic")
public class TeboShopPicDO extends Model<TeboShopPicDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 门店id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 图片url
     */
    @TableField("pic_url")
    private String picUrl;

    /**
     * 图片类型 1门头照片
     */
    @TableField("pic_type")
    private Integer picType;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;


    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;



    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
