package com.tebo.system.service;

import com.tebo.system.api.model.TeboAccountInfoVO;
import com.tebo.system.domain.dto.account.RescueStatusChangeDTO;
import com.tebo.system.domain.dto.account.TeboAccountCreateDTO;
import com.tebo.system.domain.dto.account.TeboAccountQueryDTO;
import com.tebo.system.domain.dto.account.TeboAccountUpdateDTO;
import com.tebo.system.domain.view.TeboAccountVO;
import com.tebo.system.domain.view.TeboRoleVO;
import com.tebo.system.domain.vo.TeboShopExtraVO;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON>an
 * @date : 2023/12/12 13:32
 * @Desc :
 */
public interface ITeboAccountService {

    TeboAccountVO selectUserByUserName(String username);


    TeboAccountVO selectAccountInfoByUserName(String username);

    TeboAccountVO getAccountInfoByRoleId(String username);

    List<TeboAccountVO> selectAccountList(TeboAccountQueryDTO query);

    List<TeboAccountVO> notActivePartner();

    int createAccount(TeboAccountCreateDTO create);
    int updateAccount(TeboAccountCreateDTO create);

    /**
     * 登录时使用，不对name校验
     * @param create
     * @return
     */
    int updateById(TeboAccountCreateDTO create);

    TeboAccountVO getInfoById(Long id);



    /**
     * 改变是否接单状态
     */
    void changeRescueStatus(RescueStatusChangeDTO rescueStatusChangeDTO);

    int deleteAccount(Long[] ids);

    int batchUpdateAccount(List<TeboAccountUpdateDTO> updates);

    /**
     * 根据门店id删除师傅账号
     * @param shopId
     * @return
     */
    int batchDelAccountByShopId(Long shopId);

    List<TeboAccountInfoVO> getAccountInfoListByShopId(Long shopId);
    /**
     * 店铺额外数据 保证金等
     * @return
     */
    TeboShopExtraVO shopExtra();


    List<TeboRoleVO> getAccountRoleList(Integer shopType);

    /**
     * 根据手机号查询师傅账号
     * @param phone
     * @return
     */
    TeboAccountVO selectUserByPhone(String phone);

    /**
     * 根据手机号修改密码
     * @param account
     * @return
     */
    Boolean updatePwdByPhone(TeboAccountUpdateDTO account);

    /**
     * 根据手机号查询师傅账号
     */
    TeboAccountVO selectUserByPhoneAndRole(String phone);
}
