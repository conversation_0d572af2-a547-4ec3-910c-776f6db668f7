package com.tebo.system.domain.dto.merchant;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tebo.common.core.web.page.PageDomain;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class TeboShopReviewQueryDTO extends PageDomain implements Serializable {

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 门店集合
     */
    private List<Long> shopIdList;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 商户名称
     */
    private String wxSellerName;

    /**
     * 门店老板
     */
    private String shopBossName;

    /**
     * 合伙人名字
     */
    private String tenantName;

    /**
     * 联系电话
     */
    private String phoneNumber;

    /**
     * 门店审核状态（0:待审核 1:通过 2:不通过）
     */
    private Integer reviewStatus;

    /**
     * 微信审核状态
     */
    private String wechatReviewStatus;

    /**
     * 审核时间(开始)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate auditTimeStart;

    /**
     * 审核时间(结束)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate auditTimeEnd;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime auditTimeStartSecond;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime auditTimeEndSecond;

    /**
     * 业务经理
     */
    private String businessManager;
}