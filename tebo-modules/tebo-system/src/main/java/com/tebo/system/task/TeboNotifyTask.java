//package com.tebo.system.task;
//
//import cn.hutool.core.util.ObjectUtil;
//import cn.hutool.http.HttpUtil;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.tebo.common.core.utils.StringUtils;
//import com.tebo.common.redis.service.RedisService;
//import com.tebo.common.util.time.LocalDateUtil;
//import com.tebo.system.config.WxPushConfig;
//import com.tebo.system.constant.TeboSystemCommonCacheConstant;
//import com.tebo.system.domain.WxTemplateMsg;
//import com.tebo.system.domain.so.WechatUserSubscribeSO;
//import com.tebo.system.manager.TeboWechatUserSubscribeManger;
//import com.tebo.system.manager.WeChatManger;
//import com.tebo.system.mapper.TeboWechatUserSubscribeMapper;
//import com.tebo.system.redisson.queue.RedisDelayQueueHandle;
//import com.tebo.system.redisson.queue.RedisDelayQueueUtil;
//import com.tebo.system.util.DistributedLock;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.TimeUnit;
//
///**
// * <AUTHOR> ZhangFan
// * @date : 2024/3/9 14:14
// * @Desc :
// */
//@Slf4j
//@Component
//public class TeboNotifyTask {
//    @Resource
//    private TeboWechatUserSubscribeManger teboWechatUserSubscribeManger;
//    @Resource
//    private TeboWechatUserSubscribeMapper teboWechatUserSubscribeMapper;
//    @Resource
//    private WeChatManger weChatManger;
//    @Resource
//    private WxPushConfig wxPushConfig;
//    @Resource
//    private RedisService redisService;
//    @Resource
//    private RedisDelayQueueUtil redisDelayQueueUtil;
//    @Resource
//    private DistributedLock distributedLock;
//
//    private static final String TEMPLATE_ID_NOTIFY_LOTTERY = "YKGqSHhpKa9759jYOahbhYNc3edMpt9XFKj1gWlsC1M";
//
//    private static final String appid = "wx62366beef2f4025b";
//
//    @Scheduled(cron = "0 0/1 * * * *")
//    public void notifyLottery() {
//        log.info("TeboNotifyTask:notifyLottery start");
//        //判断是不是已关注服务号的用户，若没关注，不推送
//        WechatUserSubscribeSO wechatUserSubscribeSO = new WechatUserSubscribeSO();
//        String unionId = "ozRHL6VQXGp7aJbI3bQmh28fnBbA";
//        wechatUserSubscribeSO.setUnionid(unionId);
//        wechatUserSubscribeSO.setOfficialAccount("泰博出行");
//        String openId = teboWechatUserSubscribeManger.getSubscribeUserOpenId(wechatUserSubscribeSO);
//        if (StringUtils.isEmpty(openId)) {
//            return;
//        }
//        String accessToken = weChatManger.getAppletAccessToken();
//        WxTemplateMsg templateMsg = new WxTemplateMsg();
//        // 前面准备的模板消息的id
//        templateMsg.setTemplate_id(TEMPLATE_ID_NOTIFY_LOTTERY);
//        // 推送参数，格式为keyword,value。可看下微信提供的demo会清楚点
//        templateMsg.setData(buildWxPushData());
//        // 跳转小程序
//        HashMap<String, String> miniPro = new HashMap<>(2);
//        miniPro.put("appid", appid);
//        miniPro.put("pagepath", "tripPackages/pages/lst/home/<USER>");
//        templateMsg.setMiniprogram(miniPro);
//        templateMsg.setTouser(openId);
//        // 发送Http请求
//        String respStr = HttpUtil.post(wxPushConfig.getPushUrl() + accessToken, JSON.toJSONString(templateMsg));
//        JSONObject result = JSON.parseObject(respStr);
//        if (!"0".equals(result.getString("errcode"))) {
//            log.info("微信消息推送失败：{}", result);
//            return;
//        }
//        redisService.incrementValue("tebo_system:notifyLottery:" + LocalDateUtil.formatDate(LocalDate.now()) , 1L);
//    }
//
//    private Map<String, Object> buildWxPushData() {
////        String key = "tebo_system:buildWxPushData:" + LocalDateUtil.getMinOfDayTime(LocalDate.now());
////        if (redisService.hasKey(key)) {
////            return redisService.getCacheObject(key);
////        }
//        Map<String, Object> data = new HashMap();
//        data.put("character_string14", new HashMap() {{
//            put("value", "TBCX00001");
//        }});
//        data.put("time69", new HashMap() {{
//            put("value", "2025-06-30 12:00:00");
//        }});
//        data.put("thing19", new HashMap() {{
//            put("value", "服务工单");
//        }});
////        redisService.setCacheObject(key, data, 1L, TimeUnit.DAYS);
//        return data;
//    }
//}
