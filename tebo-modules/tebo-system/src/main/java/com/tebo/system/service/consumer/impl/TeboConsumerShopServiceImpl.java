package com.tebo.system.service.consumer.impl;

import cn.hutool.core.util.ObjectUtil;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.system.domain.dto.consumer.TeboConsumerShopDTO;
import com.tebo.system.entity.TeboCustomerShopDO;
import com.tebo.system.mapper.TeboCustomerShopMapper;
import com.tebo.system.service.consumer.TeboConsumerShopService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Service
@Slf4j
public class TeboConsumerShopServiceImpl implements TeboConsumerShopService {
    @Resource
    private TeboCustomerShopMapper teboCustomerShopMapper;

    @Override
    public void markConsumerShop(TeboConsumerShopDTO teboConsumerShopDTO) {
        TeboCustomerShopDO teboCustomerShopDO = new TeboCustomerShopDO();
        teboCustomerShopDO.setId(SnowFlakeUtil.nextId());
        teboCustomerShopDO.setCustomerId(teboConsumerShopDTO.getCustomerId());
        teboCustomerShopDO.setPhoneNumber(teboConsumerShopDTO.getPhoneNumber());
        TeboCustomerShopDO shopDO = teboCustomerShopMapper.selectByPhoneAndCustomerId(teboConsumerShopDTO);
        if (ObjectUtil.isNotEmpty(shopDO)){
            teboCustomerShopMapper.insert(teboCustomerShopDO);
        }else {
            shopDO.setUpdateTime(LocalDateTime.now());
            teboCustomerShopMapper.updateById(shopDO);
        }


    }
}