package com.tebo.system.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.tebo.common.core.utils.file.FileUtils;
import com.tebo.common.util.http.HttpTool;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.system.domain.view.BusinessLicenseVO;
import com.tebo.system.service.IFileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Random;

/**
 * <AUTHOR> ZhangFan
 * @date : 2023/12/11 18:44
 * @Desc :
 */
@Slf4j
@Service
public class FileServiceImpl implements IFileService {

    @Value("${oss.access_key}")
    private String accessKey;
    @Value("${oss.access_key_secret}")
    private String accessKeySecret;
    public static final String ENDPOINT = "https://oss-cn-hangzhou.aliyuncs.com";

    public static final String BUCKET = "tby-statics";

    @Override
    public String upload(InputStream inputStream, String fileName) {
        // 创建OSSClient实例。
        OSS ossClient = null;
        String url = "";
        try {
            // 创建OSSClient实例。
            ossClient = new OSSClientBuilder().build(ENDPOINT, accessKey, accessKeySecret);
            //创建上传Object的Metadata
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setCacheControl("no-cache");
            objectMetadata.setContentType(getContentType(fileName.substring(fileName.lastIndexOf("."))));
            objectMetadata.setHeader("Pragma", "no-cache");
            //设置Content-disposition的内容模板格式，基于base64编码格式
            String downloadFileNameTemplate = "inline; filename=\"=?UTF8?B?%s?=\"";
            //对真正文件名称进行base64编码
            String base64FileName = new String(Base64.getEncoder().encode(FileUtils.getName(fileName).getBytes(StandardCharsets.UTF_8)));
            //设置下载文件的名称
            objectMetadata.setContentDisposition(String.format(downloadFileNameTemplate, base64FileName));
            //上传文件
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(BUCKET, fileName, inputStream, objectMetadata);
            // 设置该属性可以返回response。如果不设置，则返回的response为空。
            putObjectRequest.setProcess("true");
            // 创建PutObject请求。
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            url = result.getResponse().getUri();
//            log.info("[OSSUtils][],reslut:{}", result);
        } catch (OSSException e){
            e.printStackTrace();
        } finally {
            // 关闭OSSClient。
            ossClient.shutdown();
        }
//        log.info("[OSSUtils][],url:{}", url);
        return "https://tby-statics.tntab.cn/" + fileName;
    }

    @Override
    public String uploadFile(InputStream inputStream, String fileName) {
        String url = "";
        OSS ossClient = null;
        try {
            // 创建OSSClient实例。
            ossClient = new OSSClientBuilder().build(ENDPOINT, accessKey, accessKeySecret);
            //创建上传Object的Metadata
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setCacheControl("no-cache");
            objectMetadata.setContentType("text/plain");
            objectMetadata.setHeader("Pragma", "no-cache");
            //设置Content-disposition的内容模板格式，基于base64编码格式
            String downloadFileNameTemplate = "inline; filename=\"=?UTF8?B?%s?=\"";
            //对真正文件名称进行base64编码
            String base64FileName = new String(Base64.getEncoder().encode(FileUtils.getName(fileName).getBytes(StandardCharsets.UTF_8)));
            //设置下载文件的名称
            objectMetadata.setContentDisposition(String.format(downloadFileNameTemplate, base64FileName));
            //上传文件
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(BUCKET, fileName, inputStream, objectMetadata);
            // 设置该属性可以返回response。如果不设置，则返回的response为空。
            putObjectRequest.setProcess("true");
            // 创建PutObject请求。
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            url = result.getResponse().getUri();
            log.info("[OSSUtils][],reslut:{}", result);
        } catch (OSSException e) {
            e.printStackTrace();
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return url;
    }

    public static String getContentType(String filenameExtension) {
        if (filenameExtension.equalsIgnoreCase(".bmp")) {
            return "image/bmp";
        }
        if (filenameExtension.equalsIgnoreCase(".gif")) {
            return "image/gif";
        }
        if (filenameExtension.equalsIgnoreCase(".jpeg") ||
                filenameExtension.equalsIgnoreCase(".jpg") ||
                filenameExtension.equalsIgnoreCase(".png")) {
            return "image/jpg";
        }
        if (filenameExtension.equalsIgnoreCase(".html")) {
            return "text/html";
        }
        if (filenameExtension.equalsIgnoreCase(".txt")) {
            return "text/plain";
        }
        if (filenameExtension.equalsIgnoreCase(".vsd")) {
            return "application/vnd.visio";
        }
        if (filenameExtension.equalsIgnoreCase(".pptx") ||
                filenameExtension.equalsIgnoreCase(".ppt")) {
            return "application/vnd.ms-powerpoint";
        }
        if (filenameExtension.equalsIgnoreCase(".docx") ||
                filenameExtension.equalsIgnoreCase(".doc")) {
            return "application/msword";
        }
        if (filenameExtension.equalsIgnoreCase(".xml")) {
            return "text/xml";
        }
        //增加excel
        if (filenameExtension.equalsIgnoreCase(".xlsx") ||
                filenameExtension.equalsIgnoreCase(".xls")) {
            return "application/vnd.ms-excel";
        }
        return "image/jpg";
    }

    @Override
    public BusinessLicenseVO identify(String url) {
        String getPicUrl = "https://tby.tntab.cn/prod-api/tnyShop/pc/qiandan/aiBusinessLicense?imageUrl=" + url;
        String ocrResult = HttpTool.sendGet(getPicUrl);
        JSONObject result = JSONObject.parseObject(ocrResult).getJSONObject("result");
        BusinessLicenseVO licenseVO = new BusinessLicenseVO(String.valueOf(result.get("ENTERPRISE_TAXPAYER_REGISTER_ID")), String.valueOf(result.get("ENTERPRISE_OWNER")), String.valueOf(result.get("ENTERPRISE_CAPITAL")), String.valueOf(result.get("ENTERPRISE_VALID_DATE_START")));
        licenseVO.setLicenseAddress(result.getString("ENTERPRISE_REGISTER_ADDRESS"));
        licenseVO.setWxSellerName(result.getString("ENTERPRISE_NAME_CH"));
        return licenseVO;
    }

    /**
     * 将pdf转换成图片并上传oss
     */
    @Override
    public  String convertPdfToImage(String pdfUrl){
        Long random = SnowFlakeUtil.nextId();
        String imagePath = "tebo/insurance/battery"+random+".png";
        try {
            // 创建File对象，用于检查和创建文件夹
            File folder = new File(imagePath);
            // 检查文件夹是否存在，如果不存在，则创建它
            if (!folder.exists()) {
                boolean isFolderCreated = folder.mkdirs(); // 创建所有必需的父目录
                if (!isFolderCreated) {
                    log.error("Failed to create directory: " + imagePath);
                    return null; // 或者根据你的逻辑返回适当的值
                }
            }
            // 使用HttpURLConnection下载PDF文件
            HttpURLConnection connection = (HttpURLConnection) new URL(pdfUrl).openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (InputStream inputStream = connection.getInputStream();
                     PDDocument document = PDDocument.load(inputStream)) {
                    // 创建PDF渲染器
                    PDFRenderer pdfRenderer = new PDFRenderer(document);
                    int pages = document.getNumberOfPages();
                    int imageWidth = -1; // 用于保存图片宽度，假设所有页面宽度一致
                    int heightSum = 0; // 用于累加所有页面的高度
                    // 逐页渲染PDF并计算总宽度和高度
                    for (int i = 0; i < pages; i++) {
                        BufferedImage image = pdfRenderer.renderImageWithDPI(i, 300); // 300 DPI
                        if (imageWidth == -1) { // 如果是第一页，保存宽度
                            imageWidth = image.getWidth();
                        }
                        heightSum += image.getHeight();
                    }
                    // 初始化一个足够大的BufferedImage来容纳所有页面
                    BufferedImage fullImage = new BufferedImage(imageWidth, heightSum, BufferedImage.TYPE_INT_ARGB);
                    Graphics g = fullImage.getGraphics(); // 获取图形上下文
                    // 再次逐页渲染PDF并绘制到fullImage上
                    heightSum = 0; // 重置高度累加器
                    for (int i = 0; i < pages; i++) {
                        BufferedImage image = pdfRenderer.renderImageWithDPI(i, 300); // 300 DPI
                        g.drawImage(image, 0, heightSum, null); // 绘制图片
                        heightSum += image.getHeight(); // 更新累加的高度
                    }
                    g.dispose(); // 释放图形上下文
                    // 保存长图
                    ImageIO.write(fullImage, "PNG", new File(imagePath));
                } catch (IOException e) {
                    log.error("Error processing the PDF document",e);
                }
            }
            InputStream inputStream = new FileInputStream(imagePath);
            String imageUrl = upload(inputStream, imagePath);
            return imageUrl;
        } catch (Exception e) {
            log.error("Error downloading the PDF file", e);
        }
        return "";
    }

    private static String getFileName(String filePath) {
        File file = new File(filePath);
        return file.getName(); // 返回文件名
    }

    public static void main(String[] args){

    }
}
