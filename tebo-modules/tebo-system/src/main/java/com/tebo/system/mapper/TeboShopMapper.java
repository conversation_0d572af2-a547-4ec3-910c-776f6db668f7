package com.tebo.system.mapper;

import com.tebo.system.domain.dto.TeboShopQueryDTO;
import com.tebo.system.domain.dto.TeboShopStatusChangeDTO;
import com.tebo.system.domain.vo.PersonGroupCountVO;
import com.tebo.system.domain.vo.TeboShopDataProvVO;
import com.tebo.system.domain.vo.TimeGroupCountVO;
import com.tebo.system.domain.vo.shop.TeboShopNumVO;
import com.tebo.system.entity.TeboPartnerInfoDO;
import com.tebo.system.entity.TeboShopDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 门店信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Mapper
public interface TeboShopMapper extends TeboBaseMapper<TeboShopDO> {

    /**
     * 门店列表
     */

    List<TeboShopDO> list(TeboShopQueryDTO queryDTO);

    /**
     * 附近门店
     * @param queryDTO
     * @return
     */
    List<TeboShopDO> rescueNearbyStore(TeboShopQueryDTO queryDTO);


    /**
     * 附近门店
     */
    List<TeboShopDO> nearbyStore(TeboShopQueryDTO queryDTO);


    List<TeboShopDO> getShopListForPc(TeboShopQueryDTO queryDTO);
    /**
     * 门店列表
     */

    List<TeboShopDO> listNew(TeboShopQueryDTO queryDTO);

    /**
     * 附近门店
     * @param queryDTO
     * @return
     */
    List<TeboShopDO> nearbyShopList(TeboShopQueryDTO queryDTO);

    /**
     * 泰博商城附近门店(首页定位点击进入)
     * @param queryDTO
     * @return
     */
    List<TeboShopDO> getHomePageMallShopByDistrictCodeList(TeboShopQueryDTO queryDTO);

    List<TeboShopDO> listNewOne(TeboShopQueryDTO queryDTO);

    /**
     * 提供救援
     * @param queryDTO
     * @return
     */
    List<TeboShopDO> listForRescue(TeboShopQueryDTO queryDTO);
    /**
     * 根据日期分组
     * @param queryDTO
     * @return
     */
    List<TimeGroupCountVO> groupByDay(TeboShopQueryDTO queryDTO);
    List<TimeGroupCountVO> groupByMonth(TeboShopQueryDTO queryDTO);

    List<PersonGroupCountVO> groupByPerson(TeboShopQueryDTO queryDTO);

    /**
     * 批量修改门店状态
     */
    int updateStatus(TeboShopStatusChangeDTO shopStatusChangeDTO);

    int batchDelete(@Param("idList") List<Long> idList);

    /**
     * 获取合伙人下门店数量
     */
    List<TeboShopNumVO> selectShopCountByPartnerId(@Param("tenantIdList") List<String> tenantIdList);

    int selectCountByPartnerId(@Param("idList") List<Long> ids);


    TeboShopDO getByCid(@Param("cid") String  cid);

    /**
     * 备案编号的数量
     * @param businessLicenseCode
     * @return
     */
    int countByBusinessLicenseCode(@Param("businessLicenseCode") String  businessLicenseCode);


    List<TeboShopDO> listMarkShop(@Param("customerId") Long customerId);

    void updateTeboShop(TeboShopDO teboShopDO);


//    /**
//     * 根据省份分组 计数
//     * @param shopQueryDTO
//     * @return
//     */
//    List<TeboShopDataProvVO> groupByProv(TeboShopQueryDTO shopQueryDTO);


    TeboShopDO selectByShopCode(@Param("shopCode") String shopCode);
    /**
     * 根据手机号查询门店
     */
    TeboShopDO getShopByPhoneNumber(@Param("phoneNumber") String phoneNumber);

    /**
     * 根据合伙人id查询门店
     * @param newTenantId
     * @return
     */
    TeboShopDO selectByNewTenantId(@Param("newTenantId") Long newTenantId);

    /**
     * 查找合伙人钱包id
     */
    Long getPartnerWalletId(@Param("cloudShopId") String cloudShopId);

    String getDistanceByShopId(@Param("shopId") Long shopId);

    TeboShopDO getSapCode(Long shopId);


    List<TeboShopDO> getAllTenantSapCode(@Param("sapCodeList") List<String> sapCodeList);

    String selectUnionIdById(@Param("id") Long id);
}
