package com.tebo.system.service.impl.push;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tebo.common.core.enums.WeChatOfficialAccountEnum;
import com.tebo.common.core.enums.WxSubscribeEventEnum;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.util.env.EnvUtils;
import com.tebo.common.util.time.LocalDateUtil;
import com.tebo.common.util.xml.XMLUtil;
import com.tebo.system.api.domain.dto.wechat.TeboWechatNotifyForShopDTO;
import com.tebo.system.api.model.TeboShop;
import com.tebo.system.config.WxPushConfig;
import com.tebo.system.domain.WxTemplateMsg;
import com.tebo.system.domain.so.WechatBonusSO;
import com.tebo.system.domain.so.WechatUserSubscribeSO;
import com.tebo.system.domain.so.WxPushSO;
import com.tebo.system.entity.TeboShopDO;
import com.tebo.system.entity.TeboWechatReplyDO;
import com.tebo.system.manager.TeboShopManager;
import com.tebo.system.manager.TeboWechatReplyManger;
import com.tebo.system.manager.TeboWechatUserSubscribeManger;
import com.tebo.system.manager.WeChatManger;
import com.tebo.system.mapper.TeboShopMapper;
import com.tebo.system.service.push.IWxPushService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class WxPushServiceImpl implements IWxPushService {

    @Resource
    private RedisService redisService;


    @Resource
    private WxPushConfig wxPushConfig;
//    @Value("${wx.push.token_url}")
//    private String tokenUrl;
//
//    @Value("${wx.push.push_url}")
//    private String pushUrl;

    @Autowired
    private TeboWechatReplyManger wechatReplyManger;


    @Autowired
    private TeboWechatUserSubscribeManger teboWechatUserSubscribeManger;

    @Autowired
    private WeChatManger weChatManger;
    @Resource
    private TeboShopMapper teboShopMapper;

    @Override
    public void sendMessage(WxPushSO wxPushSO) {
        String runEnv = EnvUtils.getRunEnv();
        log.info("当前环境：{}", runEnv);
        if (!"prod".equals(runEnv)){
            log.info("不是生产环境，不做推送");
            return;
        }
        //判断是不是已关注服务号的用户，若没关注，不推送
        WechatUserSubscribeSO wechatUserSubscribeSO = new WechatUserSubscribeSO();
        wechatUserSubscribeSO.setUnionid(wxPushSO.getUnionid());
        wechatUserSubscribeSO.setOfficialAccount(wxPushSO.getOfficialAccount());

        String openId = teboWechatUserSubscribeManger.getSubscribeUserOpenId(wechatUserSubscribeSO);
        if (StringUtils.isEmpty(openId)) {
            return;
        }

        //todo 后面如果有多个公众号，下面的逻辑通过抽象类拆分

        /*WxPushTokenSO wxPushTokenSO = new WxPushTokenSO();
        wxPushTokenSO.setSecret(wxPushSO.getSecret());
        wxPushTokenSO.setAppid(wxPushSO.getAppid());*/
        String accessToken = weChatManger.getAppletAccessToken();
        //String appid = wxPushTokenSO.getAppid();
        WxTemplateMsg templateMsg = new WxTemplateMsg();
        // 公众号appid
        //templateMsg.setAppId(appid);
        // 前面准备的模板消息的id
        templateMsg.setTemplate_id(wxPushSO.getTemplate_id());
        // 推送参数，格式为keyword,value。可看下微信提供的demo会清楚点
        templateMsg.setData(wxPushSO.getData());

        // 跳转小程序
        String pagepath = wxPushSO.getPagepath();
        if (StringUtils.isNotEmpty((pagepath))) {
            HashMap<String, String> miniPro = new HashMap<>(2);
            miniPro.put("appid", wxPushSO.getAppid());
            miniPro.put("pagepath", pagepath);
            templateMsg.setMiniprogram(miniPro);
        }
        templateMsg.setTouser(openId);
        // 发送Http请求
        // POST https://api.weixin.qq.com/cgi-bin/message/subscribe/bizsend?access_token=ACCESS_TOKEN
        String respStr = HttpUtil.post(wxPushConfig.getPushUrl() + accessToken, JSON.toJSONString(templateMsg));
        log.info("微信下发统一消息接口返回：{}", respStr);
        JSONObject result = JSON.parseObject(respStr);
        if (!"0".equals(result.getString("errcode"))) {
            throw new GlobalException("微信消息推送失败，" + result.getString("errMsg"));
        }
    }

    @Override
    public Boolean hasSubscribeUser(WechatUserSubscribeSO wechatUserSubscribeSO) {
        return teboWechatUserSubscribeManger.isSubscribeUser(wechatUserSubscribeSO);
    }

    @Override
    public void subscribeEvent(WechatUserSubscribeSO wechatUserSubscribeSO) {
        //是否已关注
        Boolean hasSubScribe = teboWechatUserSubscribeManger.isSubscribeUser(wechatUserSubscribeSO);
        if (Boolean.TRUE.equals(hasSubScribe)) {
            return;
        }
        //关注
        teboWechatUserSubscribeManger.subScribe(wechatUserSubscribeSO);

    }

    @Override
    public void unsubscribeEvent(WechatUserSubscribeSO wechatUserSubscribeSO) {
        //是否已关注
        Boolean hasSubScribe = teboWechatUserSubscribeManger.isSubscribeUser(wechatUserSubscribeSO);
        if (Boolean.FALSE.equals(hasSubScribe)) {
            return;
        }
        //取消关注
        teboWechatUserSubscribeManger.unsubScribe(wechatUserSubscribeSO);

    }

    @Override
    public void subscribeCallback(HttpServletRequest request, HttpServletResponse response) throws UnsupportedEncodingException {
        try {
            request.setCharacterEncoding("UTF-8");
            response.setCharacterEncoding("UTF-8");
            Map<String, String> requestMap = XMLUtil.xmlToMap(request);
//            log.info("解析====>{}", JSONObject.toJSONString(requestMap));
            String messageType = requestMap.get("MsgType");
//            log.info("微信类型===>{}", messageType);
            String eventType = requestMap.get("Event");
            // 发送方帐号（open_id）
            String openid = requestMap.get("FromUserName");
            // 公众帐号
            String toUserName = requestMap.get("ToUserName");
            // 消息类型
            String eventKey = requestMap.get("EventKey");
            String content = requestMap.get("Content");


            OutputStream os = response.getOutputStream();


            if ("event".equals(messageType)) {
                //判断消息类型是否是事件消息类型
//                log.info("公众号====>事件消息");
//                log.info("openid:" + openid);
//                log.info("Event:" + eventType);
                if (eventType.equals(WxSubscribeEventEnum.subscribe.getValue())) {
//                    log.info("公众号====>新用户关注");
                    WechatUserSubscribeSO wechatUserSubscribeSO = new WechatUserSubscribeSO();
                    wechatUserSubscribeSO.setOfficialAccount(WeChatOfficialAccountEnum.teboTravel.getOfficialAccountName());
                    wechatUserSubscribeSO.setOpenId(openid);
                    subscribeEvent(wechatUserSubscribeSO);

                } else if (eventType.equals(WxSubscribeEventEnum.unsubscribe.getValue())) {
//                    log.info("公众号====>用户取消关注");
                    WechatUserSubscribeSO wechatUserSubscribeSO = new WechatUserSubscribeSO();
                    wechatUserSubscribeSO.setOfficialAccount(WeChatOfficialAccountEnum.teboTravel.getOfficialAccountName());
                    wechatUserSubscribeSO.setOpenId(openid);
                    unsubscribeEvent(wechatUserSubscribeSO);

                } else {
//                    log.info("微信类型===>{}", messageType);
//                    log.info("公众号===>其他");
                }
            } else if (messageType.equals(WxSubscribeEventEnum.text.getValue())) {
                handleInputContent(openid, toUserName, content, os);

            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }


    /**
     * 处理用户输入
     * @param openid
     * @param toUserName
     * @param content
     * @param os
     * @throws Exception
     */
    private void handleInputContent(String openid, String toUserName, String content, OutputStream os) throws Exception {
        log.info("公众号====>用户发送消息");
        if (StringUtils.isNotEmpty(content)) {
            List<String> askList = wechatReplyManger.getAllAskContent();
            if (CollectionUtil.isNotEmpty(askList)) {
                for (String ask : askList) {
                    if (content.contains(ask)) {

                        Map<String, String> msgMap = new HashMap<>();
                        msgMap.put("CreateTime", String.valueOf(new Date().getTime()));
                        msgMap.put("ToUserName", openid);
                        msgMap.put("FromUserName", toUserName);
                        msgMap.put("MsgType", "text");
                        TeboWechatReplyDO wechatReplyDO = wechatReplyManger.getByAskContent(ask);
                        String answerContent = wechatReplyDO.getAnswerContent();
                        msgMap.put("Content",answerContent);

                        String mapToXml = XMLUtil.mapToXml(msgMap);
                        log.info("发送文本数据:" + mapToXml);
                        os.write(mapToXml.getBytes());
                        break;
                    }
                }
            }
        }
    }

    @Override
    public void handleHistoryUser() {
        teboWechatUserSubscribeManger.handleHistoryUser();
    }

    @Override
    public void testInput(String input,HttpServletResponse response) throws Exception {
        handleInputContent("xxxopenid","xxxusername",input, response.getOutputStream());
    }

    @Override
    public void awardBonus(WechatBonusSO wechatBonusSO) {
        weChatManger.awardBonus(wechatBonusSO);
    }

    @Override
    public void sendNotifyForShop(TeboWechatNotifyForShopDTO dto) {
        log.info("sendNotifyForShop TeboWechatNotifyForShopDTO {}", dto);
        String unionId = teboShopMapper.selectUnionIdById(dto.getShopId());
        if (ObjectUtils.isEmpty(unionId)) {
            return;
        }
        //判断是不是已关注服务号的用户，若没关注，不推送
        WechatUserSubscribeSO wechatUserSubscribeSO = new WechatUserSubscribeSO();
//        unionId = "ozRHL6VQXGp7aJbI3bQmh28fnBbA";
        wechatUserSubscribeSO.setUnionid(unionId);
        wechatUserSubscribeSO.setOfficialAccount("泰博出行");
        String openId = teboWechatUserSubscribeManger.getSubscribeUserOpenId(wechatUserSubscribeSO);
        if (StringUtils.isEmpty(openId)) {
            return;
        }
        String accessToken = weChatManger.getAppletAccessToken();
        WxTemplateMsg templateMsg = new WxTemplateMsg();
        // 前面准备的模板消息的id
        templateMsg.setTemplate_id("YKGqSHhpKa9759jYOahbhYNc3edMpt9XFKj1gWlsC1M");
        // 推送参数，格式为keyword,value。可看下微信提供的demo会清楚点
        templateMsg.setData(buildWxShopPushData(dto.getOrderNo(), dto.getOrderTime(), dto.getType()));
        // 跳转小程序
        HashMap<String, String> miniPro = new HashMap<>(2);
        // 商家版小程序
        miniPro.put("appid", "wx62366beef2f4025b");
        miniPro.put("pagepath", "tripPackages/pages/lst/home/<USER>");
        templateMsg.setMiniprogram(miniPro);
        templateMsg.setTouser(openId);
        // 发送Http请求
        String respStr = HttpUtil.post(wxPushConfig.getPushUrl() + accessToken, JSON.toJSONString(templateMsg));
        log.info("sendNotifyForShop push respStr {}", respStr);
        JSONObject result = JSON.parseObject(respStr);
        if (!"0".equals(result.getString("errcode"))) {
            log.error("微信消息推送失败：{}", result);
        }
    }


    private Map<String, Object> buildWxShopPushData(String orderNo, String orderTime, Integer type) {
        Map<String, Object> data = new HashMap();
        data.put("character_string14", new HashMap() {{
            put("value", orderNo);
        }});
        data.put("time69", new HashMap() {{
            put("value", orderTime);
        }});
        // 1 服务工单 2售后工单
        if (type == 1) {
            data.put("thing19", new HashMap() {{
                put("value", "服务工单");
            }});
        }else if (type == 2) {
            data.put("thing19", new HashMap() {{
                put("value", "售后工单");
            }});
        }
        return data;
    }

}
