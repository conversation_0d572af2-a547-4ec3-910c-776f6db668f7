package com.tebo.system.service.impl.push;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.util.time.LocalDateUtil;
import com.tebo.system.config.WxPushConfig;
import com.tebo.system.domain.WxTemplateMsg;
import com.tebo.system.domain.so.WechatUserSubscribeSO;
import com.tebo.system.domain.so.WxPushSO;
import com.tebo.system.manager.TeboWechatUserSubscribeManger;
import com.tebo.system.manager.WeChatManger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class TeboPushCustomerService {
    @Resource
    private TeboWechatUserSubscribeManger teboWechatUserSubscribeManger;
    @Resource
    private WeChatManger weChatManger;
    @Resource
    private WxPushConfig wxPushConfig;
    @Resource
    private RedisService redisService;

    private static final String TEMPLATE_ID_NOTIFY_LOTTERY = "YKGqSHhpKa9759jYOahbhf3FeT16gPQqmy5mWXdZKe8";

    private static final String appid = "wx42e36eb3f3dd94fe";


    public Boolean notifyLottery(String unionId) {
        //判断是不是已关注服务号的用户，若没关注，不推送
        WechatUserSubscribeSO wechatUserSubscribeSO = new WechatUserSubscribeSO();
        wechatUserSubscribeSO.setUnionid(unionId);
        wechatUserSubscribeSO.setOfficialAccount("泰博出行");
        String openId = teboWechatUserSubscribeManger.getSubscribeUserOpenId(wechatUserSubscribeSO);
        if (StringUtils.isEmpty(openId)) {
            return Boolean.FALSE;
        }
        String accessToken = weChatManger.getAppletAccessToken();
        WxTemplateMsg templateMsg = new WxTemplateMsg();
        // 前面准备的模板消息的id
        templateMsg.setTemplate_id(TEMPLATE_ID_NOTIFY_LOTTERY);
        // 推送参数，格式为keyword,value。可看下微信提供的demo会清楚点
        templateMsg.setData(buildWxPushData());
        // 跳转小程序
        HashMap<String, String> miniPro = new HashMap<>(2);
        miniPro.put("appid", appid);
        miniPro.put("pagepath", "activityPackages/pages/lottery/index");
        templateMsg.setMiniprogram(miniPro);
        templateMsg.setTouser(openId);
        // 发送Http请求
        String respStr = HttpUtil.post(wxPushConfig.getPushUrl() + accessToken, JSON.toJSONString(templateMsg));
        JSONObject result = JSON.parseObject(respStr);
        if (!"0".equals(result.getString("errcode"))) {
            log.info("微信消息推送失败：{}", result);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private Map<String, Object> buildWxPushData() {
        String key = "tebo_sytem:buildWxPushData:" + LocalDateUtil.getMinOfDayTime(LocalDate.now());
        if (redisService.hasKey(key)) {
            return redisService.getCacheObject(key);
        }
        Map<String, Object> data = new HashMap();
        data.put("thing6", new HashMap() {{
            put("value", "久久会员日抽奖开始啦");
        }});
        data.put("time37", new HashMap() {{
            put("value", LocalDateUtil.getMinOfDayTime(LocalDate.now()));
        }});
        data.put("time38", new HashMap() {{
            put("value", LocalDateUtil.getMinOfDayTime(LocalDate.now().plusDays(1)));
        }});
        redisService.setCacheObject(key, data, 1L, TimeUnit.DAYS);
        return data;
    }

}
