package com.tebo.system.applet.controller;


import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.common.security.util.MaintainerOnlineUserUtil;
import com.tebo.common.util.applet.AppletUtil;
import com.tebo.common.util.number.DistanceUtil;
import com.tebo.rescue.api.domain.dto.lst.ShopDto;
import com.tebo.system.api.RemoteAccountService;
import com.tebo.system.api.model.TeboAccountInfoVO;
import com.tebo.system.api.model.TeboShop;
import com.tebo.system.domain.dto.*;
import com.tebo.system.domain.so.ShopAliUpdateSO;
import com.tebo.system.domain.vo.TeboServiceFeeVO;
import com.tebo.system.domain.vo.consumer.TeboConsumerVO;
import com.tebo.system.domain.vo.shop.*;
import com.tebo.system.entity.TeboShopPostionChangeAccountDO;
import com.tebo.system.mapper.TeboShopPostionChangeAccountMapper;
import com.tebo.system.service.IShopService;
import com.tebo.system.service.ISysUserService;
import com.tebo.system.service.ITeboConsumerService;
import com.tebo.system.service.TeboSurfaceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/applet/shop")
public class TeboAppletShopController extends BaseController {
    @Resource
    private IShopService shopService;
    @Resource
    private RemoteAccountService remoteAccountService;
    @Resource
    private HttpServletRequest request;
    @Resource
    private TeboShopPostionChangeAccountMapper teboShopPostionChangeAccountMapper;
    @Resource
    private ITeboConsumerService teboConsumerService;

    @Resource
    private ISysUserService sysUserService;
    @Resource
    private TeboSurfaceService teboSurfaceService;
    /**
     * 查询合伙人下的门店
     */
    @PostMapping("/list")
    public R<List<TeboShopListVO>> page(@RequestBody TeboShopQueryDTO queryDTO) {
        if (queryDTO.getTenantId() == null) {
            throw new IllegalArgumentException("合伙人id不能为空");
        }
        List<TeboShopListVO> list = shopService.list(queryDTO);
        return R.ok(list);
    }


    /**
     * 门店详情-轮播图
     *
     * @return
     */
    @PostMapping("/carouselChart")
    public R<List<String>> carouselChart(@RequestBody NearbyShopQueryDTO nearbyShopQueryDTO) {
        Assert.notNull(nearbyShopQueryDTO.getId(), "未指定门店");

        List<String> strings = shopService.shopPicList(nearbyShopQueryDTO.getId());
        return R.ok(strings);
    }


    /**
     * 门店详情
     *
     * @return
     */
    @PostMapping("nearby/shop/detail")
    public R<AppletShopDetailVO> detail(@RequestBody NearbyShopQueryDTO nearbyShopQueryDTO) {

        AppletShopDetailVO shopDetailVO = shopService.appletDetail(nearbyShopQueryDTO);
        return R.ok(shopDetailVO);
    }


    @PostMapping("/updateShopAli")
    public R updateShopAli(@RequestBody ShopAliUpdateSO shopDto) {
        shopService.updateShopAli(shopDto);
        return R.ok();

    }

    /**
     * 同步门店会员信息
     */
    @PostMapping("/synchronousMemberShop")
    public R memberShop(@RequestBody MemberShopDTO memberShopDTO) {
        log.info("synchronousMemberShop param:{}", JSONObject.toJSONString(memberShopDTO));
        Assert.notNull(memberShopDTO.getPhoneNumber(), "手机号码不能为空");
        shopService.memberShop(memberShopDTO);
        return R.ok();

    }

    /**
     * 更新门店坐标位置
     */
    @PostMapping("/updateShopCoordinate")
    public R updateShopCoordinate(@RequestBody TeboShopDTO teboShopDTO) {
          Long accountId = MaintainerOnlineUserUtil.getUserId();
          TeboAccountInfoVO accountInfoVO = remoteAccountService.getAccountInfoById(accountId).getData();
         if(accountInfoVO == null) {
             throw new GlobalException("当前师傅账号不存在");
         }
        teboShopDTO.setId(accountInfoVO.getShopId());
        shopService.updateShopCoordinate(teboShopDTO);
        return R.ok();
    }

    /**
     * 查询门店位置是否合理
     */
    @PostMapping("/isDistanceReasonable")
    public R isisDistanceReasonableReasonable(@RequestBody TeboShopDTO teboShopDTO) {
        Long accountId = MaintainerOnlineUserUtil.getUserId();
        TeboAccountInfoVO accountInfoVO = remoteAccountService.getAccountInfoById(accountId).getData();
        TeboShopVO teboShopVO = shopService.selectShopById(accountInfoVO.getShopId());
        ShopDistanceVO shopDistanceVO = new ShopDistanceVO();
        shopDistanceVO.setReasonable(true);
        if (!ObjectUtils.isEmpty(teboShopVO.getLatitude()) && !ObjectUtils.isEmpty(teboShopVO.getLongitude())) {
            Double distance = DistanceUtil.getDistance(teboShopVO.getLongitude(), teboShopVO.getLatitude(), teboShopDTO.getLongitude(), teboShopDTO.getLatitude());
            shopDistanceVO.setDistance(distance);
            if (distance > 1) {
                shopDistanceVO.setReasonable(false);
            }
        }
        return R.ok(shopDistanceVO);
    }



    /**
     * 判断该账号是否有位置校准权限
     */
    @GetMapping("/hasPermissionChange")
    public R hasPermissionChange() {
        TeboConsumerVO teboConsumerVO = teboConsumerService.selectConsumerInfo(request);
        if (ObjectUtils.isEmpty(teboConsumerVO) || ObjectUtils.isEmpty(teboConsumerVO.getPhoneNumber())) {
            return R.ok(false);
        }
        String phoneNumber = teboConsumerVO.getPhoneNumber();
        LambdaQueryWrapper<TeboShopPostionChangeAccountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeboShopPostionChangeAccountDO::getPhoneNumber, phoneNumber);
        queryWrapper.eq(TeboShopPostionChangeAccountDO::getStatus, 2);
        if (teboShopPostionChangeAccountMapper.selectCount(queryWrapper) >= 1) {
            return R.ok(true);
        }
        return R.ok(false);
    }


    /**
     * 查询门店位置是否合理
     */
    @PostMapping("/new/isDistanceReasonable")
    public R isisDistanceReasonableReasonableNew(@RequestBody TeboShopDTO teboShopDTO) {
        TeboShopVO teboShopVO = shopService.selectShopById(teboShopDTO.getId());
        ShopDistanceVO shopDistanceVO = new ShopDistanceVO();
        shopDistanceVO.setReasonable(true);
        if (!ObjectUtils.isEmpty(teboShopVO.getLatitude()) && !ObjectUtils.isEmpty(teboShopVO.getLongitude())) {
            Double distance = DistanceUtil.getDistance(teboShopVO.getLongitude(), teboShopVO.getLatitude(), teboShopDTO.getLongitude(), teboShopDTO.getLatitude());
            shopDistanceVO.setDistance(distance);
            if (distance > 1) {
                shopDistanceVO.setReasonable(false);
            }
        }
        return R.ok(shopDistanceVO);
    }


    /**
     * 更新门店坐标位置
     */
    @PostMapping("/new/updateShopCoordinate")
    public R updateShopCoordinateNew(@RequestBody TeboShopDTO teboShopDTO) {
        teboShopDTO.setId(teboShopDTO.getId());
        shopService.updateShopCoordinate(teboShopDTO);
        return R.ok();
    }

    /**
     * 校验门店是否是超服店
     */
    @PostMapping("/vipShop")
    public R vipShop(@RequestBody TeboShopDTO teboShopDTO) {
       if (StringUtils.isEmpty(teboShopDTO.getPhoneNumber())){
         return R.ok();
       }
       TeboShop teboShop = shopService.getShopByPhoneNumber(teboShopDTO.getPhoneNumber());
       if(ObjectUtils.isEmpty(teboShop)){
           return R.ok();
       }
       if (teboShop.getVip() == 1){
           throw new ServiceException("您是超服店，为保障您的权益，请前往商家版分享");
       }
        return R.ok();
    }

    /**
     * 通过门店获取门店的自己的sap账号以及绑定的共赢商编号
     */
    @GetMapping("/getSapCode/{shopId}")
    public R<ShopSapCodeVO> getSapCode(@PathVariable("shopId")Long shopId) {
        ShopSapCodeVO shopSapCodeVO = shopService.getSapCode(shopId);
        return R.ok(shopSapCodeVO);
    }

    /**
     * 查出所有绑定了门店sap编码的供应商编码
     */
    @GetMapping("/getAllTenantSapCode")
    public R<List<String>> getAllTenantSapCode() {
        List<String> tenantSapCodeList = sysUserService.getAllTenantSapCode();
        return R.ok(tenantSapCodeList);
    }

    /**
     * 查询产品服务费设置
     */
    @PostMapping("/getServiceFeeList")
    public R<List<TeboServiceFeeVO>> getServiceFeeList(@RequestBody TeboServiceFeeDTO query) {
        return R.ok(teboSurfaceService.getServiceFeeList(query));
    }

}