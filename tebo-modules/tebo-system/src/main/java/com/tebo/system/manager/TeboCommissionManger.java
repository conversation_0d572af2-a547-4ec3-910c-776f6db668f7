package com.tebo.system.manager;

import com.tebo.system.api.domain.view.TeboCommissionRecordVO;
import com.tebo.system.entity.TeboCommissionRecordDO;

import java.util.List;

public interface TeboCommissionManger {
    List<TeboCommissionRecordVO> getTeboCommissionRecordList(Long leaderId);
    void insertCommissionRecord(TeboCommissionRecordDO teboCommissionRecordDO);
    TeboCommissionRecordVO getTeboCommissionRecordByOrderNo(String orderNo);
}