package com.tebo.lst.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tebo.lst.common.domain.dataobject.RegionDO;
import com.tebo.lst.common.domain.dto.RegionDTO;
import com.tebo.lst.common.domain.model.Region;
import com.tebo.lst.common.domain.vo.RegionTreeVo;

import java.util.List;

public interface IRegionInfoService extends IService<RegionDO> {

    /**
     * 获取区域树
     *
     * @return 区域树
     */
    List<RegionTreeVo> getRegionTree();


    /**
     * 根据区县名称查询完整的省市区信息
     * 支持模糊查询
     *
     * @param districtName 区县名称
     * @return 匹配的区域列表
     */
    List<Region> getRegionsByDistrictName(String districtName);

    /**
     * 根据区县编码查询完整的省市区信息
     *
     * @param countyCode 区县编码
     * @return 匹配的区域列表
     */
    Region selectRegionByCountyCode(String countyCode);

    List<RegionDTO> selectRegionByCountyCodeList(List<String> countyCodeList);

}