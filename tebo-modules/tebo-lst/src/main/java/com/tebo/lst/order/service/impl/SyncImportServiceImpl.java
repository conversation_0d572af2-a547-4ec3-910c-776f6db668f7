package com.tebo.lst.order.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.tebo.common.core.context.SecurityContextHolder;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.security.utils.SecurityUtils;
import com.tebo.common.util.time.LocalDateUtil;
import com.tebo.lst.common.domain.model.Region;
import com.tebo.lst.common.service.IRegionInfoService;
import com.tebo.lst.customer.domain.entity.MerchantStoreDO;
import com.tebo.lst.customer.domain.req.MerchantStoreBalanceQueryDTO;
import com.tebo.lst.customer.domain.vo.MerchantStoreDetailVo;
import com.tebo.lst.customer.service.IMerchantStoreService;
import com.tebo.lst.customer.service.MerchantStoreBalanceService;
import com.tebo.lst.customer.service.OrderBalanceService;
import com.tebo.lst.disruptor.service.DisMqService;
import com.tebo.lst.map.MapServiceManager;
import com.tebo.lst.map.dto.AddressInfo;
import com.tebo.lst.order.converter.ImportBatchConverter;
import com.tebo.lst.order.converter.OrderConverter;
import com.tebo.lst.order.domain.dataobject.ImportBatchDO;
import com.tebo.lst.order.domain.dataobject.OrderInfoDO;
import com.tebo.lst.order.domain.dto.LstOrderDTO;
import com.tebo.lst.order.domain.dto.StagingOrderImportDTO;
import com.tebo.lst.order.domain.model.*;
import com.tebo.lst.order.enums.BatchStatusEnum;
import com.tebo.lst.order.enums.CreationModeEnum;
import com.tebo.lst.order.enums.OrderOperationEnum;
import com.tebo.lst.order.enums.StagingOrderStatusEnum;
import com.tebo.lst.order.mapper.ImportBatchMapper;
import com.tebo.lst.order.mapper.StagingOrderMapper;
import com.tebo.lst.order.repository.ImportBatchRepository;
import com.tebo.lst.order.service.*;
import com.tebo.lst.product.domain.vo.ProductPriceListVo;
import com.tebo.lst.product.service.IProductLevelService;
import com.tebo.lst.shopArea.domain.ShopServerArea;
import com.tebo.lst.shopArea.service.IShopServerAreaService;
import com.tebo.lst.utils.AddressUtils;
import com.tebo.lst.utils.SystemSecurityUtils;
import com.tebo.system.api.RemoteShopService;
import com.tebo.system.api.domain.view.TeboAdditionalShopVO;
import com.tebo.system.api.domain.view.TeboMerchantShopVO;
import com.tebo.system.api.model.TeboShop;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;

/**
 * 订单导入服务
 */
@Service
@Slf4j
public class SyncImportServiceImpl implements SyncImportService {

    @Resource
    private ImportBatchRepository batchRepository;
    @Resource
    private MapServiceManager mapServiceManager;
    @Resource
    private IProductLevelService productLevelService;
    @Resource
    private IOrderInfoService orderInfoService;
    @Resource
    private IOrderSkusService orderSkusService;
    @Resource
    private IOrderOperationLogService orderOperationLogService;
    @Resource
    private IShopServerAreaService shopServerAreaService;
    @Resource
    private IRegionInfoService iRegionInfoService;

    @Resource
    private RemoteShopService remoteShopService;

    @Resource
    private DisMqService disMqService;

    /**
     * 处理单个订单
     *
     * @param batchId 批次ID
     * @param dto 订单DTO
     * @param userName 用户名
     * @param seenOrderNos 已处理的订单号集合
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void processSingleOrder(Long batchId,
                                   StagingOrderImportDTO dto,
                                   String userName,
                                   ConcurrentHashMap<String, Boolean> seenOrderNos) {
        StagingOrder stagingOrder = dto.convertToStagingOrder();
        stagingOrder.setImportType(dto.getImportType());
        stagingOrder.setSerialNumber(dto.getSerialNumber());
        stagingOrder.setAddressCode(dto.getAddressCode());
        String orderNo = stagingOrder.getOrderNo();

        if (stagingOrder.getQty() != 1){
            stagingOrder.processException("导入的excel中商品数量只能为1");
            batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
            return;
        }

        // 检查当前批次内是否重复
        if (seenOrderNos.putIfAbsent(orderNo, Boolean.TRUE) != null) {
            stagingOrder.processException("导入的excel中该订单重复,请删除后再确认导入");
            batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
            return;
        }

        if (StringUtils.isEmpty(stagingOrder.getProductSkuNo())) {
            stagingOrder.processException("导入的excel中商品编码不能为空");
            batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
            return;
        }

        if (ObjectUtil.isEmpty(stagingOrder.getSerialNumber())) {
            stagingOrder.processException("导入的excel中商品序号不能为空");
            batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
            return;
        }
        if (StringUtils.isEmpty(stagingOrder.getCustomerName())) {
            stagingOrder.processException("导入的excel中客户名称不能为空");
            batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
            return;
        }
        if (StringUtils.isEmpty(stagingOrder.getCustomerPhone())) {
            stagingOrder.processException("导入的excel中客户电话不能为空");
            batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
            return;
        }
        if (StringUtils.isEmpty(stagingOrder.getCustomerAddress())) {
            stagingOrder.processException("导入的excel中客户地址不能为空");
            batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
            return;
        }

        // 检查外部服务是否已存在该订单号
        OrderVo serviceOrderNo = orderInfoService.getOrderInfoByServiceOrderNo(orderNo);
        if (serviceOrderNo != null) {
            stagingOrder.processException("订单号已存在,请删除后再确认导入");
            batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
            return;
        }
        boolean sub = getStagingOrder(stagingOrder);
        // 截取地址进行解析，如果解析成功，则直接返回
        if (sub) {
            batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
            return;
        }
        // 其他业务逻辑（获取地区编码、价格等）
        AddressInfo addressInfo = mapServiceManager.getAdcode(stagingOrder.getCustomerAddress(), "");
        if (addressInfo == null) {
            stagingOrder.processException("地址解析失败，请完善地址信息,请删除后再确认导入");
            batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
            return;
        }
        String adcode = addressInfo.getAdCode();
        if (ObjectUtil.isNotEmpty(dto.getImportType()) && dto.getImportType() == 1){
            adcode =  dto.getAddressCode().get(2);
        }
        if (StringUtils.isEmpty(adcode)) {
            stagingOrder.processException("地址解析失败，请完善地址信息,请删除后再确认导入");
            batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
            return;
        }
        ProductPriceListVo productPrice = productLevelService.getProductPrice(stagingOrder.getProductSkuNo(), adcode);
        if (productPrice == null) {
            adcode = addressInfo.getTownCode();
            if (StringUtils.isEmpty(adcode)) {
                stagingOrder.processException("该地区未找到对应的商品价格,请删除后再确认导入");
                batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
                return;
            }
            productPrice = productLevelService.getProductPrice(stagingOrder.getProductSkuNo(), adcode);
            if (productPrice == null) {
                stagingOrder.processException("该地区未找到对应的商品价格,请删除后再确认导入");
                batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
                return;
            }
        }
        ShopServerArea serverArea = shopServerAreaService.selectRegion(adcode);
        if (serverArea == null) {
            stagingOrder.processException("未找到对应的区域服务商,请删除后再确认导入");
            batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
            return;
        }
        R<TeboAdditionalShopVO> teboShopR = remoteShopService.selectLstShopById(serverArea.getTeboShopId());
        if (teboShopR.getCode() != 200 || ObjectUtil.isEmpty(teboShopR.getData())){
            stagingOrder.processException("门店查询不到");
            batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
            return;
        }
        TeboAdditionalShopVO teboMerchantShopVO = teboShopR.getData();
        if (ObjectUtil.isEmpty(teboMerchantShopVO.getLstStatus())|| teboMerchantShopVO.getLstStatus() != 1) {
            stagingOrder.processException("门店状态未开启,请开启后再确认导入");
            batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
            return;
        }
        if (ObjectUtil.isEmpty(teboMerchantShopVO.getLstReviewStatus()) || teboMerchantShopVO.getLstReviewStatus() != 1) {
            stagingOrder.processException("门店未审核通过,请审核通过后再确认导入");
            batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
            return;
        }

        stagingOrder.assignRegionCode(adcode);
        stagingOrder.setProductSecondCategory(productPrice.getProductSecondCategory());
        stagingOrder.setProductName(productPrice.getProductName());
        stagingOrder.assignPrice(productPrice.getRefPriceFen());
        stagingOrder.setRefServicePrice(BigDecimal.valueOf(productPrice.getRefPriceFen()).multiply(BigDecimal.valueOf(0.01)).longValue());
        stagingOrder.setRefTotalPrice(BigDecimal.valueOf(productPrice.getRefPriceFen()).multiply(BigDecimal.valueOf(1.01)).longValue());

        stagingOrder.setTeboShopId(serverArea.getTeboShopId());
        stagingOrder.setTeboShopName(serverArea.getLstShopName());
        stagingOrder.setTeboShopPhone(serverArea.getPhoneNumber());
        batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
    }

    /**
     * 使用截取的地址去派单
     *
     * @param stagingOrder
     * @return
     */
    private boolean getStagingOrder(StagingOrder stagingOrder) {
        String adcode = "";
        AddressInfo addressInfo = new AddressInfo();
        if (StringUtils.isEmpty(stagingOrder.getAddressCode())){
            String subAddress = AddressUtils.truncateToAdministrativeUnit(stagingOrder.getCustomerAddress());
            // 其他业务逻辑（获取地区编码、价格等）
             addressInfo = mapServiceManager.getAdcode(subAddress, "");
            if (addressInfo == null) {
                return false;
            }
            adcode = addressInfo.getAdCode();
        }
        if (stagingOrder.getImportType() != null  && stagingOrder.getImportType() == 1 ){
            adcode = stagingOrder.getAddressCode().get(2);
        }
        if (StringUtils.isEmpty(adcode)) {
            return false;
        }
        ProductPriceListVo productPrice = productLevelService.getProductPrice(stagingOrder.getProductSkuNo(), adcode);
        if (productPrice == null) {
            adcode = addressInfo.getTownCode();
            if (StringUtils.isEmpty(adcode)) {
                return false;
            }
            productPrice = productLevelService.getProductPrice(stagingOrder.getProductSkuNo(), adcode);
            if (productPrice == null) {
                return false;
            }
        }
        ShopServerArea serverArea = shopServerAreaService.selectRegion(adcode);
        if (serverArea == null) {
            return false;
        }
        R<TeboAdditionalShopVO> teboShopR = remoteShopService.selectLstShopById(serverArea.getTeboShopId());
        if (teboShopR.getCode() != 200 || ObjectUtil.isEmpty(teboShopR.getData())){
            return false;
        }
        TeboAdditionalShopVO teboMerchantShopVO = teboShopR.getData();
        if (ObjectUtil.isEmpty(teboMerchantShopVO.getLstStatus())|| teboMerchantShopVO.getLstStatus() != 1) {
            return false;
        }
        if (ObjectUtil.isEmpty(teboMerchantShopVO.getLstReviewStatus()) || teboMerchantShopVO.getLstReviewStatus() != 1) {
            return false;
        }
        stagingOrder.assignRegionCode(adcode);
        stagingOrder.setProductSecondCategory(productPrice.getProductSecondCategory());
        stagingOrder.setProductName(productPrice.getProductName());
        stagingOrder.assignPrice(productPrice.getRefPriceFen());
        stagingOrder.setRefServicePrice(BigDecimal.valueOf(productPrice.getRefPriceFen()).multiply(BigDecimal.valueOf(0.01)).longValue());
        stagingOrder.setRefTotalPrice(BigDecimal.valueOf(productPrice.getRefPriceFen()).multiply(BigDecimal.valueOf(1.01)).longValue());
        stagingOrder.setTeboShopId(serverArea.getTeboShopId());
        stagingOrder.setTeboShopName(serverArea.getLstShopName());
        stagingOrder.setTeboShopPhone(serverArea.getPhoneNumber());
        return true;
    }

    /**
     * 确认订单
     *
     * @param batch 批次
     * @param merchantStoreDetailVo 商户门店详情
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public boolean syncConfirm(ImportBatch batch, MerchantStoreDetailVo merchantStoreDetailVo, StagingOrder stagingOrder) {
        String orderNo = stagingOrder.getOrderNo();
        // 检查外部服务是否已存在该订单号
        OrderVo serviceOrderNo = orderInfoService.getOrderInfoByServiceOrderNo(orderNo);
        if (serviceOrderNo != null) {
            batchRepository.confirmStagingOrderFail(stagingOrder.getId(),
                    "订单号已存在,请删除后再确认导入: " + orderNo);
            return true;
        }

        // 获取商品信息
        ProductPriceListVo productPrice = productLevelService.getProductPrice(stagingOrder.getProductSkuNo(), stagingOrder.getRegionCode());
        if (productPrice == null) {
            batchRepository.confirmStagingOrderFail(stagingOrder.getId(),
                    "商品价格信息不存在: " + stagingOrder.getProductSkuNo());
            return true;
        }

        OrderSkus sku = OrderSkus.createSku(
                null,  // orderId will be set after order creation
                productPrice.getProductName(),
                stagingOrder.getProductSkuNo(),
                productPrice.getProductCategory(),
                productPrice.getProductSecondCategory(),
                productPrice.getProductMode(),
                stagingOrder.getQty(),
                null,  // serviceCode 暂时为空
                stagingOrder.getRefSettlePrice()
        );

        // 4.3 创建订单（使用第一个stagingOrder的客户信息）
        MerchantStoreDO merchantStoreDO = new MerchantStoreDO();
        merchantStoreDO.setId(batch.getMerchantStoreId());
        merchantStoreDO.setMerchantName(merchantStoreDetailVo.getMerchantName());
        String orderSource = "";
        Integer creationMode = null;
        OrderOperationEnum orderOperationEnum = null;
        if (StringUtils.isEmpty(batch.getFileUrl())){
            orderSource = "CREATE";
            creationMode = CreationModeEnum.CREATE.getCode();
            orderOperationEnum = OrderOperationEnum.CREATE;
        }else {
            orderSource = "IMPORT";
            creationMode = CreationModeEnum.IMPORT.getCode();
            orderOperationEnum = OrderOperationEnum.IMPORT;
        }
        Order order = Order.createOrder(
                orderNo,
                batch.getId(),
                orderSource,  // 订单来源为导入
                stagingOrder.getRefSettlePrice(),
                null,  // 平台备注为空
                stagingOrder.getCreateBy(),
                Collections.singletonList(sku),
                merchantStoreDO,
                stagingOrder,
                creationMode,
                orderOperationEnum
        );

        // 4.4 保存订单及相关信息
        OrderInfoDO orderInfoDO = OrderConverter.toOrderInfoDO(order);
        Region region = iRegionInfoService.selectRegionByCountyCode(orderInfoDO.getCountyCode());
        if (region != null) {
            orderInfoDO.setProvinceCode(region.getProvinceCode());
            orderInfoDO.setCityCode(region.getCityCode());
        }
        if (ObjectUtil.isNotEmpty(merchantStoreDetailVo.getStoreType()) && merchantStoreDetailVo.getStoreType() == 1){
            orderInfoDO.setOrderNature(1);
        }else {
            orderInfoDO.setOrderNature(2);
        }
        orderInfoService.saveOrUpdateOrderInfo(orderInfoDO);
        order.setId(orderInfoDO.getId());
        orderSkusService.batchSaveOrderSkus(OrderConverter.toOrderSkusDOs(order));
        orderOperationLogService.recordOrderOperations(OrderConverter.toOrderOperationLogDOs(order));
        // 4.5 更新stagingOrder状态为成功
        batchRepository.confirmStagingOrderSuccess(stagingOrder.getId());
        disMqService.sendShopNotifyMsg(orderInfoDO.getTeboShopId(),orderInfoDO.getOrderNo(), LocalDateUtil.formatTime(LocalDateTime.now()),1);
        return false;
    }
}
