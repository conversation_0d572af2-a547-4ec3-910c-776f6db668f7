package com.tebo.lst.controller.merchant;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.lst.customer.domain.dto.FundTransferFormDTO;
import com.tebo.lst.customer.domain.dto.FundTransferFormReviewDTO;
import com.tebo.lst.customer.domain.req.FundTransferFormQueryDTO;
import com.tebo.lst.customer.service.FundTransferFormService;
import com.tebo.lst.utils.logs.LogRequestData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * 资金调拨单
 */
@RequestMapping("/transfer")
@Slf4j
@RestController
public class FundTransferFormController extends BaseController {
    @Resource
    private FundTransferFormService fundTransferFormService;

    /**
     * 资金调拨单列表
     */
    @LogRequestData("资金调拨单列表")
    @PostMapping("/getRefundTransferFormList")
    public TableDataInfo getRefundTransferFormList(@Valid @RequestBody FundTransferFormQueryDTO queryDTO) {
        if (queryDTO == null || queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            throw new ServiceException("分页参数不能为空");
        }
        if (Objects.nonNull(queryDTO.getCreateTimeStart())) {
            queryDTO.setCreateTimeStartSecond(queryDTO.getCreateTimeStart().atStartOfDay());
        }
        if (Objects.nonNull(queryDTO.getCreateTimeEnd())) {
            queryDTO.setCreateTimeEndSecond(queryDTO.getCreateTimeEnd().plusDays(1).atStartOfDay());
        }
        Page page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        return getDataTable(fundTransferFormService.getRefundTransferFormList(queryDTO), page);
    }


    /**
     * 新增调拨单
     */
    @LogRequestData("新增调拨单")
    @PostMapping("/addFundTransferForm")
    public R addFundTransferForm(@Valid @RequestBody FundTransferFormDTO fundTransferFormDTO) {
        fundTransferFormService.addFundTransferForm(fundTransferFormDTO);
        return R.ok();
    }


    /**
     * 删除调拨单
     */
    @LogRequestData("删除调拨单")
    @GetMapping("/deleteFundTransferForm/{ids}")
    public R deleteFundTransferForm(@PathVariable List<Long> ids) {
        fundTransferFormService.deleteFundTransferForm(ids);
        return R.ok();
    }

    /**
     * 调拨单审核
     */
    @LogRequestData("调拨单审核")
    @PostMapping("/review")
    public R review(@Valid @RequestBody FundTransferFormReviewDTO reviewDTO) {
        if (ObjectUtil.isEmpty(reviewDTO.getId())) {
            throw new ServiceException("id不能为空");
        }
        if (ObjectUtil.isEmpty(reviewDTO.getReviewStatus())) {
            throw new ServiceException("审核状态不能为空");
        }
        fundTransferFormService.review(reviewDTO);
        return R.ok();
    }

    /**
     * 调拨单修改
     */
    @LogRequestData("调拨单修改")
    @PostMapping("/update")
    public R update(@Valid @RequestBody FundTransferFormDTO fundTransferFormDTO) {
        if (ObjectUtil.isEmpty(fundTransferFormDTO.getId())) {
            throw new ServiceException("id不能为空");
        }
        fundTransferFormService.update(fundTransferFormDTO);
        return R.ok();
    }

}