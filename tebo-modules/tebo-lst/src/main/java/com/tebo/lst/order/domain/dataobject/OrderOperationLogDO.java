package com.tebo.lst.order.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tebo.lst.common.domain.BaseDO;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("order_operation_log")
public class OrderOperationLogDO extends BaseDO<OrderOperationLogDO> {


    /**
     * 主键ID
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 操作人类型
     */
    @TableField("operation_type")
    private Integer operationType;

    /**
     * 操作类型名称
     */
    @TableField("operation_value")
    private String operationValue;

    /**
     * 订单号
     */
    @TableField("service_code")
    private String serviceCode;

    @TableField("operation_img")
    private String operationImg;

    /**
     * 操作
     */
    @TableField("content")
    private String content;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 订单类型 1：服务单 2：售后单
     */
    @TableField("order_type")
    private Integer orderType;

    /**
     * 创建人id
     */
    @TableField(value = "create_by_id")
    private String createById;

    /**
     * 创建人手机号
     */
    @TableField(value = "create_by_phone")
    private String createByPhone;

    /**
     * 更新人id
     */
    @TableField(value = "update_by_id")
    private String updateById;


}
