package com.tebo.lst.order.domain.model;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.tebo.lst.common.config.LocalDateTimeConverter;
import com.tebo.lst.invoice.domain.OrderInvoiceInfoDO;
import com.tebo.lst.order.domain.dataobject.OrderInfoDO;
import com.tebo.lst.order.domain.dataobject.OrderOperationLogDO;
import com.tebo.lst.order.domain.dataobject.OrderSkusDO;
import com.tebo.lst.order.domain.dto.OrderInfoDTO;
import com.tebo.lst.order.enums.OrderStatusEnum;
import com.tebo.lst.utils.AmountUtil;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


@Data
public class OrderVo {

    @ExcelIgnore
    private Long id;
    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号", index = 0)
    private String orderNo;

    /**
     * 服务工单号
     */
    @ExcelProperty(value = "服务工单号", index = 1)
    private String serviceOrderNo;

    /**
     * 订单状态枚举对应的说明：
     * - 待分配：状态码为 0，此状态下订单尚未分配给具体的处理人员或资源。
     * - 待接单：状态码为 1，订单已分配，但相关人员还未确认接单。
     * - 服务中：状态码为 2，订单正在被处理，相关服务正在进行。
     * - 已完结：状态码为 3，订单的服务流程已经完成。
     * - 已核销：状态码为 4，订单已完成核销操作。
     * - 已结算：状态码为 5，订单涉及的费用等已经完成结算。
     * - 已提现：状态码为 6，与订单相关的款项已经完成提现操作。
     * - 挂起：状态码为 7，订单暂时停止处理，等待进一步的操作或条件满足。
     * - 已取消：状态码为 8，订单已被取消，不再进行后续处理。
     * - 服务中 - 待审核：状态码为 9，订单处于服务过程中，但需要经过审核流程。
     * - 默认状态：状态码为 -1，代表无特定状态或初始状态。
     *
     * @see OrderStatusEnum
     */
    @ExcelIgnore
    private Integer orderStatus;

    /**
     * 订单状态描述
     */
    @ExcelProperty(value = "订单状态", index = 2)
    private String orderStatusStr;

    /**
     * 价格(元)
     */
    @ExcelProperty(value = "价格(元)", index = 3)
    private String orderPrice;

    /**
     * 用户备注
     */
    @ExcelProperty(value = "用户备注", index = 4)
    private String remarkUser;
    /**
     * 平台备注
     */
    @ExcelProperty(value = "平台备注", index = 5)
    private String remarkPlatform;

    /**
     * 挂起备注
     */
    @ExcelIgnore
    private String hangingRemarks;

    /**
     * 数据来源方式 1:导入，2:创建
     */
    @ExcelIgnore
    private Integer creationMode;

    /**
     * 订单类型
     * 1 - 服务工单
     * 2 - 售后工单
     *
     * @see com.tebo.lst.order.enums.IOrderTypeEnum
     */
    @ExcelIgnore
    private Integer orderType;

    /**
     * 订单性质 1:自营 2:非自营
     */
    @ExcelIgnore
    private Integer orderNature;

    /**
     * 订单性质 1:自营 2:非自营
     */
    @ExcelProperty(value = "订单属性", index = 6)
    private String orderNatureStr;

    /**
     * 订单类型描述
     */
    @ExcelProperty(value = "订单类型", index = 6)
    private String orderTypeStr;


    /**
     * 上传完结信息时间
     */
    @ExcelProperty(value = "上传完结信息时间", index = 7, converter = LocalDateTimeConverter.class)
    private LocalDateTime uploadToAuditTime;

    /**
     * 完结审核通过时间
     */
    @ExcelProperty(value = "完结审核通过时间", index = 8, converter = LocalDateTimeConverter.class)
    private LocalDateTime auditTime;

    /**
     * 接单时间
     */
    @ExcelProperty(value = "接单时间", index = 9, converter = LocalDateTimeConverter.class)
    private LocalDateTime acceptTime;

    /**
     * 结算时间
     */
    @ExcelProperty(value = "结算时间", index = 14, converter = LocalDateTimeConverter.class)
    private LocalDateTime settlementTime;

    /**
     * 省编码
     */
    @ExcelIgnore
    private String provinceCode;

    /**
     * 市编码
     */
    @ExcelIgnore
    private String cityCode;

    /**
     * 县编码
     */
    @ExcelIgnore
    private String countyCode;

    @ExcelIgnore
    private Integer delFlag;
    @ExcelIgnore
    private String createBy;
    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间", index = 10, converter = LocalDateTimeConverter.class)
    private LocalDateTime createTime;
    @ExcelIgnore
    private String updateBy;
    @ExcelIgnore
    private LocalDateTime updateTime;
    @ExcelIgnore
    private List<OrderSkusVO> skusList;
    @ExcelIgnore
    private OrderAssignment assignments;
    @ExcelIgnore
    private List<OrderOperationLogDO> operationLogs;
    @ExcelIgnore
    private Customer customer;

    /**
     * 分配的泰博出行商家版ID
     */
    @ExcelProperty(value = "分配的泰博出行商家版ID", index = 11)
    private Long teboShopId;

    /**
     * 服务商名称
     */
    @ExcelProperty(value = "服务商名称", index = 12)
    private String teboShopName;

    /**
     * 服务商电话
     */
    @ExcelProperty(value = "服务商电话", index = 13)
    private String teboShopPhone;

    /**
     * 订单来源
     */
    @ExcelIgnore
    private String orderSource;

    /**
     * 店铺Id
     */
    @ExcelIgnore
    private String merchantId;

    /**
     * 店铺名称
     */
    @ExcelIgnore
    private String merchantStoreName;

    /**
     * 完结电池编码照片
     */
    @ExcelIgnore
    private String batteryCodeImgUrl;

    /**
     * 安装完毕照片
     */
    @ExcelIgnore
    private String installCompletedImgUrl;

    /**
     * 签收照片
     */
    @ExcelIgnore
    private String signedImgUrl;

    /**
     * 接单人员
     */
    @ExcelIgnore
    private String orderRecipient;

    /**
     * 师傅姓名
     */
    @ExcelIgnore
    private String accountName;

    /**
     * 售后订单信息
     */
    @ExcelIgnore
    private AfterSaleOrderVO afterSaleOrderVO;

    /**
     * 开票信息
     */
    @ExcelIgnore
    private OrderInvoiceInfoDO orderInvoiceInfoDO;

    public OrderVo(OrderInfoDO orderInfoDO, List<OrderSkusDO> orderSkusList, List<OrderOperationLogDO> orderOperationLogDOList,OrderInvoiceInfoDO orderInvoiceInfoDO) {
        BeanUtil.copyProperties(orderInfoDO, this);
        this.id = orderInfoDO.getId();
        this.teboShopId = orderInfoDO.getTeboShopId();
        this.teboShopName = orderInfoDO.getTeboShopName();
        this.teboShopPhone = orderInfoDO.getTeboShopPhone();
        this.orderNo = orderInfoDO.getOrderNo();
        this.serviceOrderNo = orderInfoDO.getServiceOrderNo();
        this.orderStatus = orderInfoDO.getOrderStatus();
        this.orderPrice = AmountUtil.convertFenToYuan(orderInfoDO.getOrderPrice());
        this.creationMode = orderInfoDO.getCreationMode();
        this.remarkUser = orderInfoDO.getRemarkUser();
        this.remarkPlatform = orderInfoDO.getRemarkPlatform();
        this.delFlag = orderInfoDO.getDelFlag();
        this.createBy = orderInfoDO.getCreateBy();
        this.createTime = orderInfoDO.getCreateTime();
        this.updateBy = orderInfoDO.getUpdateBy();
        this.updateTime = orderInfoDO.getUpdateTime();
        this.batteryCodeImgUrl = orderInfoDO.getBatteryCodeImgUrl();
        this.installCompletedImgUrl = orderInfoDO.getInstallCompletedImgUrl();
        this.signedImgUrl = orderInfoDO.getSignedImgUrl();
        if (CollectionUtil.isNotEmpty(orderSkusList)){
            List<OrderSkusVO> skusVOS = BeanUtil.copyToList(orderSkusList, OrderSkusVO.class);
            for (OrderSkusVO orderSkusVO : skusVOS) {
                orderSkusVO.setRefSettlePriceYuan(AmountUtil.convertFenToYuan(orderSkusVO.getRefSettlePrice()));
            }
            this.skusList = skusVOS;
        }
        this.operationLogs = orderOperationLogDOList;
        this.customer = new Customer(orderInfoDO.getCustomerName(), orderInfoDO.getCustomerPhone(), orderInfoDO.getCustomerAddress());
        this.orderSource = orderInfoDO.getOrderSource();
        this.orderInvoiceInfoDO = orderInvoiceInfoDO;
    }

    public OrderVo(OrderInfoDTO orderInfoDTO, List<OrderSkusDO> orderSkusList, List<OrderOperationLogDO> orderOperationLogDOList, OrderInvoiceInfoDO orderInvoiceInfoDO) {
        BeanUtil.copyProperties(orderInfoDTO, this);
        this.id = orderInfoDTO.getId();
        this.teboShopId = orderInfoDTO.getTeboShopId();
        this.teboShopName = orderInfoDTO.getTeboShopName();
        this.teboShopPhone = orderInfoDTO.getTeboShopPhone();
        this.orderNo = orderInfoDTO.getOrderNo();
        this.serviceOrderNo = orderInfoDTO.getServiceOrderNo();
        this.orderStatus = orderInfoDTO.getOrderStatus();
        this.orderPrice = AmountUtil.convertFenToYuan(orderInfoDTO.getOrderPrice());
        this.remarkUser = orderInfoDTO.getRemarkUser();
        this.remarkPlatform = orderInfoDTO.getRemarkPlatform();
        this.delFlag = orderInfoDTO.getDelFlag();
        this.createBy = orderInfoDTO.getCreateBy();
        this.createTime = orderInfoDTO.getCreateTime();
        this.updateBy = orderInfoDTO.getUpdateBy();
        this.updateTime = orderInfoDTO.getUpdateTime();
        List<OrderSkusVO> skusVOS = BeanUtil.copyToList(orderSkusList, OrderSkusVO.class);
        for (OrderSkusVO orderSkusVO : skusVOS) {
            orderSkusVO.setRefSettlePriceYuan(AmountUtil.convertFenToYuan(orderSkusVO.getRefSettlePrice()));
        }
        this.skusList = skusVOS;
        this.operationLogs = orderOperationLogDOList;
        this.customer = new Customer(orderInfoDTO.getCustomerName(), orderInfoDTO.getCustomerPhone(), orderInfoDTO.getCustomerAddress());
        this.orderSource = orderInfoDTO.getOrderSource();
        this.orderInvoiceInfoDO = orderInvoiceInfoDO;
    }
}
