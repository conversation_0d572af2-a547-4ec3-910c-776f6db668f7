package com.tebo.lst.product.service.impl;

import com.alibaba.csp.sentinel.util.AssertUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.util.number.CodeGenerator;
import com.tebo.lst.product.domain.entity.ProductPricingPolicyDO;
import com.tebo.lst.product.domain.req.AddPolicyReq;
import com.tebo.lst.product.domain.req.EditPolicyReq;
import com.tebo.lst.product.domain.req.QueryPolicyReq;
import com.tebo.lst.product.domain.vo.PolicyDetailItemVo;
import com.tebo.lst.product.domain.vo.PolicyDetailVo;
import com.tebo.lst.product.domain.vo.PolicyListVo;
import com.tebo.lst.product.domain.vo.ProductRegionDto;
import com.tebo.lst.product.mapper.ProductPricingPolicyMapper;
import com.tebo.lst.product.service.IProductPricingPloyService;
import com.tebo.lst.product.service.IProductPricingPolicyService;
import com.tebo.lst.utils.SystemSecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ProductPricingPolicyServiceImpl extends ServiceImpl<ProductPricingPolicyMapper, ProductPricingPolicyDO>
        implements IProductPricingPolicyService {

    @Autowired
    private ProductPricingPolicyMapper productPricingPolicyMapper;

    @Autowired
    private IProductPricingPloyService productPricingPloyService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPolicy(AddPolicyReq addPolicyReq) {
        log.info("---addPolicy:addPolicyReq:{}", addPolicyReq);
        List<ProductPricingPolicyDO> productPricingPolicyDOS = addPolicyReq.convertToProductPricingPolicyDOList();

        checkPolicy(addPolicyReq.getPloyId(), addPolicyReq.getRegionList(), addPolicyReq.getProductList());

        String policyCode = CodeGenerator.generatePolicyCode();
        productPricingPolicyDOS.forEach(productPricingPolicyDO -> {
            productPricingPolicyDO.setPolicyCode(policyCode);
            SystemSecurityUtils.fillAuditFields(productPricingPolicyDO);
        });
        batchInsertPolicies(productPricingPolicyDOS, 100);
    }


    @Override
    public List<PolicyListVo> getPolicyList(QueryPolicyReq req) {
        List<PolicyListVo> policyList = productPricingPolicyMapper.selectPolicyList(req);
        List<String> policyCodes = policyList.stream()
                .map(PolicyListVo::getPolicyCode)
                .collect(Collectors.toList());
        List<PolicyDetailItemVo> detailList = productPricingPolicyMapper.selectByPolicyCode(policyCodes);
        // 将明细列表按 policyCode 分组
        Map<String, List<PolicyDetailItemVo>> groupedDetailMap = detailList.stream()
                .collect(Collectors.groupingBy(PolicyDetailItemVo::getPolicyCode));

        // 填充每条策略的聚合数据
        for (PolicyListVo vo : policyList) {
            List<PolicyDetailItemVo> items = groupedDetailMap.get(vo.getPolicyCode());
            if (items == null || items.isEmpty()) {
                continue;
            }

            // 聚合字段
            Set<String> productIds = new LinkedHashSet<>();
            Set<String> productNames = new LinkedHashSet<>();
            Set<String> regionIds = new LinkedHashSet<>();
            Set<String> regionNames = new LinkedHashSet<>();

            for (PolicyDetailItemVo item : items) {
                if (item.getProductId() != null){
                        productIds.add(item.getProductId());
                }
                if (item.getItemProductName() != null) {
                    productNames.add(item.getItemProductName());
                }
                if (item.getRegionId() != null) {
                    regionIds.add(item.getRegionId());
                }
                if (item.getRegionName() != null) {
                    regionNames.add(item.getRegionName());
                }
            }

            // 填充进 vo 对象
            vo.setProductIds(String.join(",", productIds));
            vo.setProductNames(String.join(",", productNames));
            vo.setRegionIds(String.join(",", regionIds));
            vo.setRegionNames(String.join(",", regionNames));
        }
        return policyList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editPolicy(EditPolicyReq editPolicyReq) {
        // 使用 QueryWrapper 构建查询条件
        QueryWrapper<ProductPricingPolicyDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("policy_code", editPolicyReq.getPolicyCode());
        queryWrapper.eq("del_flag", 0); // 确保只查询未删除的记录
        // 执行查询
        List<ProductPricingPolicyDO> policyCode = productPricingPolicyMapper.selectList(queryWrapper);
        if (policyCode.isEmpty()) {
            throw new GlobalException("策略不存在！");
        }
        Long ployId = policyCode.get(0).getPloyId();
        List<String> productIds = new ArrayList<>();
        List<String> regions = new ArrayList<>();
        for (ProductPricingPolicyDO pricingPolicyDO : policyCode) {
            productIds.add(pricingPolicyDO.getProductId().toString());
            regions.add(pricingPolicyDO.getRegionId().toString());
        }
        checkEditPolicy(ployId.toString(), editPolicyReq.getRegionList(), productIds, editPolicyReq.getPolicyCode());
        for (ProductPricingPolicyDO pricingPolicyDO : policyCode) {
            if (!editPolicyReq.getRegionList().contains(pricingPolicyDO.getRegionId().toString())) {
                productPricingPolicyMapper.deleteByProductIdAndRegionId(pricingPolicyDO.getProductId(), pricingPolicyDO.getRegionId());
            }
        }
        for (String region : editPolicyReq.getRegionList()) {
            if (!regions.contains(region)) {
                ProductPricingPolicyDO productPricingPolicyDO = new ProductPricingPolicyDO();
                productPricingPolicyDO.setRegionId(Long.valueOf(region));
                productPricingPolicyDO.setPloyId(ployId);
                productPricingPolicyDO.setPolicyCode(editPolicyReq.getPolicyCode());
                productPricingPolicyDO.setProductId(Long.valueOf(productIds.get(0)));
                SystemSecurityUtils.fillAuditFields(productPricingPolicyDO);
                productPricingPolicyMapper.insert(productPricingPolicyDO);
            }
        }
    }

    @Override
    public PolicyDetailVo getPolicyDetail(String policyCode) {
        // 查出所有该策略的商品、区域明细
        List<PolicyDetailItemVo> detailList = productPricingPolicyMapper.selectByPolicyCode(Collections.singletonList(policyCode));

        if (detailList == null || detailList.isEmpty()) {
            return null;
        }

        // 取第一条填充策略基础信息
        PolicyDetailItemVo first = detailList.get(0);
        PolicyDetailVo detailVo = new PolicyDetailVo();
        detailVo.setPolicyCode(first.getPolicyCode());
        detailVo.setPloyId(first.getPloyId());
        detailVo.setPloyName(first.getPloyName());
        detailVo.setPloyCode(first.getPloyCode());
        detailVo.setProductName(first.getProductName());
        detailVo.setProductSkuCode(first.getProductSkuCode());

        // 聚合其他字段
        Set<String> productIds = new LinkedHashSet<>();
        Set<String> productNames = new LinkedHashSet<>();
        Set<String> regionIds = new LinkedHashSet<>();
        Set<String> regionNames = new LinkedHashSet<>();

        for (PolicyDetailItemVo item : detailList) {
            if (item.getProductId() != null) {
                productIds.add(item.getProductId());
            }
            if (item.getItemProductName() != null) {
                productNames.add(item.getItemProductName());
            }
            if (item.getRegionId() != null) {
                regionIds.add(item.getRegionId());
            }
            if (item.getRegionName() != null) {
                regionNames.add(item.getRegionName());
            }
        }

        detailVo.setProductIds(String.join(",", productIds));
        detailVo.setProductNames(String.join(",", productNames));
        detailVo.setRegionIds(String.join(",", regionIds));
        detailVo.setRegionNames(String.join(",", regionNames));
        detailVo.setRegionIdList(new ArrayList<>(regionIds));
        return detailVo;
    }



    private int batchInsertPolicies(List<ProductPricingPolicyDO> policies, int BATCH_SIZE) {
        int total = policies.size();
        int result = 0;
        for (int i = 0; i < total; i += BATCH_SIZE) {
            int end = Math.min(i + BATCH_SIZE, total);
            List<ProductPricingPolicyDO> batchList = policies.subList(i, end);
            result += productPricingPolicyMapper.batchInsertPolicies(batchList);
        }
        AssertUtil.isTrue(result == total, "批量插入失败！");
        return result;
    }


    private void checkPolicy(String policyId, List<String> regionList, List<String> productList) {
        AssertUtil.notNull(productPricingPloyService.getById(policyId), "策略不存在！");
        List<ProductRegionDto> productIds = productPricingPolicyMapper.selectProductIdsByRegionIds(regionList);
        // 检查商品列表和区域列表是否有交集
        if (productIds == null || productList == null) {
            return;
        }
        Set<String> set = new HashSet<>(productList);
        for(ProductRegionDto productRegionDto : productIds) {
            if (set.contains(productRegionDto.getProductId().toString())) {
                throw new GlobalException(productRegionDto.getRegionName()+":商品区域重复！");
            }
        }
    }

    private void checkEditPolicy(String policyId, List<String> regionList, List<String> productList, String policyCode) {
        AssertUtil.notNull(productPricingPloyService.getById(policyId), "策略不存在！");
        List<ProductRegionDto> productIds = productPricingPolicyMapper.selectProductIdsByRegionIdsAndPolicy(regionList, policyCode);
        // 检查商品列表和区域列表是否有交集
        if (productIds == null || productList == null) {
            return;
        }
        Set<String> set = new HashSet<>(productList);
        for (ProductRegionDto productRegionDto : productIds) {
            if (set.contains(productRegionDto.getProductId().toString())) {
                throw new GlobalException(productRegionDto.getRegionName() + ":商品区域重复！");
            }
        }
    }

    private <T> boolean hasIntersection(List<T> list1, List<T> list2) {
        if (list1 == null || list2 == null) {
            return false;
        }
        Set<T> set = new HashSet<>(list1);
        for (T element : list2) {
            if (set.contains(element)) {
                return true;
            }
        }
        return false;
    }
}