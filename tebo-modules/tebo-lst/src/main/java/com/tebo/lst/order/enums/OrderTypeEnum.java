package com.tebo.lst.order.enums;

import com.tebo.lst.common.excelConvert.CodeDescEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * 订单业务类型
 *
 * <AUTHOR>
 */
public enum OrderTypeEnum implements CodeDescEnum {
    /**
     * 订单业务类型
     */
    F1(1, "电池订单"),
    F2(2, "旧车回收"),
    F3(3, "车辆维修"),
    F4(4, "整车订单");

    private final Integer code;
    private final String desc;

    OrderTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public static final Map<Integer, String> keyInfo = new HashMap<>();
    public static final Map<String, Integer> infoKey = new HashMap<>();

    static {
        for (OrderTypeEnum type : OrderTypeEnum.values()) {
            keyInfo.put(type.getCode(), type.getDesc());
        }
    }

    static {
        for (OrderTypeEnum type : OrderTypeEnum.values()) {
            infoKey.put(type.getDesc(), type.getCode());
        }
    }
}