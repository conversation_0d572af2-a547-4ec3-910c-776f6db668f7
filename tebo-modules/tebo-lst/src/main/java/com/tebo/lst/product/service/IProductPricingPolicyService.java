package com.tebo.lst.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tebo.lst.product.domain.entity.ProductPricingPolicyDO;
import com.tebo.lst.product.domain.req.AddPolicyReq;
import com.tebo.lst.product.domain.req.EditPolicyReq;
import com.tebo.lst.product.domain.req.QueryPolicyReq;
import com.tebo.lst.product.domain.vo.PolicyDetailItemVo;
import com.tebo.lst.product.domain.vo.PolicyDetailVo;
import com.tebo.lst.product.domain.vo.PolicyListVo;

import java.util.List;

public interface IProductPricingPolicyService extends IService<ProductPricingPolicyDO> {

    /**
     * 获取策略列表（聚合）
     *
     * @return 策略列表
     */
    List<PolicyListVo> getPolicyList(QueryPolicyReq req);

    /**
     * 新增策略
     *
     * @param addPolicyReq 新增策略请求
     */
    void addPolicy(AddPolicyReq addPolicyReq);

    /**
     * 编辑策略
     *
     * @param editPolicyReq 编辑策略请求
     */
    void editPolicy(EditPolicyReq editPolicyReq);

    /**
     * 策略详情
     *
     * @param policyCode 策略编码
     */
    PolicyDetailVo getPolicyDetail(String policyCode);
}