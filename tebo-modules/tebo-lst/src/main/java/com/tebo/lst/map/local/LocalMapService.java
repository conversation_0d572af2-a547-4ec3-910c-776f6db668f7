package com.tebo.lst.map.local;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.csp.sentinel.util.AssertUtil;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.lst.common.domain.model.Region;
import com.tebo.lst.common.service.IRegionInfoService;
import com.tebo.lst.map.config.MapServiceProperties;
import com.tebo.lst.map.core.AbstractMapService;
import com.tebo.lst.map.core.ServiceHealth;
import com.tebo.lst.map.dto.AddressInfo;
import com.tebo.lst.utils.AddrResolutUtil;
import com.tebo.lst.utils.repeat.AddressParserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
public class LocalMapService extends AbstractMapService {

    private final String serviceName = "localMapService";

    private final IRegionInfoService regionInfoService;

    public LocalMapService(IRegionInfoService regionInfoService) {
        super(Collections.singletonList(new MapServiceProperties.KeyConfig("123", 1)), new ServiceHealth("localMapService", Integer.MAX_VALUE));
        this.regionInfoService = regionInfoService;
    }

    @Override
    public AddressInfo getAdcode(String address, String city) throws ServiceException {
        Map<String, String> resolute = AddressParserUtil.resolve(address);
        boolean getAdCode = true;
        List<Region> finalResult = new ArrayList<>();
        List<Region> regionList = null;
        if (StringUtils.isEmpty(resolute.get("area"))) {
            getAdCode = false;
        }else {
            regionList = regionInfoService.getRegionsByDistrictName(resolute.get("area"));
            if (CollectionUtil.isEmpty(regionList)) {
                getAdCode = false;
            }else {
                regionList.forEach(item ->{
                    if (address.contains(item.getCityName()) || address.contains(item.getProvinceName())){
                        finalResult.add(item);
                    }
                });
            }
        }
//        if (StringUtils.isNotEmpty(regionList.get(0).getDistrictCode()) && regionList.get(0).getDistrictCode().length() == 6){
//            getAdCode = false;
//        }
        if (!getAdCode) {
            resolute = AddrResolutUtil.resolut(address);
            if (StringUtils.isEmpty(resolute.get("area"))) {
                log.error("调用本地地图失败，未找到对应的区域信息，地址：{}", address);
                throw new ServiceException("未找到对应的区域信息");
            }
            regionList = regionInfoService.getRegionsByDistrictName(resolute.get("area"));
            if (CollectionUtil.isEmpty(regionList)) {
                log.error("调用本地地图失败，未找到对应的区域信息，地址：{}", address);
                throw new ServiceException("未找到对应的区域信息");
            }else {
                regionList.forEach(item ->{
                    if (address.contains(item.getCityName()) || address.contains(item.getProvinceName())){
                        finalResult.add(item);
                    }
                });
            }
            if (ObjectUtils.isEmpty(finalResult)){
                log.error("调用本地地图失败，未找到对应的区域信息，地址：{}", address);
                throw new ServiceException("未找到对应的区域信息");
            }
            if (StringUtils.isEmpty(finalResult.get(0).getDistrictCode())) {
                log.error("调用本地地图失败，区域编码无效，地址：{}，区域编码：{}", address, regionList.get(0).getDistrictCode());
                throw new ServiceException("区域编码无效");
            }
        }
//        AssertUtil.isTrue(regionList != null && regionList.size() == 1, "未找到对应的区域信息");
//        AssertUtil.isTrue(StringUtils.isNotEmpty(regionList.get(0).getDistrictCode()) && regionList.get(0).getDistrictCode().length() == 6, "区域编码无效");
        if (ObjectUtils.isEmpty(finalResult)){
            log.error("调用本地地图失败，未找到对应的区域信息，地址：{}", address);
            throw new ServiceException("未找到对应的区域信息");
        }
        AddressInfo addressInfo = new AddressInfo();
        addressInfo.setDistrict(finalResult.get(0).getDistrictName());
        addressInfo.setCity(finalResult.get(0).getCityName());
        addressInfo.setProvince(finalResult.get(0).getProvinceName());
        addressInfo.setAdCode(finalResult.get(0).getDistrictCode());
        log.info("调用本地地图成功");
        return addressInfo;
    }

    @Override
    public String getServiceName() {
        return serviceName;
    }
}
