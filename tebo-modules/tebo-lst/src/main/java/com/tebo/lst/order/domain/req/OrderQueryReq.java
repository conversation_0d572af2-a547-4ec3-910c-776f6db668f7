package com.tebo.lst.order.domain.req;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project tebo-back
 * @description
 * @date 2025/3/20 08:59:04
 */
@Data
public class OrderQueryReq {

    /**
     * 订单号/买家会员名/接单门店
     */
    private String kyeWord;

    /**
     * 订单状态
     */
    private Integer orderStatus;


    /**
     * 商品二级分类
     */
    private String productSecondCategory;

    /**
     * 发起时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 门店ID
     */
    private String teboShopId;


    /**
     * 创建人
     */
    private String createBy;

    /**
     * 商户ID
     */
    private String merchantId;


    private Integer pageNum;


    private Integer pageSize;

    /**
     * 订单状态
     */
    private List<Integer> orderStatusList;
}
