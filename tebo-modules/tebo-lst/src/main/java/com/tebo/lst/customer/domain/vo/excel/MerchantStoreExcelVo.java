package com.tebo.lst.customer.domain.vo.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class MerchantStoreExcelVo {
    /**
     * 店铺名称
     */
    @ExcelProperty("商铺名称")
    private String merchantName;

    /**
     * 店铺编码
     */
    @ExcelProperty("商铺编码")
    private String merchantCode;

    /**
     * 用户登录的账号
     */
    @ExcelProperty("登录账号")
    private String userName;

    /**
     * 店铺联系人名称
     */
    @ExcelProperty("商铺老板")
    private String contactName;

    /**
     * 商铺属性 1:自营商铺 2:非自营商铺
     */
    @ExcelIgnore()
    private Integer storeType;

    /**
     * 商铺属性 1:自营商铺 2:非自营商铺
     */
    @ExcelProperty("商铺类型")
    private String storeTypeStr;

    @ExcelProperty("商铺来源")
    private String merchantType;

    /**
     * 店铺联系人电话
     */
    @ExcelProperty("联系电话")
    private String contactPhone;

    /**
     * 账号状态 0:正常 1：停用
     */
    @ExcelProperty("账号状态")
    private String userStatus;

    /**
     * 导单状态  1:启用 2:禁用
     */
    @ExcelProperty("店铺状态")
    private String enabledStatusStr;

    /**
     * 店铺状态 1:待审核 2:审核通过 3:审核驳回
     */
    @ExcelProperty("审核状态")
    private String merchantStatusStr;


    /**
     * 认证类型 1:商铺入驻 2：个人入驻
     */
    @ExcelIgnore()
    private Integer approvalMethod;

    @ExcelProperty("入驻类型")
    private String approvalMethodStr;

    /**
     * 启用状态
     */
    @ExcelIgnore()
    private Integer enabledStatus;


    /**
     * 审核状态
     */
    @ExcelIgnore()
    private Integer merchantStatus;


    @ExcelIgnore()
    private List<Integer> merchantTypeList;

    @ExcelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
}
