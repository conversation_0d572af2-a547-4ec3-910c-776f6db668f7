package com.tebo.lst.order.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tebo.lst.common.domain.BaseDO;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName("staging_order")
public class StagingOrderDO extends BaseDO<StagingOrderDO> {

    /**
     * 导入批次
     */
    @TableField("batch_id")
    private Long batchId;

    /**
     * 序号
     */
    @TableField("serial_number")
    private Integer serialNumber;

    /**
     * 订单编码
     */
    @TableField("order_no")
    private String orderNo;


    /**
     * 门店ID
     */
    @TableField("tebo_shop_id")
    private Long teboShopId;

    /**
     * 服务商店铺名称
     */
    @TableField("tebo_shop_name")
    private String teboShopName;

    /**
     * 服务商店铺电话
     */
    @TableField("tebo_shop_phone")
    private String teboShopPhone;


    /**
     * 客户名称
     */
    @TableField("customer_name")
    private String customerName;

    /**
     * 客户联系电话
     */
    @TableField("customer_phone")
    private String customerPhone;

    /**
     * 客户地址
     */
    @TableField("customer_address")
    private String customerAddress;

    /**
     * 区域编码（第三级的区域编码）
     */
    @TableField("region_code")
    private String regionCode;

    /**
     * 商品编码
     */
    @TableField("product_sku_no")
    private String productSkuNo;

    //二级分类
    @TableField("product_second_category")
    private String productSecondCategory;

    //商品型号
    @TableField("product_name")
    private String productName;

    /**
     * 商品数量
     */
    @TableField("qty")
    private Integer qty;

    /**
     * 客户备注
     */
    @TableField("remark_usr")
    private String remarkUsr;

    /**
     * 订单收取费用
     */
    @TableField("ref_settle_price")
    private Long refSettlePrice;

    /**
     * 总计费用
     */
    @TableField("ref_total_price")
    private Long refTotalPrice;

    /**
     * 平台服务费
     */
    @TableField("ref_service_price")
    private Long refServicePrice;

    /**
     * -1:已取消，1：待确认，2：成功，3：失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 异常原因
     */
    @TableField("exception_message")
    private String exceptionMessage;

    /**
     * 错误原因
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 处理时间
     */
    @TableField("processed_time")
    private LocalDateTime processedTime;

}