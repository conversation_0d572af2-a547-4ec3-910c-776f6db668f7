package com.tebo.lst.order.service;

import com.tebo.lst.order.domain.dataobject.OrderInfoDO;
import com.tebo.lst.order.domain.model.OrderCountVo;
import com.tebo.lst.order.domain.model.OrderVo;
import com.tebo.lst.order.domain.param.OrderQueryParam;
import com.tebo.lst.order.domain.req.*;
import com.tebo.lst.req.order.OrderStatusChangeReq;
import com.tebo.lst.resp.BusinessStats;

import java.util.List;

public interface IOrderInfoService {
    /**
     * 保存或更新订单主信息
     * 如果订单ID为空则创建新订单，否则更新已有订单
     */
    void saveOrUpdateOrderInfo(OrderInfoDO orderInfo);

    /**
     * 根据订单ID查询订单主信息
     */
    OrderVo getOrderInfoById(Long orderId);

    OrderVo getPcOrderDetail(Long orderId);

    /**
     * 商家版小程序订单详情
     * @param orderId
     * @return
     */
    OrderVo getOrderDetail(Long orderId);

    /**
     * 根据服务订单号查询订单主信息
     */
    OrderVo getOrderInfoByServiceOrderNo(String orderNo);

    /**
     * 修改订单状态
     *
     * @param req 订单状态修改请求
     */
    void updateOrderStatus(OrderStatusChangeReq req);

    /**
     * 查询订单列表
     */
    List<OrderVo> getOrderList(OrderQueryReq orderQueryReq);

    /**
     * 导出服务工单
     */
    List<OrderVo> exportOrderList(OrderQueryReq orderQueryReq);

    /**
     * 查询订单列表(商家版)
     */
    List<OrderVo> getOrderListForMerchant(OrderQuery4ShopReq orderQuery4ShopReq);

    /**
     * 查询订单数量(云门店)
     */
    OrderCountVo getOrderCount(OrderQuery4ShopReq orderQuery4ShopReq);

    /**
     * 接单
     */
    void acceptOrder(OrderStatusChangeReq req);

    /**
     * 完结订单
     */
    void completeOrder(CompleteOrderReq req);

    /**
     * 取消订单
     */
    void cancelOrder(Long orderId);

    /**
     * 取消订单(门店)
     */
    void cancelOrderByMerchant(Long orderId);

    /**
     * 强制完结订单
     */
    void forceCompleteOrder(Long orderId);

    /**
     * 完结确认
     */
    void confirmationComplete(Long orderId);

    /**
     * 审核订单
     */
    void audit(AuditOrderReq req);

    /**
     * 挂起订单
     */
    void suspendOrder(SuspendOrderReq req);


    /**
     * 修改订单
     */
    void updateOrder(EditOrderReq editOrderReq, Boolean plat);

    void updateOrderUserRemark(OrderUpdateReq editOrderReq);

    /**
     * 结算订单
     */
    void importSettleOrder(BatchSettlementOrderReq batchSettlementOrderReq);


    /**
     * 获取首页订单统计数据
     */
    BusinessStats getBusinessStats(OrderQueryParam orderQueryParam);

    /**
     * 挂起时候清除服务商信息
     */
    void clearShopInfo(Long orderId);
}
