package com.tebo.lst.customer.service;

import com.tebo.lst.customer.domain.dto.PrePayReviewDTO;
import com.tebo.lst.customer.domain.dto.PreRechargeRecordDTO;
import com.tebo.lst.customer.domain.req.PreRechargeQueryDTO;
import com.tebo.lst.customer.domain.vo.LstPrePayReviewRecordVO;
import com.tebo.lst.customer.domain.vo.LstPrePayVO;

import java.util.List;

/**
 * 预支付接口
 */
public interface PreRechargeRecordService {
    /**
     * 预充值
     */
    void preRecharge(PreRechargeRecordDTO preRechargeRecordDTO);
    /***
     * 审核
     */
    void review(PrePayReviewDTO prePayReviewDTO);

    /**
     * 预充值列表
     */
    List<LstPrePayVO> getPreRechargeList(PreRechargeQueryDTO queryDTO);

    LstPrePayVO getLatestPreRechargeByUserId(Long userId);

    List<LstPrePayReviewRecordVO> getPreRechargeRecordList(Long id);
}