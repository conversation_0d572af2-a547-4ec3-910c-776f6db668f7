package com.tebo.lst.controller.admin;

import com.alibaba.csp.sentinel.util.AssertUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.context.SecurityContextHolder;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.common.redis.service.RedisService;
import com.tebo.lst.common.constants.RedisPreConstants;
import com.tebo.lst.customer.domain.vo.MerchantStoreDetailVo;
import com.tebo.lst.customer.service.IMerchantStoreService;
import com.tebo.lst.product.domain.entity.ProductLevelDO;
import com.tebo.lst.product.domain.req.*;
import com.tebo.lst.product.domain.vo.*;
import com.tebo.lst.product.enums.LevelTypeEnum;
import com.tebo.lst.product.enums.ShelfStatusEnum;
import com.tebo.lst.product.service.IProductLevelService;
import com.tebo.lst.utils.logs.LogRequestData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 商品/型号包管理
 */
@RestController("adminProductLevelController")
@RequestMapping("/admin/product")
@Slf4j
public class ProductLevelController extends BaseController {

    @Autowired
    private IProductLevelService productLevelService;

    @Resource
    private RedisService redisService;


    /**
     * 添加商品/型号包
     *
     * @param addProductReq 商品/型号包信息
     */
    @LogRequestData("添加商品")
    @PostMapping("/add")
    public R<Void> addProduct(@Valid @RequestBody AddProductReq addProductReq) {
        validAddProductDto(addProductReq);
        productLevelService.addProductLevel(addProductReq);
        return R.ok();
    }

    /**
     * 查询商品列表（按区域：价格）
     *
     * @param queryProductReq 查询商品列表请求参数
     */
    @PostMapping("/price/list")
    @LogRequestData("查询商品列表（按区域：价格）")
    public TableDataInfo getProductPriceList(@Valid @RequestBody QueryProductReq queryProductReq) {
        Page<Object> page = PageHelper.startPage(queryProductReq.getPageNum(), queryProductReq.getPageSize());
        List<ProductPriceListVo> productPriceList = productLevelService.getProductPriceList(queryProductReq);
        return getDataTable(productPriceList, page);
    }

    /**
     * 获取商品导出地址
     */
    @GetMapping("/export/url")
    @LogRequestData("获取商品导出地址")
    public R<String> getProductExportUrl() {
        String url = redisService.getCacheObject(RedisPreConstants.GOODS_EXCEL_DOWNLOAD_URL);
        return R.ok(url);
    }

    /**
     * 查询商品列表(单品)
     *
     * @param queryProductReq 查询商品列表请求参数
     */
    @PostMapping("/sku/list")
    @LogRequestData("查询商品列表(单品)")
    public R<List<ProductSkuListVo>> getProductSkuList(@Valid @RequestBody QueryProductReq queryProductReq) {
        return R.ok(productLevelService.getProductSkuList(queryProductReq));
    }

    /**
     * 查询商品列表(单品)
     *
     */
    @PostMapping("/sku/page")
    @LogRequestData("查询商品列表(单品)")
    public TableDataInfo getProductPageList(@Valid @RequestBody QueryProductReq queryProductReq) {
        Page<Object> page = PageHelper.startPage(queryProductReq.getPageNum(), queryProductReq.getPageSize());
        return getDataTable(productLevelService.getProductSkuPageList(queryProductReq), page);
    }

    /**
     * 商品导出
     *
     * @param queryProductReq 查询商品列表请求参数
     */
    @PostMapping("/sku/export")
    @LogRequestData("商品列表导出(单品)")
    public void skuExport(@Valid @RequestBody QueryProductReq queryProductReq, HttpServletResponse httpServletResponse) {
        productLevelService.productExport(httpServletResponse,queryProductReq);
    }

    /**
     * 获取商品详情
     *
     * @param productId 商品ID
     */
    @GetMapping("/getProductDetail/{productId}")
    @LogRequestData("获取商品详情")
    public R<ProductDetailVo> getProductDetail(@PathVariable Long productId) {
        return R.ok(productLevelService.getProductInfoDetail(productId));
    }

    /**
     * 编辑商品/型号包
     *
     * @param editProductReq 商品/型号包信息
     */
    @PostMapping("/edit/level/product")
    @LogRequestData("编辑商品")
    public R<Void> editProduct(@Valid @RequestBody EditProductReq editProductReq) {
        productLevelService.editProductLevel(editProductReq);
        return R.ok();
    }


    private void validAddProductDto(AddProductReq addProductReq) {
        if (addProductReq.getLevelType().equals(LevelTypeEnum.FIRST_LEVEL.getCode())) {
            return;
        } else if (addProductReq.getLevelType().equals(LevelTypeEnum.SECOND_LEVEL.getCode())) {
            AssertUtil.notEmpty(addProductReq.getProductSkuCode(), "商品编码（SKU编码）不能为空！");
            AssertUtil.notEmpty(addProductReq.getProductBrand(), "商品品牌不能为空！");
//            AssertUtil.notEmpty(addProductReq.getProductMode(), "商品型号不能为空！");
            AssertUtil.notEmpty(addProductReq.getProductSecondCategory(), "二级分类不能为空！");
        } else {
            throw new IllegalArgumentException("请求参数异常！");
        }
    }

    /**
     * 商品批量上下架
     */
    @PostMapping("/batch/shelf")
    @LogRequestData("商品批量上下架")
    public R<Void> batchShelf(@RequestBody BatchShelfReq batchShelfReq) {
        productLevelService.updateProductShelfStatus(batchShelfReq);
        return R.ok();
    }


    /**
     * 添加型号包
     *
     * @param addLevelReq 型号包信息
     */
    @LogRequestData("添加型号包")
    @PostMapping("/add/level")
    public R<Void> addProductLevel(@Valid @RequestBody AddLevelReq addLevelReq) {
        productLevelService.addLevelGroup(addLevelReq);
        return R.ok();
    }

    /**
     * 编辑型号包
     *
     * @param editLevelReq 型号包信息
     */
    @LogRequestData("编辑型号包")
    @PostMapping("/edit/level")
    public R<Void> editProductLevel(@Valid @RequestBody EditLevelReq editLevelReq) {
        productLevelService.editLevelGroup(editLevelReq);
        return R.ok();
    }

    /**
     * 型号包列表（含物料）
     */
    @PostMapping("/level/list")
    @LogRequestData("型号包列表（含物料）")
    public TableDataInfo getLevelList(@Valid @RequestBody ProductLevelAndProductReq req) {
        Page<Object> page = PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<ProductLevelAndProductDTO> data = productLevelService.selectProductLevelAndProduct(req);
        return getDataTable(data, page);
    }

    /**
     * 查询型号包列表(不含物料)
     */
    @PostMapping("/level/list/simple")
    @LogRequestData("查询型号包列表(不含物料)")
    public TableDataInfo getLevelListSimple(@Valid @RequestBody ProductLevelQueryReq req) {
        Page<Object> page = PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<ProductLevelDO> data = productLevelService.selectProductLevelAndProduct(req);
        return getDataTable(data, page);
    }

    /**
     * 查询型号包详情
     */
    @GetMapping("/level/detail/{id}")
    @LogRequestData("查询型号包详情")
    public R<ProductLevelAndProductDetailDTO> getLevelDetail(@PathVariable Long id) {
        return R.ok(productLevelService.selectProductLevelAndProductDetail(id));
    }

}
