package com.tebo.lst.product.enums;

public enum ShelfStatusEnum {

    /**
     * 下架
     */
    UNSHELF(0, "下架"),
    /**
     * 上架
     */
    SHELF(1, "上架");

    private final Integer code;
    private final String desc;

    ShelfStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ShelfStatusEnum getByCode(Integer code) {
        for (ShelfStatusEnum shelfStatusEnum : ShelfStatusEnum.values()) {
            if (shelfStatusEnum.getCode().equals(code)) {
                return shelfStatusEnum;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
