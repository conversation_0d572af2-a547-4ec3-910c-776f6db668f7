package com.tebo.lst.controller;

import com.tebo.common.core.domain.R;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.lst.common.domain.vo.RegionTreeVo;
import com.tebo.lst.common.service.IRegionInfoService;
import com.tebo.lst.disruptor.service.DisMqService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 区域信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common/region")
@Slf4j
public class RegionInfoController extends BaseController {


    @Autowired
    private IRegionInfoService regionInfoService;
    @Resource
    private DisMqService disMqService;

    /**
     * 获取父子树
     */
    @GetMapping("/getRegionTree")
    public R<List<RegionTreeVo>> getRegionTree() {
        return R.ok(regionInfoService.getRegionTree());
    }
    /**
     * 获取父子树
     */
    @GetMapping("/sendShopNotifyMsg/{shopId}")
    public R<List<RegionTreeVo>> sendShopNotifyMsg(@PathVariable("shopId") Long shopId) {
        disMqService.sendShopNotifyMsg(shopId, "tbcx0001", "2025-12-30 12:30:00", 1);
        return R.ok();
    }
}
