package com.tebo.lst.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tebo.lst.customer.domain.dto.AddMerchantStoreDto;
import com.tebo.lst.customer.domain.dto.MerchantReviewDTO;
import com.tebo.lst.customer.domain.dto.MerchantUserDTO;
import com.tebo.lst.customer.domain.dto.UpdateMerchantStoreDto;
import com.tebo.lst.customer.domain.entity.MerchantStoreDO;
import com.tebo.lst.customer.domain.req.MerchantStoreListReq;
import com.tebo.lst.customer.domain.vo.MerchantStoreDetailVo;
import com.tebo.lst.customer.domain.vo.MerchantStoreListVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface IMerchantStoreService extends IService<MerchantStoreDO> {


    /**
     * 商铺列表
     *
     * @param merchantStoreListReq 查询条件
     * @return 商铺列表
     */
    List<MerchantStoreListVo> getList(MerchantStoreListReq merchantStoreListReq);

    /**
     * 平台获取商铺详情
     *
     * @return 商铺详情
     */
    MerchantStoreDetailVo getDetailById(Long id);

    /**
     * 商铺信息
     *
     * @param updateMerchantStoreDto 商铺信息
     */
    void updateStoreInfoById(UpdateMerchantStoreDto updateMerchantStoreDto);


    /**
     * 启用商铺
     *
     * @param id 商铺id
     */
    void enable(Long id);

    /**
     * 启用/禁用 所有商铺的导单功能
     */
    void updateEnableStatus(Integer status);

    /**
     * 禁用商铺
     *
     * @param id 商铺id
     */
    void disable(Long id);

    /**
     * 注册商铺信息
     *
     * @param addMerchantStoreDto 注册商铺信息
     */
    void registerStoreInfo(AddMerchantStoreDto addMerchantStoreDto);

    /**
     * 获取商铺信息
     *
     * @return 商铺详情
     */
    MerchantStoreDetailVo getDetailByUserId(Long userId);

    /**
     * 更新商铺信息
     *
     * @param updateMerchantStoreDto 更新商铺信息
     */
    void updateStoreInfoByUserId(UpdateMerchantStoreDto updateMerchantStoreDto);

    /**
     * 店铺审核
     */
    void review(MerchantReviewDTO merchantReviewDTO);

    /**
     * 重置用户密码
     */
    void resetPwd(List<Long> ids);

    /**
     * 停用账号
     */
    void changeUserStatus(MerchantUserDTO merchantUserDTO);

    boolean getMerchantEnableStatus();

    void export(HttpServletResponse response, MerchantStoreListReq merchantStoreListReq);
}
