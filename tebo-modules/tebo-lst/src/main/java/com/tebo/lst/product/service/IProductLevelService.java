package com.tebo.lst.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tebo.lst.product.domain.entity.ProductLevelDO;
import com.tebo.lst.product.domain.req.*;
import com.tebo.lst.product.domain.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface IProductLevelService extends IService<ProductLevelDO> {

    /**
     * 添加商品或型号包
     *
     * @param addProductReq 商品/型号包信息
     */
    void addProductLevel(AddProductReq addProductReq);

    /**
     * 获取商品列表(按区域：价格)
     *
     * @param queryProductReq 查询商品请求
     * @return 商品列表
     */
    List<ProductPriceListVo> getProductPriceList(QueryProductReq queryProductReq);

    /**
     * 获取商品列表(单品)
     *
     * @param queryProductReq 查询商品请求
     * @return 商品列表
     */
    List<ProductSkuListVo> getProductSkuList(QueryProductReq queryProductReq);

    void productExport(HttpServletResponse response,QueryProductReq queryProductReq);

    /**
     * 商品列表分页
     */
    List<ProductSkuListVo> getProductSkuPageList(QueryProductReq queryProductReq);


    /**
     * 获取商品详情
     *
     * @param productId 商品ID
     * @return 商品详情
     */
    ProductDetailVo getProductInfoDetail(Long productId);

    /**
     * 编辑商品或型号包
     *
     * @param editProductReq 商品/型号包信息
     */
    void editProductLevel(EditProductReq editProductReq);

    /**
     * 查询商品价格
     */
    ProductPriceListVo getProductPrice(String productSkuCode, String regionId);

    /**
     * 批量上下架
     */
    void updateProductShelfStatus(BatchShelfReq batchShelfReq);

    /**
     * 添加型号包
     */
    void addLevelGroup(AddLevelReq addLevelReq);

    /**
     * 编辑型号包
     */
    void editLevelGroup(EditLevelReq editLevelReq);

    /**
     * 查詢型号包（含物料）
     */
    List<ProductLevelAndProductDTO> selectProductLevelAndProduct(ProductLevelAndProductReq req);

    /**
     * 查詢型号包详情
     */
    ProductLevelAndProductDetailDTO selectProductLevelAndProductDetail(Long id);

    /**
     * 查询型号包列表(不含物料)
     */
    List<ProductLevelDO> selectProductLevelAndProduct(ProductLevelQueryReq req);
}