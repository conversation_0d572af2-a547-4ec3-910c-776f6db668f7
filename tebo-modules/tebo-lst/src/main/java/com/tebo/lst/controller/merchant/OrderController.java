package com.tebo.lst.controller.merchant;


import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.context.SecurityContextHolder;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.page.LstTableDataInfo;
import com.tebo.common.security.annotation.RequiresPermissions;
import com.tebo.lst.customer.domain.vo.MerchantStoreDetailVo;
import com.tebo.lst.customer.service.IMerchantStoreService;
import com.tebo.lst.order.domain.model.OrderVo;
import com.tebo.lst.order.domain.req.*;
import com.tebo.lst.order.enums.IOrderTypeEnum;
import com.tebo.lst.order.service.AfterSaleOrderService;
import com.tebo.lst.order.service.impl.OrderInfoService;
import com.tebo.lst.utils.ExcelExportUtil;
import com.tebo.lst.utils.logs.LogRequestData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 订单列表（店铺）
 */
@Slf4j
@RestController
@RequestMapping("/order")
public class OrderController extends BaseController {

    @Autowired
    private OrderInfoService orderInfoService;

    @Resource
    private IMerchantStoreService merchantStoreService;

    @Resource
    private AfterSaleOrderService afterSaleOrderService;

    /**
     * 查询订单详情
     */
    @GetMapping("/detail")
    @LogRequestData("查询服务订单详情")
    public R<OrderVo> getOrderDetail(@RequestParam("orderId") Long orderId) {
        setMerchantName();
        OrderVo orderVo = orderInfoService.getPcOrderDetail(orderId);
        return R.ok(orderVo);
    }

    private void setMerchantName() {
        String role = SecurityContextHolder.getUserRole();
        if (role.contains("lst:default:role") || role.contains("lst:approved:role")) {
            MerchantStoreDetailVo merchantStoreDetailVo = merchantStoreService.getDetailByUserId(SecurityContextHolder.getUserId());
            if (merchantStoreDetailVo == null) {
                throw new ServiceException("请先完善商户信息");
            }
            SecurityContextHolder.setUserName(merchantStoreDetailVo.getMerchantName());
        }
    }

    /**
     * 查询服务订单列表
     */
    @PostMapping("/list")
    @LogRequestData("查询服务订单列表")
    public LstTableDataInfo<OrderVo> getOrderList(@RequestBody OrderQueryReq orderQueryReq) {
        String role = SecurityContextHolder.getUserRole();
        if (role.contains("lst:default:role") || role.contains("lst:approved:role")) {
            MerchantStoreDetailVo merchantStoreDetailVo = merchantStoreService.getDetailByUserId(SecurityContextHolder.getUserId());
            if (merchantStoreDetailVo == null) {
                return new LstTableDataInfo<>();
            }
            Long storeId = merchantStoreDetailVo.getId();
            orderQueryReq.setMerchantId(storeId.toString());
        }
        Page<Object> page = PageHelper.startPage(orderQueryReq.getPageNum(), orderQueryReq.getPageSize());
        List<OrderVo> orderList = orderInfoService.getOrderList(orderQueryReq);
        return getLstDataTable(orderList, page);
    }

    /**
     * 导出服务订单列表
     */
    @PostMapping("/export")
    @LogRequestData("导出服务订单列表")
    public ResponseEntity<byte[]> exportOrderList(@RequestBody OrderQueryReq orderQueryReq) {
        String role = SecurityContextHolder.getUserRole();
        if (role.contains("lst:default:role") || role.contains("lst:approved:role")) {
            MerchantStoreDetailVo merchantStoreDetailVo = merchantStoreService.getDetailByUserId(SecurityContextHolder.getUserId());
            if (merchantStoreDetailVo == null) {
                throw new ServiceException("请先完善商户信息");
            }
            Long storeId = merchantStoreDetailVo.getId();
            orderQueryReq.setMerchantId(storeId.toString());
        }
        List<OrderVo> orderList = orderInfoService.exportOrderList(orderQueryReq);
        return ExcelExportUtil.export(
                orderList,
                "服务订单列表.xlsx",
                OrderVo.class
        );
    }

    /**
     * 取消服务订单
     */
    @PostMapping("/cancel")
    @LogRequestData("取消服务订单")
    public R<Void> cancelOrder(@RequestParam("orderId") Long orderId) {
        setMerchantName();
        orderInfoService.cancelOrder(orderId);
        return R.ok();
    }

    /**
     * 取消服务订单(店铺操作)
     */
    @PostMapping("/cancelByMerchant")
    @LogRequestData("取消服务订单")
    public R<Void> cancelOrderByMerchant(@RequestParam("orderId") Long orderId) {
        setMerchantName();
        orderInfoService.cancelOrder(orderId);
        return R.ok();
    }


    /**
     * 平台端强制完结按钮
     */
    @PostMapping("/complete")
    @LogRequestData("强制完结服务订单")
    public R<Void> completeOrder(@RequestParam("orderId") Long orderId) {
        orderInfoService.forceCompleteOrder(orderId);
        return R.ok();
    }

    /**
     * 商铺端完结确认按钮 非自营订单
     */
    @PostMapping("/confirmationComplete")
    @LogRequestData("完结确认")
    public R<Void> confirmationComplete(@RequestParam("orderId") Long orderId) {
        setMerchantName();
        orderInfoService.confirmationComplete(orderId);
        return R.ok();
    }

    /**
     * 审核服务订单
     */
    @PostMapping("/audit")
    @LogRequestData("审核服务订单")
    public R<Void> auditOrder(@RequestBody @Validated AuditOrderReq auditOrderReq) {
        setMerchantName();
        orderInfoService.audit(auditOrderReq);
        return R.ok();
    }

    /**
     * 修改服务订单
     */
    @PostMapping("/update")
    @LogRequestData("修改服务订单")
    public R<Void> updateOrder(@RequestBody @Validated EditOrderReq editOrderReq) {
        setMerchantName();
        String role = SecurityContextHolder.getUserRole();
        Boolean plat = true;
        if (role.contains("lst:default:role") || role.contains("lst:approved:role")) {
            plat = false;
        }
        orderInfoService.updateOrder(editOrderReq, plat);
        return R.ok();
    }

    /**
     * 平台端-修改服务订单用户备注
     */
    @PostMapping("/updateUserRemark")
    @LogRequestData("修改服务订单用户备注")
    public R<Void> updateUserRemark(@RequestBody @Validated OrderUpdateReq updateReq) {
        orderInfoService.updateOrderUserRemark(updateReq);
        return R.ok();
    }

    /**
     * 挂起订单
     */
    @PostMapping("/suspend")
    @LogRequestData("挂起订单")
    public R<Void> suspendOrder(@RequestBody @Validated SuspendOrderReq req) {
        setMerchantName();
        if (IOrderTypeEnum.SERVICE_ORDER.getCode().equals(req.getOrderType())) {
            orderInfoService.suspendOrder(req);
        } else {
            afterSaleOrderService.suspendAfterSaleOrder(req);
        }
        return R.ok();
    }

    /**
     * 平台端挂起订单
     */
    @PostMapping("/platSuspendOrder")
    @LogRequestData("平台端挂起订单")
    public R platSuspendOrder(@RequestBody @Validated SuspendOrderReq req) {
        if (IOrderTypeEnum.SERVICE_ORDER.getCode().equals(req.getOrderType())) {
            orderInfoService.suspendOrder(req);
        } else {
            afterSaleOrderService.suspendAfterSaleOrder(req);
        }
        return R.ok();
    }
}
