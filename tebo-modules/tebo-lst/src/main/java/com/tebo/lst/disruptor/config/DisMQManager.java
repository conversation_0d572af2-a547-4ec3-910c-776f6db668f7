package com.tebo.lst.disruptor.config;

import com.lmax.disruptor.BlockingWaitStrategy;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import com.tebo.lst.disruptor.consumer.DisEventHandler;
import com.tebo.lst.disruptor.domain.DisMsgData;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR> ZhangFan
 * @date : 2024/1/12 10:05
 * @Desc :
 */
@Configuration
public class DisMQManager {


    @Bean("messageModel")
    public RingBuffer<DisMsgData> messageModelRingBuffer() {
        // 获取CPU的处理器数量
        int curSystemThreads = Runtime.getRuntime().availableProcessors() + 1;

        // 定义用于事件处理的线程池， Disruptor通过java.util.concurrent.ExecutorSerivce提供的线程来触发consumer的事件处理
        ExecutorService executor = Executors.newFixedThreadPool(curSystemThreads);

        // 指定事件工厂
        DisEventFactory factory = new DisEventFactory();

        // 指定 RingBuffer 字节大小，必须为2的N次方（能将求模运算转为位运算提高效率），否则将影响效率
        int bufferSize = 1024 * 256;

        // 单线程模式，获取额外的性能
        Disruptor<DisMsgData> disruptor = new Disruptor<>(factory, bufferSize, executor,
                ProducerType.SINGLE, new BlockingWaitStrategy());

        // 设置事件业务处理器---消费者
        disruptor.handleEventsWith(new DisEventHandler());

        // 启动disruptor线程
        disruptor.start();

        // 获取 RingBuffer 环，用于接取生产者生产的事件
        RingBuffer<DisMsgData> ringBuffer = disruptor.getRingBuffer();

        return ringBuffer;
    }
}
