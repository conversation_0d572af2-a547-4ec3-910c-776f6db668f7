package com.tebo.lst.controller.domain;

import lombok.Data;

import java.util.List;

@Data
public class TeboHFAppletPayDTO {
    // 支付必填参数
    /**
     * 130 商家联盟保证金 140 商家联盟交易订单
     */
    private Integer businessType;
    /**
     * 商品描述
     */
    private String goodsDesc;
    /**
     * 交易金额（分）
     */
    private Integer amount;

    /**
     * 保证金级别
     * 1 500
     * 2 1000
     * 3 1500
     * 4 3000
     */
    private Integer level;

    /**
     * 订单id
     */
    private String remark;

    /**
     * 用户openid
     */
    private String subOpenid;

    // 业务参数
    /**
     * 1 泰博出行 2 泰博出行-商家版
     */
    private Integer appletSource;

    /**
     * 微信用户标识
     */
    private String unionId;

    /**
     * 订单编号
     */
    private String orderNo;

    private String openId;

    private Long shopId;

    /**
     * 子订单id
     */
    private Long orderId;
    /**
     * 实际退款
     */
    private Integer cancelAmount;
    /**
     * 退款手续费
     */
    private Integer cancelFeeAmount;

    /**
     * 退款回调类型 1 订单退款 2 保证金退款
     */
    private Integer type;

}
