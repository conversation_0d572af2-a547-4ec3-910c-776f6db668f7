package com.tebo.lst.order.enums;

/**
 * 订单属性
 *
 */
public enum OrderNatureEnum {
    SELF(1, "自营订单"),
    NONSELF(2, "非自营订单");
    private int code;
    private String msg;

    OrderNatureEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static OrderNatureEnum getEnum(int code) {
        for (OrderNatureEnum typeEnum : OrderNatureEnum.values()) {
            if (typeEnum.getCode() == code) {
                return typeEnum;
            }
        }
        return null;
    }



    public int getCode() {
        return code;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setCode(int code) {
        this.code = code;
    }
}
