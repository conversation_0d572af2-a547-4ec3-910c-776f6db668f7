package com.tebo.lst.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.context.SecurityContextHolder;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.util.time.LocalDateUtil;
import com.tebo.lst.common.aspectj.ReLock;
import com.tebo.lst.common.constants.RedisPreConstants;
import com.tebo.lst.disruptor.service.DisMqService;
import com.tebo.lst.order.domain.dataobject.AfterSaleOrderDO;
import com.tebo.lst.order.domain.dataobject.OrderOperationLogDO;
import com.tebo.lst.order.domain.model.AfterSaleOrderVO;
import com.tebo.lst.order.domain.model.OrderVo;
import com.tebo.lst.order.domain.req.AddAfterSaleOrderReq;
import com.tebo.lst.order.domain.req.CompleteOrderReq;
import com.tebo.lst.order.domain.req.SuspendOrderReq;
import com.tebo.lst.order.domain.req.QueryAfterSaleOrderReq;
import com.tebo.lst.order.enums.AfterSaleOrderOperationEnum;
import com.tebo.lst.order.enums.AfterSaleOrderStatusEnum;
import com.tebo.lst.order.enums.IOrderTypeEnum;
import com.tebo.lst.order.enums.OrderStatusEnum;
import com.tebo.lst.order.mapper.AfterSaleOrderMapper;
import com.tebo.lst.order.mapper.OrderOperationLogMapper;
import com.tebo.lst.order.service.AfterSaleOrderService;
import com.tebo.lst.req.aftersaleOrder.AfterSaleOrderStatusChangeReq;
import com.tebo.lst.req.aftersaleOrder.DispatchAfterSaleOrderReq;
import com.tebo.lst.resp.BusinessStats;
import com.tebo.lst.utils.SnowflakeIdWorker;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class AfterSaleOrderServiceImpl extends ServiceImpl<AfterSaleOrderMapper, AfterSaleOrderDO> implements AfterSaleOrderService {

    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private OrderInfoService orderInfoService;
    @Resource
    private OrderOperationLogService orderOperationLogService;
    @Resource
    private OrderOperationLogMapper orderOperationLogMapper;

    @Resource
    private DisMqService disMqService;

    /**
     * 增加售后订单
     *
     * @param req 订单号
     * @return 售后单
     */
    @Override
    @ReLock(prefix = RedisPreConstants.AFTER_ORDER_LOCK,key = "#req.getOrderId()")
    public void addAfterSaleOrder(AddAfterSaleOrderReq req) {
        OrderVo orderVo = orderInfoService.getOrderInfoById(req.getOrderId());
        if (!OrderStatusEnum.BO3.getCode().equals(orderVo.getOrderStatus()) && !OrderStatusEnum.BO5.getCode().equals(orderVo.getOrderStatus())) {
            throw new ServiceException("服务工单未完结，不可创建售后工单");
        }
        List<AfterSaleOrderDO> saleOrderDOList = afterSaleOrderMapper.selectList(
                new QueryWrapper<AfterSaleOrderDO>()
                        .eq("order_id", req.getOrderId())
        );
        for (AfterSaleOrderDO afterSaleOrderDO : saleOrderDOList) {
            if (AfterSaleOrderStatusEnum.getExistingCodes().contains(afterSaleOrderDO.getStatus())) {
                throw new ServiceException("已存在进行中的售后订单");
            }
        }
        AfterSaleOrderDO afterSaleOrderDO = new AfterSaleOrderDO();
        afterSaleOrderDO.setOrderId(req.getOrderId());
        afterSaleOrderDO.setTeboShopId(orderVo.getTeboShopId());
        afterSaleOrderDO.setTeboShopName(orderVo.getTeboShopName());
        afterSaleOrderDO.setStatus(AfterSaleOrderStatusEnum.ASSIGNED_PENDING_ACCEPTANCE.getCode());
        afterSaleOrderDO.setAfterSaleOrderNo(generateSaleOrderNo());
        afterSaleOrderDO.setOrderNo(orderVo.getOrderNo());
        afterSaleOrderDO.setFailureType(req.getFailureType());
        afterSaleOrderDO.setFailureDescription(req.getFailureDescription());
        afterSaleOrderDO.setPhotoPath(JSON.toJSONString(req.getPhotoPathList()));
        afterSaleOrderDO.setCreateBy(SecurityContextHolder.getUserId().toString());
        afterSaleOrderMapper.insert(afterSaleOrderDO);
        addLog(afterSaleOrderDO, AfterSaleOrderOperationEnum.CREATE);
        disMqService.sendShopNotifyMsg(afterSaleOrderDO.getTeboShopId(),afterSaleOrderDO.getOrderNo(), LocalDateUtil.formatTime(LocalDateTime.now()),2);

    }

    /**
     * 增加售后单操作日志
     *
     * @param afterSaleOrderDO   售后单
     * @param orderOperationEnum 操作类型
     */
    private void addLog(AfterSaleOrderDO afterSaleOrderDO, AfterSaleOrderOperationEnum orderOperationEnum) {

        OrderOperationLogDO orderOperationLogDO = new OrderOperationLogDO();
        orderOperationLogDO.setOrderId(afterSaleOrderDO.getId());
        orderOperationLogDO.setOperationType(orderOperationEnum.getCode());
        orderOperationLogDO.setOperationValue(orderOperationEnum.getDesc());
        orderOperationLogDO.setServiceCode(afterSaleOrderDO.getAfterSaleOrderNo());
        orderOperationLogDO.setContent(orderOperationEnum.getDesc());
        orderOperationLogDO.setCreateById(SecurityContextHolder.getUserId().toString());
        orderOperationLogDO.setCreateBy(SecurityContextHolder.getUserName());
        orderOperationLogDO.setCreateByPhone(SecurityContextHolder.getUserPhone());
        orderOperationLogDO.setOrderType(IOrderTypeEnum.AFTER_SALE_ORDER.getCode());
        orderOperationLogService.recordOrderOperations(Collections.singletonList(orderOperationLogDO));
    }

    /**
     * 生成售后单号
     */
    public static String generateSaleOrderNo() {
        String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        return "SH" + dateStr + SnowflakeIdWorker.generateLongId();
    }

    /**
     * 分页查询售后单
     *
     * @param queryAfterSaleOrderReq
     * @return 售后单
     */
    @Override
    public List<AfterSaleOrderVO> selectAfterSaleOrderPage(QueryAfterSaleOrderReq queryAfterSaleOrderReq) {
        List<AfterSaleOrderVO> afterSaleOrderVOList = afterSaleOrderMapper.selectAfterSaleOrderPage(queryAfterSaleOrderReq);
        for (AfterSaleOrderVO afterSaleOrderVO : afterSaleOrderVOList) {
            afterSaleOrderVO.setPhotoPathList(JSON.parseArray(afterSaleOrderVO.getPhotoPath(), String.class));
        }
        return afterSaleOrderVOList;
    }

    /**
     * 根据售后单id查询售后单
     *
     * @param afterSaleOrderId 售后单id
     * @return 售后单
     */
    @Override
    public AfterSaleOrderVO getAfterOrderInfoById(Long afterSaleOrderId) {
        AfterSaleOrderDO afterSaleOrderDO = afterSaleOrderMapper.selectById(afterSaleOrderId);
        if (afterSaleOrderDO == null) {
            throw new ServiceException("售后单不存在");
        }
        OrderVo orderVo = orderInfoService.getOrderInfoById(afterSaleOrderDO.getOrderId());
        AfterSaleOrderVO afterSaleOrderVO = BeanUtil.copyProperties(afterSaleOrderDO, AfterSaleOrderVO.class);
        afterSaleOrderVO.setPhotoPathList(JSON.parseArray(afterSaleOrderVO.getPhotoPath(), String.class));
        afterSaleOrderVO.setOrderVo(orderVo);
        List<OrderOperationLogDO> orderOperationLogDOList = orderOperationLogMapper.selectList(
                new QueryWrapper<OrderOperationLogDO>().eq("order_id", afterSaleOrderId).eq("order_type", IOrderTypeEnum.AFTER_SALE_ORDER.getCode())
        );
        afterSaleOrderVO.setOperationLogDOS(orderOperationLogDOList);
        return afterSaleOrderVO;
    }

    /**
     * 售后单挂起
     *
     * @return 售后单号
     */
    @Override
    @ReLock(prefix = RedisPreConstants.AFTER_ORDER_LOCK,key = "#orderReq.getOrderId()")
    @Transactional
    public void suspendAfterSaleOrder(SuspendOrderReq orderReq) {
        AfterSaleOrderDO afterSaleOrderDO = afterSaleOrderMapper.selectById(orderReq.getOrderId());
        if (afterSaleOrderDO == null) {
            throw new ServiceException("售后单不存在");
        }
//        if (!OrderStatusEnum.BO2.getCode().equals(afterSaleOrderDO.getStatus())) {
//            throw new ServiceException("售后单状态不正确");
//        }
        AfterSaleOrderStatusChangeReq req = new AfterSaleOrderStatusChangeReq();
        req.setAfterSaleOrderId(orderReq.getOrderId());
        req.setAfterSaleOrderOperation(AfterSaleOrderOperationEnum.SUSPEND.getCode());
        req.setRemark(orderReq.getRemark());
        clearShopInfo(orderReq.getOrderId());
        updateAfterSaleOrderStatus(req);
    }

    /**
     * 售后单更新状态
     *
     * @param req
     * @return 售后单号
     */
    @Override
    @ReLock(prefix = RedisPreConstants.AFTER_ORDER_LOCK,key = "#req.getAfterSaleOrderId()")
    public void updateAfterSaleOrderStatus(AfterSaleOrderStatusChangeReq req) {
        AfterSaleOrderDO afterSaleOrderDO = afterSaleOrderMapper.selectById(req.getAfterSaleOrderId());
        if (afterSaleOrderDO == null) {
            throw new ServiceException("售后单不存在");
        }
        AfterSaleOrderOperationEnum orderOperationEnum = AfterSaleOrderOperationEnum.of(req.getAfterSaleOrderOperation());
        if (orderOperationEnum == null) {
            throw new ServiceException("售后单操作类型不正确");
        }
        afterSaleOrderDO.setStatus(orderOperationEnum.getAfterSaleOrderStatusEnum().getCode());
        if (req.getTeboShopId() != null) {
            afterSaleOrderDO.setTeboShopId(req.getTeboShopId());
            afterSaleOrderDO.setTeboShopName(req.getTeboShopName());
        }
        if (AfterSaleOrderOperationEnum.COMPLETE.getCode().equals(req.getAfterSaleOrderOperation())) {
            afterSaleOrderDO.setBatteryCodeImgUrl(req.getBatteryCodeImgUrlStr());
            afterSaleOrderDO.setInstallCompletedImgUrl(req.getInstallCompletedImgUrlStr());
            afterSaleOrderDO.setSignedImgUrl(req.getSignedImgUrlStr());
            afterSaleOrderDO.setUploadTime(LocalDateTime.now());
            afterSaleOrderDO.setAfterSalesInstructions(req.getAfterSalesInstructions());
        }else if (AfterSaleOrderOperationEnum.ACCEPT.getCode().equals(req.getAfterSaleOrderOperation())) {
            afterSaleOrderDO.setAcceptTime(LocalDateTime.now());
        }
        afterSaleOrderMapper.updateById(afterSaleOrderDO);
        addLog(afterSaleOrderDO, orderOperationEnum);
    }

    /**
     * 售后单接单
     *
     * @param afterSaleOrderId
     * @return 售后单号
     */
    @Override
    @ReLock(prefix = RedisPreConstants.AFTER_ORDER_LOCK,key = "#afterSaleOrderId")
    public void acceptAfterSaleOrder(Long afterSaleOrderId) {
        AfterSaleOrderDO afterSaleOrderDO = afterSaleOrderMapper.selectById(afterSaleOrderId);
        if (afterSaleOrderDO == null) {
            throw new ServiceException("售后单不存在");
        }
        if (!AfterSaleOrderStatusEnum.ASSIGNED_PENDING_ACCEPTANCE.getCode().equals(afterSaleOrderDO.getStatus())) {
            throw new ServiceException("售后单状态不正确");
        }
        AfterSaleOrderStatusChangeReq req = new AfterSaleOrderStatusChangeReq();
        req.setAfterSaleOrderId(afterSaleOrderId);
        req.setAfterSaleOrderOperation(AfterSaleOrderOperationEnum.ACCEPT.getCode());
        req.setRemark(req.getRemark());
        updateAfterSaleOrderStatus(req);
    }

    /**
     * 售后单派单
     *
     * @param req
     * @return 售后单号
     */
    @Override
    @ReLock(prefix = RedisPreConstants.AFTER_ORDER_LOCK,key = "#req.getAfterSaleOrderId()")
    public void dispatchAfterSaleOrder(DispatchAfterSaleOrderReq req) {
        AfterSaleOrderDO afterSaleOrderDO = afterSaleOrderMapper.selectById(req.getAfterSaleOrderId());
        if (afterSaleOrderDO == null) {
            throw new ServiceException("售后单不存在");
        }
        if (!AfterSaleOrderStatusEnum.PENDING_ASSIGNMENT.getCode().equals(afterSaleOrderDO.getStatus()) && !AfterSaleOrderStatusEnum.SUSPENDED.getCode().equals(afterSaleOrderDO.getStatus()) &&
                !AfterSaleOrderStatusEnum.ASSIGNED_PENDING_ACCEPTANCE.getCode().equals(afterSaleOrderDO.getStatus())) {
            throw new ServiceException("售后单状态不正确");
        }
        AfterSaleOrderStatusChangeReq changeReq = new AfterSaleOrderStatusChangeReq();
        changeReq.setAfterSaleOrderId(req.getAfterSaleOrderId());
        changeReq.setAfterSaleOrderOperation(AfterSaleOrderOperationEnum.DELIVER.getCode());
        changeReq.setTeboShopId(req.getTeboShopId());
        changeReq.setTeboShopName(req.getTeboShopName());
        changeReq.setRemark(req.getRemark());
        updateAfterSaleOrderStatus(changeReq);
        disMqService.sendShopNotifyMsg(afterSaleOrderDO.getTeboShopId(),afterSaleOrderDO.getOrderNo(), LocalDateUtil.formatTime(LocalDateTime.now()),2);
    }

    /**
     * 售后单完成
     *
     * @param req
     * @return 售后单号
     */
    @Override
    @ReLock(prefix = RedisPreConstants.AFTER_ORDER_LOCK,key = "#req.getOrderId()")
    public void completeAfterSaleOrder(CompleteOrderReq req) {
        if (StringUtils.isEmpty(req.getAfterSalesInstructions())){
            throw new ServiceException("售后说明不能为空");
        }
        if (CollectionUtil.isEmpty(req.getInstallCompletedImgUrlList())){
            throw new ServiceException("安装完毕照片不能为空");
        }
        AfterSaleOrderDO afterSaleOrderDO = afterSaleOrderMapper.selectById(req.getOrderId());
        if (afterSaleOrderDO == null) {
            throw new ServiceException("售后单不存在");
        }
        if (!AfterSaleOrderStatusEnum.ACCEPTED_PENDING_COMPLETION.getCode().equals(afterSaleOrderDO.getStatus())) {
            throw new ServiceException("售后单状态不正确");
        }
        AfterSaleOrderStatusChangeReq changeReq = new AfterSaleOrderStatusChangeReq();
        changeReq.setAfterSaleOrderId(req.getOrderId());
        changeReq.setAfterSaleOrderOperation(AfterSaleOrderOperationEnum.COMPLETE.getCode());
        String batteryCodeImgUrlStr = req.getBatteryCodeImgUrlList() == null ? null : String.join(",", req.getBatteryCodeImgUrlList());
        String installCompletedImgUrlStr = req.getInstallCompletedImgUrlList() == null ? null : String.join(",", req.getInstallCompletedImgUrlList());
        String signedImgUrlStr = req.getSignedImgUrlList() == null ? null : String.join(",", req.getSignedImgUrlList());
        changeReq.setBatteryCodeImgUrlStr(batteryCodeImgUrlStr);
        changeReq.setInstallCompletedImgUrlStr(installCompletedImgUrlStr);
        changeReq.setSignedImgUrlStr(signedImgUrlStr);
        updateAfterSaleOrderStatus(changeReq);
    }

    /**
     * 售后单3天未接单设置为超时(任务)
     *
     * @return 售后单号
     */
    @Override
    public void setAfterSaleOrderTimeout() {
        List<AfterSaleOrderDO> afterSaleOrderDOList = afterSaleOrderMapper.selectList(
                new QueryWrapper<AfterSaleOrderDO>()
                        .eq("status", AfterSaleOrderStatusEnum.ASSIGNED_PENDING_ACCEPTANCE.getCode())
                        .lt("dispatch_time", LocalDateTime.now().minusDays(3))
        );
        for (AfterSaleOrderDO afterSaleOrderDO : afterSaleOrderDOList) {
            afterSaleOrderDO.setTimeOutStatus(1);
            afterSaleOrderMapper.updateById(afterSaleOrderDO);
        }
    }

    /**
     * 获取首页售后订单统计数据
     *
     * @param req
     */
    @Override
    public BusinessStats getBusinessStats(QueryAfterSaleOrderReq req) {
        BusinessStats businessStats = new BusinessStats();
        Page<Object> totalOrder = PageHelper.startPage(1, 1);
        afterSaleOrderMapper.selectAfterSaleOrderPage(req);
        businessStats.setTotalOrders(totalOrder.getTotal());

        Page<Object> completeOrder = PageHelper.startPage(1, 1);
        req.setOrderStatusList(Collections.singletonList(AfterSaleOrderStatusEnum.COMPLETED.getCode()));
        afterSaleOrderMapper.selectAfterSaleOrderPage(req);
        businessStats.setCompletedOrders(completeOrder.getTotal());

        Page<Object> ongoingOrder = PageHelper.startPage(1, 1);
        req.setOrderStatusList(Arrays.asList(0,1,2,7));
        afterSaleOrderMapper.selectAfterSaleOrderPage(req);
        businessStats.setOngoingOrders(ongoingOrder.getTotal());

        Page<Object> abnormalOrder = PageHelper.startPage(1, 1);
        req.setOrderStatusList(Collections.singletonList(AfterSaleOrderStatusEnum.SETTLED.getCode()));
        afterSaleOrderMapper.selectAfterSaleOrderPage(req);
        businessStats.setAbnormalOrders(abnormalOrder.getTotal());
        return businessStats;
    }

    @Override
    public void clearShopInfo(Long orderId){
        afterSaleOrderMapper.clearShopInfo(orderId);
    }
}