package com.tebo.lst.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 商家联盟支付表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Getter
@Setter
@TableName("tebo_pay_record")
public class TeboPayRecordDO extends Model<TeboPayRecordDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 1:泰博出行  2:泰博出行-商家端
     */
    @TableField("`source`")
    private Integer source;

    /**
     * 业务类型
     */
    @TableField("business_type")
    private Integer businessType;

    /**
     * 订单金额以分为单位
     */
    @TableField("amount")
    private Integer amount;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 订单描述
     */
    @TableField("description")
    private String description;

    /**
     * 微信用户标识
     */
    @TableField("open_id")
    private String openId;

    /**
     * 微信用户标识
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 支付状态 1 预支付成功   2 支付成功  3 超时取消 10 退款中 11 已退款
     */
    @TableField("pay_status")
    private Integer payStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time",  fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 第三方交易流水号
     */
    @TableField("transaction_id")
    private String transactionId;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
