package com.tebo.lst.service.order;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tebo.lst.order.domain.dataobject.AfterSaleOrderDO;
import com.tebo.lst.order.domain.dataobject.OrderInfoDO;
import com.tebo.lst.order.mapper.AfterSaleOrderMapper;
import com.tebo.lst.order.mapper.OrderInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class TeboOrderCommonServiceImpl implements TeboOrderCommonService{

    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;

    /**
     * -- 服务工单
     * select count(1) from order_info
     * where order_status not in (5,8)
     * and tebo_shop_id = '1000000000000000008'
     * -- 售后工单
     * select * from after_sale_order
     * where status not in (3,4)
     * and tebo_shop_id = 1920731937954070528
     * @param shopId
     * @return
     */
    @Override
    public Map<String, Object> checkRefundShopPay(Long shopId) {
        Map<String, Object> result = new HashMap<>();
        LambdaQueryWrapper<OrderInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderInfoDO::getTeboShopId, shopId);
        queryWrapper.notIn(OrderInfoDO::getOrderStatus, 5, 8);
        if (orderInfoMapper.selectCount(queryWrapper) > 0) {
            result.put("code", 201);
            result.put("msg", "该门店存在未结算服务工单，请先通知门店处理完结后再退保证金");
            return result;
        }
        LambdaQueryWrapper<AfterSaleOrderDO> afterSaleOrder = new LambdaQueryWrapper<>();
        afterSaleOrder.eq(AfterSaleOrderDO::getTeboShopId, shopId);
        afterSaleOrder.notIn(AfterSaleOrderDO::getStatus, 3, 4);
        if (afterSaleOrderMapper.selectCount(afterSaleOrder) > 0) {
            result.put("code", 202);
            result.put("msg", "该门店存在未完结售后工单，请先通知门店处理完结后再退保证金");
            return result;
        }
        result.put("code", 200);
        return result;
    }
}
