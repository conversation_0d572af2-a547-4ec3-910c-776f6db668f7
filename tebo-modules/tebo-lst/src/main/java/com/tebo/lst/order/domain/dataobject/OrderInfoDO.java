package com.tebo.lst.order.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tebo.lst.common.domain.BaseDO;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@TableName("order_info")
public class OrderInfoDO extends BaseDO<OrderInfoDO> {
    /**
     * 师傅id
     */
    @TableField("employee_id")
    private Long employeeId;

    /**
     * 师傅电话
     */
    @TableField("employee_phone")
    private String employeePhone;

    /**
     * 店铺id
     */
    @TableField("merchant_id")
    private Long merchantId;

    /**
     * 批次id
     */
    @TableField("batch_id")
    private Long batchId;

    /**
     * 店铺名称
     */
    @TableField("merchant_store_name")
    private String merchantStoreName;

    /**
     * 服务商店铺id（云门店）
     */
    @TableField("tebo_shop_id")
    private Long teboShopId;

    /**
     * 服务商店铺名称
     */
    @TableField("tebo_shop_name")
    private String teboShopName;

    /**
     * 服务商店铺电话
     */
    @TableField("tebo_shop_phone")
    private String teboShopPhone;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 服务单号
     */
    @TableField("service_order_no")
    private String serviceOrderNo;

    /**
     * 订单类型
     */
    @TableField("order_type")
    private Integer orderType;

    /**
     * 订单属性 1:自营订单 2：非自营订单
     */
    @TableField("order_nature")
    private Integer orderNature;

    /**
     * 总计费用
     */
    @TableField("ref_total_price")
    private Long refTotalPrice;

    /**
     * 平台服务费
     */
    @TableField("ref_service_price")
    private Long refServicePrice;


    /**
     * 上传完结信息时间
     */
    @TableField("upload_to_audit_time")
    private LocalDateTime uploadToAuditTime;

    /**
     * 完结审核通过时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;
    /**
     * 接单时间
     */
    @TableField("accept_time")
    private LocalDateTime acceptTime;

    /**
     * 结算时间
     */
    @TableField("settlement_time")
    private LocalDateTime settlementTime;


    /**
     * 订单来源
     */
    @TableField("order_source")
    private String orderSource;

    /**
     * 订单状态
     */
    @TableField("order_status")
    private Integer orderStatus;

    /**
     * 订单价格
     */
    @TableField("order_price")
    private Long orderPrice;

    /**
     * 省编码
     */
    @TableField("province_code")
    private String provinceCode;

    /**
     * 市编码
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 县编码
     */
    @TableField("county_code")
    private String countyCode;

    /**
     * 收货人姓名
     */
    @TableField("customer_name")
    private String customerName;

    /**
     * 收货人电话
     */
    @TableField("customer_phone")
    private String customerPhone;

    /**
     * 收货详细地址
     */
    @TableField("customer_address")
    private String customerAddress;

    /**
     * 用户备注
     */
    @TableField("remark_user")
    private String remarkUser;

    /**
     * 平台备注
     */
    @TableField("remark_platform")
    private String remarkPlatform;

    /**
     * 挂起备注
     */
    @TableField("hanging_remarks")
    private String hangingRemarks;

    /**
     * 创建方式 1:导入，2:创建
     */
    @TableField("creation_mode")
    private Integer creationMode;

    /**
     * 完结电池编码照片
     */
    @TableField("battery_code_img_url")
    private String batteryCodeImgUrl;

    /**
     * 安装完毕照片
     */
    @TableField("install_completed_img_url")
    private String installCompletedImgUrl;

    /**
     * 签收照片
     */
    @TableField("signed_img_url")
    private String signedImgUrl;
}