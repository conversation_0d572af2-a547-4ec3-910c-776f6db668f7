package com.tebo.lst.order.domain.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project tebo-back
 * @description
 * @date 2025/3/20 08:59:04
 */
@Data
public class OrderUpdateReq {

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空")
    private Long orderId;

    /**
     * 用户备注
     */
    private String remarkUser;

    /**
     * 平台备注
     */
    private String remarkPlatform;
}
