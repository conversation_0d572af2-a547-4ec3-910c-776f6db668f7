package com.tebo.lst.product.domain.vo;

import com.tebo.lst.utils.AmountUtil;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


@Data
public class ProductSkuListVo {

    /**
     * 商品ID
     */
    private String id;

    /**
     * 父ID，默认为0
     */
    private String parentId = "0";

    /**
     * 商品/分类名称
     */
    private String productName;

    /**
     * SKU编码
     */
    private String productSkuCode;

    /**
     * 商品品类
     */
    private String productCategory;

    /**
     * 第二分类
     */
    private String productSecondCategory;

    /**
     * 商品型号
     */
    private String productMode;

    /**
     * 商品图片URL
     */
    private String productImage;

    /**
     * 商品图片URL
     */
    private List<String> productDetailImages;

    /**
     * 基准价格/成本价(分)
     */
    private Long productBasePrice;

    /**
     * 基准价格/成本价(元)
     */
    private String productBasePriceYuan;

    /**
     * 商品品牌
     */
    private String productBrand;

    /**
     * 商品描述
     */
    private String productDesc;

    /**
     * 结算基准价格
     */
    private Long refBasePrice;

    /**
     * 结算基准价格
     */
    private String refBasePriceYuan;


    private String refBasePriceStr;

    /**
     * 购买方式 (1: 以旧换新, 2: 直接购买)
     */
    private Integer buyMethod;

    /**
     * 类型 (1: 分类, 2: 商品)，默认为1
     */
    private Integer levelType;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private Integer shelfStatus;

    public void calculateRefBasePriceYuan() {
        if (refBasePrice != null) {
            refBasePriceYuan = AmountUtil.convertFenToYuan(refBasePrice);
            refBasePriceStr = AmountUtil.convertFenToYuan(refBasePrice);
        }

    }

    public void calculateProductBasePriceYuan() {
        if (productBasePrice != null) {
            productBasePriceYuan = AmountUtil.convertFenToYuan(productBasePrice);
        }
    }
}
