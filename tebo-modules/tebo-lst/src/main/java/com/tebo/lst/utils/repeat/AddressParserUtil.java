package com.tebo.lst.utils.repeat;

import java.util.HashMap;
import java.util.Map;

public class AddressParserUtil {

    public static Map<String, String> resolve(String rawAddress) {
        Map<String, String> result = new HashMap<>();

        if (rawAddress == null || rawAddress.isEmpty()) {
            return result;
        }

        // 去除扩展信息，如 [8655]
        int bracketIndex = rawAddress.lastIndexOf('[');
        if (bracketIndex != -1) {
            rawAddress = rawAddress.substring(0, bracketIndex).trim();
        }

        // 按空格分割
        String[] parts = rawAddress.split("\\s+");

        if (parts.length >= 3) {
            result.put("province", parts[0]);
            result.put("city", parts[1]);
            result.put("area", parts[2]); // 这里用 area 替代 district
        } else {
            result.put("province", parts.length > 0 ? parts[0] : "");
            result.put("city", parts.length > 1 ? parts[1] : "");
            result.put("area", parts.length > 2 ? parts[2] : "");
        }

        return result;
    }

    // 测试示例
    public static void main(String[] args) {
        String address = "湖北省 襄阳市 南漳县 城关镇金漳大道与丹阳大道交汇处 皇室名著9栋[8655]";
        Map<String, String> map = resolve(address);
        System.out.println(map);
    }
}
