package com.tebo.lst.controller.merchant;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.lst.customer.domain.dto.PlatServiceFeeWithdrawalDTO;
import com.tebo.lst.customer.domain.req.PlatBalanceQueryDTO;
import com.tebo.lst.customer.domain.vo.PlatBalanceSumVo;
import com.tebo.lst.customer.service.PlatBalanceRecordService;
import com.tebo.lst.utils.logs.LogRequestData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 平台余额
 */
@RequestMapping("/platBalance")
@Slf4j
@RestController
public class PlatBalanceController extends BaseController {
    @Resource
    private PlatBalanceRecordService platBalanceRecordService;

    /**
     * 平台余额列表
     */
    @LogRequestData("平台余额列表")
    @PostMapping("/getPlatBalanceList")
    public TableDataInfo getPlatBalanceList(@Valid @RequestBody PlatBalanceQueryDTO queryDTO) {
        if (queryDTO == null || queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            throw new GlobalException("分页参数不能为空");
        }
        Page page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        return getDataTable(platBalanceRecordService.getPlatBalanceList(queryDTO), page);
    }

    /**
     * 导出
     */
    @PostMapping("/export")
    public void export(@RequestBody PlatBalanceQueryDTO queryDTO, HttpServletResponse response) {
        platBalanceRecordService.export(response, queryDTO);
    }

    /**
     * 获取平台账户总金额,平台服务费金额
     */
    @LogRequestData("平台余额")
    @GetMapping("/getPlatBalance")
    public R getPlatBalance() {
        PlatBalanceSumVo platBalanceSumVo = platBalanceRecordService.getPlatBalance();
        return R.ok(platBalanceSumVo);
    }

    /**
     * 服务费提现
     */
    @LogRequestData("服务费提现")
    @PostMapping("/platServiceFeesWithdrawal")
    public R platServiceFeesWithdrawal(@RequestBody PlatServiceFeeWithdrawalDTO withdrawalDTO) {
        if (StringUtils.isEmpty(withdrawalDTO.getPlatServiceFee())) {
            throw new ServiceException("平台提现金额不能为空");
        }
        platBalanceRecordService.platServiceFeesWithdrawal(withdrawalDTO);
        return R.ok();
    }

}