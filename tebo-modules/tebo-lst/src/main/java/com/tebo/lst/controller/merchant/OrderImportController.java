package com.tebo.lst.controller.merchant;


import com.tebo.common.core.context.SecurityContextHolder;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.core.utils.excel.ExcelUtil;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.page.LstTableDataInfo;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.lst.customer.domain.req.MerchantStoreBalanceQueryDTO;
import com.tebo.lst.customer.domain.vo.MerchantStoreDetailVo;
import com.tebo.lst.customer.service.IMerchantStoreService;
import com.tebo.lst.customer.service.MerchantStoreBalanceService;
import com.tebo.lst.order.domain.dto.BatchSettlementOrderDTO;
import com.tebo.lst.order.domain.dto.StagingOrderImportDTO;
import com.tebo.lst.order.domain.model.ImportBatch;
import com.tebo.lst.order.domain.model.OrderVo;
import com.tebo.lst.order.domain.model.StagingOrder;
import com.tebo.lst.order.domain.req.AddOrderReq;
import com.tebo.lst.order.domain.req.BatchSettlementOrderReq;
import com.tebo.lst.order.domain.req.OrderQueryReq;
import com.tebo.lst.order.domain.resp.BatchSettlementOrderExcelResp;
import com.tebo.lst.order.domain.resp.BatchSettlementOrderResp;
import com.tebo.lst.order.domain.resp.BatchSettlementResp;
import com.tebo.lst.order.repository.ImportBatchRepository;
import com.tebo.lst.order.service.IOrderImportService;
import com.tebo.lst.order.service.impl.OrderInfoService;
import com.tebo.lst.utils.ExcelExportUtil;
import com.tebo.lst.utils.logs.LogRequestData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 订单导入
 */
@Slf4j
@RestController
@RequestMapping("/order/import")
public class OrderImportController extends BaseController {

    @Resource
    private IOrderImportService orderImportService;

    @Resource
    private ImportBatchRepository batchRepository;

    @Resource
    private IMerchantStoreService merchantStoreService;

    @Resource
    private OrderInfoService orderInfoService;

    private void setMerchantName() {
        String role = SecurityContextHolder.getUserRole();
        if (role.contains("lst:default:role") || role.contains("lst:approved:role")) {
            MerchantStoreDetailVo merchantStoreDetailVo = merchantStoreService.getDetailByUserId(SecurityContextHolder.getUserId());
            if (merchantStoreDetailVo == null) {
                throw new ServiceException("请先完善商户信息");
            }
            SecurityContextHolder.setUserName(merchantStoreDetailVo.getMerchantName());
        }
    }
    /**
     * 导入订单(平台端)
     */
    @GetMapping("/fileForPlat")
    @LogRequestData("导入订单")
    public R<ImportBatch> importOrderForPlat(@RequestParam("fileUrl") String fileUrl,
                                      @RequestParam(required = false, defaultValue = "0") Long storeId) {
        List<StagingOrderImportDTO> stagingOrderImportDTOS = new ArrayList<>();
        try {
            URL url = new URL(fileUrl);
            try (InputStream inputStream = url.openStream()) {
                ExcelUtil<StagingOrderImportDTO> util = new ExcelUtil<>(StagingOrderImportDTO.class);
                stagingOrderImportDTOS = util.importExcel(inputStream);
            }
        } catch (IOException e) {
            log.error("导入订单失败", e);
            return R.fail("导入订单失败!");
        }
        if (storeId == 0) {
            MerchantStoreDetailVo merchantStoreDetailVo = merchantStoreService.getDetailByUserId(SecurityContextHolder.getUserId());
            if (merchantStoreDetailVo == null) {
                return R.fail("请先完善商户信息");
            }
            if (merchantStoreDetailVo.getEnabledStatus() == 2){
                return R.fail("该商铺禁止导单");
            }
            storeId = merchantStoreDetailVo.getId();
        }
        ImportBatch importBatch = orderImportService.handleImportForPlat(fileUrl, stagingOrderImportDTOS, storeId);
        return R.ok(importBatch);
    }

    /**
     * 导入订单(商铺端)
     */
    @GetMapping("/fileForMerchant")
    @LogRequestData("导入订单")
    public R<ImportBatch> importOrderForMerchant(@RequestParam("fileUrl") String fileUrl,
                                      @RequestParam(required = false, defaultValue = "0") Long storeId) {
        setMerchantName();
        List<StagingOrderImportDTO> stagingOrderImportDTOS = new ArrayList<>();
        try {
            URL url = new URL(fileUrl);
            try (InputStream inputStream = url.openStream()) {
                ExcelUtil<StagingOrderImportDTO> util = new ExcelUtil<>(StagingOrderImportDTO.class);
                stagingOrderImportDTOS = util.importExcel(inputStream);
            }
        } catch (IOException e) {
            log.error("导入订单失败", e);
            return R.fail("导入订单失败!");
        }
        if (storeId == 0) {
            MerchantStoreDetailVo merchantStoreDetailVo = merchantStoreService.getDetailByUserId(SecurityContextHolder.getUserId());
            if (merchantStoreDetailVo == null) {
                return R.fail("请先完善商户信息");
            }
            if (merchantStoreDetailVo.getEnabledStatus() == 2){
                return R.fail("该商铺禁止导单");
            }
            storeId = merchantStoreDetailVo.getId();
        }
        ImportBatch importBatch = orderImportService.handleImportForMerchant(fileUrl, stagingOrderImportDTOS, storeId);
        return R.ok(importBatch);
    }



    /**
     * 确认导入
     */
    @GetMapping("/confirm")
    @LogRequestData("确认导入")
    public R<ImportBatch> confirmImport(@RequestParam("batchId") String batchId) {
        setMerchantName();
        ImportBatch importBatch = orderImportService.confirmImport(Long.parseLong(batchId));
        return R.ok(importBatch);
    }

    /**
     * 根据批次号查询导入订单
     */
    @GetMapping("staging/list")
    @LogRequestData("查询导入订单")
    public R<ImportBatch> getStagingOrderList(@RequestParam("batchId") String batchId) {
        ImportBatch batch = batchRepository.findById(Long.parseLong(batchId));
        if (batch == null) {
            throw new ServiceException("批次不存在");
        }

        AtomicReference<Long> totalAmount = new AtomicReference<>(new Long(0));
        for (StagingOrder stagingOrder : batch.getStagingOrders()) {
            if (stagingOrder.getRefTotalPrice() != null) {
                totalAmount.updateAndGet(v -> v + stagingOrder.getRefTotalPrice());
            }
        }
        batch.setTotalAmount(totalAmount.get());
        MerchantStoreBalanceQueryDTO queryDTO = new MerchantStoreBalanceQueryDTO();
        queryDTO.setMerchantStoreId(batch.getMerchantStoreId());
       // Integer balance = merchantStoreBalanceService.getLatestMerchantStoreBalance(queryDTO);
       // batch.setBalance(Long.valueOf(balance));
        return R.ok(batch);
    }

    /**
     * 删除导入订单
     */
    @GetMapping("staging/delete")
    @LogRequestData("删除导入订单")
    public R<Boolean> deleteStagingOrder(@RequestParam("id") Long id) {
        orderImportService.deleteStagingOrderById(id);
        return R.ok();
    }

    /**
     * 导入结算订单
     */
    @GetMapping("/file/settlement")
    @LogRequestData("导入结算订单")
    public R<BatchSettlementResp> importSettlementOrder(@RequestParam("fileUrl") String fileUrl) {
        setMerchantName();
        BatchSettlementResp batchSettlementResp = new BatchSettlementResp();
        List<BatchSettlementOrderReq> batchSettlementOrderReqs = new ArrayList<>();
        List<BatchSettlementOrderResp> batchSettlementOrderResps = new ArrayList<>();
        try {
            URL url = new URL(fileUrl);
            try (InputStream inputStream = url.openStream()) {
                ExcelUtil<BatchSettlementOrderReq> util = new ExcelUtil<>(BatchSettlementOrderReq.class);
                batchSettlementOrderReqs = util.importExcel(inputStream);
            }
        } catch (IOException e) {
            log.error("导入结算订单", e);
            return R.fail("导入结算订单!");
        }
        Integer successCount = 0;
        Integer errorCount = 0;
        for (BatchSettlementOrderReq batchSettlementOrderReq : batchSettlementOrderReqs) {
            try {
                orderInfoService.importSettleOrder(batchSettlementOrderReq);
                successCount++;
            } catch (Exception e){
                BatchSettlementOrderResp resp = new BatchSettlementOrderResp();
                resp.setOrderNo(batchSettlementOrderReq.getOrderNo());
                resp.setErrorMsg(e.getMessage());
                batchSettlementOrderResps.add(resp);
                errorCount++;
            }
        }
        batchSettlementResp.setSuccessCount(successCount);
        batchSettlementResp.setErrorCount(errorCount);
        if (errorCount > 0) {
            batchSettlementResp.setErrorList(batchSettlementOrderResps);
        }
        return R.ok(batchSettlementResp);
    }

    /**
     * 未正确完结结算单导出
     */
    @PostMapping("/settlementExport")
    @LogRequestData("未正确完结结算单导出")
    public ResponseEntity<byte[]> settlementExport(@RequestBody BatchSettlementOrderDTO batchSettlementOrderDTO) {
        return ExcelExportUtil.export(
                batchSettlementOrderDTO.getErrorList(),
                "结算单导出.xlsx",
                BatchSettlementOrderExcelResp.class
        );
    }

    /**
     * 新增服务订单
     */
    @PostMapping("/add")
    @LogRequestData("新增服务订单")
    public R<Long> addOrder(@RequestBody @Validated AddOrderReq addOrderReq) {
        setMerchantName();
        String role = SecurityContextHolder.getUserRole();
        if (role.contains("lst:default:role") || role.contains("lst:approved:role")) {
            MerchantStoreDetailVo merchantStoreDetailVo = merchantStoreService.getDetailByUserId(SecurityContextHolder.getUserId());
            if (merchantStoreDetailVo == null) {
                throw new ServiceException("请先完善商户信息");
            }
            Long storeId = merchantStoreDetailVo.getId();
            addOrderReq.setStoreType(2);
            addOrderReq.setMerchantId(storeId.toString());
            addOrderReq.setMerchantName(merchantStoreDetailVo.getMerchantName());
            addOrderReq.setStoreType(merchantStoreDetailVo.getStoreType());
        }else {
            if (StringUtils.isBlank(addOrderReq.getMerchantId())){
                throw new ServiceException("商户ID不能为空");
            }
            addOrderReq.setStoreType(1);
        }
        return R.ok(orderImportService.addOrder(addOrderReq));
    }
}
