package com.tebo.lst.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.csp.sentinel.util.AssertUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.core.utils.excel.ExcelUtil;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.lst.common.constants.RedisPreConstants;
import com.tebo.lst.customer.domain.vo.excel.MerchantStoreExcelVo;
import com.tebo.lst.product.domain.entity.ProductLevelDO;
import com.tebo.lst.product.domain.params.QueryProductParams;
import com.tebo.lst.product.domain.req.*;
import com.tebo.lst.product.domain.vo.*;
import com.tebo.lst.product.enums.BuyMethodEnum;
import com.tebo.lst.product.enums.LevelTypeEnum;
import com.tebo.lst.product.enums.ShelfStatusEnum;
import com.tebo.lst.product.mapper.ProductLevelMapper;
import com.tebo.lst.product.mapper.ProductPricingPolicyMapper;
import com.tebo.lst.product.service.IProductLevelService;
import com.tebo.lst.product.service.IProductPricingPolicyService;
import com.tebo.lst.utils.AmountUtil;
import com.tebo.lst.utils.DataTransformer;
import com.tebo.lst.utils.SystemSecurityUtils;
import com.tebo.lst.utils.repeat.RepeatCheckItem;
import com.tebo.lst.utils.repeat.RepeatCheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ProductLevelServiceImpl extends ServiceImpl<ProductLevelMapper, ProductLevelDO>
        implements IProductLevelService {

    @Autowired
    private ProductLevelMapper productLevelMapper;

    @Autowired
    private ProductPricingPolicyMapper productPricingPolicyMapper;


    @Autowired
    private RedisService redisService;

    @Autowired
    private IProductPricingPolicyService productPricingPolicyService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addProductLevel(AddProductReq addProductReq) {
        if (StringUtils.isNotEmpty(addProductReq.getParentId())) {
            AssertUtil.notNull(productLevelMapper.selectById(addProductReq.getParentId()), "型号包不存在！");
        }
        RepeatCheckItem.Param<ProductLevelDO> eqProductName = RepeatCheckItem.Param
                .of(ProductLevelDO::getProductName, "eq", addProductReq.getProductName() + BuyMethodEnum.TRADE_IN.getDesc());
        RepeatCheckItem<ProductLevelDO> productNameItem = RepeatCheckItem.of(ProductLevelDO.class, eqProductName, "型号包名称/商品名称已存在");

        RepeatCheckItem.Param<ProductLevelDO> eqProductName2 = RepeatCheckItem.Param
                .of(ProductLevelDO::getProductName, "eq", addProductReq.getProductName() + BuyMethodEnum.BUY_NOW.getDesc());
        RepeatCheckItem<ProductLevelDO> productNameItem2 = RepeatCheckItem.of(ProductLevelDO.class, eqProductName2, "型号包名称/商品名称已存在");

        RepeatCheckItem.Param<ProductLevelDO> eqProductSkuCode = RepeatCheckItem.Param
                .of(ProductLevelDO::getProductSkuCode, "eq", addProductReq.getProductSkuCode() + "-" + BuyMethodEnum.TRADE_IN.getCode());
        RepeatCheckItem<ProductLevelDO> productCodeItem = RepeatCheckItem.of(ProductLevelDO.class, eqProductSkuCode, "商品SKU编码已存在");

        RepeatCheckItem.Param<ProductLevelDO> eqProductSkuCode2 = RepeatCheckItem.Param
                .of(ProductLevelDO::getProductSkuCode, "eq", addProductReq.getProductSkuCode() + "-" + BuyMethodEnum.BUY_NOW.getCode());
        RepeatCheckItem<ProductLevelDO> productCodeItem2 = RepeatCheckItem.of(ProductLevelDO.class, eqProductSkuCode2, "商品SKU编码已存在");

        RepeatCheckUtil.repeatCheck(productLevelMapper, Arrays.asList(productNameItem, productNameItem2, productCodeItem, productCodeItem2));
        /*
         * 型号包
         */
        if (addProductReq.getLevelType().equals(LevelTypeEnum.FIRST_LEVEL.getCode())) {
            ProductLevelDO productLevelDO = addProductReq.convertToProductLevelDOMenu();
            log.info("---productLevelDO:{}", productLevelDO);
            SystemSecurityUtils.fillAuditFields(productLevelDO);
            productLevelMapper.insertProduct(productLevelDO);
            return;
        }
        /*
         * 商品项
         */
        List<ProductLevelDO> productLevelDOS = addProductReq.convertToProductLevelDOList();
        productLevelDOS.forEach(productLevelDO -> {
            SystemSecurityUtils.fillAuditFields(productLevelDO);
            productLevelMapper.insertProduct(productLevelDO);
            log.info("---productLevelDO:{}", productLevelDO);
        });
    }

    @Override
    public List<ProductPriceListVo> getProductPriceList(QueryProductReq queryProductReq) {
        QueryProductParams queryProductParams = new QueryProductParams();
        BeanUtil.copyProperties(queryProductReq, queryProductParams);
        List<ProductPriceListVo> productList = productLevelMapper.selectProductPriceList(queryProductParams);
        for (ProductPriceListVo productPriceListVo : productList) {
            productPriceListVo.calculateRefPrice();
            if (ObjectUtils.isNotEmpty(productPriceListVo.getRefBasePrice())){
                productPriceListVo.setRefBasePriceStr(MoneyUtil.fenToYuan(productPriceListVo.getRefBasePrice()));
            }
            productPriceListVo.setMixId(productPriceListVo.getId() + productPriceListVo.getAreaCode());
        }
        return productList;
    }

    @Override
    public List<ProductSkuListVo> getProductSkuList(QueryProductReq queryProductReq) {
        QueryProductParams queryProductParams = new QueryProductParams();
        BeanUtil.copyProperties(queryProductReq, queryProductParams, "areaCode");
        if (queryProductParams.getKeyWord() != null) {
            queryProductParams.setProductMode(queryProductParams.getKeyWord().trim());
            queryProductParams.setProductSkuCode(queryProductParams.getKeyWord().trim());
        }
        queryProductParams.setLevelType(LevelTypeEnum.SECOND_LEVEL.getCode());
        List<ProductLevelDO> fList = productLevelMapper.selectProductSkuList(queryProductParams);
        List<ProductSkuListVo> results = DataTransformer
                .transform(fList, ProductSkuListVo.class);
        results.forEach(productSkuListVo -> {
            productSkuListVo.calculateProductBasePriceYuan();
            productSkuListVo.calculateRefBasePriceYuan();
        });
        return results;
    }
    @Override
    public void productExport(HttpServletResponse response, QueryProductReq queryProductReq){
        List<ProductSkuListVo> list = getProductSkuList(queryProductReq);
        if (CollectionUtil.isNotEmpty(list)){
            List<ProductListVo> result = BeanConvert.copyList(list,ProductListVo::new);
            for (ProductListVo productListVo : result){
                productListVo.setStatusStr(ShelfStatusEnum.getByCode(productListVo.getShelfStatus()).getDesc());
            }
            ExcelUtil.exportExcelToResponse(response, result, ProductListVo.class, "商品列表");
        }

    }
    @Override
    public List<ProductSkuListVo> getProductSkuPageList(QueryProductReq queryProductReq) {
        QueryProductParams queryProductParams = new QueryProductParams();
        BeanUtil.copyProperties(queryProductReq, queryProductParams, "areaCode");
        queryProductParams.setLevelType(LevelTypeEnum.SECOND_LEVEL.getCode());
        List<ProductLevelDO> fList = productLevelMapper.selectProductSkuList(queryProductParams);
        List<ProductSkuListVo> results = DataTransformer
                .transform(fList, ProductSkuListVo.class);
        results.forEach(productSkuListVo -> {
            productSkuListVo.calculateProductBasePriceYuan();
            productSkuListVo.calculateRefBasePriceYuan();
        });
        return results;
    }

    @Override
    public ProductDetailVo getProductInfoDetail(Long productId) {
        ProductLevelDO productLevelDO = productLevelMapper.selectProductById(productId);
        AssertUtil.notNull(productLevelDO, "商品不存在！");
        ProductDetailVo productDetailVo = new ProductDetailVo();
        BeanUtil.copyProperties(productLevelDO, productDetailVo);
        productDetailVo.calculateProductBasePriceYuanYuan();
        productDetailVo.calculateRefBasePriceYuan();
        QueryProductParams queryProductParams = new QueryProductParams();
        queryProductParams.setProductSkuCode(productDetailVo.getProductSkuCode());
        List<ProductPriceListVo> productList = productLevelMapper.selectProductPriceList(queryProductParams);
        productList.forEach(ProductPriceListVo::calculateRefPrice);
        productDetailVo.setProductPriceListVos(productList);
        productDetailVo.setProductDetailImages(JSON.parseArray(productLevelDO.getProductDetailImages(), String.class));
        if (!productLevelDO.getParentId().equals(0L)) {
            ProductLevelDO productParent = productLevelMapper.selectProductById(productLevelDO.getParentId());
            if (productParent != null) {
                productDetailVo.setParentName(productParent.getProductName());
            }
        } else {
            productDetailVo.setParentName("全部");
        }
        return productDetailVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editProductLevel(EditProductReq editProductReq) {
        ProductLevelDO productLevelDO = productLevelMapper.selectById(editProductReq.getId());
        AssertUtil.notNull(productLevelDO, "商品不存在！");
        if (productLevelDO.getShelfStatus() == 1 ){
          throw new ServiceException("该商品正在销售中，不允许修改");
        }
        RepeatCheckItem.Param<ProductLevelDO> eqProductName = RepeatCheckItem.Param
                .of(ProductLevelDO::getProductName, "eq", editProductReq.getProductName());
        RepeatCheckItem.Param<ProductLevelDO> neProductSkuCode = RepeatCheckItem.Param
                .of(ProductLevelDO::getProductSkuCode, "ne", editProductReq.getProductSkuCode());
        RepeatCheckItem.Param<ProductLevelDO> neId = RepeatCheckItem.Param
                .of(ProductLevelDO::getId, "ne", editProductReq.getId());
        RepeatCheckItem<ProductLevelDO> productNameItem = RepeatCheckItem.of(ProductLevelDO.class, Arrays.asList(eqProductName, neProductSkuCode, neId), "型号包名称/商品名称已存在");
        RepeatCheckUtil.repeatCheck(productLevelMapper, productNameItem);

        BeanUtil.copyProperties(editProductReq, productLevelDO);
        productLevelDO.setProductDetailImages(JSON.toJSONString(editProductReq.getProductDetailImages()));
        if (StringUtils.isNotEmpty(editProductReq.getRefBasePrice())) {
            productLevelDO.setRefBasePrice(AmountUtil.convertYuanToFen(editProductReq.getRefBasePrice()));
        }
        if (StringUtils.isNotEmpty(editProductReq.getProductBasePrice())) {
            productLevelDO.setProductBasePrice(AmountUtil.convertYuanToFen(editProductReq.getProductBasePrice()));
        }
        SystemSecurityUtils.fillAuditFields(productLevelDO);
        AssertUtil.isTrue(productLevelMapper.updateProductById(productLevelDO) > 0, "编辑商品失败！");
    }

    @Override
    public ProductPriceListVo getProductPrice(String productSkuCode, String regionId) {
        QueryProductParams queryProductParams = new QueryProductParams();
        queryProductParams.setProductSkuCode(productSkuCode);
        queryProductParams.setAreaCode(regionId);
        List<ProductPriceListVo> productList = productLevelMapper.selectProductPriceList(queryProductParams);
        List<ProductPriceListVo> priceListVos = productList.stream().filter(productPriceListVo -> ShelfStatusEnum.SHELF.getCode().equals(productPriceListVo.getShelfStatus())).collect(Collectors.toList());
        if (priceListVos.isEmpty()) {
            return null;
        }
        priceListVos.forEach(ProductPriceListVo::calculateRefPrice);
        return priceListVos.get(0);
    }

    /**
     * 批量上下架
     *
     * @param batchShelfReq
     */
    @Override
    public void updateProductShelfStatus(BatchShelfReq batchShelfReq) {
        productLevelMapper.updateProductShelfStatus(batchShelfReq.getProductIds(), batchShelfReq.getShelfStatus());
    }

    /**
     * 添加型号包
     *
     * @param addLevelReq
     */
    @Override
    public void addLevelGroup(AddLevelReq addLevelReq) {
        RepeatCheckItem.Param<ProductLevelDO> eqProductName = RepeatCheckItem.Param
                .of(ProductLevelDO::getProductName, "eq", addLevelReq.getProductName());
        RepeatCheckItem<ProductLevelDO> productNameItem = RepeatCheckItem.of(ProductLevelDO.class, eqProductName, "型号包名称已存在");
        RepeatCheckUtil.repeatCheck(productLevelMapper, productNameItem);
        checkParentLevel(addLevelReq.getProductIds(), null);
        ProductLevelDO productLevelDO = new ProductLevelDO();
        productLevelDO.setProductName(addLevelReq.getProductName());
        redisService.incrementValue(RedisPreConstants.PRODUCT_SKU_CODE_KEY, 1L);
        Integer productSkuCode = redisService.getCacheObject(RedisPreConstants.PRODUCT_SKU_CODE_KEY);
        String skuCode = StringUtils.leftPad(productSkuCode.toString(), 6, "0");
        productLevelDO.setProductSkuCode(skuCode);
        productLevelDO.setParentId(0L);
        productLevelDO.setLevelType(LevelTypeEnum.FIRST_LEVEL.getCode());
        SystemSecurityUtils.fillAuditFields(productLevelDO);
        productLevelMapper.insertProduct(productLevelDO);
        productLevelMapper.updateParentIdByIds(addLevelReq.getProductIds(), productLevelDO.getId());
    }

    /**
     * 编辑型号包
     *
     * @param editLevelReq
     */
    @Override
    public void editLevelGroup(EditLevelReq editLevelReq) {
        ProductLevelDO productLevelDO = productLevelMapper.selectById(editLevelReq.getId());
        AssertUtil.notNull(productLevelDO, "型号包不存在！");
        if (!LevelTypeEnum.FIRST_LEVEL.getCode().equals(productLevelDO.getLevelType())) {
            throw new IllegalArgumentException("非型号包不可编辑！");
        }
        RepeatCheckItem.Param<ProductLevelDO> eqProductName = RepeatCheckItem.Param
                .of(ProductLevelDO::getProductName, "eq", editLevelReq.getProductName());
        RepeatCheckItem.Param<ProductLevelDO> neId = RepeatCheckItem.Param
                .of(ProductLevelDO::getId, "ne", editLevelReq.getId());
        RepeatCheckItem<ProductLevelDO> productNameItem = RepeatCheckItem.of(ProductLevelDO.class, Arrays.asList(eqProductName, neId), "型号包名称已存在");
        RepeatCheckUtil.repeatCheck(productLevelMapper, productNameItem);
        QueryProductParams queryProductParams = new QueryProductParams();
        queryProductParams.setParentId(Long.valueOf(editLevelReq.getId()));
        queryProductParams.setLevelType(LevelTypeEnum.SECOND_LEVEL.getCode());
        List<ProductLevelDO> productLevelDOS = productLevelMapper.selectProductSkuList(queryProductParams);
        List<Long> productIds = productLevelDOS.stream().map(ProductLevelDO::getId).collect(Collectors.toList());
        productLevelMapper.updateParentIdByIds(productIds, 0L);
        productLevelDO.setProductName(editLevelReq.getProductName());
        SystemSecurityUtils.fillAuditFields(productLevelDO);
        AssertUtil.isTrue(productLevelMapper.updateProductById(productLevelDO) > 0, "编辑型号包失败！");
        checkParentLevel(editLevelReq.getProductIds(), productLevelDO);
        productLevelMapper.updateParentIdByIds(editLevelReq.getProductIds(), Long.valueOf(editLevelReq.getId()));
    }

    /**
     * 查詢型号包
     */
    @Override
    public List<ProductLevelAndProductDTO> selectProductLevelAndProduct(ProductLevelAndProductReq req) {
        List<ProductLevelAndProductDTO> productLevelAndProductDTOS = productLevelMapper.selectProductLevelAndProduct(req);
        for (ProductLevelAndProductDTO productLevelAndProductDTO : productLevelAndProductDTOS) {
            productLevelAndProductDTO.setProductBasePriceYuan(AmountUtil.convertFenToYuan(productLevelAndProductDTO.getProductBasePrice()));
            productLevelAndProductDTO.setRefBasePriceYuan(AmountUtil.convertFenToYuan(productLevelAndProductDTO.getRefBasePrice()));
        }
        return productLevelAndProductDTOS;
    }

    /**
     * 查詢型号包详情
     *
     * @param id
     */
    @Override
    public ProductLevelAndProductDetailDTO selectProductLevelAndProductDetail(Long id) {
        ProductLevelDO productLevelDO = productLevelMapper.selectProductById(id);
        if (productLevelDO == null) {
            throw new IllegalArgumentException("型号包不存在！");
        }
        if (!LevelTypeEnum.FIRST_LEVEL.getCode().equals(productLevelDO.getLevelType())) {
            throw new IllegalArgumentException("非型号包！");
        }
        ProductLevelAndProductDetailDTO productLevelAndProductDetailDTO = new ProductLevelAndProductDetailDTO();
        BeanUtil.copyProperties(productLevelDO, productLevelAndProductDetailDTO);
        QueryProductParams queryProductParams = new QueryProductParams();
        queryProductParams.setParentId(id);
        queryProductParams.setLevelType(LevelTypeEnum.SECOND_LEVEL.getCode());
        List<ProductLevelDO> productLevelDOS = productLevelMapper.selectProductSkuList(queryProductParams);
        List<ProductLevelAndProductDTO> productLevelAndProductDTOS = new ArrayList<>();
        for (ProductLevelDO productLevel : productLevelDOS) {
            ProductLevelAndProductDTO productLevelAndProductDTO = new ProductLevelAndProductDTO();
            BeanUtil.copyProperties(productLevel, productLevelAndProductDTO);
            productLevelAndProductDTO.setProductBasePriceYuan(AmountUtil.convertFenToYuan(productLevel.getProductBasePrice()));
            productLevelAndProductDTO.setRefBasePriceYuan(AmountUtil.convertFenToYuan(productLevel.getRefBasePrice()));
            productLevelAndProductDTOS.add(productLevelAndProductDTO);
        }
        productLevelAndProductDetailDTO.setProductLevelAndProductDTOS(productLevelAndProductDTOS);
        return productLevelAndProductDetailDTO;
    }

    /**
     * 查询型号包列表(不含物料)
     *
     * @param req
     */
    @Override
    public List<ProductLevelDO> selectProductLevelAndProduct(ProductLevelQueryReq req) {
        QueryProductParams queryProductParams = new QueryProductParams();
        queryProductParams.setLevelType(LevelTypeEnum.FIRST_LEVEL.getCode());
        queryProductParams.setProductName(req.getParentName());
        queryProductParams.setProductSkuCode(req.getParentCode());
        return productLevelMapper.selectProductSkuList(queryProductParams);
    }

    /**
     * 检查父级型号包
     *
     * @param productIds
     * @param productLevelDO
     */
    private void checkParentLevel(List<Long> productIds, ProductLevelDO productLevelDO) {
        List<ProductLevelDO> productDOS = productLevelMapper.selectBatchIds(productIds);
        for (ProductLevelDO productDO : productDOS) {
            if (productLevelDO == null) {
                if (!Long.valueOf(0L).equals(productDO.getParentId())) {
                    throw new IllegalArgumentException(productDO.getProductSkuCode() + "已存在于其他型号包中！");
                }
            } else {
                if (!Long.valueOf(0L).equals(productDO.getParentId()) && !productDO.getParentId().equals(productLevelDO.getId())) {
                    throw new IllegalArgumentException(productDO.getProductSkuCode() + "已存在于其他型号包中！");
                }
            }
        }
    }
}