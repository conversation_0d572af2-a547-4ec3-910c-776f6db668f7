package com.tebo.lst.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.context.SecurityContextHolder;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.util.DataUtils;
import com.tebo.common.util.time.LocalDateUtil;
import com.tebo.lst.common.aspectj.ReLock;
import com.tebo.lst.common.constants.RedisPreConstants;
import com.tebo.lst.common.domain.dto.RegionDTO;
import com.tebo.lst.common.domain.model.Region;
import com.tebo.lst.common.service.IRegionInfoService;
import com.tebo.lst.disruptor.service.DisMqService;
import com.tebo.lst.order.enums.IOrderTypeEnum;
import com.tebo.lst.order.enums.OrderNatureEnum;
import com.tebo.lst.invoice.domain.OrderInvoiceInfoDO;
import com.tebo.lst.invoice.mapper.OrderInvoiceInfoMapper;
import com.tebo.lst.order.domain.dataobject.OrderInfoDO;
import com.tebo.lst.order.domain.dataobject.OrderOperationLogDO;
import com.tebo.lst.order.domain.dataobject.OrderSkusDO;
import com.tebo.lst.order.domain.dto.OrderInfoDTO;
import com.tebo.lst.order.domain.model.*;
import com.tebo.lst.order.domain.param.OrderQueryParam;
import com.tebo.lst.order.domain.req.*;
import com.tebo.lst.order.enums.OrderOperationEnum;
import com.tebo.lst.order.enums.OrderStatusEnum;
import com.tebo.lst.order.mapper.OrderInfoMapper;
import com.tebo.lst.order.mapper.OrderOperationLogMapper;
import com.tebo.lst.order.mapper.OrderSkusMapper;
import com.tebo.lst.order.service.IOrderInfoService;
import com.tebo.lst.product.domain.vo.ProductPriceListVo;
import com.tebo.lst.product.service.IProductLevelService;
import com.tebo.lst.req.invoice.InvoiceQueryReq;
import com.tebo.lst.req.order.OrderStatusChangeReq;
import com.tebo.lst.resp.BusinessStats;
import com.tebo.lst.resp.invoice.InvoiceResp;
import com.tebo.lst.service.pay.TeboPayConfirmService;
import com.tebo.lst.shopArea.domain.ShopServerArea;
import com.tebo.lst.shopArea.service.IShopServerAreaService;
import com.tebo.lst.utils.AmountUtil;
import com.tebo.lst.utils.DateParser;
import com.tebo.system.api.RemoteAccountService;
import com.tebo.system.api.domain.SysUser;
import com.tebo.system.api.model.TeboAccountInfoVO;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
//@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class OrderInfoService implements IOrderInfoService {
    @Resource
    private  OrderInfoMapper orderInfoMapper;
    @Resource
    private TeboPayConfirmService confirmOrderPay;
    @Resource
    private OrderSkusMapper orderSkusMapper;
    @Resource
    private  OrderOperationLogMapper orderOperationLogMapper;
    @Resource
    private OrderOperationLogService orderOperationLogService;
    @Resource
    private IProductLevelService productLevelService;
    @Resource
    private IShopServerAreaService shopServerAreaService;
    @Resource
    private OrderInvoiceInfoMapper orderInvoiceInfoMapper;

    @Resource
    private TeboPayConfirmService confirmService;

    @Resource
    private RemoteAccountService remoteAccountService;

    @Resource
    private DisMqService disMqService;

    @Resource
    private IRegionInfoService regionInfoService;

    @Override
    public void saveOrUpdateOrderInfo(OrderInfoDO orderInfo) {
        if (orderInfo.getId() == null) {
            orderInfoMapper.insert(orderInfo);
        } else {
            orderInfoMapper.updateById(orderInfo);
        }
    }

    /**
     * 根据订单ID查询订单主信息
     *
     * @param orderId
     */
    @Override
    public OrderVo getOrderInfoById(Long orderId) {
        OrderInfoDO orderInfoDO = orderInfoMapper.selectById(orderId);
        if (orderInfoDO == null) {
            throw new ServiceException("订单不存在");
        }
        //根据order_id查询未删除的订单商品信息
        List<OrderSkusDO> orderSkusList = orderSkusMapper.selectList(
                new QueryWrapper<OrderSkusDO>().eq("order_id", orderId).eq("del_flag", 0)
        );
        List<OrderOperationLogDO> orderOperationLogDOList = orderOperationLogMapper.selectList(
                new QueryWrapper<OrderOperationLogDO>().eq("order_id", orderId)
        );

        QueryWrapper<OrderInvoiceInfoDO> queryWrapper = new QueryWrapper<>();
        // 设置查询条件：订�� ID 相等
        queryWrapper.eq("order_id", orderId);
        // 设置查询条件：逻辑删除标志为 0（未删除）
        queryWrapper.eq("del_flag", 0);

        List<OrderInvoiceInfoDO> invoiceInfoDOList = orderInvoiceInfoMapper.selectList(queryWrapper);
        OrderInvoiceInfoDO orderInvoiceInfoDO = new OrderInvoiceInfoDO();
        if (CollectionUtil.isNotEmpty(invoiceInfoDOList)){
            orderInvoiceInfoDO =  invoiceInfoDOList.get(0);
        }
        if (!ObjectUtils.isEmpty(orderInfoDO.getOrderPrice())){
            orderInfoDO.setOrderPrice(BigDecimal.valueOf(orderInfoDO.getOrderPrice()).multiply(BigDecimal.valueOf(0.99)).longValue());
        }
        return new OrderVo(orderInfoDO, orderSkusList, orderOperationLogDOList,orderInvoiceInfoDO);
    }

    @Override
    public OrderVo getPcOrderDetail(Long orderId) {
        OrderInfoDO orderInfoDO = orderInfoMapper.selectById(orderId);
        if (orderInfoDO == null) {
            throw new ServiceException("订单不存在");
        }
        //根据order_id查询未删除的订单商品信息
        List<OrderSkusDO> orderSkusList = orderSkusMapper.selectList(
                new QueryWrapper<OrderSkusDO>().eq("order_id", orderId).eq("del_flag", 0)
        );
        List<OrderOperationLogDO> orderOperationLogDOList = orderOperationLogMapper.selectList(
                new QueryWrapper<OrderOperationLogDO>().eq("order_id", orderId)
        );

        QueryWrapper<OrderInvoiceInfoDO> queryWrapper = new QueryWrapper<>();
        // 设置查询条件：订�� ID 相等
        queryWrapper.eq("order_id", orderId);
        // 设置查询条件：逻辑删除标志为 0（未删除）
        queryWrapper.eq("del_flag", 0);

        List<OrderInvoiceInfoDO> invoiceInfoDOList = orderInvoiceInfoMapper.selectList(queryWrapper);
        OrderInvoiceInfoDO orderInvoiceInfoDO = new OrderInvoiceInfoDO();
        if (CollectionUtil.isNotEmpty(invoiceInfoDOList)){
            orderInvoiceInfoDO =  invoiceInfoDOList.get(0);
        }
        if (!ObjectUtils.isEmpty(orderInfoDO.getOrderPrice())){
            orderInfoDO.setOrderPrice(BigDecimal.valueOf(orderInfoDO.getOrderPrice()).multiply(BigDecimal.valueOf(0.99)).longValue());
        }
        OrderVo orderVo = new OrderVo(orderInfoDO, orderSkusList, orderOperationLogDOList,orderInvoiceInfoDO);
        String address = orderVo.getCustomer().getAddress();
        if (orderVo.getCreationMode() == 1) {
            Region district = regionInfoService.selectRegionByCountyCode(orderVo.getCountyCode());
            if (ObjectUtil.isNotEmpty(district)) {
                // 处理省级名称（考虑自治区、直辖市等特殊情况）
                String provinceName = district.getProvinceName();
                String provinceShortName = provinceName;

                // 处理不同类型的省级行政区名称
                if (provinceName.endsWith("自治区")) {
                    provinceShortName = provinceName.replace("壮族", "").replace("自治区", "");
                } else if (provinceName.endsWith("省")) {
                    provinceShortName = provinceName.substring(0, provinceName.length() - 1);
                } else if (provinceName.endsWith("市")) {
                    // 处理直辖市
                    provinceShortName = provinceName.substring(0, provinceName.length() - 1);
                }

                // 替换地址中的行政区划名称
                address = address.replace(provinceName, "")
                        .replace(provinceShortName, "")
                        .replace(district.getCityName(), "")
                        .replace(district.getDistrictName(), "")

                        // 处理可能的重复镇名（如示例中的"新圩镇新圩镇"）
                      //  .replaceFirst(district.getDistrictName() + district.getDistrictName(), district.getDistrictName())

                        // 最后清理多余空格
                        .trim()
                        .replaceAll("\\s+", " ");
            }
        }
        Customer customer = new Customer(orderVo.getCustomer().getName(),orderVo.getCustomer().getPhone(),address);
        orderVo.setCustomer(customer);
        return orderVo;
    }

    /**
     * 根据订单ID查询订单主信息
     *
     * @param orderId
     */
    @Override
    public OrderVo getOrderDetail(Long orderId) {
        OrderInfoDO orderInfoDO = orderInfoMapper.selectById(orderId);
        if (orderInfoDO == null) {
            throw new ServiceException("订单不存在");
        }
        //根据order_id查询未删除的订单商品信息
        List<OrderSkusDO> orderSkusList = orderSkusMapper.selectList(
                new QueryWrapper<OrderSkusDO>().eq("order_id", orderId).eq("del_flag", 0)
        );
        List<OrderOperationLogDO> orderOperationLogDOList = orderOperationLogMapper.selectList(
                new QueryWrapper<OrderOperationLogDO>().eq("order_id", orderId)
        );

        QueryWrapper<OrderInvoiceInfoDO> queryWrapper = new QueryWrapper<>();
        // 设置查询条件：订�� ID 相等
        queryWrapper.eq("order_id", orderId);
        // 设置查询条件：逻辑删除标志为 0（未删除）
        queryWrapper.eq("del_flag", 0);

        List<OrderInvoiceInfoDO> invoiceInfoDOList = orderInvoiceInfoMapper.selectList(queryWrapper);
        OrderInvoiceInfoDO orderInvoiceInfoDO = new OrderInvoiceInfoDO();
        if (CollectionUtil.isNotEmpty(invoiceInfoDOList)){
            orderInvoiceInfoDO =  invoiceInfoDOList.get(0);
        }
        OrderVo OrderVo = new OrderVo(orderInfoDO, orderSkusList, orderOperationLogDOList,orderInvoiceInfoDO);
        if (!ObjectUtils.isEmpty(orderInfoDO.getOrderPrice())){
            OrderVo.setOrderPrice(AmountUtil.convertFenToYuan(BigDecimal.valueOf(orderInfoDO.getOrderPrice()).multiply(BigDecimal.valueOf(0.99)).longValue()));
        }
        if (ObjectUtil.isNotEmpty(orderInfoDO.getEmployeeId())){
            R<TeboAccountInfoVO> teboAccountInfoVOR = remoteAccountService.getAccountInfoById(orderInfoDO.getEmployeeId());
            if (ObjectUtil.isNotEmpty(teboAccountInfoVOR) && teboAccountInfoVOR.getCode() == 200){
                OrderVo.setAccountName(teboAccountInfoVOR.getData().getAccountName());
            }

        }
        return OrderVo;
    }
    /**
     * 根据服务订单号查询订单主信息
     *
     * @param orderNo
     */
    @Override
    public OrderVo getOrderInfoByServiceOrderNo(String orderNo) {
        OrderInfoDO orderInfoDO = orderInfoMapper.selectOne(new QueryWrapper<OrderInfoDO>().eq("order_no", orderNo));
        if (orderInfoDO == null) {
            return null;
        }
        return new OrderVo(orderInfoDO, null, null,null);
    }

    /**
     * 修改订单状态
     *
     * @param req 订单状态修改请求
     */
    @Override
    @ReLock(prefix = RedisPreConstants.ORDER_LOCK, key = "#req.getOrderId()")
    @Transactional
    public void updateOrderStatus(OrderStatusChangeReq req) {
        OrderOperationEnum orderOperationEnum = OrderOperationEnum.of(req.getOrderOperation());
        OrderInfoDO orderInfoDO = orderInfoMapper.selectById(req.getOrderId());
        if (orderInfoDO == null) {
            throw new ServiceException("订单不存在");
        }
        orderInfoDO.setOrderStatus(orderOperationEnum.getOrderStatusEnum().getCode());
        orderInfoDO.setUpdateBy(req.getUserName());
        if (OrderOperationEnum.UPLOAD_COMPLETE.getCode().equals(req.getOrderOperation())) {
            orderInfoDO.setBatteryCodeImgUrl(req.getBatteryCodeImgUrlStr());
            orderInfoDO.setInstallCompletedImgUrl(req.getInstallCompletedImgUrlStr());
            orderInfoDO.setSignedImgUrl(req.getSignedImgUrlStr());
            orderInfoDO.setUploadToAuditTime(req.getUploadToAuditTime());
            orderInfoDO.setEmployeeId(req.getEmployeeId());
        }else if (OrderOperationEnum.ACCEPT.getCode().equals(req.getOrderOperation())){
            orderInfoDO.setAcceptTime(LocalDateTime.now());
        }else if (OrderOperationEnum.COMPLETE.getCode().equals(req.getOrderOperation()) || OrderOperationEnum.AUDIT_PASS_TO_COMPLETE.getCode().equals(req.getOrderOperation()) || OrderOperationEnum.AUTO_AUDIT_PASS_TO_COMPLETE.getCode().equals(req.getOrderOperation()) ) {
            orderInfoDO.setAuditTime(LocalDateTime.now());
            orderInfoDO.setBatteryCodeImgUrl(req.getBatteryCodeImgUrlStr());
            orderInfoDO.setInstallCompletedImgUrl(req.getInstallCompletedImgUrlStr());
            orderInfoDO.setSignedImgUrl(req.getSignedImgUrlStr());
            orderInfoDO.setEmployeeId(req.getEmployeeId());
        }else if (OrderOperationEnum.SETTLE.getCode().equals(req.getOrderOperation())) {
            orderInfoDO.setSettlementTime(req.getSettlementTime());
        }
        if (!StringUtils.isBlank(req.getRemark())){
            if (StringUtils.isBlank(orderInfoDO.getHangingRemarks())){
                orderInfoDO.setHangingRemarks(req.getRemark());
            }else {
                orderInfoDO.setHangingRemarks(orderInfoDO.getHangingRemarks()+";"+req.getRemark());
            }
        }
        orderInfoMapper.updateById(orderInfoDO);
        OrderOperationLogDO operationLogs = new OrderOperationLogDO();
        operationLogs.setOrderId(orderInfoDO.getId());
        operationLogs.setOperationType(orderOperationEnum.getCode());
        operationLogs.setOperationValue(orderOperationEnum.getDesc());
        operationLogs.setContent(orderOperationEnum.getDesc());
        operationLogs.setServiceCode(orderInfoDO.getServiceOrderNo());
        operationLogs.setCreateBy(req.getUserName());
        operationLogs.setCreateById(SecurityContextHolder.getUserId().toString());
        operationLogs.setCreateByPhone(SecurityContextHolder.getUserPhone());
        operationLogs.setRemark(req.getRemark());
        orderOperationLogService.recordOrderOperations(Collections.singletonList(operationLogs));
    }

    /**
     * 查询订单列表
     */
    @Override
    public List<OrderVo> getOrderList(OrderQueryReq orderQueryReq) {
        OrderQueryParam orderQueryParam = BeanUtil.copyProperties(orderQueryReq, OrderQueryParam.class);
        if (ObjectUtil.isNotEmpty((orderQueryReq.getOrderStatus()))) {
            orderQueryParam.setOrderStatusList(Collections.singletonList(orderQueryReq.getOrderStatus()));
        }
        if (StringUtils.isBlank(orderQueryParam.getEndTime()) && StringUtils.isNotBlank(orderQueryParam.getStartTime())){
            orderQueryParam.setEndTime(orderQueryParam.getStartTime()+" 23:59:59");
        }
        List<OrderInfoDO> orderInfoDOS = orderInfoMapper.selectOrderList(orderQueryParam);
        if (orderInfoDOS.isEmpty()) {
            return Collections.emptyList();
        }
        List<Long> array = orderInfoDOS.stream().map(OrderInfoDO::getId).collect(Collectors.toList());
        List<String> districtCodeList = orderInfoDOS.stream().map(item->item.getCountyCode()).distinct().collect(Collectors.toList());
        Map<String, RegionDTO> districtMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(districtCodeList)){
            List<RegionDTO> list = regionInfoService.selectRegionByCountyCodeList(districtCodeList);
            districtMap = DataUtils.listToMap(list,RegionDTO::getDistrictCode);
        }
        List<OrderSkusDO> orderSkusDOS = orderSkusMapper.selectList(new QueryWrapper<OrderSkusDO>().in("order_id", array).eq("del_flag", 0));
        List<OrderOperationLogDO> orderOperationLogDOS = orderOperationLogMapper.selectList(new QueryWrapper<OrderOperationLogDO>().in("order_id", array).eq("del_flag", 0));

        // 创建 QueryWrapper 并指定泛型为 OrderInvoiceInfoDO
        QueryWrapper<OrderInvoiceInfoDO> queryWrapper = new QueryWrapper<>();
        // 设置查询条件：订单 ID 在给定的列表中
        queryWrapper.in("order_id", array);
        // 设置查询条件：逻辑删除标志为 0（未删除）
        queryWrapper.eq("del_flag", 0);
        List<OrderInvoiceInfoDO> invoices = orderInvoiceInfoMapper.selectList(queryWrapper);

        Map<String, RegionDTO> finalDistrictMap = districtMap;
        return orderInfoDOS.stream().map(orderInfoDO -> {
            List<OrderSkusDO> orderSkusList = orderSkusDOS.stream().filter(orderSkusDO -> orderSkusDO.getOrderId().equals(orderInfoDO.getId())).collect(Collectors.toList());
            List<OrderOperationLogDO> orderOperationLogDOList = orderOperationLogDOS.stream().filter(orderOperationLogDO -> orderOperationLogDO.getOrderId().equals(orderInfoDO.getId())).collect(Collectors.toList());
            List<OrderInvoiceInfoDO> invoiceList = invoices.stream().filter(orderInvoiceInfoDO -> orderInvoiceInfoDO.getOrderId().equals(orderInfoDO.getId())).collect(Collectors.toList());
            OrderInvoiceInfoDO invoice = new OrderInvoiceInfoDO();
            if (CollectionUtil.isNotEmpty(invoiceList)){
                invoice = invoiceList.get(0);
            }
            orderInfoDO.setOrderPrice(BigDecimal.valueOf(orderInfoDO.getOrderPrice()).multiply(BigDecimal.valueOf(0.99)).longValue());
            RegionDTO regionDTO = finalDistrictMap.get(orderInfoDO.getCountyCode());
            if (orderInfoDO.getCreationMode() == 2){
                if (ObjectUtil.isNotEmpty(regionDTO)){
                    orderInfoDO.setCustomerAddress(regionDTO.getProvinceName()+regionDTO.getCityName()+regionDTO.getDistrictName()+orderInfoDO.getCustomerAddress());
                }
            }else if (orderInfoDO.getCreationMode() == 1){
              String customerAddress = orderInfoDO.getCustomerAddress();
              if (!StringUtils.isEmpty(customerAddress) && ObjectUtil.isNotEmpty(regionDTO) &&!customerAddress.contains(regionDTO.getProvinceName())){
                  orderInfoDO.setCustomerAddress(regionDTO.getProvinceName()+regionDTO.getCityName()+regionDTO.getDistrictName()+orderInfoDO.getCustomerAddress());
              }
            }
            return new OrderVo(orderInfoDO, orderSkusList, orderOperationLogDOList, invoice);
        }).collect(Collectors.toList());
    }

    @Override
    public List<OrderVo> exportOrderList(OrderQueryReq orderQueryReq){
        OrderQueryParam orderQueryParam = BeanUtil.copyProperties(orderQueryReq, OrderQueryParam.class);
        if (orderQueryReq.getOrderStatus() != null) {
            orderQueryParam.setOrderStatusList(Collections.singletonList(orderQueryReq.getOrderStatus()));
        }
        if (StringUtils.isBlank(orderQueryParam.getEndTime()) && StringUtils.isNotBlank(orderQueryParam.getStartTime())){
            orderQueryParam.setEndTime(orderQueryParam.getStartTime()+" 23:59:59");
        }
        List<OrderInfoDO> orderInfoDOS = orderInfoMapper.selectOrderList(orderQueryParam);
        if (orderInfoDOS.isEmpty()) {
            return Collections.emptyList();
        }
        List<Long> array = orderInfoDOS.stream().map(OrderInfoDO::getId).collect(Collectors.toList());
        List<OrderSkusDO> orderSkusDOS = orderSkusMapper.selectList(new QueryWrapper<OrderSkusDO>().in("order_id", array).eq("del_flag", 0));
        List<OrderOperationLogDO> orderOperationLogDOS = orderOperationLogMapper.selectList(new QueryWrapper<OrderOperationLogDO>().in("order_id", array).eq("del_flag", 0));

        // 创建 QueryWrapper 并指定泛型为 OrderInvoiceInfoDO
        QueryWrapper<OrderInvoiceInfoDO> queryWrapper = new QueryWrapper<>();
        // 设置查询条件：订单 ID 在给定的列表中
        queryWrapper.in("order_id", array);
        // 设置查询条件：逻辑删除标志为 0（未删除）
        queryWrapper.eq("del_flag", 0);
        List<OrderInvoiceInfoDO> invoices = orderInvoiceInfoMapper.selectList(queryWrapper);

        return orderInfoDOS.stream().map(orderInfoDO -> {
            List<OrderSkusDO> orderSkusList = orderSkusDOS.stream().filter(orderSkusDO -> orderSkusDO.getOrderId().equals(orderInfoDO.getId())).collect(Collectors.toList());
            List<OrderOperationLogDO> orderOperationLogDOList = orderOperationLogDOS.stream().filter(orderOperationLogDO -> orderOperationLogDO.getOrderId().equals(orderInfoDO.getId())).collect(Collectors.toList());
            List<OrderInvoiceInfoDO> invoiceList = invoices.stream().filter(orderInvoiceInfoDO -> orderInvoiceInfoDO.getOrderId().equals(orderInfoDO.getId())).collect(Collectors.toList());
            OrderInvoiceInfoDO invoice = new OrderInvoiceInfoDO();
            if (CollectionUtil.isNotEmpty(invoiceList)){
                invoice = invoiceList.get(0);
            }
            orderInfoDO.setOrderPrice(BigDecimal.valueOf(orderInfoDO.getOrderPrice()).multiply(BigDecimal.valueOf(0.99)).longValue());
            OrderVo orderVo = new OrderVo(orderInfoDO, orderSkusList, orderOperationLogDOList, invoice);
            if (ObjectUtil.isNotEmpty(orderVo)){
                if (ObjectUtil.isNotEmpty(orderVo.getOrderStatus())){
                    orderVo.setOrderStatusStr(OrderStatusEnum.of(orderVo.getOrderStatus()).getDesc());
                }
               if (ObjectUtil.isNotEmpty(orderVo.getOrderNature())){
                   orderVo.setOrderNatureStr(OrderNatureEnum.getEnum(orderVo.getOrderNature()).getMsg());
               }
               if (ObjectUtil.isNotEmpty(orderVo.getOrderType())){
                   orderVo.setOrderTypeStr(IOrderTypeEnum.of(orderVo.getOrderType()).getDesc());
               }
            }
            return orderVo ;
        }).collect(Collectors.toList());
    }

    /**
     * 查询订单列表(商家版)
     *
     * @param orderQuery4ShopReq
     */
    @Override
    public List<OrderVo> getOrderListForMerchant(OrderQuery4ShopReq orderQuery4ShopReq) {
        OrderQueryParam orderQueryParam = BeanUtil.copyProperties(orderQuery4ShopReq, OrderQueryParam.class);
        if (orderQuery4ShopReq.getOrderStatus() != null) {
            if (orderQuery4ShopReq.getOrderStatus() != 20 ){
                if (OrderStatusEnum.BO9.getCode().equals(orderQuery4ShopReq.getOrderStatus())){
                    List<Integer> orderStatusList  = new ArrayList<>();
                    orderStatusList.add(OrderStatusEnum.BO9.getCode());
                    orderStatusList.add(OrderStatusEnum.BO3.getCode());
                    orderQueryParam.setOrderStatusList(orderStatusList);
                }else {
                    orderQueryParam.setOrderStatusList(Collections.singletonList(orderQuery4ShopReq.getOrderStatus()));
                }
            }else {
                List<Integer> orderStatusList  = new ArrayList<>();
                orderStatusList.add(OrderStatusEnum.BO8.getCode());
                orderQueryParam.setOrderStatusList(orderStatusList);
            }
        }
        orderQueryParam.setKyeWord4Shop(orderQuery4ShopReq.getKyeWord());
        List<OrderInfoDTO> orderInfoDTOS = orderInfoMapper.selectOrderList4Merchant(orderQueryParam);
        if (orderInfoDTOS.isEmpty()) {
            return Collections.emptyList();
        }
        List<Long> array = orderInfoDTOS.stream()
                .map(orderInfoDTO -> {
                    if (orderInfoDTO.getOrderType() == 1) {
                        return orderInfoDTO.getId();
                    } else if (orderInfoDTO.getOrderType() == 2) {
                        return orderInfoDTO.getOrderId();
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<OrderSkusDO> orderSkusDOS = orderSkusMapper.selectList(new QueryWrapper<OrderSkusDO>().in("order_id", array).eq("del_flag", 0));
        List<OrderOperationLogDO> orderOperationLogDOS = orderOperationLogMapper.selectList(new QueryWrapper<OrderOperationLogDO>().in("order_id", array).eq("del_flag", 0));

        // 创建 QueryWrapper 并指定泛型为 OrderInvoiceInfoDO
        QueryWrapper<OrderInvoiceInfoDO> queryWrapper = new QueryWrapper<>();
        // 设置查询条件：订单 ID 在给定的列表中
        queryWrapper.in("order_id", array);
        // 设置查询条件：逻辑删除标志为 0（未删除）
        queryWrapper.eq("del_flag", 0);
        List<OrderInvoiceInfoDO> invoices = orderInvoiceInfoMapper.selectList(queryWrapper);

        return orderInfoDTOS.stream().map(orderInfoDTO -> {
            List<OrderSkusDO> orderSkusList = orderSkusDOS.stream().filter(orderSkusDO -> {
                Long compareId;
                if (orderInfoDTO.getOrderType() == 2) {
                    compareId = orderInfoDTO.getOrderId();
                } else {
                    compareId = orderInfoDTO.getId();
                }
                return orderSkusDO.getOrderId().equals(compareId);
            }).collect(Collectors.toList());
            List<OrderOperationLogDO> orderOperationLogDOList = orderOperationLogDOS.stream().filter(
                    orderOperationLogDO -> {
                        Long compareId;
                        if (orderInfoDTO.getOrderType() == 2) {
                            compareId = orderInfoDTO.getOrderId();
                        } else {
                            compareId = orderInfoDTO.getId();
                        }
                        return orderOperationLogDO.getOrderId().equals(compareId);
                    }
            ).collect(Collectors.toList());
            List<OrderInvoiceInfoDO> invoiceList = invoices.stream().filter(orderInvoiceInfoDO -> {
                Long compareId;
                if (orderInfoDTO.getOrderType() == 2) {
                    compareId = orderInfoDTO.getOrderId();
                } else {
                    compareId = orderInfoDTO.getId();
                }
                return orderInvoiceInfoDO.getOrderId().equals(compareId);
            }).collect(Collectors.toList());
            OrderInvoiceInfoDO invoice = new OrderInvoiceInfoDO();
            if (CollectionUtil.isNotEmpty(invoiceList)){
                invoice = invoiceList.get(0);
            }
            if (!ObjectUtils.isEmpty(orderInfoDTO.getOrderPrice())){
                orderInfoDTO.setOrderPrice(BigDecimal.valueOf(orderInfoDTO.getOrderPrice()).multiply(BigDecimal.valueOf(0.99)).longValue());
            }
            return new OrderVo(orderInfoDTO, orderSkusList, orderOperationLogDOList, invoice);
        }).collect(Collectors.toList());
    }

    /**
     * 查询订单数量(云门店)
     *
     * @param orderQuery4ShopReq
     */
    @Override
    public OrderCountVo getOrderCount(OrderQuery4ShopReq orderQuery4ShopReq) {
        OrderCountVo orderCountVo = new OrderCountVo();
        OrderQueryParam orderQueryParam = BeanUtil.copyProperties(orderQuery4ShopReq, OrderQueryParam.class);
        orderQueryParam.setKyeWord4Shop(orderQuery4ShopReq.getKyeWord());
        Page<Object> toReceiveCount = PageHelper.startPage(1, 1);
        orderQueryParam.setOrderStatusList(Collections.singletonList(OrderStatusEnum.BO1.getCode()));
        orderInfoMapper.selectOrderList4Merchant(orderQueryParam);
        orderCountVo.setToReceiveCount(toReceiveCount.getTotal());

        Page<Object> toSettleCount = PageHelper.startPage(1, 1);
        orderQueryParam.setOrderStatusList(Collections.singletonList(OrderStatusEnum.BO2.getCode()));
        orderInfoMapper.selectOrderList4Merchant(orderQueryParam);
        orderCountVo.setToSettleCount(toSettleCount.getTotal());

        Page<Object> auditingCount = PageHelper.startPage(1, 1);
        List<Integer> auditStatusList = new ArrayList<>();
        auditStatusList.add(OrderStatusEnum.BO9.getCode());
        auditStatusList.add(OrderStatusEnum.BO3.getCode());
        orderQueryParam.setOrderStatusList(auditStatusList);
        orderInfoMapper.selectOrderList4Merchant(orderQueryParam);
        orderCountVo.setAuditingCount(auditingCount.getTotal());

        Page<Object> settlementCount = PageHelper.startPage(1, 1);
        orderQueryParam.setOrderStatusList(Collections.singletonList(OrderStatusEnum.BO3.getCode()));
        orderInfoMapper.selectOrderList4Merchant(orderQueryParam);
        orderCountVo.setSettlementCount(settlementCount.getTotal());

        /**
         * 已结算
         */
        Page<Object> completedCount = PageHelper.startPage(1, 1);
        orderQueryParam.setOrderStatusList(Collections.singletonList(OrderStatusEnum.BO5.getCode()));
        orderInfoMapper.selectOrderList4Merchant(orderQueryParam);
        orderCountVo.setCompletedCount(completedCount.getTotal());

        /**
         * 已挂起/已取消
         */
        Page<Object> cancelCount = PageHelper.startPage(1, 1);
        List<Integer> cancelStatusList = new ArrayList<>();
        cancelStatusList.add(OrderStatusEnum.BO7.getCode());
        cancelStatusList.add(OrderStatusEnum.BO8.getCode());
        orderQueryParam.setOrderStatusList(cancelStatusList);
        orderInfoMapper.selectOrderList4Merchant(orderQueryParam);
        orderCountVo.setCancelCount(cancelCount.getTotal());
        /**
         * 已结算未开票数量
         */
        InvoiceQueryReq queryReq = new InvoiceQueryReq();
        queryReq.setInvoiceStatus(0);
        queryReq.setTeboShopId(orderQuery4ShopReq.getTeboShopId());
        queryReq.setOrderStatus(OrderStatusEnum.BO5.getCode());
        queryReq.setKyeWord(orderQuery4ShopReq.getKyeWord());
        List<InvoiceResp> invoiceRespList = orderInvoiceInfoMapper.selectOrderInvoiceInfoListForApp(queryReq);
        if (CollectionUtil.isNotEmpty(invoiceRespList)){
            orderCountVo.setInvoiceCount((long)invoiceRespList.size());
        }

        /**
         * 审核中/已完结未开票数量
         */
        InvoiceQueryReq query = new InvoiceQueryReq();
        query.setTeboShopId(orderQuery4ShopReq.getTeboShopId());
        List<Integer> examineStatusList = new ArrayList<>();
        examineStatusList.add(OrderStatusEnum.BO9.getCode());
        examineStatusList.add(OrderStatusEnum.BO3.getCode());
        query.setOrderStatusList(examineStatusList);
        query.setInvoiceStatus(0);
        query.setKyeWord(orderQuery4ShopReq.getKyeWord());
        List<InvoiceResp> list = orderInvoiceInfoMapper.selectOrderInvoiceInfoListForApp(query);
        if (CollectionUtil.isEmpty(list)){
            orderCountVo.setReviewInvoiceCount(0L);
        }else {
            orderCountVo.setReviewInvoiceCount((long)list.size());
        }
        return orderCountVo;
    }

    /**
     * 接单
     *
     * @param req
     */
    @Override
    @ReLock(prefix = RedisPreConstants.ORDER_LOCK, key = "#req.getOrderId()")
    public void acceptOrder(OrderStatusChangeReq req) {
        OrderInfoDO orderInfoDO = orderInfoMapper.selectById(req.getOrderId());
        if (orderInfoDO == null) {
            throw new ServiceException("接单失败，订单不存在");
        }
        if (!SecurityContextHolder.getUserId().equals(orderInfoDO.getTeboShopId())) {
            throw new ServiceException("接单失败，不能接单其他商家的订单");
        }
        if (!OrderStatusEnum.BO1.getCode().equals(orderInfoDO.getOrderStatus())) {
            throw new ServiceException("接单失败，订单状态不正确");
        }
        updateOrderStatus(req);
    }

    /**
     * 完结订单
     *
     * @param req
     */
    @Override
    @ReLock(prefix = RedisPreConstants.ORDER_LOCK, key = "#req.getOrderId()")
    public void completeOrder(CompleteOrderReq req) {
        OrderInfoDO orderInfoDO = orderInfoMapper.selectById(req.getOrderId());
        if (orderInfoDO == null) {
            throw new ServiceException("完结订单失败，订单不存在");
        }
        if (!SecurityContextHolder.getUserId().equals(orderInfoDO.getTeboShopId())) {
            throw new ServiceException("完结订单失败，不能完结其他商家的订单");
        }
        if (!OrderStatusEnum.BO2.getCode().equals(orderInfoDO.getOrderStatus())) {
            throw new ServiceException("完结订单失败，订单状态不正确");
        }
        OrderStatusChangeReq changeReq = new OrderStatusChangeReq();
        changeReq.setOrderId(req.getOrderId());
        if (orderInfoDO.getOrderNature() == 1){
            changeReq.setOrderOperation(OrderOperationEnum.COMPLETE.getCode());
        }else if (orderInfoDO.getOrderNature() == 2){
            changeReq.setOrderOperation(OrderOperationEnum.UPLOAD_COMPLETE.getCode());
        }
        changeReq.setUserName(SecurityContextHolder.getUserName());
        changeReq.setUploadToAuditTime(LocalDateTime.now());
        changeReq.setOrderNature(orderInfoDO.getOrderNature());
        String batteryCodeImgUrlStr = req.getBatteryCodeImgUrlList() == null ? null : String.join(",", req.getBatteryCodeImgUrlList());
        String installCompletedImgUrlStr = req.getInstallCompletedImgUrlList() == null ? null : String.join(",", req.getInstallCompletedImgUrlList());
        String signedImgUrlStr = req.getSignedImgUrlList() == null ? null : String.join(",", req.getSignedImgUrlList());
        changeReq.setBatteryCodeImgUrlStr(batteryCodeImgUrlStr);
        changeReq.setInstallCompletedImgUrlStr(installCompletedImgUrlStr);
        changeReq.setSignedImgUrlStr(signedImgUrlStr);
        changeReq.setEmployeeId(SecurityContextHolder.getAccountId());
        updateOrderStatus(changeReq);
    }

    /**
     * 取消订单
     *
     * @param orderId
     */
    @Override
    @Transactional
    @ReLock(prefix = RedisPreConstants.ORDER_LOCK, key = "#orderId")
    public void cancelOrder(Long orderId) {
        OrderInfoDO orderInfoDO = orderInfoMapper.selectById(orderId);
        if (orderInfoDO == null) {
            throw new ServiceException("订单不存在");
        }
        if (orderInfoDO.getOrderStatus() == OrderStatusEnum.BO8.getCode()){
            throw new ServiceException("订单已取消，不能再操作");
        }
        if (OrderStatusEnum.BO5.getCode().equals(orderInfoDO.getOrderStatus()) || OrderStatusEnum.BO6.getCode().equals(orderInfoDO.getOrderStatus())) {
            throw new ServiceException("订单已结算，不能再操作");
        }
        OrderStatusChangeReq changeReq = new OrderStatusChangeReq();
        changeReq.setOrderId(orderId);
        changeReq.setOrderOperation(OrderOperationEnum.CANCEL.getCode());
        changeReq.setUserName(SecurityContextHolder.getUserName());
        updateOrderStatus(changeReq);
        if (orderInfoDO.getOrderNature() == 2){
            confirmService.cancelOrder(orderInfoDO.getBatchId()+"-"+orderInfoDO.getOrderNo());
        }

    }

    /**
     * 取消订单(门店)
     *
     * @param orderId
     */
    @Override
    public void cancelOrderByMerchant(Long orderId) {
        OrderInfoDO orderInfoDO = orderInfoMapper.selectById(orderId);
        if (orderInfoDO == null) {
            throw new ServiceException("订单不存在");
        }
        if (!OrderStatusEnum.BO1.getCode().equals(orderInfoDO.getOrderStatus())) {
            throw new ServiceException("订单已分配，不能再操作");
        }
        OrderStatusChangeReq changeReq = new OrderStatusChangeReq();
        changeReq.setOrderId(orderId);
        changeReq.setOrderOperation(OrderOperationEnum.CANCEL.getCode());
        changeReq.setUserName(SecurityContextHolder.getUserName());
        updateOrderStatus(changeReq);
    }

    /**
     * 强制完结订单
     *
     * @param orderId
     */
    @Override
    @ReLock(prefix = RedisPreConstants.ORDER_LOCK, key = "#orderId")
    public void forceCompleteOrder(Long orderId) {
        OrderInfoDO orderInfoDO = orderInfoMapper.selectById(orderId);
        if (orderInfoDO == null) {
            throw new ServiceException("订单不存在");
        }
        if (OrderStatusEnum.BO3.getCode().equals(orderInfoDO.getOrderStatus()) || OrderStatusEnum.BO5.getCode().equals(orderInfoDO.getOrderStatus()) || OrderStatusEnum.BO6.getCode().equals(orderInfoDO.getOrderStatus())) {
            throw new ServiceException("订单已完结，不能再操作");
        }
        if (OrderStatusEnum.BO8.getCode().equals(orderInfoDO.getOrderStatus())) {
            throw new ServiceException("订单已取消，不能强制完结");
        }
        if (OrderStatusEnum.BO7.getCode().equals(orderInfoDO.getOrderStatus())) {
            throw new ServiceException("订单已挂起，不能强制完结");
        }
        OrderStatusChangeReq changeReq = new OrderStatusChangeReq();
        changeReq.setOrderId(orderId);
        /**
         * 自营订单已完结,非自营订单已结算
         */
        if (orderInfoDO.getOrderNature() == 1){
            changeReq.setOrderOperation(OrderOperationEnum.COMPLETE.getCode());
            changeReq.setUploadToAuditTime(LocalDateTimeUtil.now());
        }else if (orderInfoDO.getOrderNature() == 2){
            changeReq.setOrderOperation(OrderOperationEnum.SETTLE.getCode());
            changeReq.setSettlementTime(LocalDateTimeUtil.now());
        }
        changeReq.setUserName(SecurityContextHolder.getUserName());
        updateOrderStatus(changeReq);
        /**
         * 通过order_no找到预览数据
         */
        if ((orderInfoDO.getOrderNature() == 2)){
            confirmOrderPay.confirmOrderPay(orderInfoDO.getBatchId() +"-"+orderInfoDO.getOrderNo(),orderInfoDO.getTeboShopId());
        }

    }

    /**
     * 商铺端完结确认
     *
     * @param orderId
     */
    @Override
    @ReLock(prefix = RedisPreConstants.ORDER_LOCK, key = "#orderId")
    public void confirmationComplete(Long orderId) {
        OrderInfoDO orderInfoDO = orderInfoMapper.selectById(orderId);
        if (orderInfoDO == null) {
            throw new ServiceException("订单不存在");
        }
        if (OrderStatusEnum.BO5.getCode().equals(orderInfoDO.getOrderStatus()) || OrderStatusEnum.BO6.getCode().equals(orderInfoDO.getOrderStatus())) {
            throw new ServiceException("订单已结算，不能再操作");
        }
        if (OrderStatusEnum.BO8.getCode().equals(orderInfoDO.getOrderStatus())) {
            throw new ServiceException("订单已取消，不能强制完结");
        }
        if (OrderStatusEnum.BO7.getCode().equals(orderInfoDO.getOrderStatus())) {
            throw new ServiceException("订单已挂起，不能强制完结");
        }
        if (orderInfoDO.getOrderNature() == 1) {
            throw new ServiceException("自营订单不能在商铺端操作");
        }
        OrderStatusChangeReq changeReq = new OrderStatusChangeReq();
        changeReq.setOrderId(orderId);
        /**
         * 自营订单已完结,非自营订单已结算
         */
        changeReq.setOrderOperation(OrderOperationEnum.SETTLE.getCode());
        changeReq.setSettlementTime(LocalDateTimeUtil.now());
        changeReq.setUserName(SecurityContextHolder.getUserName());
        updateOrderStatus(changeReq);
        /**
         * 打钱
         */
        confirmOrderPay.confirmOrderPay(orderInfoDO.getBatchId()+"-"+orderInfoDO.getOrderNo(),orderInfoDO.getTeboShopId());
    }

    /**
     * 审核订单
     *
     * @param req
     */
    @Override
    @ReLock(prefix = RedisPreConstants.ORDER_LOCK, key = "#req.getOrderId()")
    public void audit(AuditOrderReq req) {
        OrderInfoDO orderInfoDO = orderInfoMapper.selectById(req.getOrderId());
        if (orderInfoDO == null) {
            throw new ServiceException("订单不存在");
        }
        if (!OrderOperationEnum.AUDIT_PASS_TO_COMPLETE.getCode().equals(req.getOperation()) && !OrderOperationEnum.AUDIT_REJECT_TO_COMPLETE.getCode().equals(req.getOperation())) {
            throw new ServiceException("审核订单失败，操作类型不正确");
        }
        if (!OrderStatusEnum.BO9.getCode().equals(orderInfoDO.getOrderStatus())) {
            throw new ServiceException("审核订单失败，订单状态不正确");
        }
        OrderOperationEnum orderOperationEnum = OrderOperationEnum.of(req.getOperation());
        OrderStatusChangeReq changeReq = new OrderStatusChangeReq();
        changeReq.setOrderId(req.getOrderId());
        changeReq.setOrderOperation(orderOperationEnum.getCode());
        changeReq.setUserName(SecurityContextHolder.getUserName());
        changeReq.setAuditTime(LocalDateTime.now());
        changeReq.setRemark(req.getAuditRemark());
        updateOrderStatus(changeReq);
    }

    /**
     * 挂起订单
     *
     * @param req
     */
    @Override
    @Transactional
    @ReLock(prefix = RedisPreConstants.ORDER_LOCK, key = "#req.getOrderId()")
    public void suspendOrder(SuspendOrderReq req) {
        OrderInfoDO orderInfoDO = orderInfoMapper.selectById(req.getOrderId());
        if (orderInfoDO == null) {
            throw new ServiceException("订单不存在");
        }
        if (OrderStatusEnum.BO5.getCode().equals(orderInfoDO.getOrderStatus())) {
              throw new ServiceException("已结算订单，不能再操作");
        }
        if (OrderStatusEnum.BO7.getCode().equals(orderInfoDO.getOrderStatus())) {
            throw new ServiceException("订单已挂起，不能再操作");
        }
        if (!OrderStatusEnum.BO2.getCode().equals(orderInfoDO.getOrderStatus())) {
          //  throw new ServiceException("订单已挂起，不能再操作");
        }
        OrderStatusChangeReq changeReq = new OrderStatusChangeReq();
        changeReq.setOrderId(req.getOrderId());
        changeReq.setOrderOperation(OrderOperationEnum.SUSPEND.getCode());
        /**
         * 挂起的时候清除原服务商信息
         */
        clearShopInfo(req.getOrderId());
        changeReq.setUserName(SecurityContextHolder.getUserName());
        changeReq.setRemark(req.getRemark());
        updateOrderStatus(changeReq);
    }

    /**
     * 修改订单
     *
     * @param editOrderReq
     * @param plat true:平台 false:门店
     */
    @Override
    @Transactional
    @ReLock(prefix = RedisPreConstants.ORDER_LOCK, key = "#editOrderReq.getOrderId()")
    public void updateOrder(EditOrderReq editOrderReq, Boolean plat) {
        OrderInfoDO orderInfoDO = orderInfoMapper.selectById(editOrderReq.getOrderId());
        if (orderInfoDO == null) {
            throw new ServiceException("订单不存在");
        }
        if (!OrderStatusEnum.BO0.getCode().equals(orderInfoDO.getOrderStatus()) && !OrderStatusEnum.BO7.getCode().equals(orderInfoDO.getOrderStatus())) {
            throw new ServiceException("当前订单状态不允许修改");
        }
        if (StringUtils.isBlank(editOrderReq.getProductSkuNo())) {
            throw new ServiceException("商品SKU不能为空");
        }
        if (editOrderReq.getAddressCode().size() != 3) {
            throw new ServiceException("地址编码不正确");
        }

        // Store original values for comparison
        OrderInfoDO originalOrderInfoDO = BeanUtil.copyProperties(orderInfoDO, OrderInfoDO.class);
        List<OrderSkusDO> orderSkusDOS = orderSkusMapper.selectList(new QueryWrapper<OrderSkusDO>().eq("order_id", editOrderReq.getOrderId()).eq("del_flag", 0));
        if (orderSkusDOS.isEmpty()) {
            throw new ServiceException("订单商品不存在");
        }
        OrderSkusDO originalOrderSkusDO = BeanUtil.copyProperties(orderSkusDOS.get(0), OrderSkusDO.class);

        // Update fields
        String provinceCode = editOrderReq.getAddressCode().get(0);
        String cityCode = editOrderReq.getAddressCode().get(1);
        String districtCode = editOrderReq.getAddressCode().get(2);
        ProductPriceListVo productPrice = productLevelService.getProductPrice(editOrderReq.getProductSkuNo(), districtCode);
        if (productPrice == null) {
            throw new ServiceException("该地区未找到对应的商品价格");
        }
        Long teboShopId = editOrderReq.getTeboShopId();
        String teboShopName = editOrderReq.getTeboShopName();
        String teboShopPhone = editOrderReq.getTeboShopPhone();
        if (!plat) {
            ShopServerArea serverArea = shopServerAreaService.selectRegion(districtCode);
            if (serverArea == null) {
                throw new ServiceException("未找到对应的区域服务商");
            }
            teboShopId = serverArea.getTeboShopId();
            teboShopName = serverArea.getLstShopName();
            teboShopPhone = serverArea.getPhoneNumber();
        }
        if (teboShopId!=  null && !teboShopId.equals(orderInfoDO.getTeboShopId())){
            disMqService.sendShopNotifyMsg(teboShopId,orderInfoDO.getOrderNo(), LocalDateUtil.formatTime(LocalDateTime.now()),1);
        }
        orderInfoDO.setOrderStatus(OrderStatusEnum.BO1.getCode());
        orderInfoDO.setProvinceCode(provinceCode);
        orderInfoDO.setCityCode(cityCode);
        orderInfoDO.setCountyCode(districtCode);
        orderInfoDO.setTeboShopId(teboShopId);
        orderInfoDO.setTeboShopName(teboShopName);
        orderInfoDO.setTeboShopPhone(teboShopPhone);
        orderInfoDO.setRemarkUser(editOrderReq.getRemarkUser());
        orderInfoDO.setRemarkPlatform(editOrderReq.getRemarkPlatform());
        orderInfoDO.setCustomerName(editOrderReq.getCustomerName());
        orderInfoDO.setCustomerPhone(editOrderReq.getCustomerPhone());
        orderInfoDO.setCustomerAddress(editOrderReq.getCustomerAddress());
        orderInfoDO.setOrderPrice(productPrice.getRefPriceFen());

        OrderSkusDO orderSkusDO = orderSkusDOS.get(0);
        orderSkusDO.setProductSkuCode(productPrice.getProductSkuCode());
        orderSkusDO.setProductName(productPrice.getProductName());
        orderSkusDO.setProductCategory(productPrice.getProductCategory());
        orderSkusDO.setProductSecondCategory(productPrice.getProductSecondCategory());
        orderSkusDO.setProductMode(productPrice.getProductMode());
        orderSkusDO.setRefSettlePrice(productPrice.getRefPriceFen());

        // Save changes
        orderSkusMapper.updateById(orderSkusDO);
        orderInfoMapper.updateById(orderInfoDO);

        // Track changes
        StringBuilder remarkBuilder = new StringBuilder();
        if (!Objects.equals(originalOrderInfoDO.getProvinceCode(), orderInfoDO.getProvinceCode())) {
            remarkBuilder.append("省份由 ").append(originalOrderInfoDO.getProvinceCode()).append(" 修改为 ").append(orderInfoDO.getProvinceCode()).append("; ");
        }
        if (!Objects.equals(originalOrderInfoDO.getCityCode(), orderInfoDO.getCityCode())) {
            remarkBuilder.append("城市由 ").append(originalOrderInfoDO.getCityCode()).append(" 修改为 ").append(orderInfoDO.getCityCode()).append("; ");
        }
        if (!Objects.equals(originalOrderInfoDO.getCountyCode(), orderInfoDO.getCountyCode())) {
            remarkBuilder.append("区县由 ").append(originalOrderInfoDO.getCountyCode()).append(" 修改为 ").append(orderInfoDO.getCountyCode()).append("; ");
        }
        if (!Objects.equals(originalOrderInfoDO.getCustomerName(), orderInfoDO.getCustomerName())) {
            remarkBuilder.append("客户名称由 ").append(originalOrderInfoDO.getCustomerName()).append(" 修改为 ").append(orderInfoDO.getCustomerName()).append("; ");
        }
        if (!Objects.equals(originalOrderInfoDO.getCustomerPhone(), orderInfoDO.getCustomerPhone())) {
            remarkBuilder.append("客户电话由 ").append(originalOrderInfoDO.getCustomerPhone()).append(" 修改为 ").append(orderInfoDO.getCustomerPhone()).append("; ");
        }
        if (!Objects.equals(originalOrderInfoDO.getCustomerAddress(), orderInfoDO.getCustomerAddress())) {
            remarkBuilder.append("客户地址由 ").append(originalOrderInfoDO.getCustomerAddress()).append(" 修改为 ").append(orderInfoDO.getCustomerAddress()).append("; ");
        }
        if (!Objects.equals(originalOrderSkusDO.getProductSkuCode(), orderSkusDO.getProductSkuCode())) {
            remarkBuilder.append("商品SKU由 ").append(originalOrderSkusDO.getProductSkuCode()).append(" 修改为 ").append(orderSkusDO.getProductSkuCode()).append("; ");
        }
        if (!Objects.equals(originalOrderSkusDO.getProductName(), orderSkusDO.getProductName())) {
            remarkBuilder.append("商品名称由 ").append(originalOrderSkusDO.getProductName()).append(" 修改为 ").append(orderSkusDO.getProductName()).append("; ");
        }
        if (!Objects.equals(originalOrderInfoDO.getTeboShopName(), orderInfoDO.getTeboShopName())) {
            remarkBuilder.append("服务商由 ")
                    .append(StringUtils.defaultString(originalOrderInfoDO.getTeboShopName(), " "))
                    .append(" 修改为 ")
                    .append(StringUtils.defaultString(orderInfoDO.getTeboShopName(), " "))
                    .append("; ");
        }

        // Log operation
        OrderOperationLogDO operationLogs = new OrderOperationLogDO();
        operationLogs.setOrderId(orderInfoDO.getId());
        operationLogs.setOperationType(OrderOperationEnum.EDIT.getCode());
        operationLogs.setOperationValue(OrderOperationEnum.EDIT.getDesc());
        operationLogs.setContent(OrderOperationEnum.EDIT.getDesc());
        operationLogs.setServiceCode(orderInfoDO.getServiceOrderNo());
        operationLogs.setCreateBy(SecurityContextHolder.getUserName());
        operationLogs.setCreateById(SecurityContextHolder.getUserId().toString());
        operationLogs.setCreateByPhone(SecurityContextHolder.getUserPhone());
        operationLogs.setRemark(remarkBuilder.toString());
        orderOperationLogService.recordOrderOperations(Collections.singletonList(operationLogs));
    }

    /**
     * 修改订单
     *
     * @param editOrderReq
     */
    @Override
    @ReLock(prefix = RedisPreConstants.ORDER_LOCK, key = "#editOrderReq.getOrderId()")
    public void updateOrderUserRemark(OrderUpdateReq editOrderReq) {
        OrderInfoDO orderInfoDO = orderInfoMapper.selectById(editOrderReq.getOrderId());
        if (orderInfoDO == null) {
            throw new ServiceException("订单不存在");
        }
        OrderInfoDO newOrder = new OrderInfoDO();
        newOrder.setId(editOrderReq.getOrderId());
        newOrder.setRemarkPlatform(editOrderReq.getRemarkPlatform());
        orderInfoMapper.updateById(newOrder);
    }

    /**
     * 结算订单
     *
     * @param batchSettlementOrderReq
     */
    @Override
    public void importSettleOrder(BatchSettlementOrderReq batchSettlementOrderReq) {
        List<OrderInfoDO> orderInfoDOS = orderInfoMapper.selectList(new QueryWrapper<OrderInfoDO>().eq("order_no", batchSettlementOrderReq.getOrderNo()).eq("del_flag", 0));
        if (orderInfoDOS.isEmpty()) {
            throw new ServiceException("订单不存在");
        }
        for (OrderInfoDO orderInfoDO : orderInfoDOS) {
            if (!OrderStatusEnum.BO3.getCode().equals(orderInfoDO.getOrderStatus())) {
                throw new ServiceException("订单状态不是已完结，不能进行结算价导入");
            }
            OrderStatusChangeReq changeReq = new OrderStatusChangeReq();
            changeReq.setOrderId(orderInfoDO.getId());
            changeReq.setOrderOperation(OrderOperationEnum.SETTLE.getCode());
            changeReq.setUserName(SecurityContextHolder.getUserName());
            // 将 LocalDate 转换为 LocalDateTime，时间部分默认为 00:00:00
            LocalDateTime localDateTime = DateParser.parseDate(batchSettlementOrderReq.getSettlementDate()).atStartOfDay();
            changeReq.setSettlementTime(localDateTime);
            updateOrderStatus(changeReq);
            // 结算订单
           // LstOrderDTO lstOrderDTO = new LstOrderDTO();
           // lstOrderDTO.setOrderAmount(orderInfoDO.getOrderPrice().intValue());
           // lstOrderDTO.setMerchantStoreId(orderInfoDO.getMerchantId());
           // lstOrderDTO.setShopId(orderInfoDO.getTeboShopId());
            //lstOrderDTO.setServiceOrderNo(orderInfoDO.getServiceOrderNo());
           // orderBalanceService.orderSettlement(lstOrderDTO);
        }
    }

    @Override
    public BusinessStats getBusinessStats(OrderQueryParam orderQueryParam) {
        BusinessStats businessStats = new BusinessStats();
        Page<Object> totalOrder = PageHelper.startPage(1, 1);
        orderInfoMapper.selectOrderList(orderQueryParam);
        businessStats.setTotalOrders(totalOrder.getTotal());

        Page<Object> completeOrder = PageHelper.startPage(1, 1);
        orderQueryParam.setOrderStatusList(Collections.singletonList(OrderStatusEnum.BO5.getCode()));
        orderInfoMapper.selectOrderList(orderQueryParam);
        businessStats.setCompletedOrders(completeOrder.getTotal());

        Page<Object> ongoingOrder = PageHelper.startPage(1, 1);
        orderQueryParam.setOrderStatusList(Arrays.asList(0,1,2,3,4,6,7,9));
        orderInfoMapper.selectOrderList(orderQueryParam);
        businessStats.setOngoingOrders(ongoingOrder.getTotal());

        Page<Object> abnormalOrder = PageHelper.startPage(1, 1);
        orderQueryParam.setOrderStatusList(Collections.singletonList(OrderStatusEnum.BO7.getCode()));
        orderInfoMapper.selectOrderList(orderQueryParam);
        businessStats.setAbnormalOrders(abnormalOrder.getTotal());
        return businessStats;
    }
    @Override
    public void clearShopInfo(Long orderId){
        orderInfoMapper.clearShopInfo(orderId);
    }

}
