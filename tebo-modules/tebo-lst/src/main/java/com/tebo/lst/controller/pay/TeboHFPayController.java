package com.tebo.lst.controller.pay;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.utils.uuid.UUID;
import com.tebo.lst.controller.domain.TeboHFAppletPayDTO;
import com.tebo.lst.controller.pay.manage.domain.TeboHFAppletPayVO;
import com.tebo.lst.controller.pay.manage.domain.TeboHFPayDTO;
import com.tebo.lst.controller.pay.manage.domain.TeboPayRecordVO;
import com.tebo.lst.customer.service.TeboDraftOrderService;
import com.tebo.lst.service.order.TeboOrderCommonService;
import com.tebo.lst.service.pay.TeboPayConfirmService;
import com.tebo.lst.service.pay.TeboPayService;
import com.tebo.system.api.domain.dto.PayOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/hf/pay")
@Slf4j
public class TeboHFPayController {


    @Resource
    private TeboPayService teboPayService;
    @Resource
    private TeboPayConfirmService teboPayConfirmService;
    @Resource
    private TeboOrderCommonService teboOrderCommonService;

    /**
     * 门店保证金支付
     */
    @PostMapping("/shop/appletPay")
    public R<?> appletPay(@RequestBody TeboHFAppletPayDTO payOrderDTO) {
        return R.ok(teboPayService.appletPay(payOrderDTO)) ;
    }


    /**
     * 门店保证金支付回调
     */
    @PostMapping("/shop/appletPayNotify")
    public R<?> appletPayNotify(@RequestParam("orderNo") String orderNo) {
        // 1. 门店保证金缴纳记录更新为已支付
        // 2. 门店状态更新为已缴纳保证金/支付时间/缴纳状态
        return R.ok() ;
    }

    /**
     * 创建商户订单
     */
    @PostMapping("/merchant/createOrder")
    public R<?> createOrder(@RequestBody TeboHFPayDTO payOrderDTO) {
        return R.ok(teboPayService.createOrder(payOrderDTO.getBatchId())) ;
    }

    /**
     * 刷新订单支付二维码
     */
    @PostMapping("/merchant/flashQrCode")
    public R<?> flashQrCode(@RequestBody TeboHFPayDTO payOrderDTO) {
        return R.ok(teboPayService.flashQrCode(payOrderDTO.getBatchId())) ;
    }


    /**
     * 商户订单支付
     * @param payOrderDTO
     * @return
     */
    @PostMapping("/merchant/orderPay")
    public R<?> orderPay(@RequestBody TeboHFAppletPayDTO payOrderDTO) {
        return R.ok(teboPayService.orderPay(payOrderDTO)) ;
    }

    /**
     * 支付回调
     */
    @GetMapping("/merchant/payNotify")
    public R<?> orderPayNotify(@RequestParam("orderNo") String orderNo) {
        teboPayService.orderPayNotify(orderNo);
        return R.ok() ;
    }

//    /**
//     * 支付货款
//     */
//    @PostMapping("/merchant/confirmOrderPay")
//    public R<?> confirmOrderPay(@RequestBody TeboHFAppletPayDTO payOrderDTO) {
//        teboPayConfirmService.confirmOrderPay(payOrderDTO.getOrderNo(), payOrderDTO.getShopId());
//        return R.ok() ;
//    }


    /**
     * 订单取消
     */
    @PostMapping("/merchant/cancelOrder")
    public R<?> cancelOrder(@RequestBody TeboHFAppletPayDTO payOrderDTO) {
        teboPayConfirmService.cancelOrder(payOrderDTO.getOrderNo());
        return R.ok() ;
    }

    /**
     * 订单退款回调
     */
    @PostMapping("/merchant/cancelOrderNotify")
    public R<?> cancelOrderNotify(@RequestBody TeboHFAppletPayDTO payOrderDTO) {
        if (ObjectUtils.isEmpty(payOrderDTO.getType()) || payOrderDTO.getType() == 1) {
            // 订单退款回调
            teboPayService.cancelOrderNotify(payOrderDTO.getOrderId(),payOrderDTO.getCancelAmount(),payOrderDTO.getCancelFeeAmount());
        }
        if (payOrderDTO.getType() == 2) {
            // 门店保证金退款回调
            teboPayService.shopRefundPayNotify(payOrderDTO.getOrderId(),payOrderDTO.getCancelAmount(),payOrderDTO.getCancelFeeAmount());
        }
        return R.ok() ;
    }


    /**
     * 门店保证金退款
     */
    @PostMapping("/shop/refundPay")
    public R<?> shopRefundPay(@RequestBody TeboHFAppletPayDTO payOrderDTO) {
        teboPayService.shopRefundPay(payOrderDTO.getOrderNo());
        return R.ok() ;
    }

    /**
     * 门店保证金记录
     */
    @GetMapping("/shop/payRecord")
    public R<List<TeboPayRecordVO>> shopPayRecord(@RequestParam("shopId") Long shopId) {
        return R.ok(teboPayService.selectShopPayRecord(shopId)) ;
    }

    /**
     * 门店保证金已支付记录
     */
    @GetMapping("/shop/payRecordForPC")
    public R<List<TeboPayRecordVO>> shopPayRecordForPC(@RequestParam("shopId") Long shopId) {
        Map<String,Object> check = teboOrderCommonService.checkRefundShopPay(shopId);
        if (!check.get("code").toString().equals("200")) {
            return R.fail(Integer.parseInt(check.get("code").toString()), (String) check.get("msg"));
        }
        List<TeboPayRecordVO> list = teboPayService.selectShopPayRecord(shopId);
        if (CollectionUtils.isEmpty(list)) {
            return R.ok(Collections.emptyList()) ;
        }
        return R.ok(list.stream().filter(teboPayRecordVO -> teboPayRecordVO.getPayStatus() == 2).collect(Collectors.toList())) ;
    }

    /**
     * 门店保证金已支付记录
     */
    @GetMapping("/shop/payRecordForApplet")
    public R<List<TeboPayRecordVO>> payRecordForApplet(@RequestParam("shopId") Long shopId) {
        List<TeboPayRecordVO> list = teboPayService.selectShopPayRecord(shopId);
        if (CollectionUtils.isEmpty(list)) {
            return R.ok(Collections.emptyList()) ;
        }
        return R.ok(list.stream().filter(teboPayRecordVO -> teboPayRecordVO.getPayStatus() == 2).collect(Collectors.toList())) ;
    }


}
