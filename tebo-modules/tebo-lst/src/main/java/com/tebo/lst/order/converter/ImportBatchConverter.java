package com.tebo.lst.order.converter;

import com.tebo.lst.order.domain.dataobject.ImportBatchDO;
import com.tebo.lst.order.domain.model.ImportBatch;
import com.tebo.lst.order.domain.model.StagingOrder;

import java.util.List;

public class ImportBatchConverter {

    // 转换为持久化对象（用于存储）
    public static ImportBatchDO toPersistenceObject(ImportBatch batch) {
        ImportBatchDO dobj = new ImportBatchDO();
        dobj.setId(batch.getId());
        dobj.setBatchNo(batch.getBatchNo());
        dobj.setMerchantStoreId(batch.getMerchantStoreId());
        dobj.setFileUrl(batch.getFileUrl());
        dobj.setImportTime(batch.getImportTime());
        // 统计字段转换
        dobj.setTotalCount(batch.getTotalCount());
        dobj.setSuccessCount(batch.getSuccessCount());
        dobj.setPendingCount(batch.getPendingCount());
        dobj.setFailCount(batch.getFailCount());
        // 状态转换
        dobj.setStatus(batch.getStatus().getCode());
        return dobj;
    }

    public static ImportBatch toDomainObject(ImportBatchDO importBatchDO, List<StagingOrder> stagingOrderDOS) {
        return ImportBatch.reconstruct(importBatchDO.getId(), importBatchDO.getBatchNo(), importBatchDO.getMerchantStoreId(),
                importBatchDO.getFileUrl(), importBatchDO.getImportTime(),
                importBatchDO.getStatus(), importBatchDO.getTotalCount(),
                importBatchDO.getSuccessCount(), importBatchDO.getPendingCount(), importBatchDO.getFailCount(), stagingOrderDOS);
    }
}
