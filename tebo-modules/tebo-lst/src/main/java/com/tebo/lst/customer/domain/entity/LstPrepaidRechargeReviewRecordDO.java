package com.tebo.lst.customer.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 预充值审核记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@Getter
@Setter
@TableName("lst_prepaid_recharge_review_record")
public class LstPrepaidRechargeReviewRecordDO extends Model<LstPrepaidRechargeReviewRecordDO> {

    private static final long serialVersionUID = 1L;

    @TableField("id")
    private Long id;

    /**
     * 预充值id
     */
    @TableField("pre_recharge_id")
    private Long preRechargeId;

    /**
     *  审核状态 1：待审核  2：审核通过 3：审核驳回
     */
    @TableField("review_status")
    private Integer reviewStatus;

    /**
     * 审核人员
     */
    @TableField("reviewer")
    private String reviewer;

    /**
     * 备注
     */
    @TableField("review_remark")
    private String reviewRemark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    @Override
    public Serializable pkVal() {
        return null;
    }

}
