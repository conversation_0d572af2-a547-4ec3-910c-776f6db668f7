package com.tebo.lst.customer.domain.vo;


import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 资金调拨单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Data
public class FundTransferFormVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 关联订单编号
     */
    private String associationOrderNo;

    /**
     * 调拨单号
     */
    private String orderNo;

    /**
     * 调出单位 1:商铺  2:服务商 3:平台
     */
    private Integer transferOutUnit;

    /**
     * 调出单位名称
     */
    private Integer transferOutName;

    /**
     * 调拨类型 1：商铺调拨至服务商 2：商铺调拨至平台 3：服务商调拨至商铺 4：服务商调拨至平台 5：平台调拨至商铺  6：平台调拨至服务商
     */
    private Integer transferType;

    /**
     * 调出商铺id
     */
    private Long transferOutMerchantId;

    /**
     * 调出门店id
     */
    private Long transferOutShopId;

    /**
     * 调入单位 1:商铺  2:服务商 3:平台
     */
    private Integer transferredUnit;

    /**
     * 调入商铺id
     */
    private Long transferredMerchantId;

    /**
     * 调入门店id
     */
    private Long transferredShopId;

    /**
     * 调入单位名称
     */
    private String transferredUnitName;

    /**
     * 调拨金额
     */
    private Integer transferAmount;

    /**
     * 调拨原因
     */
    private String transferReason;

    /**
     * 审核状态
     */
    private Integer reviewStatus;

    /**
     * 审核人
     */
    private String reviewer;

    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 调拨人
     */
    private String transferor;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

}
