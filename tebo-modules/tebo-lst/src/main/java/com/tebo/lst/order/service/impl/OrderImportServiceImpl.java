package com.tebo.lst.order.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.security.utils.SecurityUtils;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.lst.customer.domain.entity.MerchantStoreDO;
import com.tebo.lst.customer.domain.entity.TeboDraftOrderDO;
import com.tebo.lst.customer.domain.req.MerchantStoreBalanceQueryDTO;
import com.tebo.lst.customer.domain.vo.MerchantStoreDetailVo;
import com.tebo.lst.customer.mapper.TeboDraftOrderMapper;
import com.tebo.lst.customer.service.IMerchantStoreService;
import com.tebo.lst.order.converter.ImportBatchConverter;
import com.tebo.lst.order.domain.dataobject.ImportBatchDO;
import com.tebo.lst.order.domain.dto.StagingOrderImportDTO;
import com.tebo.lst.order.domain.model.*;
import com.tebo.lst.order.domain.req.AddOrderReq;
import com.tebo.lst.order.enums.BatchStatusEnum;
import com.tebo.lst.order.enums.StagingOrderStatusEnum;
import com.tebo.lst.order.mapper.ImportBatchMapper;
import com.tebo.lst.order.mapper.StagingOrderMapper;
import com.tebo.lst.order.repository.ImportBatchRepository;
import com.tebo.lst.order.service.*;
import com.tebo.lst.product.domain.vo.ProductPriceListVo;
import com.tebo.lst.product.service.IProductLevelService;
import com.tebo.lst.shopArea.domain.ShopServerArea;
import com.tebo.lst.shopArea.service.IShopServerAreaService;
import com.tebo.lst.utils.SystemSecurityUtils;
import com.tebo.lst.utils.TeboNumberGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;

/**
 * 订单导入服务
 */
@Service
@Slf4j
public class OrderImportServiceImpl implements IOrderImportService {

    @Resource
    private ImportBatchRepository batchRepository;
    @Resource
    private IMerchantStoreService iMerchantStoreService;

    @Resource
    private StagingOrderMapper stagingOrderMapper;
    @Resource
    private ImportBatchMapper importBatchMapper;

    @Resource
    private SyncImportService syncImportService;

    @Resource
    private TeboDraftOrderMapper teboDraftOrderMapper;

    @Resource
    private IOrderInfoService orderInfoService;

    @Resource
    private IProductLevelService productLevelService;

    @Resource
    private IShopServerAreaService shopServerAreaService;

    @Autowired
    @Qualifier("importTaskExecutor")
    private Executor taskExecutor;

    @Resource(name = "confirmOrderExecutor")
    private Executor confirmOrderExecutor;

    /**
     * 导入订单
     * @param fileUrl 文件URL
     * @param orderDtos 订单DTO列表
     * @param userId 用户ID
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ImportBatch handleImportForPlat(String fileUrl, List<StagingOrderImportDTO> orderDtos, Long userId) {
        // 1. 通过工厂方法创建聚合根
        ImportBatch batch = ImportBatch.create(
                userId,
                fileUrl
        );
        batch.setTotalCount(orderDtos.size());
        batch.setPendingCount(orderDtos.size());
        ImportBatchDO importBatchDO = ImportBatchConverter.toPersistenceObject(batch);
        SystemSecurityUtils.fillAuditFields(importBatchDO);
        importBatchMapper.insertImportBatch(importBatchDO);
        batch.setId(importBatchDO.getId());
        ConcurrentHashMap<String, Boolean> seenOrderNos = new ConcurrentHashMap<>();
        orderDtos.forEach(item ->{
            if (seenOrderNos.putIfAbsent(item.getOrderNo(), Boolean.TRUE) != null){
                throw new ServiceException(item.getOrderNo()+"订单号重复，请删除后再导入");
            }
        });
        // 异步处理
        taskExecutor.execute(() -> {
            try {
                processBatchAsyncForPlat(batch.getId(), orderDtos);
            } catch (Exception e) {
                log.error("批次处理异常", e);
                batchRepository.updateBatchStatus(batch.getId(), BatchStatusEnum.FAILED);
            }
        });
        return batch;
    }

    /**
     * 导入订单
     * @param fileUrl 文件URL
     * @param orderDtos 订单DTO列表
     * @param userId 用户ID
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ImportBatch handleImportForMerchant(String fileUrl, List<StagingOrderImportDTO> orderDtos, Long userId) {
        // 1. 通过工厂方法创建聚合根
        ImportBatch batch = ImportBatch.create(
                userId,
                fileUrl
        );
        batch.setTotalCount(orderDtos.size());
        batch.setPendingCount(orderDtos.size());
        ImportBatchDO importBatchDO = ImportBatchConverter.toPersistenceObject(batch);
        SystemSecurityUtils.fillAuditFields(importBatchDO);
        importBatchMapper.insertImportBatch(importBatchDO);
        batch.setId(importBatchDO.getId());
        ConcurrentHashMap<String, Boolean> seenOrderNos = new ConcurrentHashMap<>();
        orderDtos.forEach(item ->{
            if (seenOrderNos.putIfAbsent(item.getOrderNo(), Boolean.TRUE) != null){
                throw new ServiceException(item.getOrderNo()+"订单号重复，请删除后再导入");
            }
        });
        // 异步处理
        taskExecutor.execute(() -> {
            try {
                processBatchAsyncForMerchant(batch.getId(), orderDtos);
            } catch (Exception e) {
                log.error("批次处理异常", e);
                batchRepository.updateBatchStatus(batch.getId(), BatchStatusEnum.FAILED);
            }
        });
        /**
         * 生成草稿订单
         */
        insert(importBatchDO.getId());
        return batch;
    }

    /**
     * 异步处理批次
     *
     * @param batchId 批次ID
     * @param orderDtos 订单DTO列表
     */
    public void processBatchAsyncForPlat(Long batchId, List<StagingOrderImportDTO> orderDtos) {
        ConcurrentHashMap<String, Boolean> seenOrderNos = new ConcurrentHashMap<>();
        String userName = SecurityUtils.getUsername();

        orderDtos.parallelStream().forEach(dto -> {
            try {
                if (StringUtils.isBlank(dto.getOrderNo())) {
                   return;
                }
                syncImportService.processSingleOrder(batchId, dto, userName, seenOrderNos);
            } catch (Exception e) {
                log.error("订单处理失败: {}", dto.getOrderNo(), e);
                StagingOrder stagingOrder = new StagingOrder();
                stagingOrder.setOrderNo(dto.getOrderNo());
                stagingOrder.setStatus(StagingOrderStatusEnum.EXCEPTION);
                stagingOrder.processException("订单处理失败: " + e.getMessage());
                batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
            }
        });
        // 更新批次状态
        batchRepository.updateBatchStatus(batchId, BatchStatusEnum.PREPROCESSED);
    }

    /**
     * 异步处理批次
     *
     * @param batchId 批次ID
     * @param orderDtos 订单DTO列表
     */
    public void processBatchAsyncForMerchant(Long batchId, List<StagingOrderImportDTO> orderDtos) {
        ConcurrentHashMap<String, Boolean> seenOrderNos = new ConcurrentHashMap<>();
        String userName = SecurityUtils.getUsername();

        orderDtos.parallelStream().forEach(dto -> {
            try {
                if (StringUtils.isBlank(dto.getOrderNo())) {
                    return;
                }
                syncImportService.processSingleOrder(batchId, dto, userName, seenOrderNos);
            } catch (Exception e) {
                log.error("订单处理失败: {}", dto.getOrderNo(), e);
                StagingOrder stagingOrder = new StagingOrder();
                stagingOrder.setOrderNo(dto.getOrderNo());
                stagingOrder.setStatus(StagingOrderStatusEnum.EXCEPTION);
                stagingOrder.processException("订单处理失败: " + e.getMessage());
                batchRepository.saveStagingOrder(batchId, stagingOrder, userName);
            }
        });
        // 更新批次状态
        batchRepository.updateBatchStatus(batchId, BatchStatusEnum.PREPROCESSED);
    }

    /**
     * 确认导入
     *
     * @param batchId 批次ID
     * @return 导入批次
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportBatch confirmImport(Long batchId) {
        // 1. 获取导入批次
        ImportBatch batch = batchRepository.findById(batchId);
        if (batch == null) {
            throw new IllegalArgumentException("导入批次不存在");
        }
        MerchantStoreDetailVo merchantStoreDetailVo = iMerchantStoreService.getDetailById(batch.getMerchantStoreId());
        Integer storeType = merchantStoreDetailVo.getStoreType();
        /**
         * 如果是非官方旗舰店,需要校验是否付款
         */
        if (storeType == 2){
            TeboDraftOrderDO teboDraftOrderDO = teboDraftOrderMapper.selectDaftOrderByBatchId(batchId);
            if (ObjectUtil.isEmpty(teboDraftOrderDO)){
                throw new ServiceException("没有支付记录不能导入");
            }
            if (teboDraftOrderDO.getPayStatus() != 1){
                throw new ServiceException("未支付不能导入");
            }
        }
        batch.getStagingOrders().stream().filter(order -> order.getStatus() == StagingOrderStatusEnum.EXCEPTION)
                .forEach(order -> {
                    throw new IllegalStateException("导入批次中存在异常订单，请先处理异常订单");
                });
        MerchantStoreBalanceQueryDTO queryDTO = new MerchantStoreBalanceQueryDTO();
        queryDTO.setMerchantStoreId(batch.getMerchantStoreId());
        // 2. 开始确认处理
        batch.startConfirming();
        batchRepository.updateBatchStatus(batch.getId(), BatchStatusEnum.CONFIRMING);
        // 4. 处理每个订单组
        confirmOrderExecutor.execute(() -> {
            try {
                confirmOrder(batch, merchantStoreDetailVo);
            } catch (Exception e) {
                log.error("订单确认失败", e);
                batchRepository.updateBatchStatus(batch.getId(), BatchStatusEnum.FAILED);
            }
        });
        return batch;
    }

    private void confirmOrder(ImportBatch batch, MerchantStoreDetailVo merchantStoreDetailVo) {
        boolean someFail = false;
        for (StagingOrder stagingOrder : batch.getStagingOrders()) {
            boolean localFail = false;
            try {
                localFail = syncImportService.syncConfirm(batch, merchantStoreDetailVo, stagingOrder);
            }catch (Exception e) {
                log.error("订单确认失败: {}", stagingOrder.getOrderNo(), e);
                batchRepository.confirmStagingOrderFail(stagingOrder.getId(),
                        "系统异常：" + e.getMessage());
                localFail = true;
            }
            someFail = someFail || localFail;
        }
        if (someFail) {
            batchRepository.updateBatchStatus(batch.getId(), BatchStatusEnum.PARTIAL_SUCCESS);
        } else {
            batchRepository.updateBatchStatus(batch.getId(), BatchStatusEnum.COMPLETED);
        }
    }

    void insert(Long batchId){
        TeboDraftOrderDO teboDraftOrderDO = new TeboDraftOrderDO();
        teboDraftOrderDO.setId(SnowFlakeUtil.nextId());
        teboDraftOrderDO.setBatchId(batchId);
        teboDraftOrderDO.setOrderNo(TeboNumberGenerator.buildDraftOrderNo());
        teboDraftOrderMapper.insert(teboDraftOrderDO);
    }
    /**
     * 删除预览的订单
     *
     * @param id
     */
    @Override
    public void deleteStagingOrderById(Long id) {
        stagingOrderMapper.batchDeleteByIds(Collections.singletonList(id));
    }

    @Override
    public void payNotify(Long id) {
        TeboDraftOrderDO teboDraftOrderDO = teboDraftOrderMapper.selectDaftOrderByBatchId(id);
        if (ObjectUtil.isEmpty(teboDraftOrderDO)){
            throw new ServiceException("订单不存在");
        }
        if (teboDraftOrderDO.getPayStatus() != 0){
            throw new ServiceException("订单状态不是待支付");
        }
        teboDraftOrderDO.setPayStatus(1);
        teboDraftOrderDO.setPayTime(LocalDateTime.now());
        teboDraftOrderMapper.updateById(teboDraftOrderDO);
        confirmImport(teboDraftOrderDO.getBatchId());
    }
    /**
     * 新增订单
     *
     * @param addOrderReq
     */
    @Override
    public Long addOrder(AddOrderReq addOrderReq) {
        // 检查外部服务是否已存在该订单号
        OrderVo serviceOrderNo = orderInfoService.getOrderInfoByServiceOrderNo(addOrderReq.getOrderNo());
        if (serviceOrderNo != null) {
            throw new ServiceException("订单号已存在");
        }
        if (addOrderReq.getAddressCode().size() != 3) {
            throw new ServiceException("地址编码不正确");
        }
        String districtCode = addOrderReq.getAddressCode().get(2);
        ProductPriceListVo productPrice = productLevelService.getProductPrice(addOrderReq.getProductSkuNo(), districtCode);
        if (productPrice == null) {
            throw new ServiceException("该地区未找到对应的商品价格");
        }
        ShopServerArea serverArea = shopServerAreaService.selectRegion(districtCode);
        if (serverArea == null) {
            serverArea = new ShopServerArea();
        }
        MerchantStoreDO merchantStoreDO = new MerchantStoreDO();
        merchantStoreDO.setId(Long.valueOf(addOrderReq.getMerchantId()));
        merchantStoreDO.setMerchantName(addOrderReq.getMerchantName());
        StagingOrder stagingOrder = StagingOrder.create(addOrderReq.getOrderNo(), addOrderReq.getCustomerName(), addOrderReq.getCustomerPhone(), addOrderReq.getCustomerAddress(), addOrderReq.getProductSkuNo(), addOrderReq.getQty(), addOrderReq.getRemarkUsr(), serverArea.getTeboShopId());
        stagingOrder.assignRegionCode(districtCode);
        stagingOrder.assignPrice(productPrice.getRefPriceFen());
        stagingOrder.setTeboShopId(serverArea.getTeboShopId());
        stagingOrder.setTeboShopName(serverArea.getLstShopName());
        stagingOrder.setTeboShopPhone(serverArea.getPhoneNumber());

        List<StagingOrderImportDTO> list = new ArrayList<>();
        StagingOrderImportDTO stagingOrderImportDTO = new StagingOrderImportDTO();
        BeanConvert.copy(stagingOrder,stagingOrderImportDTO);
        stagingOrderImportDTO.setImportType(1);
        stagingOrderImportDTO.setAddressCode(addOrderReq.getAddressCode());
        stagingOrderImportDTO.setSerialNumber(1);
        list.add(stagingOrderImportDTO);
        if (addOrderReq.getStoreType() == 1){
            ImportBatch importBatch = handleImportForPlat("",list,Long.parseLong(addOrderReq.getMerchantId()));
            return importBatch.getId();
        }else if (addOrderReq.getStoreType() == 2){
            ImportBatch importBatch = handleImportForMerchant("",list,Long.parseLong(addOrderReq.getMerchantId()));
            return importBatch.getId();
        }
        return null;
    }
}
