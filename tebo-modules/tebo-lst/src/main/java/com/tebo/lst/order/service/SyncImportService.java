package com.tebo.lst.order.service;

import com.tebo.lst.customer.domain.vo.MerchantStoreDetailVo;
import com.tebo.lst.order.domain.dto.StagingOrderImportDTO;
import com.tebo.lst.order.domain.model.ImportBatch;
import com.tebo.lst.order.domain.model.StagingOrder;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public interface SyncImportService {

    /**
     * 处理单个订单
     *
     * @param batchId 批次ID
     * @param dto 订单DTO
     * @param userName 用户名
     * @param seenOrderNos 已处理的订单号集合
     */
    void processSingleOrder(Long batchId,
                                   StagingOrderImportDTO dto,
                                   String userName,
                                   ConcurrentHashMap<String, Boolean> seenOrderNos);

    /**
     * 确认订单
     *
     * @param batch 批次
     * @param merchantStoreDetailVo 商户门店详情
     */
    boolean syncConfirm(ImportBatch batch, MerchantStoreDetailVo merchantStoreDetailVo, StagingOrder stagingOrder);
}
