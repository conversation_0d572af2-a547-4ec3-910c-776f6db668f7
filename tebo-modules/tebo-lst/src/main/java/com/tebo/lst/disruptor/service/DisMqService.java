package com.tebo.lst.disruptor.service;

import com.tebo.system.api.domain.dto.RemoteConsumerRecordDTO;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024/1/15 13:52
 * @Desc : Dis 消息接口封装-统一发送入口
 */
public interface DisMqService {

    /**
     * 推送公众号消息给门店
     * @param shopId 门店id
     * @param orderNo 订单编号
     * @param orderTime 订单时间
     * @param type 1 服务工单 2售后工单
     * @return
     */
    Boolean sendShopNotifyMsg(Long shopId, String orderNo, String orderTime, Integer type);

    /**
     * 异步处理给门店的公众号消息
     * @param shopId 门店id
     * @param orderNo 订单编号
     * @param orderTime 订单时间
     * @param type 1 服务工单 2售后工单
     * @return
     */
    Boolean delShopNotifyMsg(Long shopId, String orderNo, String orderTime, Integer type);


}
