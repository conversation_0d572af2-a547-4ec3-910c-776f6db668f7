package com.tebo.lst.customer.model.strategy.merchant.detail;

import com.tebo.lst.customer.domain.entity.MerchantStoreDO;
import com.tebo.lst.customer.mapper.MerchantStoreMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class GetMerchantDetailByIdStrategy implements MerchantStoreDetailStrategy<Long> {

    private final MerchantStoreMapper merchantStoreMapper;

    @Autowired
    public GetMerchantDetailByIdStrategy(MerchantStoreMapper merchantStoreMapper) {
        this.merchantStoreMapper = merchantStoreMapper;
    }

    @Override
    public MerchantStoreDO getStoreDetail(Long id) {
        if (id == null || id == 0) {
            return null;
        }
        return merchantStoreMapper.selectDetailById(id);
    }


}
