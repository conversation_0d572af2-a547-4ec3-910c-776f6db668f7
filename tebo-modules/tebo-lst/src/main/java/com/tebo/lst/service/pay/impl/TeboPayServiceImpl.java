package com.tebo.lst.service.pay.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.core.utils.uuid.UUID;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.util.applet.AppletRequestUrl;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.http.HttpTool;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.lst.controller.domain.TeboHFAppletPayDTO;
import com.tebo.lst.controller.pay.manage.domain.TeboHFAppletPayVO;
import com.tebo.lst.controller.pay.manage.domain.TeboPayRecordVO;
import com.tebo.lst.customer.domain.entity.TeboDraftOrderDO;
import com.tebo.lst.customer.mapper.TeboDraftOrderMapper;
import com.tebo.lst.customer.service.TeboDraftOrderService;
import com.tebo.lst.entity.TeboPayRecordDO;
import com.tebo.lst.entity.TeboPaySplitDetailDO;
import com.tebo.lst.mapper.TeboPayRecordMapper;
import com.tebo.lst.mapper.TeboPaySplitDetailMapper;
import com.tebo.lst.order.domain.dataobject.StagingOrderDO;
import com.tebo.lst.order.mapper.StagingOrderMapper;
import com.tebo.lst.order.service.IOrderImportService;
import com.tebo.lst.order.service.IOrderInfoService;
import com.tebo.lst.service.pay.TeboPayService;
import com.tebo.lst.utils.AmountUtil;
import com.tebo.system.api.RemotePayService;
import com.tebo.system.api.RemoteShopService;
import com.tebo.system.api.domain.dto.TeboLstShopDTO;
import com.tebo.system.api.model.TeboShop;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PathVariable;

import javax.annotation.Resource;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TeboPayServiceImpl implements TeboPayService {

    @Value("${tebo.pay.baseUrl}")
    public String teboPayBaseUrl;
    @Resource
    private TeboPayRecordMapper teboPayRecordMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private RemotePayService remotePayService;
    @Resource
    private StagingOrderMapper stagingOrderMapper;
    @Resource
    private TeboDraftOrderMapper teboDraftOrderMapper;
    @Resource
    private TeboPaySplitDetailMapper splitDetailMapper;
    @Resource
    private IOrderImportService orderImportService;
    @Resource
    private RemoteShopService remoteShopService;
//    @Resource
//    private DistributedLock distributedLock;
    private static final String ORDER_KEY_PREFIX = "tebo_lst:tebo_pay:order:";

    @Override
    public Object appletPay(TeboHFAppletPayDTO teboAppletPayDTO) {
        String orderNo = "TEBOS" + SnowFlakeUtil.nextId();
        teboAppletPayDTO.setAppletSource(2);
        teboAppletPayDTO.setGoodsDesc("门店保证金");
        teboAppletPayDTO.setBusinessType(130);
        teboAppletPayDTO.setSubOpenid(teboAppletPayDTO.getOpenId());
        teboAppletPayDTO.setUnionId(teboAppletPayDTO.getUnionId());
        teboAppletPayDTO.setOrderNo(orderNo);
        teboAppletPayDTO.setRemark(orderNo);
        // 保证金级别
        if (teboAppletPayDTO.getLevel().equals(1)) {
            teboAppletPayDTO.setAmount(100000);
//             todo 临时支付一分钱
//            teboAppletPayDTO.setAmount(1);
        }else if (teboAppletPayDTO.getLevel().equals(2)) {
            teboAppletPayDTO.setAmount(200000);
        }else if (teboAppletPayDTO.getLevel().equals(3)) {
            teboAppletPayDTO.setAmount(400000);
        }else if (teboAppletPayDTO.getLevel().equals(4)) {
            teboAppletPayDTO.setAmount(1000000);
        }
        // 插入支付记录
        insertRecord(2, 130, teboAppletPayDTO.getAmount(), orderNo, teboAppletPayDTO.getGoodsDesc(), teboAppletPayDTO.getShopId().toString(), teboAppletPayDTO.getUnionId(), "");
        // 调用支付
        String resultJson = HttpUtil.post(teboPayBaseUrl + "/tebo-pay/applet/pay/appletPaySjlm", JSONObject.toJSONString(teboAppletPayDTO));
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        if (jsonObject.getInteger("code") != 200){
            throw new ServiceException(jsonObject.getString("msg"));
        }
        return jsonObject.get("data");
    }

    @Override
    public Object orderPay(TeboHFAppletPayDTO teboAppletPayDTO) {
        String orderNo = teboAppletPayDTO.getOrderNo();
        if (!redisService.hasKey(ORDER_KEY_PREFIX + orderNo)) {
            throw new ServiceException("二维码已过期，请刷新后重试！");
        }
        // 查找支付订单
        TeboPayRecordDO teboPayRecordDO = teboPayRecordMapper.selectOne(new QueryWrapper<TeboPayRecordDO>().eq("order_no", orderNo));
        if (ObjectUtils.isEmpty(teboPayRecordDO)) {
            throw new ServiceException("支付记录不存在！");
        }
        teboAppletPayDTO.setAppletSource(1);
        teboAppletPayDTO.setGoodsDesc("商户订单支付");
        teboAppletPayDTO.setBusinessType(140);
        teboAppletPayDTO.setSubOpenid(teboAppletPayDTO.getOpenId());
        teboAppletPayDTO.setUnionId(teboAppletPayDTO.getUnionId());
        teboAppletPayDTO.setOrderNo(orderNo);
        teboAppletPayDTO.setRemark(orderNo);
        teboAppletPayDTO.setAmount(teboPayRecordDO.getAmount());
        // 发起预支付
        String resultJson = HttpUtil.post(teboPayBaseUrl + "/tebo-pay/applet/pay/appletPaySjlm", JSONObject.toJSONString(teboAppletPayDTO));
        log.info("orderPay  resultJson {}", resultJson);
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        if (jsonObject.getInteger("code") != 200){
            throw new ServiceException(jsonObject.getString("msg"));
        }
        return jsonObject.get("data");
    }

    @Override
    public Object createOrder(Long batchId) {
        if (ObjectUtils.isEmpty(batchId)) {
            throw new RuntimeException("批次id不可为空！");
        }
        String orderNo = "TEBOM" + batchId;
        // 先查询该批次信息
        TeboDraftOrderDO batch = teboDraftOrderMapper.selectDaftOrderByBatchId(batchId);
        if (ObjectUtils.isEmpty(batch)) {
            throw new ServiceException("批次不存在！");
        }
        // 重复生成付款二维码报错提示
        if (teboPayRecordMapper.selectCount(new QueryWrapper<TeboPayRecordDO>().eq("order_no", orderNo)) > 0) {
            throw new ServiceException("该笔订单已生成过二维码，请重新导入数据再操作！");
        }
        // 查找该批次的订单金额金额
        Integer amount = teboDraftOrderMapper.sumPrePayAmount(batchId);
        if (ObjectUtils.isEmpty(amount) || amount == 0) {
            throw new ServiceException("批次没有需要付款的订单！");
        }
        // 计算平台服务费 1 %
        Integer feeAmount = AmountUtil.discountCalculation(Long.valueOf(amount), 1).intValue();
        Integer totalAmount = amount + feeAmount;
        TeboHFAppletPayVO result = new TeboHFAppletPayVO();
        result.setAmount(totalAmount);
        result.setFeeAmount(feeAmount);
        result.setOrderAmount(amount);
        result.setOrderNo(orderNo);
        result.setOrderNum(teboDraftOrderMapper.countByBatchId(batchId));
        // 生成付款二维码
        String merchantCode = remotePayService.generateMerchantCode(orderNo).getData();
        // 讲订单号作为key，5分钟后过期
        redisService.setCacheObject(ORDER_KEY_PREFIX + orderNo, orderNo, 5L, TimeUnit.MINUTES);
        result.setQrUrl(merchantCode);
        // 插入支付记录
        insertRecord(1, 140, totalAmount, orderNo, batch.getId().toString(), "0", "0", orderNo);
        return result;
    }

    @Override
    public Object flashQrCode(Long batchId) {
        if (ObjectUtils.isEmpty(batchId)) {
            throw new RuntimeException("批次id不可为空！");
        }
        String orderNo = "TEBOM" + batchId;
        // 调用关单接口
        Map<String, Object> param = new HashMap<>();
        param.put("orderNo", orderNo);
        String resultJson = HttpUtil.post(teboPayBaseUrl + "/tebo-pay/applet/pay/sjlmOrderPaymentClose", JSONObject.toJSONString(param));
        log.info("flashQrCode  resultJson {}", resultJson);
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        if (jsonObject.getInteger("code") != 200){
            if (!jsonObject.getString("msg").equals("订单号不存在") && !jsonObject.getString("msg").equals("关单失败:原交易不存在")) {
                throw new ServiceException(jsonObject.getString("msg"));
            }
        }
        TeboHFAppletPayVO result = new TeboHFAppletPayVO();
        // 生成付款二维码
        String merchantCode = remotePayService.generateMerchantCode(orderNo).getData();
        // 更新缓存中值
        redisService.setCacheObject(ORDER_KEY_PREFIX + orderNo, orderNo, 5L, TimeUnit.MINUTES);
        result.setQrUrl(merchantCode);
        return result;
    }

    @Override
    public void orderPayNotify(String orderNo) {
        log.info("orderPayNotify orderNo {}", orderNo);
        // 1. 订单从待支付状态修改为支付成功状态
        TeboPayRecordDO teboPayRecordDO = teboPayRecordMapper.selectOne(new QueryWrapper<TeboPayRecordDO>().eq("order_no", orderNo));
        if (ObjectUtils.isEmpty(teboPayRecordDO)) {
            throw new ServiceException("支付记录不存在！");
        }
        if (teboPayRecordDO.getPayStatus() == 2) {
            throw new ServiceException("订单已回调！");
        }
        teboPayRecordDO.setPayStatus(2);
        teboPayRecordDO.setUpdateTime(LocalDateTime.now());
        teboPayRecordMapper.updateById(teboPayRecordDO);
        if (orderNo.startsWith("TEBOM")) {
            // 导单支付成功回调
            Long batchId = Long.valueOf(orderNo.substring(5));
            log.info("orderPayNotify plan batchId {} orderNo {}", batchId, orderNo);
            // 2. 订单预览数据同步到订单表
            log.info("orderPayNotify payNotify orderNO{}", orderNo);
            orderImportService.payNotify(batchId);
            log.info("orderPayNotify plan orderNO{}", orderNo);
            // 3. 生成分账计划(在第二部执行完成之后调用)
        } else if (orderNo.startsWith("TEBOS")) {
            // 门店保证金支付成功回调
            // 门店支付时，openid是门店id
            Long shopId  = Long.valueOf(teboPayRecordDO.getOpenId());
            Integer amount  = teboPayRecordDO.getAmount();
            log.info("orderPayNotify shopId {} amount{}", shopId, amount);
            // 调用更新门店
            TeboLstShopDTO shopDTO = new TeboLstShopDTO();
            shopDTO.setId(shopId);
            shopDTO.setLstBond(amount);
            remoteShopService.updateLstBond(shopDTO);
            return;
        }
    }

    @Override
    public Boolean cancelOrderNotify(Long id, Integer cancelAmount, Integer cancelFee) {
        log.info("cancelOrderNotify id {} cancelAmount {} cancelFee {}", id, cancelAmount,  cancelFee);
        // 未找到分账计划或者分账计划已执行，则不可重复执行
        TeboPaySplitDetailDO detailDO = splitDetailMapper.selectById(id);
        if (ObjectUtils.isEmpty(detailDO)) {
            return Boolean.FALSE;
        }
        // 请勿重复调用
        if (detailDO.getPayStatus() == 11) {
            return Boolean.FALSE;
        }
        // 1. 更新分账计划
        detailDO.setCancelFinshedTime(LocalDateTime.now());
        detailDO.setPayStatus(11);
        detailDO.setCancelAmount(cancelAmount);
        detailDO.setCancelFeeAmount(cancelFee);
        detailDO.setUpdateTime(LocalDateTime.now());
        splitDetailMapper.updateById(detailDO);
        return Boolean.TRUE;
    }

    @Override
    public List<TeboPayRecordVO> selectShopPayRecord(Long shopId) {
        LambdaQueryWrapper<TeboPayRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeboPayRecordDO::getOpenId, shopId);
        queryWrapper.eq(TeboPayRecordDO::getBusinessType, 130);
        queryWrapper.in(TeboPayRecordDO::getPayStatus, 2, 10, 11);
        queryWrapper.orderByDesc(TeboPayRecordDO::getUpdateTime);
        List<TeboPayRecordDO> list = teboPayRecordMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return BeanConvert.copyList(list, TeboPayRecordVO::new);
    }

    @Override
    public Boolean shopRefundPay(String orderNo) {
        log.info("shopRefundPay orderNo {}", orderNo);
        // 查询支付订单是否存在
        LambdaQueryWrapper<TeboPayRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeboPayRecordDO::getOrderNo, orderNo);
        queryWrapper.eq(TeboPayRecordDO::getBusinessType, 130);
        queryWrapper.orderByDesc(TeboPayRecordDO::getUpdateTime);
        List<TeboPayRecordDO> list = teboPayRecordMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("未找到支付订单，不可退款！");
        }
        TeboPayRecordDO teboPayRecordDO = list.get(0);
        // 请勿重复调用
        if (teboPayRecordDO.getPayStatus() != 2) {
            throw new ServiceException("该笔订单状态已处理，请刷新后重试");
        }
        // 调用退款，记录退款金额和退款手续费
        Map<String, Object> param = new HashMap<>();
        param.put("remark", teboPayRecordDO.getId());
        param.put("orderNo", orderNo);
        param.put("ordAmt", MoneyUtil.fenToYuan(teboPayRecordDO.getAmount()));
        log.info("shopRefundPay sjlmBzjOrderRefund  param {}", param);
        String resultJson = HttpUtil.post(teboPayBaseUrl + "/tebo-pay/applet/pay/sjlmBzjOrderRefund", JSONObject.toJSONString(param));
        log.info("shopRefundPay  resultJson {}", resultJson);
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        if (jsonObject.getInteger("code") != 200){
            throw new ServiceException(jsonObject.getString("msg"));
        }
        teboPayRecordDO.setPayStatus(10);
        teboPayRecordDO.setUpdateTime(LocalDateTime.now());
        teboPayRecordMapper.updateById(teboPayRecordDO);
        return Boolean.TRUE;
    }

    @Override
    public Boolean shopRefundPayNotify(Long id, Integer cancelAmount, Integer cancelFeeAmount) {
        log.info("shopRefundPayNotify id {} cancelAmount {} cancelFee {}", id, cancelAmount,  cancelFeeAmount);
        TeboPayRecordDO recordDO = teboPayRecordMapper.selectById(id);
        if (ObjectUtils.isEmpty(recordDO)) {
            return Boolean.FALSE;
        }
        // 请勿重复调用
        if (recordDO.getPayStatus() == 11) {
            return Boolean.FALSE;
        }
        // 不是退款中的不处理
        if (recordDO.getPayStatus() != 10) {
            return Boolean.FALSE;
        }
        // 更新支付记录
        recordDO.setPayStatus(11);
        recordDO.setUpdateTime(LocalDateTime.now());
        teboPayRecordMapper.updateById(recordDO);
        // 更新门店保证金为0
        // 调用更新门店
        TeboLstShopDTO shopDTO = new TeboLstShopDTO();
        shopDTO.setId(Long.valueOf(recordDO.getOpenId()));
        shopDTO.setLstBond(0);
        remoteShopService.updateLstBond(shopDTO);
        return Boolean.TRUE;
    }

    @Transactional
    public void batchOrderCreatePlan(Long batchId) {
        String orderNo = "TEBOM" + batchId;
        log.info("batchOrderCreatePlan plan orderNO{} batchId {}", orderNo, batchId);
        // 通过批次id查询已确认的订单
        List<StagingOrderDO> existingOrders = stagingOrderMapper.getByBatchId(batchId).stream().filter(order -> order.getStatus() == 2).collect(Collectors.toList());
        log.info("batchOrderCreatePlan plan existingOrders{}", existingOrders);
        // 创建该笔订单的分账计划
        for (StagingOrderDO order : existingOrders) {
            // 计算门店分账金额 99%
            Integer amount = AmountUtil.discountCalculation(order.getRefSettlePrice(), 99).intValue();
            insertSplitDetail(amount, orderNo, batchId + "-" +order.getOrderNo(),  1);
        }
        // 该笔插入一个平台的分账计划
        insertSplitDetail(0, orderNo, batchId + "-001",  2);
    }

    /**
     * 插入分账计划
     * @param amount 分账金额
     * @param orderNo 主订单号
     * @param orderInfo 子订单号
     * @param walletType 类型
     */
    private void insertSplitDetail(Integer amount,String orderNo, String orderInfo,  Integer walletType) {
        TeboPaySplitDetailDO splitDetail = new TeboPaySplitDetailDO();
        splitDetail.setId(SnowFlakeUtil.nextId());
        splitDetail.setAmount(amount);
        splitDetail.setOrderNo(orderNo);
        splitDetail.setOrderInfo(orderInfo);
        splitDetail.setWalletType(walletType);
        splitDetail.setPayStatus(1);
        splitDetailMapper.insert(splitDetail);
    }

    /**
     * 创建支付记录
     * @param source 1:泰博出行  2:泰博出行-商家端
     * @param businessType 业务类型
     * @param amount 订单金额以分为单位
     * @param orderNo 订单号
     * @param description 订单描述
     * @param openId 微信用户标识
     * @param unionId 微信用户标识
     */
    private void insertRecord(Integer source, Integer businessType, Integer amount, String orderNo, String description, String openId, String unionId, String transactionId) {
        TeboPayRecordDO record = new TeboPayRecordDO();
        record.setId(SnowFlakeUtil.nextId());
        record.setSource(source);
        record.setBusinessType(businessType);
        record.setAmount(amount);
        record.setOrderNo(orderNo);
        record.setDescription(description);
        record.setOpenId(openId);
        record.setUnionId(unionId);
        record.setPayStatus(1);
        record.setTransactionId(transactionId);
        teboPayRecordMapper.insert(record);
    }
}
