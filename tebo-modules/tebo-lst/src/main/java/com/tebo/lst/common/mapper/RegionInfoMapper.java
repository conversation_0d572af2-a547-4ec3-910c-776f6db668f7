package com.tebo.lst.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tebo.lst.common.domain.dataobject.RegionDO;
import com.tebo.lst.common.domain.dto.RegionDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 区域信息表 Mapper 接口
 * </p>
 */
@Mapper
public interface RegionInfoMapper extends BaseMapper<RegionDO> {

    @Select("SELECT * FROM region")
    List<RegionDO> selectAll();

    @Select("SELECT " +
            "t1.region_code AS 'provinceCode', " +
            "t1.region_name AS 'provinceName', " +
            "t2.region_code AS 'cityCode', " +
            "t2.region_name AS 'cityName', " +
            "t3.region_code AS 'districtCode', " +
            "t3.region_name AS 'districtName' " +
            "FROM " +
            "region t3 " +
            "JOIN region t2 ON t3.parent_code = t2.region_code AND t2.level = 1 " +
            "JOIN region t1 ON t2.parent_code = t1.region_code AND t1.level = 0 " +
            "WHERE " +
            "t3.level = 2 " +
            "AND t3.region_name LIKE CONCAT('%', #{keyword}, '%')")
    List<RegionDTO> selectRegionHierarchy(String keyword);


    /**
     * 根据区域编码查询区域信息
     *
     * @param countyCode 区域名称
     * @return 区域信息列表
     */
    @Select("SELECT " +
            "t1.region_code AS 'provinceCode', " +
            "t1.region_name AS 'provinceName', " +
            "t2.region_code AS 'cityCode', " +
            "t2.region_name AS 'cityName', " +
            "t3.region_code AS 'districtCode', " +
            "t3.region_name AS 'districtName' " +
            "FROM " +
            "region t3 " +
            "JOIN region t2 ON t3.parent_code = t2.region_code AND t2.level = 1 " +
            "JOIN region t1 ON t2.parent_code = t1.region_code AND t1.level = 0 " +
            "WHERE " +
            "t3.level = 2 " +
            "AND t3.region_code = #{countyCode}")
    RegionDTO selectRegionByCountyCode(String countyCode);


    List<RegionDTO> selectRegionByCountyCodeList(@Param("countyCodeList") List<String> countyCodeList);

}