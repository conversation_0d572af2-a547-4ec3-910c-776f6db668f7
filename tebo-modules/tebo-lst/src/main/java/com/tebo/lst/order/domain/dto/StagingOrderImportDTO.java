package com.tebo.lst.order.domain.dto;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.tebo.common.core.annotation.Excel;
import com.tebo.lst.order.domain.model.StagingOrder;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.List;

@Data
public class StagingOrderImportDTO {

    @Excel(name = "序号")
    @NotBlank(message = "序号不能为空")
    private Integer serialNumber;

    @Excel(name = "订单号")
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @Excel(name = "客户姓名")
    @NotBlank(message = "客户姓名不能为空")
    private String customerName;

    @Excel(name = "客户地址")
    @NotBlank(message = "客户地址不能为空")
    private String customerAddress;

    @Excel(name = "客户电话")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String customerPhone;

    @Excel(name = "商品编码")
    @NotBlank(message = "商品编码不能为空")
    private String productSkuNo;

    @Excel(name = "商品数量")
    @Min(value = 1, message = "商品数量至少为1")
    private Integer qty;

    @Excel(name = "订单备注")
    private String remarkUsr;

    /**
     * 导入类型  1:自动创建  2：excel导入
     */
    @ExcelIgnore
    private Integer importType;

    @ExcelIgnore
    private List<String> addressCode;



    public StagingOrder convertToStagingOrder() {
        return StagingOrder.create(orderNo, customerName, customerPhone, customerAddress, productSkuNo, qty, remarkUsr, null);
    }
}
