package com.tebo.lst.disruptor.domain;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024/1/11 16:46
 * @Desc : 消息体
 */
@Data
public class DisMsgData {

    /**
     * 消息主题
     */
    private String topic;

    /**
     * 消息类型
     */
    private Integer msgType;
    /**
     * 消息数据
     */
    private Map<String, Object> data;

    public void init(String topic,Integer msgType, Map<String, Object> data) {
        this.topic = topic;
        this.data = data;
        this.msgType = msgType;
    }


}
