package com.tebo.lst.order.domain.req;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @project tebo-back
 * @description
 * @date 2025/3/20 08:59:04
 */
@Data
public class OrderQuery4ShopReq {

    /**
     * 订单号/买家会员名/接单门店
     */
    private String kyeWord;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 订单类型 1:服务工单 2：售后工单
     */
    private Integer orderType;

    /**
     * 订单性质 1:自营订单 2：非自营订单
     */
    private Integer orderNature;

    /**
     * 门店ID
     */
    private String teboShopId;


    private Integer pageNum;


    private Integer pageSize;
}
