package com.tebo.lst.customer.mapper;

import com.tebo.lst.customer.domain.entity.LstPrepaidRechargeReviewRecordDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 预充值审核记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@Mapper
public interface LstPrepaidRechargeReviewRecordMapper extends TeboBaseMapper<LstPrepaidRechargeReviewRecordDO> {
    List<LstPrepaidRechargeReviewRecordDO> getPreRechargeRecordList(Long id);
}
