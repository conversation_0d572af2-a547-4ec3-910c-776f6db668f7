package com.tebo.lst.order.domain.model;

import com.tebo.lst.order.enums.StagingOrderStatusEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 中间订单实体（聚合内实体）
 */
@Data
public class StagingOrder {
    // 实体标识
    private Long id;

    /**
     * 序号
     */
    private Integer serialNumber;
    // 业务唯一标识组合
    private String orderNo;
    private String productSkuNo;
    //二级分类
    private String productSecondCategory;
    //商品型号
    private String productName;
    // 业务属性
    private String customerName;
    private String customerPhone;
    private String customerAddress;
    // 订单详情
    private int qty;
    private String remarkUsr;

    /**
     * 服务商店铺ID
     */
    private Long teboShopId;
    /**
     * 服务商店铺名称
     */
    private String teboShopName;

    /**
     * 服务商店铺电话
     */
    private String teboShopPhone;

    // 区域
    private String regionCode;
    // 结算金额
    private Long refSettlePrice;
    //平台服务费
    private Long refServicePrice;

    //小计金额
    private Long refTotalPrice;

    private StagingOrderStatusEnum status;
    private String exceptionMessage;
    private String errorMessage;
    private LocalDateTime processedTime;
    private String createBy;
    /**
     * 1:手动创建 2:excel导入
     */
    private Integer importType;

    private List<String> addressCode;

    public StagingOrder() {
    }

    // 主构造函数
    private StagingOrder(
            Long id,
            String orderNo,
            String productSkuNo,
            String customerName,
            String customerPhone,
            String customerAddress,
            Integer qty,
            String remarkUsr,
            Long teboShopId) {
        this.id = id;
        this.orderNo = orderNo;
        this.productSkuNo = Objects.requireNonNull(productSkuNo, "商品编码不能为空");
        this.customerName = Objects.requireNonNull(customerName, "客户名称不能为空");
        this.customerPhone = validatePhone(customerPhone);
        this.customerAddress = Objects.requireNonNull(customerAddress, "客户地址不能为空");
        this.qty = validateQty(qty);
        this.remarkUsr = remarkUsr;
        this.teboShopId = teboShopId;
    }

    public static StagingOrder create(
            String orderNo,
            String customerName,
            String customerPhone,
            String customerAddress,
            String productSkuNo,
            Integer qty,
            String remarkUsr,
            Long teboShopId) {
        StagingOrder stagingOrder = new StagingOrder(null, orderNo, productSkuNo, customerName, customerPhone, customerAddress, qty, remarkUsr, teboShopId);
        stagingOrder.status = StagingOrderStatusEnum.PENDING;
        return stagingOrder;
    }

    public static StagingOrder reconstruct(
            Long id,
            Integer serialNumber,
            String orderNo,
            String productSkuNo,
            String productSecondCategory,
            String productName,
            String customerName,
            String customerPhone,
            String customerAddress,
            int qty,
            String remarkUsr,
            String regionCode,
            Long refSettlePrice,
            Integer status,
            String errorMessage,
            String exceptionMessage,
            LocalDateTime processedTime,
            Long teboShopId,
            String teboShopName,
            String teboShopPhone,
            String createBy) {
        StagingOrder stagingOrder = new StagingOrder(id, orderNo, productSkuNo, customerName, customerPhone, customerAddress, qty, remarkUsr, teboShopId);
        stagingOrder.regionCode = regionCode;
        stagingOrder.setSerialNumber(serialNumber);
        stagingOrder.refSettlePrice = refSettlePrice;
        stagingOrder.status = StagingOrderStatusEnum.of(status);
        stagingOrder.exceptionMessage = exceptionMessage;
        stagingOrder.errorMessage = errorMessage;
        stagingOrder.processedTime = processedTime;
        stagingOrder.teboShopName = teboShopName;
        stagingOrder.teboShopPhone = teboShopPhone;
        stagingOrder.productSecondCategory = productSecondCategory;
        stagingOrder.productName = productName;
        stagingOrder.createBy = createBy;
        if (refSettlePrice != null) {
            stagingOrder.refServicePrice = BigDecimal.valueOf(refSettlePrice).multiply(BigDecimal.valueOf(0.01)).longValue();
            stagingOrder.refTotalPrice = BigDecimal.valueOf(refSettlePrice).multiply(BigDecimal.valueOf(1.01)).longValue();
        }
        return stagingOrder;
    }


    public void assignRegionCode(String regionCode) {
        if (regionCode == null || regionCode.trim().isEmpty()) {
            throw new IllegalArgumentException("regionCode不能为空");
        }
        this.regionCode = regionCode;
    }

    public void assignPrice(Long refSettlePrice) {
        if (refSettlePrice == null || refSettlePrice < 0) {
            throw new IllegalArgumentException("价格不合法");
        }
        this.refSettlePrice = refSettlePrice;
    }

    // 处理结果（实体内部状态变更）
    public void processException(String exceptionMessage) {
        if (this.status != StagingOrderStatusEnum.PENDING) {
            throw new IllegalStateException("只有待确认状态的订单可以处理");
        }
        this.status = StagingOrderStatusEnum.EXCEPTION;
        this.exceptionMessage = exceptionMessage;
    }

    // 处理结果（实体内部状态变更）
    public void processResult(boolean success, String errorMessage) {
        if (this.status != StagingOrderStatusEnum.PENDING) {
            throw new IllegalStateException("只有待确认状态的订单可以处理");
        }
        this.status = success ? StagingOrderStatusEnum.SUCCESS : StagingOrderStatusEnum.FAILED; // 2:成功 3:失败
        this.errorMessage = success ? null : errorMessage;
        this.processedTime = LocalDateTime.now();
    }

    // 添加取消方法
    public void cancel() {
        if (this.status != StagingOrderStatusEnum.PENDING) {
            throw new IllegalStateException("只有待确认状态的订单可以取消");
        }
        this.status = StagingOrderStatusEnum.CANCEL;
        this.processedTime = LocalDateTime.now();
    }

    // 相等性判断基于唯一标识
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        StagingOrder that = (StagingOrder) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }


    private static int validateQty(int qty) {
        if (qty <= 0) throw new IllegalArgumentException("商品数量必须大于0");
        return qty;
    }

    private static String validatePhone(String phone) {
        if (!phone.matches("^1[3-9]\\d{9}$")) {
          //  throw new IllegalArgumentException("无效的手机号码");
        }
        return phone;
    }
}