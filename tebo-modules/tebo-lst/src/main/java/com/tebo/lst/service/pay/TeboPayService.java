package com.tebo.lst.service.pay;

import com.tebo.lst.controller.domain.TeboHFAppletPayDTO;
import com.tebo.lst.controller.pay.manage.domain.TeboPayRecordVO;

import java.util.List;

public interface TeboPayService {

    /**
     * 小程序支付接口-门店保证金
     * @param dto
     */
    Object appletPay(TeboHFAppletPayDTO dto);


    /**
     * 小程序支付接口-订单
     * @param dto
     */
    Object orderPay(TeboHFAppletPayDTO dto);


    Object createOrder(Long batchId);

    /**
     * 刷新订单支付二维码
     * @param batchId
     * @return
     */
    Object flashQrCode(Long batchId);

    /**
     * 支付回调
     * @param orderNo
     */
    void orderPayNotify(String orderNo);

    /**
     * 批量订单创建分账计划
     * @param batchId
     */
    public void batchOrderCreatePlan(Long batchId);

    /**
     * 取消订单异步
     * @param id
     * @param cancelAmount
     * @param cancelFeeAmount
     * @return
     */
    Boolean cancelOrderNotify(Long id, Integer cancelAmount, Integer cancelFeeAmount);

    /**
     * 查询门店保证金记录
     * @param shopId
     * @return
     */
    List<TeboPayRecordVO> selectShopPayRecord(Long shopId);

    /**
     * 门店保证金退款
     * @param orderNo
     * @return
     */
    Boolean shopRefundPay(String orderNo);


    /**
     * 门店保证金退款异步
     * @param id
     * @param cancelAmount
     * @param cancelFeeAmount
     * @return
     */
    Boolean shopRefundPayNotify(Long id, Integer cancelAmount, Integer cancelFeeAmount);
}
