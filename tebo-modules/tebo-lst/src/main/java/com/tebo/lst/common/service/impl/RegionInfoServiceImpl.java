package com.tebo.lst.common.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.redis.service.RedisService;
import com.tebo.lst.common.constants.RedisPreConstants;
import com.tebo.lst.common.domain.dataobject.RegionDO;
import com.tebo.lst.common.domain.dto.RegionDTO;
import com.tebo.lst.common.domain.model.Region;
import com.tebo.lst.common.domain.vo.RegionTreeVo;
import com.tebo.lst.common.mapper.RegionInfoMapper;
import com.tebo.lst.common.service.IRegionInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RegionInfoServiceImpl extends ServiceImpl<RegionInfoMapper, RegionDO> implements IRegionInfoService {


    @Autowired
    private RegionInfoMapper regionInfoMapper;

    @Autowired
    private RedisService redisService;


    @Override
    public List<RegionTreeVo> getRegionTree() {
        List<RegionTreeVo> regionTreeVos;
        regionTreeVos = redisService.getCacheObject(RedisPreConstants.REGION_TREE);
        if (regionTreeVos != null) {
            return regionTreeVos;
        }
        regionTreeVos = buildTree(regionInfoMapper.selectAll());
        redisService.setCacheObject(RedisPreConstants.REGION_TREE, regionTreeVos, 24L, TimeUnit.HOURS);
        return regionTreeVos;
    }


    @Override
    public List<Region> getRegionsByDistrictName(String districtName) {
        List<RegionDTO> regionDataList = regionInfoMapper.selectRegionHierarchy(districtName);
        if (regionDataList == null || regionDataList.isEmpty()) {
            return Collections.emptyList();
        }
        return regionDataList.stream()
                .map(Region::from)
                .collect(Collectors.toList());
    }

    /**
     * 根据区县编码查询完整的省市区信息
     *
     * @param countyCode 区县编码
     * @return 匹配的区域列表
     */
    @Override
    public Region selectRegionByCountyCode(String countyCode) {
        RegionDTO regionDTO = regionInfoMapper.selectRegionByCountyCode(countyCode);
        if (regionDTO == null) {
            return null;
        }
        return Region.from(regionDTO);
    }

    @Override
    public List<RegionDTO> selectRegionByCountyCodeList(List<String> countyCodeList) {
        List<RegionDTO> list = regionInfoMapper.selectRegionByCountyCodeList(countyCodeList);
        return list;
    }

    private List<RegionTreeVo> buildTree(List<RegionDO> regionList) {
        Map<String, RegionTreeVo> map = new HashMap<>();
        List<RegionTreeVo> rootNodes = new ArrayList<>();
        for (RegionDO region : regionList) {
            RegionTreeVo node = BeanUtil.copyProperties(region, RegionTreeVo.class);
            node.setChildren(new ArrayList<>());
            map.put(node.getRegionCode(), node);
        }
        for (RegionTreeVo node : map.values()) {
            if (StringUtils.isEmpty(node.getParentCode())) {
                rootNodes.add(node);
            } else {
                RegionTreeVo parent = map.get(node.getParentCode());
                if (parent != null) {
                    parent.getChildren().add(node);
                }
            }
        }
        rootNodes.sort(Comparator.comparing(RegionTreeVo::getRegionCode));
        return rootNodes;
    }
}