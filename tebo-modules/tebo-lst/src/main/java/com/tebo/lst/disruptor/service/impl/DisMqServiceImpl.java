package com.tebo.lst.disruptor.service.impl;

import com.lmax.disruptor.RingBuffer;
import com.tebo.common.util.constant.TeboDisMqConstant;
import com.tebo.lst.disruptor.domain.DisMsgData;
import com.tebo.lst.disruptor.service.DisMqService;
import com.tebo.system.api.RemoteWxPushService;
import com.tebo.system.api.domain.dto.RemoteConsumerRecordDTO;
import com.tebo.system.api.domain.dto.wechat.TeboWechatNotifyForShopDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> ZhangFan
 * @date : 2024/1/15 13:53
 * @Desc :  Dis 消息接口封装-统一发送入口
 */
@Slf4j
@Service
public class DisMqServiceImpl implements DisMqService {


    @Resource
    private RingBuffer<DisMsgData> ringBuffer;
    @Resource
    private RemoteWxPushService remoteWxPushService;

    @Override
    public Boolean sendShopNotifyMsg(Long shopId, String orderNo, String orderTime, Integer type) {
        Boolean sendResult = Boolean.TRUE;
        log.info("recordConsumerRecord the shopId:{} orderNo:{} orderTime:{} type:{} ", shopId, orderNo, orderTime, type);
        // 获取下一个Event槽的下标
        long sequence = ringBuffer.next();
        try {
            // 给Event填充数据
            DisMsgData event = ringBuffer.get(sequence);
            // 封装参数
            Map<String, Object> paramMap = new HashMap<String, Object>(){{
                put("shopId", shopId);
                put("orderNo", orderNo);
                put("orderTime", orderTime);
                put("type", type);
            }};
            event.init(TeboDisMqConstant.DIS_MQ_TOPIC_LST, TeboDisMqConstant.NOTIFY_SHOP_MSG, paramMap);
            log.info("sendShopNotifyMsg：{}", event);
        } catch (Exception e) {
            sendResult = Boolean.FALSE;
            log.error("failed to add event to for : e = {},{}", e, e.getMessage());
        } finally {
            // 发布Event，激活观察者去消费，将sequence传递给改消费者
            // 注意最后的publish方法必须放在finally中以确保必须得到调用；如果某个请求的sequence未被提交将会堵塞后续的发布操作或者其他的producer
            ringBuffer.publish(sequence);
            return sendResult;
        }
    }

    @Override
    public Boolean delShopNotifyMsg(Long shopId, String orderNo, String orderTime, Integer type) {
        log.info("delShopNotifyMsg the shopId:{} orderNo:{} orderTime:{} type:{} ", shopId, orderNo, orderTime, type);
        TeboWechatNotifyForShopDTO wxPushDTO  = new TeboWechatNotifyForShopDTO();
        wxPushDTO.setShopId(shopId);
        wxPushDTO.setOrderNo(orderNo);
        wxPushDTO.setOrderTime(orderTime);
        wxPushDTO.setType(type);
        remoteWxPushService.sendNotifyForShop(wxPushDTO);
        return Boolean.TRUE;
    }


}
