package com.tebo.lst.product.domain.vo;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tebo.common.core.annotation.Excel;
import com.tebo.lst.common.excelConvert.BuyMethodConverter;
import com.tebo.lst.product.enums.OperatorTypeEnum;
import com.tebo.lst.utils.AmountUtil;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ProductPriceListVo {

    /**
     * ProductLevelDO
     */

    // id
    @ExcelIgnore
    private String id;

    /**
     * 唯一id
     */
    @ExcelIgnore
    private String mixId;
    
    /**
     * 父id
     */
    @ExcelIgnore
    private String parentId;
    /**
     * 商品/分类名称
     */
    @ExcelProperty("商品名称")
    private String productName;
    /**
     * 商品编码
     */
    @ExcelProperty("商品编码")
    private String productSkuCode;

    /**
     * 商品品类
     */
    @ExcelIgnore
    private String productCategory;
    /**
     * 二级分类
     */
    @ExcelProperty("二级分类")
    private String productSecondCategory;
    /**
     * 商品型号
     */
//    @ExcelProperty("商品型号")
    @ExcelIgnore
    private String productMode;


    /**
     * 商品图片URL
     */
    @ExcelIgnore
    private String productImage;

    /**
     * 所属品牌
     */
    @ExcelProperty("所属品牌")
    private String productBrand;

    /**
     * 结算基准价格(分)
     */
    @ExcelIgnore
    private Integer refBasePrice;

    /**
     *  基准价格(元)
     */
    @ExcelProperty("基准价格(元)")
    private String refBasePriceStr;

    /**
     * 购买方式 (1: 以旧换新, 2: 直接购买)
     */
//    @ExcelProperty(value = "购买方式", converter = BuyMethodConverter.class)
    @ExcelIgnore
    private Integer buyMethod;

    /**
     * 类型 (1: 分类, 2: 商品)，默认为1
     */
    @ExcelIgnore
    private Integer levelType;

    /**
     * 创建时间
     */
//    @ExcelProperty(value = "创建时间")
//    @ColumnWidth(20)
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelIgnore
    private LocalDateTime createTime;

    /*
      regionInfoDO
     */
    /**
     * 省份编码
     */
//    @ExcelProperty("省份编码")
    @ExcelIgnore
    private String provinceCode;

    /**
     * 省份
     */
    @ExcelProperty("省份")
    private String provinceName;

    /**
     * 县/区编码
     */
//    @ExcelProperty("县/区编码")
    @ExcelIgnore
    private String areaCode;

    /**
     * 县/区
     */
    @ExcelProperty("县/区")
    private String areaName;


    /*
      ProductPricingPloyDO
     */

    /**
     * id
     */
    @ExcelIgnore
    private Long ployId;

    /**
     * 操作类型; 1: +; 2: -; 3: *; 4: /
     */
    @ExcelIgnore
    private Integer operatorType;

    /**
     * 操作值(分)
     */
    @ExcelIgnore
    private Integer operatorValue;

    /*
      ActiveDiscountDO
     */
    /**
     * 折扣id
     */
    @ExcelIgnore
    private Long discountId;

    /**
     * 折扣值 %
     */
    @ExcelIgnore
    private Integer discountValue;

    /**
     * 开始时间
     */
    @ExcelIgnore
    private LocalDateTime discountBegin;

    /**
     * 结束时间
     */
    @ExcelIgnore
    private LocalDateTime discountEnd;

    /**
     * 结算价格(元)
     */
    @ExcelProperty("结算价格(元)")
    private String refPrice;

    /**
     * 结算价格(分)
     */
    @ExcelIgnore
    private Long refPriceFen;

    /**
     * 商品上下架状态 0-下架 1-上架
     */
    @ExcelIgnore
    private Integer shelfStatus;

    @ExcelIgnore
    private String policyId;

    /*
     * 计算结算价格 refBasePrice (+,-) operatorValue * discountValue
     */
    public void calculateRefPrice() {
        long result = refBasePrice;
        if (OperatorTypeEnum.ADD.getCode().equals(operatorType)) {
            result = refBasePrice + operatorValue;
        } else if (OperatorTypeEnum.SUBTRACT.getCode().equals(operatorType)) {
            result = refBasePrice - operatorValue;
        }
        if (discountValue != null) {
            result = AmountUtil.discountCalculation(result, discountValue);
        }
        this.setRefPriceFen(result);
        this.setRefPrice(AmountUtil.convertFenToYuan(result));
    }

    private boolean isCurrentTimeWithinDiscountPeriod() {
        LocalDateTime now = LocalDateTime.now();
        return (discountBegin == null || !now.isBefore(discountBegin)) && (discountEnd == null || !now.isAfter(discountEnd));
    }

}
