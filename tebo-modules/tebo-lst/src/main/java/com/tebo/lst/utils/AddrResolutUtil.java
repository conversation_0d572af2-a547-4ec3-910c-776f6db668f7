package com.tebo.lst.utils;


import com.tebo.common.core.utils.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class AddrResolutUtil {
    private static final String HK = "香港特别行政区";
    private static final String MC = "澳门特别行政区";
    private static final String TW = "台湾";
    private static final String PROVINCE = "province";
    private static final String CITY = "city";
    private static final String AREA = "area";
    private static final String SH = "上海市";
    private static final String BJ = "北京市";
    private static final String TJ = "天津市";
    private static final String CQ = "重庆市";
    private static final String NMG = "内蒙古";
    private static final String ZZQ = "自治区";

    public static Map<String, String> resolut(String address) {
        //1级 省 自治区  2级 市 自治州 地区 3级：区县市旗(镇？)
        String province = null, city = null, provinceAndCity, area = null;
        Map<String, String> row = new LinkedHashMap<>();
        List<Map<String, String>> table = new ArrayList<>();
        Map<String, String> resultMap = new HashMap<>(4);

        if (address.startsWith(HK)) {
            resultMap.put(PROVINCE, "香港");
            return resultMap;
        } else if (address.contains(MC)) {
            resultMap.put(PROVINCE, "澳门");
            return resultMap;
        } else if (address.contains(TW)) {
            resultMap.put(PROVINCE, "台湾");
            return resultMap;
        } else {
            //普通地址
            String regex = "((?<provinceAndCity>[^市]+市|.*?自治州|.*?区|.*县)(?<area>[^区]+区|.*?市|.*?县|.*?路|.*?街|.*?道|.*?镇|.*?旗)(?<detailAddress>.*))";
            Matcher m = Pattern.compile(regex).matcher(address);
            while (m.find()) {
                provinceAndCity = m.group("provinceAndCity");
                String regex2 = "((?<province>[^省]+省|.+自治区|上海市|北京市|天津市|重庆市|上海|北京|天津|重庆)(?<city>.*))";
                Matcher m2 = Pattern.compile(regex2).matcher(provinceAndCity);
                while (m2.find()) {
                    province = m2.group(PROVINCE);
                    row.put(PROVINCE, province == null ? "" : province.trim());
                    city = m2.group(CITY);
                    row.put(CITY, city == null ? "" : city.trim());
                }
                area = m.group(AREA);
                row.put(AREA, area == null ? "" : area.trim());
                table.add(row);
            }
        }
        if (table.size() > 0) {
            if (StringUtils.isNotBlank(table.get(0).get(PROVINCE))) {
                province = table.get(0).get(PROVINCE);
                //对自治区进行处理
                if (province.contains(ZZQ)) {
                    if (province.contains(NMG)) {
                        province = province.substring(0, 4);
                    } else {
                        province = province.substring(0, 3);
                    }
                }
            }
            if (StringUtils.isNotBlank(province)) {
                if (StringUtils.isNotBlank(table.get(0).get(CITY))) {
                    city = table.get(0).get(CITY);
                    if (city.equals(SH) || city.equals(CQ) || city.equals(BJ) || city.equals(TJ)) {
                        province = table.get(0).get(CITY);
                    }
                } else if (province.equals(SH) || province.equals(CQ) || province.equals(BJ) || province.equals(TJ)) {
                    city = province;
                }
                if (StringUtils.isNotBlank(table.get(0).get(AREA))) {
                    area = table.get(0).get(AREA);
                }
                province = province.substring(0, province.length() - 1);
            }
        } else {
            return resultMap;
        }
        if (StringUtils.isNotEmpty(province) && province.contains("-")) {
            province = province.substring(1);
        }
        if (StringUtils.isNotEmpty(city) && city.contains("-")) {
            city = city.substring(1);
        }
        if (StringUtils.isNotEmpty(area) && area.contains("-")) {
            area = area.substring(1);
        }
        resultMap.put(PROVINCE, province);
        resultMap.put(CITY, city);
        resultMap.put(AREA, area);
        System.out.println(resultMap);
        return resultMap;
    }

    public static void main(String[] args) {
        System.out.println(resolut("河南省 郑州市 新郑市 中心城区新区建设管理委员会郑州工业应用技术学院东校区(000000)"));
    }
}
