package com.tebo.lst.order.converter;

import com.tebo.lst.order.domain.dataobject.StagingOrderDO;
import com.tebo.lst.order.domain.model.StagingOrder;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

public class StagingOrderConverter {

    // 转换为持久化对象（通用）
    public static StagingOrderDO toPersistenceObject(StagingOrder order, Long batchId) {
        StagingOrderDO dobj = new StagingOrderDO();
        dobj.setId(order.getId());
        dobj.setSerialNumber(order.getSerialNumber());
        dobj.setBatchId(batchId);
        dobj.setOrderNo(order.getOrderNo());
        dobj.setCustomerName(order.getCustomerName());
        dobj.setCustomerPhone(order.getCustomerPhone());
        dobj.setCustomerAddress(order.getCustomerAddress());
        dobj.setRegionCode(order.getRegionCode());
        dobj.setProductSkuNo(order.getProductSkuNo());
        dobj.setProductSecondCategory(order.getProductSecondCategory());
        dobj.setProductName(order.getProductName());
        dobj.setQty(order.getQty());
        dobj.setRemarkUsr(order.getRemarkUsr());
        dobj.setRefSettlePrice(order.getRefSettlePrice());
        dobj.setRefServicePrice(order.getRefServicePrice());
        dobj.setRefTotalPrice(order.getRefTotalPrice());
        dobj.setStatus(order.getStatus().getCode());
        dobj.setErrorMessage(order.getErrorMessage());
        dobj.setExceptionMessage(order.getExceptionMessage());
        dobj.setProcessedTime(order.getProcessedTime());
        dobj.setTeboShopId(order.getTeboShopId());
        dobj.setTeboShopPhone(order.getTeboShopPhone());
        dobj.setTeboShopName(order.getTeboShopName());
        return dobj;
    }


    // 用于批量插入的转换（优化性能）
    public static List<StagingOrderDO> forBatchInsert(List<StagingOrder> orders, Long batchId) {
        return orders.stream()
                .map(order -> {
                    StagingOrderDO dobj = toPersistenceObject(order, batchId);
                    dobj.setCreateTime(LocalDateTime.now());
                    return dobj;
                })
                .collect(Collectors.toList());
    }

    // 转换为领域对象
    public static StagingOrder toDomainObject(StagingOrderDO orderDO) {
        return StagingOrder.reconstruct(
                orderDO.getId(),
                orderDO.getSerialNumber(),
                orderDO.getOrderNo(),
                orderDO.getProductSkuNo(),
                orderDO.getProductSecondCategory(),
                orderDO.getProductName(),
                orderDO.getCustomerName(),
                orderDO.getCustomerPhone(),
                orderDO.getCustomerAddress(),
                orderDO.getQty(),
                orderDO.getRemarkUsr(),
                orderDO.getRegionCode(),
                orderDO.getRefSettlePrice(),
                orderDO.getStatus(),
                orderDO.getErrorMessage(),
                orderDO.getExceptionMessage(),
                orderDO.getProcessedTime(),
                orderDO.getTeboShopId(),
                orderDO.getTeboShopName(),
                orderDO.getTeboShopPhone(),
                orderDO.getCreateBy()
        );

    }

    // 转换为领域对象
    public static List<StagingOrder> toDomainObject(List<StagingOrderDO> orderDOs) {
        return orderDOs.stream()
                .map(StagingOrderConverter::toDomainObject)
                .collect(Collectors.toList());
    }
}
