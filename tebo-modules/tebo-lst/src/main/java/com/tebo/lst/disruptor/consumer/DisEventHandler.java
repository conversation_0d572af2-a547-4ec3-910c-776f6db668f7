package com.tebo.lst.disruptor.consumer;

import com.lmax.disruptor.EventHandler;
import com.tebo.common.core.utils.SpringUtils;
import com.tebo.common.redis.constant.TeboRescueCacheConstant;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.util.constant.TeboDisMqConstant;
import com.tebo.lst.disruptor.domain.DisMsgData;
import com.tebo.lst.disruptor.service.DisMqService;
import com.tebo.system.api.domain.dto.RemoteConsumerRecordDTO;
import com.tebo.system.api.remote.RemoteTeboConsumerService;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> ZhangFan
 * @date : 2024/1/12 10:05
 * @Desc : 消费者入口
 */
@Slf4j
public class DisEventHandler implements EventHandler<DisMsgData> {

    @Override
    public void onEvent(DisMsgData event, long sequence, boolean endOfBatch) {
        try {
            log.info("消费者消费的信息是：{}", event);
            if (event != null) {
                Map<String,Object> params = event.getData();
                Integer msgType = event.getMsgType();
                // 公众号推送给门店
                if (msgType.equals(TeboDisMqConstant.NOTIFY_SHOP_MSG)) {
                    SpringUtils.getBean(DisMqService.class).delShopNotifyMsg(Long.valueOf(params.get("shopId").toString()), params.get("orderNo").toString(), params.get("orderTime").toString(), Integer.valueOf(params.get("type").toString()));
                }
            }
        } catch (Exception e) {
            log.error("DisEventHandler 消费者处理消息失败 event {}", event);
        }
        log.info("DisEventHandler 消费者处理消息结束");
    }
}
