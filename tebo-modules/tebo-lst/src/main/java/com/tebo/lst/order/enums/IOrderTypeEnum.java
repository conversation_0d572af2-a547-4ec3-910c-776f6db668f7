package com.tebo.lst.order.enums;

import com.tebo.lst.common.excelConvert.CodeDescEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * 订单业务类型
 *
 * <AUTHOR>
 */
public enum IOrderTypeEnum implements CodeDescEnum {
    /**
     * 订单业务类型
     */
    SERVICE_ORDER(1, "服务工单"),
    AFTER_SALE_ORDER(2, "售后工单");

    private final Integer code;
    private final String desc;

    IOrderTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getCode() {
        return code;
    }

    public static final Map<Integer, String> keyInfo = new HashMap<>();
    public static final Map<String, Integer> infoKey = new HashMap<>();

    static {
        for (IOrderTypeEnum type : IOrderTypeEnum.values()) {
            keyInfo.put(type.getCode(), type.getDesc());
        }
    }

    static {
        for (IOrderTypeEnum type : IOrderTypeEnum.values()) {
            infoKey.put(type.getDesc(), type.getCode());
        }
    }

    public static IOrderTypeEnum of(Integer code) {
        for (IOrderTypeEnum value : IOrderTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}