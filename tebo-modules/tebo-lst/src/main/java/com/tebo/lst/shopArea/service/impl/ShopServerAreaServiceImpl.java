package com.tebo.lst.shopArea.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tebo.common.core.domain.R;
import com.tebo.lst.common.domain.vo.ShopServerAreaVo;
import com.tebo.lst.order.enums.OrderTypeEnum;
import com.tebo.lst.shopArea.domain.ShopServerArea;
import com.tebo.lst.shopArea.mapper.ShopServerAreaMapper;
import com.tebo.lst.shopArea.service.IShopServerAreaService;
import com.tebo.system.api.RemoteShopService;
import com.tebo.system.api.model.TeboShop;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 功能描述: 门店服务区域管理接口实现类
 *
 * @author: zzh
 * @date: 2021/8/4
 */
@Service
public class ShopServerAreaServiceImpl extends ServiceImpl<ShopServerAreaMapper, ShopServerArea> implements IShopServerAreaService {
    @Resource
    private ShopServerAreaMapper serverAreaMapper;

    @Resource
    private RemoteShopService remoteShopService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deploy(ShopServerAreaVo serverAreaVo) {
        ShopServerArea queryArea = new ShopServerArea();
        queryArea.setTeboShopId(serverAreaVo.getTeboShopId());
        serverAreaMapper.delete(Wrappers.query(queryArea));
        List<String> battery = serverAreaVo.getBattery();
        R<TeboShop> shopR = remoteShopService.getShopInfo(serverAreaVo.getTeboShopId());
        TeboShop teboShop = shopR.getData();
        if (CollectionUtils.isNotEmpty(battery)) {
            for (String areaCode : battery) {
                if (!areaCode.endsWith("00")){
                    ShopServerArea saveArea = new ShopServerArea();
                    saveArea.setOrderType(OrderTypeEnum.F1.getCode());
                    saveArea.setTeboShopId(serverAreaVo.getTeboShopId());
                    saveArea.setArea(areaCode);
                    saveArea.setShopAddress(teboShop.getAddress());
                    saveArea.setLstShopName(teboShop.getLstShopName());
                    saveArea.setPhoneNumber(teboShop.getPhoneNumber());
                    serverAreaMapper.insert(saveArea);
                }

            }
        }
        List<String> recycle = serverAreaVo.getRecycle();
        if (CollectionUtils.isNotEmpty(recycle)) {
            for (String areaCode : recycle) {
                if (!areaCode.endsWith("00")){
                    ShopServerArea saveArea = new ShopServerArea();
                    saveArea.setOrderType(OrderTypeEnum.F2.getCode());
                    saveArea.setTeboShopId(serverAreaVo.getTeboShopId());
                    saveArea.setArea(areaCode);
                    saveArea.setShopAddress(teboShop.getAddress());
                    saveArea.setLstShopName(teboShop.getShopName());
                    saveArea.setPhoneNumber(teboShop.getPhoneNumber());
                    serverAreaMapper.insert(saveArea);
                }

            }
        }
    }

    /**
     * 查询地区店铺
     *
     * @param regionCode 区域编码
     * @return 地区店铺
     */
    @Override
    public ShopServerArea selectRegion(String regionCode) {
        return serverAreaMapper.selectOne(Wrappers.<ShopServerArea>lambdaQuery().eq(ShopServerArea::getArea, regionCode));
    }
}
