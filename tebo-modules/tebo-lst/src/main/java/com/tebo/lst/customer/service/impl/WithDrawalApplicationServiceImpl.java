package com.tebo.lst.customer.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.security.utils.SecurityUtils;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.lst.customer.domain.dto.CashWithdrawalReviewDTO;
import com.tebo.lst.customer.domain.dto.WithDrawalApplicationDTO;
import com.tebo.lst.customer.domain.entity.ShopBalanceRecordDO;
import com.tebo.lst.customer.domain.entity.WithDrawalApplicationDO;
import com.tebo.lst.customer.domain.req.WithDrawalApplicationQueryDTO;
import com.tebo.lst.customer.domain.vo.ShopBalanceVO;
import com.tebo.lst.customer.domain.vo.WithDrawalApplicationDetailVO;
import com.tebo.lst.customer.domain.vo.WithDrawalApplicationVO;
import com.tebo.lst.customer.mapper.ShopBalanceRecordMapper;
import com.tebo.lst.customer.mapper.WithDrawalApplicationMapper;
import com.tebo.lst.customer.service.WithDrawalApplicationService;
import com.tebo.lst.utils.TeboNumberGenerator;
import com.tebo.system.api.RemoteShopService;
import com.tebo.system.api.model.TeboShop;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class WithDrawalApplicationServiceImpl implements WithDrawalApplicationService {
    @Resource
    private WithDrawalApplicationMapper withDrawalApplicationMapper;
    @Resource
    private RemoteShopService remoteShopService;

    @Resource
    private ShopBalanceRecordMapper shopBalanceRecordMapper;

    @Override
    public List<WithDrawalApplicationVO> getWithDrawalApplicationList(WithDrawalApplicationQueryDTO withDrawalApplicationQueryDTO) {
        List<WithDrawalApplicationDO> list = withDrawalApplicationMapper.getWithDrawalApplicationList(withDrawalApplicationQueryDTO);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        return BeanConvert.copyList(list, WithDrawalApplicationVO::new);
    }

    @Override
    public void initiateWithdrawal(WithDrawalApplicationDTO withDrawalApplicationDTO) {
        if (ObjectUtil.isEmpty(withDrawalApplicationDTO.getShopId())) {
            throw new ServiceException("门店id不能为空");
        }
        WithDrawalApplicationDO withDrawalApplicationDO = new WithDrawalApplicationDO();
        withDrawalApplicationDO.setWithdrawalOrderNo(TeboNumberGenerator.buildWithDrawalOrderNo());
        withDrawalApplicationDO.setId(SnowFlakeUtil.nextId());
        R<TeboShop> teboShopR = remoteShopService.getShopInfo(withDrawalApplicationDTO.getShopId());
        /**
         * 提现账号
         */
        if (!ObjectUtil.isEmpty(teboShopR) && ObjectUtil.isNotEmpty(teboShopR.getData())) {
            withDrawalApplicationDO.setWithdrawalAccount(teboShopR.getData().getAlipayAccount());
            withDrawalApplicationDO.setWithdrawee(teboShopR.getData().getShopBossName());
            withDrawalApplicationDO.setWithdraweePhone(teboShopR.getData().getPhoneNumber());
        }
        withDrawalApplicationDO.setShopId(withDrawalApplicationDTO.getShopId());
        withDrawalApplicationDO.setWithdrawalAmount(withDrawalApplicationDTO.getWithdrawalAmount());
        if (ObjectUtil.isEmpty(withDrawalApplicationDO.getWithdrawalAccount())) {
            throw new ServiceException("提现账户不能为空");
        }
        withDrawalApplicationMapper.insert(withDrawalApplicationDO);
    }

    @Override
    public void review(CashWithdrawalReviewDTO cashWithdrawalDTO) {
        WithDrawalApplicationDO withDrawalApplicationDO = new WithDrawalApplicationDO();
        BeanConvert.copy(cashWithdrawalDTO, withDrawalApplicationDO);
        withDrawalApplicationDO.setReviewer(SecurityUtils.getUsername());
        withDrawalApplicationMapper.updateById(withDrawalApplicationDO);
    }

    @Override
    public WithDrawalApplicationDetailVO getCashWithdrawalDetail(Long id) {
        WithDrawalApplicationDetailVO withDrawalApplicationDetailVO = new WithDrawalApplicationDetailVO();
        WithDrawalApplicationDO withDrawalApplicationDO = withDrawalApplicationMapper.selectById(id);
        if (ObjectUtil.isEmpty(withDrawalApplicationDO)) {
            throw new ServiceException("提现订单不存在");
        }
        Long shopId = withDrawalApplicationDO.getShopId();
        List<ShopBalanceRecordDO> shopBalanceRecordDOList = shopBalanceRecordMapper.getShopBalanceByShopId(shopId);
        if (CollectionUtil.isNotEmpty(shopBalanceRecordDOList)) {
            int totalAmount = shopBalanceRecordDOList.stream()
                    .mapToInt(item -> item.getAmount())
                    .sum();
            List<ShopBalanceVO> list = BeanConvert.copyList(shopBalanceRecordDOList, ShopBalanceVO::new);
            withDrawalApplicationDetailVO.setShopBalanceList(list);
            withDrawalApplicationDetailVO.setTotalAmount(totalAmount);
        }
        return withDrawalApplicationDetailVO;
    }
}