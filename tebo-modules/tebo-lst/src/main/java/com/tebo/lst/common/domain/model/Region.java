package com.tebo.lst.common.domain.model;

import com.tebo.lst.common.domain.dto.RegionDTO;
import lombok.Getter;

@Getter
public class Region {

    private final String provinceCode;

    private final String provinceName;

    private final String cityCode;

    private final String cityName;

    private final String districtCode;

    private final String districtName;

    private Region(String provinceCode, String provinceName, String cityCode, String cityName, String districtCode, String districtName) {
        this.provinceCode = provinceCode;
        this.provinceName = provinceName;
        this.cityCode = cityCode;
        this.cityName = cityName;
        this.districtCode = districtCode;
        this.districtName = districtName;

    }

    public static Region of(String provinceCode, String provinceName, String cityCode, String cityName, String districtCode, String districtName) {
        return new Region(provinceCode, provinceName, cityCode, cityName, districtCode, districtName);
    }

    /**
     * 从Map创建Region对象
     *
     * @param regionData 包含省市区信息的Map
     * @return Region对象
     */
    public static Region from(RegionDTO regionData) {
        return new Region(
                regionData.getProvinceCode(),
                regionData.getProvinceName(),
                regionData.getCityCode(),
                regionData.getCityName(),
                regionData.getDistrictCode(),
                regionData.getDistrictName()
        );
    }

}
