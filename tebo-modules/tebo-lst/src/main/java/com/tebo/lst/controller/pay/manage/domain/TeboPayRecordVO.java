package com.tebo.lst.controller.pay.manage.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.tebo.common.util.number.MoneyUtil;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 商家联盟支付表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Data
public class TeboPayRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 订单金额以分为单位
     */
    private Integer amount;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付状态 1 预支付成功   2 支付成功  3 超时取消 10 退款中 11 已退款
     */
    private Integer payStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    public String getAmountStr() {
        return MoneyUtil.fenToYuan(amount);
    }

}
