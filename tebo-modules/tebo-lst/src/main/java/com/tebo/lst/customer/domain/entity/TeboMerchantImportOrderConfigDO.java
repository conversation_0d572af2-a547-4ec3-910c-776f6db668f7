package com.tebo.lst.customer.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Getter
@Setter
@TableName("tebo_merchant_import_order_config")
public class TeboMerchantImportOrderConfigDO extends Model<TeboMerchantImportOrderConfigDO> {

    private static final long serialVersionUID = 1L;

    @TableField("id")
    private Long id;

    /**
     * 1:开启 2:关闭
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField("update_by")
    private String updateBy;


    @Override
    public Serializable pkVal() {
        return null;
    }

}
