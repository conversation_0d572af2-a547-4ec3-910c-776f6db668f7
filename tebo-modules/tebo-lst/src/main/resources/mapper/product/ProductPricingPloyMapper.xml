<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.lst.product.mapper.ProductPricingPloyMapper">
    <!-- Result Map for ProductPricingPloyDO -->
    <resultMap id="ProductPricingPloyResultMap" type="com.tebo.lst.product.domain.entity.ProductPricingPloyDO">
        <id column="id" property="id"/>
        <result column="ploy_code" property="ployCode"/>
        <result column="ploy_name" property="ployName"/>
        <result column="operator_type" property="operatorType"/>
        <result column="operator_value" property="operatorValue"/>
        <result column="discount_id" property="discountId"/>
        <result column="discount_value" property="discountValue"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="insertPricingPloy" parameterType="com.tebo.lst.product.domain.entity.ProductPricingPloyDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO product_pricing_ploy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ployCode != null">
                ploy_code,
            </if>
            <if test="ployName != null">
                ploy_name,
            </if>
            <if test="operatorType != null">
                operator_type,
            </if>
            <if test="operatorValue != null">
                operator_value,
            </if>
            <if test="discountId != null">
                discount_id,
            </if>
            <if test="discountValue != null">
                discount_value,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="ployCode != null">
                #{ployCode},
            </if>
            <if test="ployName != null">
                #{ployName},
            </if>
            <if test="operatorType != null">
                #{operatorType},
            </if>
            <if test="operatorValue != null">
                #{operatorValue},
            </if>
            <if test="discountId != null">
                #{discountId},
            </if>
            <if test="discountValue != null">
                #{discountValue},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>


    <select id="selectPolyWithDiscounts" resultType="com.tebo.lst.product.domain.vo.PolyListVo">
        SELECT
        p.id,
        p.ploy_code AS ployCode,
        p.ploy_name AS ployName,
        p.operator_type AS operatorType,
        p.operator_value AS operatorValue,
        p.discount_id AS discountId,
        p.discount_value AS discountValue,
        p.create_by AS createBy,
        p.create_time AS createTime,
        p.update_by AS updateBy,
        p.update_time AS updateTime
        FROM product_pricing_ploy p
        <!--        LEFT JOIN active_discount d ON p.discount_id = d.id-->
        WHERE p.del_flag = 0
        <if test="ployName != null">
            AND p.ploy_name like concat('%', #{ployName}, '%')
        </if>
        ORDER BY p.id desc
        <!--        AND (d.del_flag = 0 OR d.del_flag IS NULL) --><!-- 假设 del_flag 表示是否被删除 -->
    </select>


    <update id="updatePricingPloyById" parameterType="com.tebo.lst.product.domain.entity.ProductPricingPloyDO">
        UPDATE product_pricing_ploy
        <set>
            <if test="ployCode != null">
                ploy_code = #{ployCode},
            </if>
            <if test="ployName != null">
                ploy_name = #{ployName},
            </if>
            <if test="operatorType != null">
                operator_type = #{operatorType},
            </if>
            <if test="operatorValue != null">
                operator_value = #{operatorValue},
            </if>
            <if test="discountId != null">
                discount_id = #{discountId},
            </if>
            <if test="discountValue != null">
                discount_value = #{discountValue},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime}
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 逻辑删除定价策略 -->
    <update id="deletePricingPloyById" parameterType="java.lang.Long">
        UPDATE product_pricing_ploy
        SET del_flag = 1
        WHERE id = #{id}
    </update>
</mapper>