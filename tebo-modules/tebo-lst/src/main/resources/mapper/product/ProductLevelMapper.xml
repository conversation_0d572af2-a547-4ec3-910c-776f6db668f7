<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.lst.product.mapper.ProductLevelMapper">
    <!-- Result Map for ProductLevelDO -->
    <resultMap id="ProductLevelResultMap" type="com.tebo.lst.product.domain.entity.ProductLevelDO">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="product_name" property="productName"/>
        <result column="product_sku_code" property="productSkuCode"/>
        <result column="product_category" property="productCategory"/>
        <result column="product_second_category" property="productSecondCategory"/>
        <result column="product_mode" property="productMode"/>
        <result column="product_image" property="productImage"/>
        <result column="product_detail_images" typeHandler="com.tebo.lst.utils.JsonTypeHandler"
                property="productDetailImages"/>
        <result column="product_base_price" property="productBasePrice"/>
        <result column="product_brand" property="productBrand"/>
        <result column="product_desc" property="productDesc"/>
        <result column="ref_base_price" property="refBasePrice"/>
        <result column="buy_method" property="buyMethod"/>
        <result column="level_type" property="levelType"/>
        <result column="shelf_status" property="shelfStatus"/>
        <result column="num" property="num"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="insertProduct" parameterType="com.tebo.lst.product.domain.entity.ProductLevelDO" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO product_level
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="productName != null">
                product_name,
            </if>
            <if test="productSkuCode != null">
                product_sku_code,
            </if>
            <if test="productCategory != null">
                product_category,
            </if>
            <if test="productSecondCategory != null">
                product_second_category,
            </if>
            <if test="productMode != null">
                product_mode,
            </if>
            <if test="productImage != null">
                product_image,
            </if>
            <if test="productDetailImages != null">
                product_detail_images,
            </if>
            <if test="productBasePrice != null">
                product_base_price,
            </if>
            <if test="productBrand != null">
                product_brand,
            </if>
            <if test="productDesc != null">
                product_desc,
            </if>
            <if test="refBasePrice != null">
                ref_base_price,
            </if>
            <if test="buyMethod != null">
                buy_method,
            </if>
            <if test="num != null">
                num,
            </if>
            <if test="levelType != null">
                level_type,
            </if>
            <if test="shelfStatus != null">
                shelf_status,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">
                #{parentId},
            </if>
            <if test="productName != null">
                #{productName},
            </if>
            <if test="productSkuCode != null">
                #{productSkuCode},
            </if>
            <if test="productCategory != null">
                #{productCategory},
            </if>
            <if test="productSecondCategory != null">
                #{productSecondCategory},
            </if>
            <if test="productMode != null">
                #{productMode},
            </if>
            <if test="productImage != null">
                #{productImage},
            </if>
            <if test="productDetailImages != null">
                #{productDetailImages,typeHandler=com.tebo.lst.utils.JsonTypeHandler},
            </if>
            <if test="productBasePrice != null">
                #{productBasePrice},
            </if>
            <if test="productBrand != null">
                #{productBrand},
            </if>
            <if test="productDesc != null">
                #{productDesc},
            </if>
            <if test="refBasePrice != null">
                #{refBasePrice},
            </if>
            <if test="buyMethod != null">
                #{buyMethod},
            </if>
            <if test="num != null">
                #{num},
            </if>
            <if test="levelType != null">
                #{levelType},
            </if>
            <if test="shelfStatus != null">
                #{shelfStatus},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>


    <select id="selectProductPriceList" resultType="com.tebo.lst.product.domain.vo.ProductPriceListVo">
        SELECT
        t6.id,
        t6.parent_id AS parentId,
        t6.product_name AS productName,
        t6.product_sku_code AS productSkuCode,
        t6.product_category AS productCategory,
        t6.ref_base_price AS refBasePrice,
        t6.product_second_category AS productSecondCategory,
        t6.product_mode AS productMode,
        t6.product_image AS productImage,
        t6.product_brand AS productBrand,
        t6.ref_base_price AS refBasePrice,
        t6.level_type AS levelType,
        t6.buy_method AS buyMethod,
        t6.create_time AS createTime,
        t6.shelf_status AS shelfStatus,
        t1.ploy_id AS ployId,
        t3.id AS policyId,
        t3.operator_type AS operatorType,
        t3.operator_value AS operatorValue,
        t3.discount_id AS discountId,
        t3.discount_value AS discountValue,
        t5.provinceCode,
        t5.provinceName,
        t5.areaName,
        t5.areaCode
        FROM
        product_level t6
        LEFT JOIN product_level t2 ON t2.id = t6.parent_id and t2.del_flag = 0
        LEFT JOIN product_pricing_policy t1 ON t2.id = t1.product_id and t1.del_flag = 0
        LEFT JOIN product_pricing_ploy t3 ON t1.ploy_id = t3.id and t3.del_flag = 0
        LEFT JOIN ( SELECT p.id AS provinceCode, p.NAME AS provinceName, c.id AS citycode, c.NAME AS cityName, a.id AS
        areaCode, a.NAME AS areaName
        FROM `region_info` p
        LEFT JOIN region_info c ON c.parent_id = p.id
        LEFT JOIN region_info a ON a.parent_id = c.id
        WHERE p.parent_id = 0 ) t5 ON t1.region_id = t5.areaCode
        <where>
            <if test="keyWord != null and keyWord != ''">
                AND (t6.product_name LIKE CONCAT(#{keyWord}, '%')
                or t6.product_sku_code LIKE CONCAT(#{keyWord}, '%')
                or t6.product_mode LIKE CONCAT(#{keyWord}, '%')
                )
            </if>
            <if test="areaCode != null and areaCode != ''">
                AND t1.region_id = #{areaCode}
            </if>
            <if test="parentId != null">
                AND t6.parent_id = #{parentId}
            </if>
            <if test="shelfStatus != null">
                AND t6.shelf_status = #{shelfStatus}
            </if>
            <if test="productSecondCategory != null and productSecondCategory != ''">
                AND t6.product_second_category = #{productSecondCategory}
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                AND t6.product_sku_code LIKE CONCAT(#{productSkuCode}, '%')
            </if>
            <if test="productMode != null and productMode != ''">
                AND t6.product_mode LIKE CONCAT(#{productMode}, '%')
            </if>
            <if test="queryPlatFormType != null and queryPlatFormType != ''">
                AND t1.id is not null
            </if>
            and t6.del_flag = 0
            and t6.level_type = 2
        </where>
        order by t1.region_id, t6.id desc
    </select>

    <select id="selectProductSkuList" parameterType="com.tebo.lst.product.domain.params.QueryProductParams"
            resultMap="ProductLevelResultMap">
        SELECT
        id,
        parent_id,
        product_name,
        product_sku_code,
        product_category,
        product_second_category,
        product_mode,
        product_image,
        product_detail_images,
        product_base_price,
        product_brand,
        product_desc,
        ref_base_price,
        buy_method,
        shelf_status,
        level_type,
        del_flag,
        create_by,
        create_time,
        update_by,
        update_time
        FROM
        product_level
        <where>
            del_flag = 0
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="parentId != null">
                AND parent_id = #{parentId}
            </if>
            <if test="productName != null and productName != ''">
                AND product_name LIKE CONCAT('%', #{productName},
                '%')
            </if>
            <if test="keyWord != null and keyWord != ''">
                AND (product_name LIKE CONCAT(#{keyWord}, '%')
                or product_sku_code LIKE CONCAT(#{keyWord}, '%')
                or product_mode LIKE CONCAT(#{keyWord}, '%')
                )
            </if>
            <if test="shelfStatus != null">
                AND shelf_status = #{shelfStatus}
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                AND product_sku_code LIKE CONCAT('%',
                #{productSkuCode}, '%')
            </if>
            <if test="productCategory != null and productCategory != ''">
                AND product_category = #{productCategory}
            </if>
            <if test="productSecondCategory != null and productSecondCategory != ''">
                AND product_second_category =
                #{productSecondCategory}
            </if>
            <if test="productMode != null and productMode != ''">
                AND product_mode = #{productMode}
            </if>
            <if test="productBasePrice != null">
                AND product_base_price = #{productBasePrice}
            </if>
            <if test="productBrand != null and productBrand != ''">
                AND product_brand = #{productBrand}
            </if>
            <if test="buyMethod != null">
                AND buy_method = #{buyMethod}
            </if>
            <if test="levelType != null">
                AND level_type = #{levelType}
            </if>
            <if test="createBy != null and createBy != ''">
                AND create_by = #{createBy}
            </if>
            <if test="updateBy != null and updateBy != ''">
                AND update_by = #{updateBy}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectProductById" resultMap="ProductLevelResultMap">
        SELECT id,
               parent_id,
               product_name,
               product_sku_code,
               product_category,
               product_second_category,
               product_mode,
               product_image,
               product_detail_images,
               product_base_price,
               product_brand,
               product_desc,
               ref_base_price,
               buy_method,
               level_type,
               num,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time
        FROM product_level
        WHERE id = #{id}
          AND del_flag = 0
    </select>

    <update id="updateProductById" parameterType="com.tebo.lst.product.domain.entity.ProductLevelDO">
        UPDATE product_level
        <set>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="productName != null and productName != ''">
                product_name = #{productName},
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                product_sku_code = #{productSkuCode},
            </if>
            <if test="productCategory != null and productCategory != ''">
                product_category = #{productCategory},
            </if>
            <if test="productSecondCategory != null and productSecondCategory != ''">
                product_second_category =
                #{productSecondCategory},
            </if>
            <if test="productMode != null and productMode != ''">
                product_mode = #{productMode},
            </if>
            <if test="productImage != null and productImage != ''">
                product_image = #{productImage},
            </if>
            <if test="productDetailImages != null">
                product_detail_images =
                #{productDetailImages,typeHandler=com.tebo.lst.utils.JsonTypeHandler},
            </if>
            <if test="productBasePrice != null">
                product_base_price = #{productBasePrice},
            </if>
            <if test="productBrand != null and productBrand != ''">
                product_brand = #{productBrand},
            </if>
            <if test="productDesc != null and productDesc != ''">
                product_desc = #{productDesc},
            </if>
            <if test="refBasePrice != null">
                ref_base_price = #{refBasePrice},
            </if>
            <if test="buyMethod != null">
                buy_method = #{buyMethod},
            </if>
            <if test="num != null">
                num = #{num},
            </if>
            <if test="levelType != null">
                level_type = #{levelType},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateProductShelfStatus" parameterType="java.util.Map">
        UPDATE product_level
        <set>
            shelf_status = #{shelfStatus}
        </set>
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateParentIdByIds" parameterType="java.util.Map">
        UPDATE product_level
        <set>
            parent_id = #{parentId}
        </set>
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectProductLevelAndProduct" parameterType="com.tebo.lst.product.domain.req.ProductLevelAndProductReq"
            resultType="com.tebo.lst.product.domain.vo.ProductLevelAndProductDTO">
        SELECT a.id,
        a.parent_id AS parentId,
        a.product_name AS productName,
        a.product_sku_code AS productSkuCode,
        a.product_category AS productCategory,
        a.product_second_category AS productSecondCategory,
        a.product_mode AS productMode,
        a.product_image AS productImage,
        a.product_detail_images AS productDetailImages,
        a.product_base_price AS productBasePrice,
        a.product_brand AS productBrand,
        a.product_desc AS productDesc,
        a.ref_base_price AS refBasePrice,
        a.buy_method AS buyMethod,
        a.level_type AS levelType,
        a.num,
        a.del_flag AS delFlag,
        a.create_by AS createBy,
        a.create_time AS createTime,
        a.update_by AS updateBy,
        a.update_time AS updateTime,
        b.product_sku_code AS parentCode,
        b.product_name AS parentName
        FROM product_level as a
        inner join product_level as b on a.parent_id = b.id
        WHERE a.level_type = 2
        AND a.del_flag = 0
        AND b.del_flag = 0
        <if test="productSecondCategory != null and productSecondCategory != ''">
            AND a.product_second_category = #{productSecondCategory}
        </if>
        <if test="productSkuCode != null and productSkuCode != ''">
            AND a.product_sku_code LIKE CONCAT(#{productSkuCode}, '%')
        </if>
        <if test="productMode != null and productMode != ''">
            AND a.product_mode LIKE CONCAT(#{productMode}, '%')
        </if>
        <if test="productName != null and productName != ''">
            AND a.product_name LIKE CONCAT('%', #{productName}, '%')
        </if>
        <if test="parentCode != null and parentCode != ''">
            AND b.product_sku_code = #{parentCode}
        </if>
        <if test="parentName != null and parentName != ''">
            AND b.product_name LIKE CONCAT('%', #{parentName}, '%')
        </if>
        order by b.create_time desc,a.parent_id,a.product_sku_code desc
    </select>
</mapper>