<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.lst.order.mapper.OrderInfoMapper">
    <!-- 通用字段 + 业务字段集合（替代*） -->
    <sql id="Base_Column_List">
        a.id,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.del_flag,
        a.employee_id,
        a.merchant_id,
        a.merchant_store_name,
        a.tebo_shop_id,
        a.batch_id,
        a.tebo_shop_name,
        a.tebo_shop_phone,
        a.order_no,
        a.service_order_no,
        a.order_source,
        a.order_type,
       a.order_nature,
        a.order_status,
        a.order_price,
        a.province_code,
        a.city_code,
        a.county_code,
        a.customer_name,
        a.customer_phone,
        a.customer_address,
        a.remark_user,
        a.remark_platform,
        a.creation_mode,
        a.battery_code_img_url,
        a.install_completed_img_url,
        a.signed_img_url,
        a.upload_to_audit_time,
        a.audit_time,
        a.accept_time,
        a.settlement_time
    </sql>

    <!-- 完整结果映射 -->
    <resultMap id="BaseResultMap" type="com.tebo.lst.order.domain.dataobject.OrderInfoDO">
        <!-- 继承基类 BaseDO 的字段（根据实际基类字段调整） -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="del_flag" property="delFlag" jdbcType="TINYINT"/>

        <!-- 当前表字段 -->
        <result column="employee_id" property="employeeId" jdbcType="BIGINT"/>
        <result column="batch_id" property="batchId" jdbcType="BIGINT"/>
        <result column="merchant_id" property="merchantId" jdbcType="BIGINT"/>
        <result column="merchant_store_name" property="merchantStoreName" jdbcType="VARCHAR"/>
        <result column="tebo_shop_id" property="teboShopId" jdbcType="BIGINT"/>
        <result column="tebo_shop_name" property="teboShopName" jdbcType="VARCHAR"/>
        <result column="tebo_shop_phone" property="teboShopPhone" jdbcType="VARCHAR"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="service_order_no" property="serviceOrderNo" jdbcType="VARCHAR"/>
        <result column="order_type" property="orderType" jdbcType="INTEGER"/>
        <result column="order_source" property="orderSource" jdbcType="VARCHAR"/>
        <result column="order_status" property="orderStatus" jdbcType="INTEGER"/>
        <result column="order_price" property="orderPrice" jdbcType="BIGINT"/>
        <result column="ref_total_price" property="refTotalPrice" jdbcType="BIGINT"/>
        <result column="ref_service_price" property="refServicePrice" jdbcType="BIGINT"/>
        <result column="province_code" property="provinceCode" jdbcType="VARCHAR"/>
        <result column="city_code" property="cityCode" jdbcType="VARCHAR"/>
        <result column="county_code" property="countyCode" jdbcType="VARCHAR"/>
        <result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
        <result column="customer_phone" property="customerPhone" jdbcType="VARCHAR"/>
        <result column="customer_address" property="customerAddress" jdbcType="VARCHAR"/>
        <result column="remark_user" property="remarkUser" jdbcType="VARCHAR"/>
        <result column="remark_platform" property="remarkPlatform" jdbcType="VARCHAR"/>
        <result column="creation_mode" property="creationMode" jdbcType="INTEGER"/>
        <result column="battery_code_img_url" property="batteryCodeImgUrl" jdbcType="VARCHAR"/>
        <result column="install_completed_img_url" property="installCompletedImgUrl" jdbcType="VARCHAR"/>
        <result column="signed_img_url" property="signedImgUrl" jdbcType="VARCHAR"/>
        <result column="upload_to_audit_time" property="uploadToAuditTime" jdbcType="TIMESTAMP"/>
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP"/>
        <result column="accept_time" property="acceptTime" jdbcType="TIMESTAMP"/>
        <result column="settlement_time" property="settlementTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <!-- 查询订单列表 -->
    <select id="selectOrderList" resultType="com.tebo.lst.order.domain.dataobject.OrderInfoDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM order_info as a
        inner join order_skus as b on a.id = b.order_id
        WHERE a.del_flag = 0
        and b.del_flag = 0
        <if test="kyeWord != null and kyeWord != ''">
            AND (order_no LIKE CONCAT('%', #{kyeWord}, '%')
            OR customer_name LIKE CONCAT('%', #{kyeWord}, '%')
            OR tebo_shop_name LIKE CONCAT('%', #{kyeWord}, '%')
            OR merchant_store_name LIKE CONCAT('%', #{kyeWord}, '%')
            OR service_order_no LIKE CONCAT('%', #{kyeWord}, '%')
            )
        </if>
        <if test="kyeWord4Shop != null and kyeWord4Shop != ''">
            AND (order_no LIKE CONCAT('%', #{kyeWord4Shop}, '%')
            OR service_order_no LIKE CONCAT('%', #{kyeWord4Shop}, '%')
            OR customer_name LIKE CONCAT('%', #{kyeWord4Shop}, '%')
            OR b.product_name LIKE CONCAT('%', #{kyeWord4Shop}, '%')
            OR merchant_store_name LIKE CONCAT('%', #{kyeWord}, '%'))
        </if>
        <if test="orderStatusList != null and orderStatusList.size() > 0">
            AND order_status IN
            <foreach item="status" collection="orderStatusList" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="productSecondCategory != null and productSecondCategory != ''">
            AND product_second_category = #{productSecondCategory}
        </if>
        <if test="startTime != null and startTime != ''">
            AND a.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND a.create_time &lt;= #{endTime}
        </if>
        <if test="teboShopId != null and teboShopId != ''">
            AND a.tebo_shop_id = #{teboShopId}
        </if>
        <if test="createBy != null and createBy != ''">
            AND a.create_by = #{createBy}
        </if>
        <if test="merchantId != null and merchantId != ''">
            AND a.merchant_id = #{merchantId}
        </if>
        ORDER BY a.create_time DESC
    </select>

    <!-- 查询订单列表 -->
    <select id="selectOrderList4Merchant" resultType="com.tebo.lst.order.domain.dto.OrderInfoDTO">
        SELECT *
        FROM (SELECT a.id,
        a.create_by AS createBy,
        a.create_time AS createTime,
        a.update_by AS updateBy,
        a.update_time AS updateTime,
        a.del_flag AS delFlag,
        a.employee_id AS employeeId,
        a.merchant_id AS merchantId,
        a.tebo_shop_id AS teboShopId,
        a.tebo_shop_name AS teboShopName,
        a.merchant_store_name AS merchantStoreName,
        a.tebo_shop_phone AS teboShopPhone,
        a.order_no AS orderNo,
        a.service_order_no AS serviceOrderNo,
        a.order_source AS orderSource,
        a.order_type AS orderType,
        a.order_nature AS orderNature,
        a.order_status AS orderStatus,
        a.order_price AS orderPrice,
        a.province_code AS provinceCode,
        a.city_code AS cityCode,
        a.county_code AS countyCode,
        a.customer_name AS customerName,
        a.customer_phone AS customerPhone,
        a.customer_address AS customerAddress,
        a.remark_user AS remarkUser,
        a.remark_platform AS remarkPlatform,
        a.creation_mode AS creationMode,
        a.battery_code_img_url AS batteryCodeImgUrl,
        a.install_completed_img_url AS installCompletedImgUrl,
        a.signed_img_url AS signedImgUrl,
        a.upload_to_audit_time AS uploadToAuditTime,
        a.audit_time AS auditTime,
        a.accept_time AS acceptTime,
        a.settlement_time AS settlementTime,
        0 as orderId
        FROM order_info as a
        inner join order_skus as b on a.id = b.order_id
        WHERE a.del_flag = 0
        and b.del_flag = 0
        <if test="kyeWord != null and kyeWord != ''">
            AND (order_no LIKE CONCAT('%', #{kyeWord}, '%')
            OR service_order_no LIKE CONCAT('%', #{kyeWord}, '%')
            OR customer_name LIKE CONCAT('%', #{kyeWord}, '%')
            OR tebo_shop_name LIKE CONCAT('%', #{kyeWord}, '%')
            OR b.product_name LIKE CONCAT('%', #{kyeWord}, '%'))
        </if>
        <if test="kyeWord4Shop != null and kyeWord4Shop != ''">
            AND (order_no LIKE CONCAT('%', #{kyeWord4Shop}, '%')
            OR service_order_no LIKE CONCAT('%', #{kyeWord4Shop}, '%')
            OR customer_name LIKE CONCAT('%', #{kyeWord4Shop}, '%')
            OR b.product_name LIKE CONCAT('%', #{kyeWord4Shop}, '%'))
        </if>
        <if test="orderStatusList != null and orderStatusList.size() > 0">
            AND order_status IN
            <foreach item="status" collection="orderStatusList" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="productSecondCategory != null and productSecondCategory != ''">
            AND product_second_category = #{productSecondCategory}
        </if>
        <if test="orderType != null and orderType != ''">
            AND order_type = #{orderType}
        </if>
        <if test="orderNature != null and orderNature != ''">
            AND order_nature = #{orderNature}
        </if>
        <if test="startTime != null and startTime != ''">
            AND a.create_time &gt;= #{startTime}
        </if>
        <if test="teboShopId != null and teboShopId != ''">
            AND a.tebo_shop_id = #{teboShopId}
        </if>
        <if test="createBy != null and createBy != ''">
            AND a.create_by = #{createBy}
        </if>
        <if test="merchantId != null and merchantId != ''">
            AND a.merchant_id = #{merchantId}
        </if>
        <if test="orderType == null or orderType == 2">
            UNION ALL
            SELECT
            c.id,
            c.create_by AS createBy,
            c.create_time AS createTime,
            c.update_by AS updateBy,
            c.update_time AS updateTime,
            c.del_flag AS delFlag,
            a.employee_id AS employeeId,
            a.merchant_id AS merchantId,
            c.tebo_shop_id AS teboShopId,
            c.tebo_shop_name AS teboShopName,
            a.merchant_store_name AS merchantStoreName,
            a.tebo_shop_phone AS teboShopPhone,
            a.order_no AS orderNo,
            c.after_sale_order_no AS serviceOrderNo,
            a.order_source AS orderSource,
            2 AS orderType,
            a.order_nature AS orderNature,
            c.status AS orderStatus,
            a.order_price AS orderPrice,
            a.province_code AS provinceCode,
            a.city_code AS cityCode,
            a.county_code AS countyCode,
            a.customer_name AS customerName,
            a.customer_phone AS customerPhone,
            a.customer_address AS customerAddress,
            a.remark_user AS remarkUser,
            a.remark_platform AS remarkPlatform,
            a.creation_mode AS creationMode,
            a.battery_code_img_url AS batteryCodeImgUrl,
            a.install_completed_img_url AS installCompletedImgUrl,
            a.signed_img_url AS signedImgUrl,
            a.upload_to_audit_time AS uploadToAuditTime,
            a.audit_time AS auditTime,
            a.accept_time AS acceptTime,
            a.settlement_time AS settlementTime,
            a.id as orderId
            FROM
            after_sale_order AS c
            INNER JOIN order_info AS a ON a.id = c.order_id
            INNER JOIN order_skus AS e ON a.id = e.order_id
            WHERE
            c.del_flag = 0
            AND a.del_flag = 0
            AND e.del_flag = 0
            <if test="kyeWord4Shop != null and kyeWord4Shop != ''">
                AND (c.order_no LIKE CONCAT('%', #{kyeWord4Shop}, '%')
                OR a.order_no LIKE CONCAT('%', #{kyeWord4Shop}, '%')
                OR c.after_sale_order_no LIKE CONCAT('%', #{kyeWord4Shop}, '%')
                OR service_order_no LIKE CONCAT('%', #{kyeWord4Shop}, '%')
                OR customer_name LIKE CONCAT('%', #{kyeWord4Shop}, '%')
                OR e.product_name LIKE CONCAT('%', #{kyeWord4Shop}, '%'))
            </if>
            <if test="orderStatusList != null and orderStatusList.size() > 0">
                AND c.status IN
                <foreach item="status" collection="orderStatusList" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="productSecondCategory != null and productSecondCategory != ''">
                AND product_second_category = #{productSecondCategory}
            </if>
            <if test="startTime != null and startTime != ''">
                AND c.create_time &gt;= #{startTime}
            </if>
            <if test="teboShopId != null and teboShopId != ''">
                AND c.tebo_shop_id = #{teboShopId}
            </if>
            <if test="createBy != null and createBy != ''">
                AND c.create_by = #{createBy}
            </if>
            <if test="merchantId != null and merchantId != ''">
                AND c.merchant_id = #{merchantId}
            </if>
        </if>
        ) combined_result
        ORDER BY
        combined_result.updateTime DESC
    </select>


    <update id="clearShopInfo">
        update order_info set tebo_shop_id = null,tebo_shop_name = null,tebo_shop_phone = null,accept_time = null
        where id = #{orderId}
    </update>
</mapper>