<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.lst.order.mapper.StagingOrderMapper">
    <resultMap id="StagingOrderResultMap" type="com.tebo.lst.order.domain.dataobject.StagingOrderDO">
        <id column="id" property="id"/>
        <result column="batch_id" property="batchId"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="order_no" property="orderNo"/>
        <result column="tebo_shop_id" property="teboShopId"/>
        <result column="customer_name" property="customerName"/>
        <result column="customer_phone" property="customerPhone"/>
        <result column="customer_address" property="customerAddress"/>
        <result column="region_code" property="regionCode"/>
        <result column="product_sku_no" property="productSkuNo"/>
        <result column="product_second_category" property="productSecondCategory"/>
        <result column="product_name" property="productName"/>
        <result column="qty" property="qty"/>
        <result column="remark_usr" property="remarkUsr"/>
        <result column="ref_settle_price" property="refSettlePrice"/>
        <result column="ref_total_price" property="refTotalPrice"/>
        <result column="ref_service_price" property="refServicePrice"/>
        <result column="status" property="status"/>
        <result column="error_message" property="errorMessage"/>
        <result column="processed_time" property="processedTime"/>
        <result column="del_flag" property="delFlag"/>
        <!-- 审计字段 -->
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 新增暂存订单 -->
    <insert id="insertStagingOrder" parameterType="com.tebo.lst.order.domain.dataobject.StagingOrderDO">
        INSERT INTO staging_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchId != null">
                batch_id,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="customerName != null">
                customer_name,
            </if>
            <if test="customerPhone != null">
                customer_phone,
            </if>
            <if test="customerAddress != null">
                customer_address,
            </if>
            <if test="regionCode != null">
                region_code,
            </if>
            <if test="productSkuNo != null">
                product_sku_no,
            </if>
            <if test="qty != null">
                qty,
            </if>
            <if test="remarkUsr != null">
                remark_usr,
            </if>
            <if test="refSettlePrice != null">
                ref_settle_price,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="errorMessage != null">
                error_message,
            </if>
            <if test="processedTime != null">
                processed_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="batchId != null">
                #{batchId},
            </if>
            <if test="orderNo != null">
                #{orderNo},
            </if>
            <if test="customerName != null">
                #{customerName},
            </if>
            <if test="customerPhone != null">
                #{customerPhone},
            </if>
            <if test="customerAddress != null">
                #{customerAddress},
            </if>
            <if test="regionCode != null">
                #{regionCode},
            </if>
            <if test="productSkuNo != null">
                #{productSkuNo},
            </if>
            <if test="qty != null">
                #{qty},
            </if>
            <if test="remarkUsr != null">
                #{remarkUsr},
            </if>
            <if test="refSettlePrice != null">
                #{refSettlePrice},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="errorMessage != null">
                #{errorMessage},
            </if>
            <if test="processedTime != null">
                #{processedTime},
            </if>
            <if test="delFlag != null">
                #{delFlag},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>

    <!-- 高效批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO staging_order (id,
        batch_id, order_no, customer_name, customer_phone, customer_address,
        region_code, product_sku_no, product_second_category, product_name, qty, remark_usr, ref_settle_price,
        status, error_message, processed_time, del_flag, create_by,
        create_time, update_by, update_time,tebo_shop_id,exception_message,tebo_shop_name,tebo_shop_phone
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.batchId},
            #{item.orderNo},
            #{item.customerName},
            #{item.customerPhone},
            #{item.customerAddress},
            #{item.regionCode},
            #{item.productSkuNo},
            #{item.productSecondCategory},
            #{item.productName},
            #{item.qty},
            #{item.remarkUsr},
            #{item.refSettlePrice},
            #{item.status},
            #{item.errorMessage},
            #{item.processedTime,jdbcType=TIMESTAMP},
            #{item.delFlag},
            #{item.createBy},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateBy},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.teboShopId},
            #{item.exceptionMessage},
            #{item.teboShopName},
            #{item.teboShopPhone}
            )
        </foreach>
    </insert>

    <select id="getByBatchId" resultType="com.tebo.lst.order.domain.dataobject.StagingOrderDO">
        SELECT id,
               serial_number,
               batch_id,
               order_no,
               customer_name,
               customer_phone,
               customer_address,
               region_code,
               product_sku_no,
                product_second_category,
                product_name,
               qty,
               remark_usr,
               ref_settle_price,
               status,
               error_message,
                exception_message,
               processed_time,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               tebo_shop_id,
            tebo_shop_name,
            tebo_shop_phone
        FROM staging_order
        WHERE batch_id = #{batchId}
          AND del_flag = 0
        order by serial_number asc
    </select>

    <select id="getByOrderNo" resultType="com.tebo.lst.order.domain.dataobject.StagingOrderDO">

        SELECT id,
               batch_id,
               order_no,
               customer_name,
               customer_phone,
               customer_address,
               region_code,
               product_sku_no,
               product_second_category,
               product_name,
               qty,
               remark_usr,
               ref_settle_price,
               status,
               error_message,
               exception_message,
               processed_time,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               tebo_shop_id,
               tebo_shop_name,
               tebo_shop_phone
        FROM staging_order
        WHERE order_no = #{orderNo}
          AND del_flag = 0 and status = 2
    </select>



    <!-- 批量更新 -->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE staging_order
            <set>
                <if test="item.orderNo != null">
                    order_no = #{item.orderNo},
                </if>
                <if test="item.customerName != null">
                    customer_name = #{item.customerName},
                </if>
                <if test="item.customerPhone != null">
                    customer_phone = #{item.customerPhone},
                </if>
                <if test="item.customerAddress != null">
                    customer_address = #{item.customerAddress},
                </if>
                <if test="item.regionCode != null">
                    region_code = #{item.regionCode},
                </if>
                <if test="item.productSkuNo != null">
                    product_sku_no = #{item.productSkuNo},
                </if>
                <if test="item.qty != null">
                    qty = #{item.qty},
                </if>
                <if test="item.remarkUsr != null">
                    remark_usr = #{item.remarkUsr},
                </if>
                <if test="item.refSettlePrice != null">
                    ref_settle_price = #{item.refSettlePrice},
                </if>
                <if test="item.status != null">
                    status = #{item.status},
                </if>
                <if test="item.errorMessage != null">
                    error_message = #{item.errorMessage},
                </if>
                <if test="item.processedTime != null">
                    processed_time = #{item.processedTime},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy},
                </if>
                update_time = CURRENT_TIMESTAMP
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>

    <!-- 批量删除 -->
    <update id="batchDeleteByIds">
        UPDATE staging_order
        SET del_flag = 1,
        update_time = CURRENT_TIMESTAMP
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateSelective" parameterType="com.tebo.lst.order.domain.dataobject.StagingOrderDO">
        UPDATE staging_order
        <set>
            <if test="orderNo != null">
                order_no = #{orderNo},
            </if>
            <if test="customerName != null">
                customer_name = #{customerName},
            </if>
            <if test="customerPhone != null">
                customer_phone = #{customerPhone},
            </if>
            <if test="customerAddress != null">
                customer_address = #{customerAddress},
            </if>
            <if test="regionCode != null">
                region_code = #{regionCode},
            </if>
            <if test="productSkuNo != null">
                product_sku_no = #{productSkuNo},
            </if>
            <if test="qty != null">
                qty = #{qty},
            </if>
            <if test="remarkUsr != null">
                remark_usr = #{remarkUsr},
            </if>
            <if test="refSettlePrice != null">
                ref_settle_price = #{refSettlePrice},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="errorMessage != null">
                error_message = #{errorMessage},
            </if>
            <if test="exceptionMessage != null">
                exception_message = #{exceptionMessage},
            </if>
            <if test="processedTime != null">
                processed_time = #{processedTime},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            update_time = CURRENT_TIMESTAMP
        </set>
        WHERE id = #{id}
    </update>
</mapper>