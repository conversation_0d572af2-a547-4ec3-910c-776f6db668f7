<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.lst.common.mapper.RegionInfoMapper">
    <!-- Result Map for RegionInfoDO -->
    <resultMap id="RegionInfoResultMap" type="com.tebo.lst.common.domain.dataobject.RegionDO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="parent_id" property="parentId"/>
    </resultMap>

    <select id="selectRegionByCountyCodeList" resultType="com.tebo.lst.common.domain.dto.RegionDTO">
        SELECT
            t1.region_code AS provinceCode,
            t1.region_name AS provinceName,
            t2.region_code AS cityCode,
            t2.region_name AS cityName,
            t3.region_code AS districtCode,
            t3.region_name AS districtName
        FROM
            region t3
                JOIN
            region t2 ON t3.parent_code = t2.region_code AND t2.level = 1
                JOIN
            region t1 ON t2.parent_code = t1.region_code AND t1.level = 0
        WHERE
            t3.level = 2
        <if test="countyCodeList != null and countyCodeList.size() > 0">
            AND t3.region_code in
            <foreach item="code" collection="countyCodeList" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>

    </select>
</mapper>