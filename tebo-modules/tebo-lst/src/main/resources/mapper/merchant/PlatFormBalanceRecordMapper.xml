<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.lst.customer.mapper.PlatFormBalanceRecordMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.lst.customer.domain.entity.PlatFormBalanceRecordDO">
        <result column="id" property="id"/>
        <result column="order_no" property="orderNo"/>
        <result column="service_order_no" property="serviceOrderNo"/>
        <result column="fund_type" property="fundType"/>
        <result column="source" property="source"/>
        <result column="document_type" property="documentType"/>
        <result column="remark" property="remark"/>
        <result column="current_balance" property="currentBalance"/>
        <result column="amount" property="amount"/>
        <result column="income" property="income"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no,source, service_order_no, fund_type, document_type, remark, current_balance, amount, income, create_time, update_time
    </sql>

    <select id="getPlatBalanceList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List">
    </include> from platForm_balance_record
        <where>
            <if test="createTimeStartSecond!= null">
                and create_time >= #{createTimeStartSecond ,jdbcType=TIMESTAMP}
            </if>
            <if test="createTimeEndSecond!= null">
                and #{createTimeEndSecond ,jdbcType=TIMESTAMP} > create_time
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="getTotalPlatformServiceFee" resultType="Integer">
        SELECT SUM(amount)
        FROM platForm_balance_record
        WHERE fund_type = 5
    </select>
</mapper>
