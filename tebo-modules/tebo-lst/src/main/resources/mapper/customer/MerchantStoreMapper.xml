<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.lst.customer.mapper.MerchantStoreMapper">
    <resultMap id="BaseResultMap" type="com.tebo.lst.customer.domain.entity.MerchantStoreDO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="wallet_id" property="walletId"/>
        <result column="merchant_code" property="merchantCode"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="contact_email" property="contactEmail"/>
        <result column="merchant_avatar" property="merchantAvatar"/>
        <result column="merchant_context" property="merchantContext"/>
        <result column="contact_name" property="contactName"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="approval_method" property="approvalMethod"/>
        <result column="business_license_image" property="businessLicenseImage"/>
        <result column="business_license_code" property="businessLicenseCode"/>
        <result column="legal_person" property="legalPerson"/>
        <result column="company_address" property="companyAddress"/>
        <result column="registered_capital" property="registeredCapital"/>
        <result column="establishment_date" property="establishmentDate"/>
        <result column="id_card_front_image" property="idCardFrontImage"/>
        <result column="id_card_back_image" property="idCardBackImage"/>
        <result column="id_card_real_name" property="idCardRealName"/>
        <result column="id_card_no" property="idCardNo"/>
        <result column="id_card_address" property="idCardAddress"/>
        <result column="id_card_start_date" property="idCardStartDate"/>
        <result column="id_card_end_date" property="idCardEndDate"/>
        <result column="enabled_status" property="enabledStatus"/>
        <result column="enabled_status" property="enabledStatus"/>
        <result column="merchant_type" property="merchantType"/>
        <result column="store_type" property="storeType"/>
        <result column="merchant_level" property="merchantLevel"/>
        <result column="merchant_status" property="merchantStatus"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="insertMerchantStore" parameterType="com.tebo.lst.customer.domain.entity.MerchantStoreDO">
        INSERT INTO merchant_store
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="walletId != null">
                wallet_id,
            </if>
            <if test="merchantCode != null and merchantCode != ''">
                merchant_code,
            </if>
            <if test="merchantName != null and merchantName != ''">
                merchant_name,
            </if>
            <if test="merchantAvatar != null">
                merchant_avatar,
            </if>
            <if test="merchantContext != null">
                merchant_context,
            </if>
            <if test="contactName != null and contactName != ''">
                contact_name,
            </if>
            <if test="contactPhone != null and contactPhone != ''">
                contact_phone,
            </if>
            <if test="approvalMethod != null">
                approval_method,
            </if>
            <if test="businessLicenseImage != null">
                business_license_image,
            </if>
            <if test="businessLicenseCode != null">
                business_license_code,
            </if>
            <if test="legalPerson != null">
                legal_person,
            </if>
            <if test="companyAddress != null">
                company_address,
            </if>
            <if test="registeredCapital != null">
                registered_capital,
            </if>
            <if test="establishmentDate != null">
                establishment_date,
            </if>
            <if test="idCardFrontImage != null">
                id_card_front_image,
            </if>
            <if test="idCardBackImage != null">
                id_card_back_image,
            </if>
            <if test="idCardRealName != null">
                id_card_real_name,
            </if>
            <if test="idCardNo != null">
                id_card_no,
            </if>
            <if test="contactEmail != null">
                contact_email,
            </if>
            <if test="idCardAddress != null">
                id_card_address,
            </if>
            <if test="idCardStartDate != null">
                id_card_start_date,
            </if>
            <if test="idCardEndDate != null">
                id_card_end_date,
            </if>
            <if test="enabledStatus != null">
                enabled_status,
            </if>
            <if test="merchantType != null">
                merchant_type,
            </if>
            <if test="merchantLevel != null">
                merchant_level,
            </if>
            <if test="merchantStatus != null">
                merchant_status,
            </if>
            <if test="createBy != null and createBy != ''">
                create_by,
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="walletId != null">
                #{walletId},
            </if>
            <if test="merchantCode != null and merchantCode != ''">
                #{merchantCode},
            </if>
            <if test="merchantName != null and merchantName != ''">
                #{merchantName},
            </if>
            <if test="merchantAvatar != null">
                #{merchantAvatar},
            </if>
            <if test="merchantContext != null">
                #{merchantContext},
            </if>
            <if test="contactName != null and contactName != ''">
                #{contactName},
            </if>
            <if test="contactPhone != null and contactPhone != ''">
                #{contactPhone},
            </if>
            <if test="approvalMethod != null">
                #{approvalMethod},
            </if>
            <if test="businessLicenseImage != null">
                #{businessLicenseImage},
            </if>
            <if test="businessLicenseCode != null">
                #{businessLicenseCode},
            </if>
            <if test="legalPerson != null">
                #{legalPerson},
            </if>
            <if test="companyAddress != null">
                #{companyAddress},
            </if>
            <if test="registeredCapital != null">
                #{registeredCapital},
            </if>
            <if test="establishmentDate != null">
                #{establishmentDate},
            </if>
            <if test="idCardFrontImage != null">
                #{idCardFrontImage},
            </if>
            <if test="idCardBackImage != null">
                #{idCardBackImage},
            </if>
            <if test="idCardRealName != null">
                #{idCardRealName},
            </if>
            <if test="idCardNo != null">
                #{idCardNo},
            </if>
            <if test="contactEmail != null">
                #{contactEmail},
            </if>
            <if test="idCardAddress != null">
                #{idCardAddress},
            </if>
            <if test="idCardStartDate != null">
                #{idCardStartDate},
            </if>
            <if test="idCardEndDate != null">
                #{idCardEndDate},
            </if>
            <if test="enabledStatus != null">
                #{enabledStatus},
            </if>
            <if test="merchantType != null">
                #{merchantType},
            </if>
            <if test="merchantLevel != null">
                #{merchantLevel},
            </if>
            <if test="merchantStatus != null">
                #{merchantStatus},
            </if>
            <if test="createBy != null and createBy != ''">
                #{createBy},
            </if>
            <if test="updateBy != null and updateBy != ''">
                #{updateBy},
            </if>
        </trim>
    </insert>

    <select id="selectStoreList" resultType="com.tebo.lst.customer.domain.vo.MerchantStoreListVo" parameterType="map">
        SELECT
        id,user_id,merchant_code,merchant_name,contact_name,merchant_type,store_type,contact_email,
        contact_phone,approval_method,enabled_status,merchant_status,
        create_time
        FROM merchant_store
        <where>
            <if test="merchantCode != null and merchantCode != ''">
                AND merchant_code = #{merchantCode}
            </if>
            <if test="merchantName != null and merchantName != ''">
                AND merchant_name LIKE CONCAT('%', #{merchantName}, '%')
            </if>
            <if test="contactName != null and contactName != ''">
                AND contact_name LIKE CONCAT('%', #{contactName}, '%')
            </if>
            <if test="merchantType != null and merchantType != ''">
                AND merchant_type = #{merchantType}
            </if>
            <if test="storeType != null and storeType != '' ">
                AND store_type = #{storeType}
            </if>
            <if test="contactPhone != null and contactPhone != ''">
                AND contact_phone = #{contactPhone}
            </if>
            <if test="enabledStatus != null and enabledStatus != ''">
                AND enabled_status = #{enabledStatus}
            </if>
            <if test="merchantStatus != null and merchantStatus != ''">
                AND merchant_status = #{merchantStatus}
            </if>
            <if test="approvalMethod != null and approvalMethod != ''">
                AND approval_method = #{approvalMethod}
            </if>
            <if test="createTimeStart != null">
                AND create_time &gt;= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                AND create_time &lt;= #{createTimeEnd}
            </if>
            <if test="idList != null and idList.size() > 0">
                and id in
                <foreach collection="idList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND del_flag = 0
        </where>
        order by  create_time desc
    </select>

    <select id="selectStoreInfoByUserId" resultMap="BaseResultMap" resultType="map">
        SELECT id,
               user_id,
               wallet_id,
               merchant_code,
               merchant_name,
               merchant_avatar,
               merchant_context,
               contact_name,
               contact_email,
               contact_phone,
               approval_method,
               business_license_image,
               business_license_code,
               legal_person,
               company_address,
               registered_capital,
               establishment_date,
               id_card_front_image,
               id_card_back_image,
               id_card_real_name,
               id_card_no,
               id_card_address,
               id_card_start_date,
               id_card_end_date,
               enabled_status,
               merchant_type,
               store_type,
               merchant_level,
               merchant_status,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time
        FROM merchant_store
        WHERE user_id = #{userId}
          and del_flag = 0
        limit 1
    </select>

    <select id="selectDetailById" resultMap="BaseResultMap" resultType="map">
        SELECT id,
               user_id,
               wallet_id,
               merchant_code,
               merchant_name,
               merchant_avatar,
               merchant_context,
               contact_name,
               contact_email,
               contact_phone,
               approval_method,
               business_license_image,
               business_license_code,
               legal_person,
               company_address,
               registered_capital,
               establishment_date,
               id_card_front_image,
               id_card_back_image,
               id_card_real_name,
               id_card_no,
               id_card_address,
               id_card_start_date,
               id_card_end_date,
               enabled_status,
               merchant_type,
               store_type,
               merchant_level,
               merchant_status,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time
        FROM merchant_store
        WHERE id = #{id}
          and del_flag = 0
        limit 1
    </select>

    <update id="updateStoreById" parameterType="com.tebo.lst.customer.domain.entity.MerchantStoreDO">
        UPDATE merchant_store
        <set>
            <if test="merchantName != null and merchantName != ''">
                merchant_name = #{merchantName},
            </if>
            <if test="merchantAvatar != null">
                merchant_avatar = #{merchantAvatar},
            </if>
            <if test="merchantContext != null ">
                merchant_context = #{merchantContext},
            </if>
            <if test="contactName != null and contactName != ''">
                contact_name = #{contactName},
            </if>
            <if test="contactPhone != null and contactPhone != ''">
                contact_phone = #{contactPhone},
            </if>
            <if test="approvalMethod != null">
                approval_method = #{approvalMethod},
            </if>
            <if test="businessLicenseImage != null and businessLicenseImage != ''">
                business_license_image = #{businessLicenseImage},
            </if>
            <if test="businessLicenseCode != null and businessLicenseCode != ''">
                business_license_code = #{businessLicenseCode},
            </if>
            <if test="legalPerson != null">
                legal_person = #{legalPerson},
            </if>
            <if test="companyAddress != null">
                company_address = #{companyAddress},
            </if>
            <if test="registeredCapital != null">
                registered_capital = #{registeredCapital},
            </if>
            <if test="establishmentDate != null">
                establishment_date = #{establishmentDate},
            </if>
            <if test="idCardFrontImage != null and idCardFrontImage != ''">
                id_card_front_image = #{idCardFrontImage},
            </if>
            <if test="idCardBackImage != null and idCardBackImage != ''">
                id_card_back_image = #{idCardBackImage},
            </if>
            <if test="idCardRealName != null and idCardRealName != ''">
                id_card_real_name = #{idCardRealName},
            </if>
            <if test="idCardNo != null and idCardNo != ''">
                id_card_no = #{idCardNo},
            </if>
            <if test="idCardAddress != null">
                id_card_address = #{idCardAddress},
            </if>
            <if test="idCardStartDate != null">
                id_card_start_date = #{idCardStartDate},
            </if>
            <if test="idCardEndDate != null">
                id_card_end_date = #{idCardEndDate},
            </if>
            <if test="enabledStatus != null">
                enabled_status = #{enabledStatus},
            </if>
            <if test="merchantType != null">
                merchant_type = #{merchantType},
            </if>
            <if test="merchantLevel != null">
                merchant_level = #{merchantLevel},
            </if>
            <if test="merchantStatus != null">
                merchant_status = #{merchantStatus},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateStoreByUserId" parameterType="com.tebo.lst.customer.domain.entity.MerchantStoreDO">
        UPDATE merchant_store
        <set>
            <if test="merchantName != null and merchantName != ''">
                merchant_name = #{merchantName},
            </if>
            <if test="merchantAvatar != null ">
                merchant_avatar = #{merchantAvatar},
            </if>
            <if test="merchantContext != null ">
                merchant_context = #{merchantContext},
            </if>
            <if test="contactName != null and contactName != ''">
                contact_name = #{contactName},
            </if>
            <if test="contactPhone != null and contactPhone != ''">
                contact_phone = #{contactPhone},
            </if>
            <if test="approvalMethod != null">
                approval_method = #{approvalMethod},
            </if>
            <if test="businessLicenseImage != null and businessLicenseImage != ''">
                business_license_image = #{businessLicenseImage},
            </if>
            <if test="businessLicenseCode != null and businessLicenseCode != ''">
                business_license_code = #{businessLicenseCode},
            </if>
            <if test="legalPerson != null">
                legal_person = #{legalPerson},
            </if>
            <if test="companyAddress != null">
                company_address = #{companyAddress},
            </if>
            <if test="registeredCapital != null">
                registered_capital = #{registeredCapital},
            </if>
            <if test="establishmentDate != null">
                establishment_date = #{establishmentDate},
            </if>
            <if test="idCardFrontImage != null and idCardFrontImage != ''">
                id_card_front_image = #{idCardFrontImage},
            </if>
            <if test="idCardBackImage != null and idCardBackImage != ''">
                id_card_back_image = #{idCardBackImage},
            </if>
            <if test="idCardRealName != null and idCardRealName != ''">
                id_card_real_name = #{idCardRealName},
            </if>
            <if test="idCardNo != null and idCardNo != ''">
                id_card_no = #{idCardNo},
            </if>
            <if test="idCardAddress != null">
                id_card_address = #{idCardAddress},
            </if>
            <if test="idCardStartDate != null">
                id_card_start_date = #{idCardStartDate},
            </if>
            <if test="idCardEndDate != null">
                id_card_end_date = #{idCardEndDate},
            </if>
            <if test="enabledStatus != null">
                enabled_status = #{enabledStatus},
            </if>
            <if test="merchantType != null">
                merchant_type = #{merchantType},
            </if>
            <if test="merchantLevel != null">
                merchant_level = #{merchantLevel},
            </if>
            <if test="merchantStatus != null">
                merchant_status = #{merchantStatus},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
        </set>
        WHERE user_id = #{userId}
    </update>

    <update id="review" parameterType="com.tebo.lst.customer.domain.dto.MerchantReviewDTO">
        update merchant_store
        <set>
            <if test="merchantStatus != null and merchantStatus != ''">
                merchant_status = #{merchantStatus},
            </if>
            <if test="enabledStatus != null and enabledStatus != ''">
                enabled_status = #{enabledStatus},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateEnableStatus">
        update merchant_store
        set enabled_status = #{status}
    </update>
</mapper>