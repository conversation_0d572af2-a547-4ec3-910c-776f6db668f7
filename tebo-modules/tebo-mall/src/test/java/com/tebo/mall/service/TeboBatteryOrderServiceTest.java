package com.tebo.mall.service;

import com.tebo.mall.service.order.TeboBatteryOrderService;
import com.tebo.mall.web.domain.dto.order.TeboBatteryOrderRefundDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * 电池订单服务测试
 */
@Slf4j
@SpringBootTest
public class TeboBatteryOrderServiceTest {

    @Resource
    private TeboBatteryOrderService teboBatteryOrderService;

    @Test
    public void testRefundBatteryOrder() {
        // 创建退款请求
        TeboBatteryOrderRefundDTO refundDTO = new TeboBatteryOrderRefundDTO();
        refundDTO.setOrderNo("TBCXA123456789A");
        refundDTO.setRefundReason("用户申请退款");
        refundDTO.setOperator("test_user");

        try {
            Boolean result = teboBatteryOrderService.refundBatteryOrder(refundDTO);
            log.info("退款结果：{}", result);

            // 验证退款后的状态
            if (result) {
                log.info("退款成功，应该已经：");
                log.info("1. 删除了2张电池券和1张大米券");
                log.info("2. 更新久久券订单状态为已退款");
                log.info("3. 更新电池订单状态为已退款");
                log.info("4. 记录了退款记录");
                log.info("5. 更新了回调状态");
            }
        } catch (Exception e) {
            log.error("退款测试失败", e);
        }
    }

    @Test
    public void testValidateOrderNo() {
        // 测试订单号格式校验
        String[] testOrderNos = {
            "TBCXA123456789A",  // 正确格式
            "TBCXA123456789B",  // 错误格式，不以A结尾
            "TBCXB123456789A",  // 错误格式，不以TBCXA开头
            "123456789A",       // 错误格式，不以TBCXA开头
            ""                  // 空字符串
        };

        for (String orderNo : testOrderNos) {
            TeboBatteryOrderRefundDTO refundDTO = new TeboBatteryOrderRefundDTO();
            refundDTO.setOrderNo(orderNo);
            refundDTO.setRefundReason("测试");
            refundDTO.setOperator("test");

            try {
                Boolean result = teboBatteryOrderService.refundBatteryOrder(refundDTO);
                log.info("订单号：{}，退款结果：{}", orderNo, result);
            } catch (Exception e) {
                log.info("订单号：{}，校验失败：{}", orderNo, e.getMessage());
            }
        }
    }
}
