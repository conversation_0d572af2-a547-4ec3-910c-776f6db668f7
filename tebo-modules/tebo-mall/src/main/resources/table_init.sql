CREATE TABLE `tebo_brand`
(
    `id`              bigint NOT NULL AUTO_INCREMENT COMMENT '品牌id',
    `brand_no`        varchar(64)   DEFAULT NULL COMMENT '品牌编号',
    `brand_name`      varchar(64)   DEFAULT NULL COMMENT '品牌名称',
    `first_char`      varchar(64)   DEFAULT NULL COMMENT '首字母',
    `sort`            int(11) DEFAULT NULL COMMENT '排序',
    `brand_logo`      varchar(255)  DEFAULT NULL COMMENT '品牌logo',
    `brand_detail`    varchar(255)  DEFAULT NULL COMMENT '品牌专区大图',
    `brand_story`     varchar(2048) DEFAULT NULL COMMENT '品牌故事',
    `is_show`         tinyint(1) DEFAULT NULL COMMENT '是否显示',
    `is_manufacturer` tinyint(1) DEFAULT NULL COMMENT '是否是品牌制造商 0:否 1:是',
    `del_flag`        tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    `create_by`       varchar(64)   DEFAULT '' COMMENT '创建者',
    `create_time`     datetime      DEFAULT NULL COMMENT '创建时间',
    `update_by`       varchar(64)   DEFAULT '' COMMENT '更新者',
    `update_time`     datetime      DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='品牌表';


CREATE TABLE `tebo_category`
(
    `id`             bigint NOT NULL AUTO_INCREMENT COMMENT '类目id',
    `category_no`    varchar(64) DEFAULT NULL COMMENT '类目编号',
    `category_name`  varchar(64) DEFAULT NULL COMMENT '类目名称',
    `category_level` int(11) NOT NULL DEFAULT 1 COMMENT '类目级别',
    `unit`           varchar(64) DEFAULT NULL COMMENT '单位',
    `del_flag`       tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    `create_by`      varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time`    datetime    DEFAULT NULL COMMENT '创建时间',
    `update_by`      varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time`    datetime    DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='类目表';

CREATE TABLE `tebo_goods`
(
    `id`           bigint      NOT NULL AUTO_INCREMENT COMMENT '商品id',
    `goods_no`     varchar(64)  DEFAULT NULL COMMENT '商品编号',
    `goods_name`   varchar(64) NOT NULL COMMENT '商品名称',
    `sku`          varchar(64)  DEFAULT NULL COMMENT '规格型号',
    `category_id`  bigint      NOT NULL COMMENT '商品类目id',
    `shop_id`      bigint      NOT NULL COMMENT '店铺id',
    `brand_id`     bigint      NOT NULL COMMENT '品牌id',
    `sale_price`   INT         NOT NULL COMMENT '售价',
    `origin_price` INT          DEFAULT NULL COMMENT '原价',
    `color`        varchar(64)  DEFAULT NULL COMMENT '颜色',
    `size`         varchar(64)  DEFAULT NULL COMMENT '尺寸',
    `unit`         varchar(64)  DEFAULT NULL COMMENT '单位',
    `goods_pic`    varchar(255) DEFAULT NULL COMMENT '商品图片',
    `publish`      tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否上架 0:否 1:是',
    `del_flag`     tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    `create_by`    varchar(64)  DEFAULT '' COMMENT '创建者',
    `create_time`  datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`    varchar(64)  DEFAULT '' COMMENT '更新者',
    `update_time`  datetime     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品表';

CREATE TABLE `tebo_goods_shop_link`
(
    `id`       bigint NOT NULL AUTO_INCREMENT COMMENT '订单id',
    `goods_id` bigint NOT NULL COMMENT '商品id',
    `shop_id`  bigint NOT NULL COMMENT '店铺id',
    `del_flag` tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品店铺关联表';