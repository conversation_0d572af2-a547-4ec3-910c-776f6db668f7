<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.mall.mapper.TeboMallOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.mall.entity.TeboMallOrderDO">
        <result column="id" property="id" />
        <result column="unionid" property="unionid" />
        <result column="consumer_id" property="consumerId" />
        <result column="customer_name" property="customerName" />
        <result column="customer_phone" property="customerPhone" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="order_status" property="orderStatus" />
        <result column="total_goods_number" property="totalGoodsNumber" />
        <result column="order_no" property="orderNo" />
        <result column="home_service_fee" property="homeServiceFee" />
        <result column="par_value" property="parValue" />
        <result column="coupon_code" property="couponCode" />
        <result column="address" property="address" />
        <result column="shop_address" property="shopAddress" />
        <result column="order_amount" property="orderAmount" />
        <result column="service_type" property="serviceType" />
        <result column="order_type" property="orderType" />
        <result column="channel" property="channel" />
        <result column="pay_time" property="payTime" />
        <result column="cancel_type" property="cancelType" />
        <result column="cancel_time" property="cancelTime" />
        <result column="appointment_service_time" property="appointmentServiceTime" />
        <result column="verification_name" property="verificationName" />
        <result column="verification_time" property="verificationTime" />
        <result column="verification_account" property="verificationAccount" />
        <result column="verification_account_id" property="verificationAccountId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
        <result column="battery_code" property="batteryCode" />
        <result column="area_name" property="areaName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, unionid,consumer_id,channel, customer_name, customer_phone, shop_id, shop_name, tenant_id,tenant_name, order_status, order_no, home_service_fee,par_value,coupon_code,address,shop_address, order_amount,total_goods_number, service_type, order_type, pay_time, cancel_type, cancel_time, appointment_service_time, verification_name, verification_time, verification_account, verification_account_id, create_time, update_time, del_flag, battery_code, area_name
    </sql>

    <select id="getOrderList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from tebo_mall_order
     <where>
         del_flag = 0
         <if test="unionid!= null">
            and unionid = #{unionid}
         </if>
         <if test="orderStatus!= null">
           and order_status = #{orderStatus}
         </if>
         <if test="orderNo!= null">
             and order_no like concat('%', #{orderNo}, '%')
         </if>
         <if test="verificationNameLike!= null and verificationNameLike != ''">
             and verification_name like concat('%', #{verificationNameLike}, '%')
         </if>
         <if test="serviceType!= null">
           and service_type = #{serviceType}
         </if>
         <if test="shopId!= null">
           and shop_id = #{shopId}
         </if>
         <if test="tenantId!= null">
            and tenant_id = #{tenantId}
         </if>
         <if test="verificationAccountId!= null">
             and verification_account_id = #{verificationAccountId}
         </if>
         <if test="tenantId!= null">
            and tenant_id = #{tenantId}
         </if>
         <if test="channel != null">
             and channel = #{channel}
         </if>
         <if test="orderType != null">
             and order_type = #{orderType}
         </if>
         <if test="shopName!= null">
           and shop_name like concat('%', #{shopName}, '%')
         </if>
         <if test="areaName!= null">
           and area_name like concat('%', #{areaName}, '%')
         </if>
         <if test="shopAddress!= null">
           and shop_address like concat('%', #{shopAddress}, '%')
         </if>
         <if test="verificationTimeStartSecond!= null">
             and verification_time >= #{verificationTimeStartSecond ,jdbcType=TIMESTAMP}
         </if>
         <if test="verificationTimeEndSecond!= null">
             and #{verificationTimeEndSecond ,jdbcType=TIMESTAMP} > verification_time
         </if>
         <if test="createTimeStartSecond!= null">
             and create_time >= #{createTimeStartSecond ,jdbcType=TIMESTAMP}
         </if>
         <if test="createTimeEndSecond!= null">
             and #{createTimeEndSecond ,jdbcType=TIMESTAMP} > create_time
         </if>
         <if test="updateTimeStartSecond!= null">
             and update_time >= #{updateTimeStartSecond ,jdbcType=TIMESTAMP}
         </if>
         <if test="updateTimeEndSecond!= null">
             and #{updateTimeEndSecond ,jdbcType=TIMESTAMP} > update_time
         </if>
         <if test="orderStatusList != null and orderStatusList.size() > 0">
             and order_status in
             <foreach collection="orderStatusList" item="item" open="(" separator="," close=")">
                 #{item}
             </foreach>
         </if>
     </where>
     order by create_time desc
    </select>


    <select id="getByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tebo_mall_order
        where order_no = #{orderNo}
    </select>

    <select id="getPayTimeOutOrder" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tebo_mall_order
        where del_flag = 0
          and order_status = 1
          and create_time &lt; #{time}
    </select>

    <update id="updateMallOrderStatus">
        update tebo_mall_order
        <set>
           <if test="orderStatus != null and orderStatus != '' ">
               order_status = #{orderStatus},
           </if>
           <if test="cancelType != null and cancelType != '' ">
               cancel_type = #{cancelType},
           </if>
           <if test="cancelTime != null">
               cancel_time = #{cancelTime},
           </if>
           <if test="payTime != null">
                pay_time = #{payTime},
           </if>
            <if test="channel != null">
                channel = #{channel},
            </if>
            <if test="orderType != null">
                order_type = #{orderType},
            </if>
            <if test="consumerId != null">
                consumer_id = #{consumerId},
            </if>
        </set>
        where id in
        <foreach collection="idList" item="unique" open="(" separator="," close=")">
            #{unique}
        </foreach>
    </update>
    <update id="updateGiftOrder">
        UPDATE tebo_rescue_prod.tebo_gift_pack_order tgpo
        SET type = 3,
            reference_phone = #{phoneNumber},
            partner_phone = #{partnerNumber}
        WHERE
            order_no = (
            SELECT tgoc.order_no FROM `tebo_goods_order_coupon` tgoc
                                 WHERE tgoc.order_id = #{orderId}
                                   AND tgoc.amount = 3000)
    </update>
    <update id="updateCouponStatusForMerge">
        update tebo_rescue_prod.tebo_coupon_customer
        set occ_status = 2, occ_number = #{orderNo}
        where
            coupon_id in (10001,1853320012069076992)
          and order_no in ( SELECT order_no FROM tebo_rescue_prod.`tebo_gift_pack_order`
                            where create_by = #{orderNo} )
    </update>

    <select id="getExtendedWarrantyOrderOrder"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tebo_mall_order
        where del_flag = 0
        and channel = 4
        and create_time &lt; #{time}
    </select>
    <select id="getMallOrderList" resultType="com.tebo.mall.entity.TeboMallOrderDO">
        select
        <include refid="Base_Column_List"/>
        from tebo_mall_order
        where del_flag = 0
        <if test="orderStatus != null">
            and order_status = #{orderStatus}
        </if>
        and order_type = 12
        and tenant_id = #{tenantId}
        order by create_time desc
    </select>
    <select id="selectOrderTypeByOrderId" resultType="java.lang.Integer">
        SELECT tgpo.type
        FROM `tebo_goods_order_coupon` tgoc
        left join tebo_rescue_prod.tebo_gift_pack_order tgpo on tgoc.order_no = tgpo.order_no
        where tgoc.order_id = #{orderId}
          and tgoc.amount = 3000
    </select>
    <select id="selectGiftPackOrderByOrderId" resultType="com.tebo.mall.domain.dto.TeboCouponGiftOrderDTO">
        select id ,order_no as orderNo,  pack_id as packId from  tebo_rescue_prod.tebo_gift_pack_order tgpo
        WHERE
            order_no = (SELECT tgoc.order_no FROM `tebo_goods_order_coupon` tgoc WHERE tgoc.order_id = #{orderId} AND tgoc.amount = 3000)
    </select>
    <select id="selectCouponByOrderNo" resultType="java.lang.String">
        select unique_code as uniqueCode from tebo_rescue_prod.tebo_coupon_customer
        where
            coupon_id in (10001,1853320012069076992)
          and order_no in ( SELECT order_no FROM tebo_rescue_prod.`tebo_gift_pack_order`
                            where create_by = #{orderNo} )
        order by coupon_id desc
    </select>
    <select id="selectCouponOrderNo" resultType="java.lang.String">
        SELECT order_no FROM tebo_rescue_prod.`tebo_gift_pack_order`
        where create_by = #{orderNo}
    </select>
</mapper>
