<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.mall.mapper.TeboSpecialSupplyDistrictGoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.mall.entity.TeboSpecialSupplyDistrictGoodsDO">
        <id column="id" property="id" />
        <result column="goods_no" property="goodsNo" />
        <result column="goods_type" property="goodsType" />
        <result column="category_id" property="categoryId" />
        <result column="second_category_id" property="secondCategoryId" />
        <result column="goods_name" property="goodsName" />
        <result column="brand_id" property="brandId" />
        <result column="business_category" property="businessCategory" />
        <result column="main_pic" property="mainPic" />
        <result column="packaging_quantity" property="packagingQuantity" />
        <result column="district" property="district" />
        <result column="province_district" property="provinceDistrict" />
        <result column="city_district" property="cityDistrict" />
        <result column="goods_detail" property="goodsDetail" />
        <result column="sale_price" property="salePrice" />
        <result column="origin_price" property="originPrice" />
        <result column="trade_in_price" property="tradeInPrice" />
        <result column="selling_point" property="sellingPoint" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="authorization_time" property="authorizationTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, goods_no,goods_detail, goods_type, category_id, second_category_id, goods_name, brand_id, business_category, main_pic, packaging_quantity, district, province_district, city_district, origin_price, sale_price, trade_in_price, selling_point, create_by, create_time, update_by, update_time, del_flag
    </sql>
    <delete id="delGoodsRelate">
        delete from tebo_special_supply_district_goods
        where goods_no = #{goodsNo}
    </delete>
    <select id="selectById" flushCache="true" useCache="false" resultMap="BaseResultMap">
       select <include refid="Base_Column_List"></include> from tebo_special_supply_district_goods
       where id = #{id}
   </select>

    <insert id="batchInsert">
        insert into tebo_special_supply_district_goods (id,category_id, second_category_id, goods_name,selling_point,
        goods_type, brand_id, goods_no, business_category, origin_price, sale_price, trade_in_price, packaging_quantity,main_pic, create_by, create_time, update_by, update_time,authorization_time,district,province_district, city_district,goods_detail)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.categoryId}, #{item.secondCategoryId}, #{item.goodsName}, #{item.sellingPoint}, #{item.goodsType}, #{item.brandId}, #{item.goodsNo},#{item.businessCategory},#{item.originPrice}, #{item.salePrice},#{item.tradeInPrice},#{item.packagingQuantity},#{item.mainPic},#{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}, #{item.authorizationTime},#{item.district},#{item.provinceDistrict},#{item.cityDistrict},#{item.goodsDetail})
        </foreach>
    </insert>

    <select id="list" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from tebo_special_supply_district_goods
    <where>
        <if test="district!= null and district !='' ">
            and district = #{district}
        </if>
        <if test="goodsNo!= null and goodsNo !='' ">
            and goods_no = #{goodsNo}
        </if>
        <if test="goodsName!= null and goodsName !='' ">
            and goods_name = #{goodsName}
        </if>
        <if test="districtList != null and districtList.size() > 0">
            and district in
            <foreach collection="districtList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="goodsIdList != null and goodsIdList.size() > 0">
            and id in
            <foreach collection="goodsIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </where>
    order by create_time desc

    </select>

    <select id="listNew" resultType="com.tebo.mall.api.domain.view.TeboSpecialSupplyDistrictGoodsVO">
        select
        tssdg.id, tssdg.goods_name, tssdg.province_district, tssdg.city_district, tssdg.goods_no, tssg.main_pic,c.category_name,ca.category_name as secondCategoryName, tssdg.sale_price, tssdg.trade_in_price, tssdg.create_time, tssdg.update_time
        from tebo_special_supply_district_goods tssdg
        LEFT JOIN tebo_special_supply_goods tssg on tssdg.goods_no = tssg.goods_no
        left join tebo_goods_category c on tssdg.category_id = c.id
        left join tebo_goods_category ca on tssdg.second_category_id = ca.id
    <where>
        <if test="district!= null and district !='' ">
            and tssdg.district = #{district}
        </if>
        <if test="goodsNo!= null and goodsNo !='' ">
            and tssdg.goods_no = #{goodsNo}
        </if>
        <if test="goodsName!= null and goodsName !='' ">
            and tssdg.goods_name = #{goodsName}
        </if>
        <if test="districtList != null and districtList.size() > 0">
            and tssdg.district in
            <foreach collection="districtList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="goodsIdList != null and goodsIdList.size() > 0">
            and tssdg.id in
            <foreach collection="goodsIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </where>
        ORDER BY
        -- 1. 提取 Ah 值并按升序排序（支持 5Ah/10ah 等大小写）
        CAST(REGEXP_SUBSTR(tssdg.goods_name, '[0-9]+\\.?[0-9]*(?i)ah') AS DECIMAL(10,2)) ASC,
        -- 2. 提取 V 值并按升序排序（支持 12V/24v 等大小写）
        CAST(REGEXP_SUBSTR(tssdg.goods_name, '[0-9]+(?i)v') AS UNSIGNED) ASC,
        -- 3. 保留原排序条件（如无 Ah/V 则按创建时间降序）
        tssdg.create_time DESC
    </select>

    <select id="goodsCount" resultType="int">
        select count(1) from tebo_special_supply_district_goods
        <where>
            <if test="district!= null and district !='' ">
                and district = #{district}
            </if>
            <if test="goodsNo!= null and goodsNo !='' ">
                and goods_no = #{goodsNo}
            </if>
            <if test="districtList != null and districtList.size() > 0">
                and district in
                <foreach collection="districtList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="goodsIdList != null and goodsIdList.size() > 0">
                and id in
                <foreach collection="goodsIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getShopIdByUnionId" resultType="java.lang.Long">
        SELECT
            id
        FROM
            tebo_system_prod.tebo_shop
        WHERE
            phone_number COLLATE utf8mb4_unicode_ci IN (
                SELECT reference_phone COLLATE utf8mb4_unicode_ci
                FROM tebo_rescue_prod.`tebo_customer_promote_record`
                WHERE union_id = #{unionId}
                  AND type = 3)
          and vip = 1
            limit 1
    </select>

</mapper>
