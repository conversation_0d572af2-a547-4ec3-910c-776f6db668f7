<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.mall.mapper.TeboBatteryOrderRefundMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.mall.entity.TeboBatteryOrderRefundDO">
        <id column="id" property="id" />
        <result column="battery_order_no" property="batteryOrderNo" />
        <result column="gift_pack_order_no" property="giftPackOrderNo" />
        <result column="refund_amount" property="refundAmount" />
        <result column="refund_status" property="refundStatus" />
        <result column="refund_reason" property="refundReason" />
        <result column="wechat_refund_no" property="wechatRefundNo" />
        <result column="operator" property="operator" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, battery_order_no, gift_pack_order_no, refund_amount, refund_status, refund_reason, wechat_refund_no, operator, create_time, update_time
    </sql>

    <select id="selectByBatteryOrderNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tebo_battery_order_refund
        where battery_order_no = #{batteryOrderNo}
        limit 1
    </select>

</mapper>
