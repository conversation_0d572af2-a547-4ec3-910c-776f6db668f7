<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.mall.mapper.integral.TeboIntegralOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.mall.entity.TeboIntegralOrderDO">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="damai_order_id" property="daMaiOrderId" />
        <result column="integral" property="integral" />
        <result column="status" property="status" />
        <result column="shipping_code" property="shippingCode" />
        <result column="shipping_time" property="shippingTime" />
        <result column="receive_time" property="receiveTime" />
        <result column="account_id" property="accountId" />
        <result column="account_name" property="accountName" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="consignee" property="consignee" />
        <result column="mobile" property="mobile" />
        <result column="all_address" property="allAddress" />
        <result column="invoice_no" property="invoiceNo" />
        <result column="shipping_name" property="shippingName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="cancel_time" property="cancelTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <resultMap id="BaseOrderMap" type="com.tebo.mall.web.domain.vo.integral.order.TeboIntegralOrderVO">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="damai_order_id" property="daMaiOrderId" />
        <result column="kehu_shichangjia" property="keHuShiChangJia" />
        <result column="integral" property="integral" />
        <result column="status" property="status" />
        <result column="shipping_code" property="shippingCode" />
        <result column="shipping_time" property="shippingTime" />
        <result column="receive_time" property="receiveTime" />
        <result column="account_id" property="accountId" />
        <result column="account_name" property="accountName" />
        <result column="shop_id" property="shopId" />
        <result column="goods_number" property="goodsNumber" />
        <result column="product_type" property="productType" />
        <result column="shop_name" property="shopName" />
        <result column="consignee" property="consignee" />
        <result column="mobile" property="mobile" />
        <result column="all_address" property="allAddress" />
        <result column="invoice_no" property="invoiceNo" />
        <result column="shipping_name" property="shippingName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="cancel_time" property="cancelTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_order_List">
        o.id, order_no,g.goods_number,g.product_type, o.damai_order_id,o.kehu_shichangjia, integral, `status`, shipping_code, shipping_time, receive_time, shop_id,shop_name,account_id,account_name, consignee, mobile, all_address,invoice_no, shipping_name, o.create_time, o.update_time, cancel_time, del_flag
    </sql>

    <sql id="Base_Column_List">
       id, order_no, damai_order_id, integral, `status`, shipping_code, shipping_time, receive_time, shop_id,shop_name,account_id,account_name, consignee, mobile, all_address,invoice_no, shipping_name, create_time, update_time, cancel_time, del_flag
    </sql>

    <select id="getOrderList" resultMap="BaseOrderMap">
    select <include refid="Base_order_List"></include> from tebo_integral_order o
        left JOIN tebo_integral_order_goods g ON  g.order_id = o.id
        <where>
            <if test="status != null and status != '' ">
                and o.status = #{status}
            </if>
            <if test="shopId != null and shopId !=''">
                and o.shop_id = #{shopId}
            </if>
            <if test="orderNo != null and orderNo !=''">
                and o.order_no like concat('%', #{orderNo}, '%')
            </if>
            <if test="mobile != null and mobile !=''">
                and o.mobile like concat('%', #{mobile}, '%')
            </if>
            <if test="goodsName != null and goodsName !=''">
                and g.goods_name like concat('%', #{goodsName}, '%')
            </if>
            <if test="accountName != null and accountName !=''">
                and o.account_name like concat('%', #{accountName}, '%')
            </if>
            <if test="startOrderTime != null and startOrderTime != ''">
                AND date_format(o.create_time,'%y%m%d') &gt;= date_format(#{startOrderTime},'%y%m%d')
            </if>
            <if test="endOrderTime != null and endOrderTime != ''">
                AND date_format(o.create_time,'%y%m%d') &lt;= date_format(#{endOrderTime},'%y%m%d')
            </if>
        </where>
            order by create_time desc
    </select>

    <select id="getDetailById" resultMap="BaseResultMap">
     select <include refid="Base_Column_List"></include> from tebo_integral_order where id = #{orderId}
    </select>

    <update id="updateOrderStatus">
        update tebo_integral_order set status = #{status} where id = #{orderId}
    </update>

    <update id="updateOrderInfo">
        update tebo_integral_order
        <set>
            <if test="daMaiOrderId!= null">
              damai_order_id = #{daMaiOrderId},
            </if>
            <if test="invoiceNo!= null">
              invoice_no = #{invoiceNo},
            </if>
            <if test="status!= null">
                status = #{status},
            </if>
            <if test="shippingCode!= null">
                shipping_code = #{shippingCode},
            </if>
            <if test="shippingName!= null">
                shipping_name = #{shippingName},
            </if>
            <if test="cancelTime!= null">
                cancel_time = #{cancelTime},
            </if>
            <if test="receiveTime!= null">
                receive_time = #{receiveTime},
            </if>
            <if test="shippingTime!= null">
                shipping_time = #{shippingTime},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>
