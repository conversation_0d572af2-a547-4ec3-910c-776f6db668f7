package com.tebo.mall.web.domain.dto.order;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 电池订单退款DTO
 */
@Data
public class TeboBatteryOrderRefundDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 操作人
     */
    private String operator;
}
