package com.tebo.mall.manager.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tebo.mall.entity.TeboGiftGoodsDO;
import com.tebo.mall.entity.TeboRankActivitiesGoodsDO;
import com.tebo.mall.manager.TeboRankActivitiesGoodsManger;
import com.tebo.mall.mapper.TeboRankActivitiesGoodsMapper;
import com.tebo.mall.mapper.TeboRankActivitiesMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class TeboRankActivitiesGoodsMangerImpl extends ServiceImpl<TeboRankActivitiesGoodsMapper, TeboRankActivitiesGoodsDO> implements TeboRankActivitiesGoodsManger {

    @Autowired
    private TeboRankActivitiesGoodsMapper rankActivitiesGoodsMapper;

    @Override
    public List<TeboRankActivitiesGoodsDO> getByRankActivitiesId(Long activitiesId) {
        if (Objects.isNull(activitiesId)) {
            return new ArrayList<>();
        }
        List<TeboRankActivitiesGoodsDO> rankActivitiesGoodsDOS = rankActivitiesGoodsMapper.getByRankActivitiesId(activitiesId);

        return rankActivitiesGoodsDOS;
    }

    @Override
    public List<TeboRankActivitiesGoodsDO> getByRankActivitiesIds(List<Long> activitiesIds) {

        if (CollectionUtil.isEmpty(activitiesIds)) {
            return new ArrayList<>();
        }
        List<TeboRankActivitiesGoodsDO> rankActivitiesGoodsDOS = rankActivitiesGoodsMapper.getByRankActivitiesIds(activitiesIds);

        return rankActivitiesGoodsDOS;
    }

    @Override
    public Boolean removeByActivitiesId(Long activitiesId) {

        if (Objects.isNull(activitiesId)) {
            return false;
        }
        List<TeboRankActivitiesGoodsDO> activitiesGoodsDOS = getByRankActivitiesId(activitiesId);
        if (CollectionUtil.isEmpty(activitiesGoodsDOS)) {
            return false;
        }
        List<Long> ids = activitiesGoodsDOS.stream().map(TeboRankActivitiesGoodsDO::getId).collect(Collectors.toList());
        removeBatchByIds(ids);

        return true;
    }
}
