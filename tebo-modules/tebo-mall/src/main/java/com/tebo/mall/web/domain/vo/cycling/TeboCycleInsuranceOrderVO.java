package com.tebo.mall.web.domain.vo.cycling;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class TeboCycleInsuranceOrderVO implements Serializable {
   /**
    * 订单的实际拥有者
    */
   private String unionId;

   /**
    * 保单单订单编号
    */
   private String orderNo;

   /**
    * 骑行卡券码
    */
   private String couponUniqueCode;

   /**
    * 推送状态 1:待审核 2：退款 3：生效中
    */
   private Integer orderStatus;

   /**
    * 订单手机号
    */
   private String orderPhoneNumber;

   /**
    * 权益人手机号码
    */
   private String stakeHolderPhoneNumber;

   /**
    * 被保险人手机号
    */
   private String insurancePhoneNumber;

   /**
    * 被保险人姓名
    */
   private String insuranceName;

   /**
    * 被保险人身份证号码
    */
   private String insuranceIdCardNumber;

   /**
    * 服务协议地址
    */
   private String servicePdfUrl;

   /**
    * 服务协议地址图片
    */
   private String servicePdfImageUrl;
   /**
    * 服务开始时间
    */
   private String serviceStartTime;

   /**
    * 服务结束时间
    */
   private String serviceEndTime;

   /**
    * 创建时间
    */
   private LocalDateTime createTime;

}