package com.tebo.mall.mapper;

import com.tebo.mall.domain.dto.TeboCouponGiftOrderDTO;
import com.tebo.mall.entity.TeboMallOrderDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.mall.web.domain.dto.order.inner.TeboOrderGoodsUpdateDTO;
import com.tebo.mall.web.domain.dto.order.inner.TeboTenantOrderDTO;
import com.tebo.mall.web.domain.dto.order.outer.TeboMallOrderQueryInnerDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 商城订单
 */
@Mapper
public interface TeboMallOrderMapper extends TeboBaseMapper<TeboMallOrderDO> {

    /**
     * 获取订单列表
     * @param orderDTO
     * @return
     */
    List<TeboMallOrderDO> getOrderList(TeboMallOrderQueryInnerDTO orderDTO);
    /**
     * 通过订单编号获取订单信息
     * @param orderNo
     * @return
     */

    TeboMallOrderDO getByOrderNo(String orderNo);
    /**
     * 获取超时订单
     * @param gap
     * @return
     */
    List<TeboMallOrderDO> getPayTimeOutOrder(@Param("time") LocalDateTime time);

    /**
     * 更新订单状态
     * @param updateDTO
     */
    void updateMallOrderStatus(TeboOrderGoodsUpdateDTO updateDTO);

    /**
     * 查询延保订单
     * @param localDateTime
     * @return
     */
    List<TeboMallOrderDO> getExtendedWarrantyOrderOrder(LocalDateTime localDateTime);


    /**
     * 专供品订单列表
     * @param query
     * @return
     */
    List<TeboMallOrderDO> getMallOrderList(TeboTenantOrderDTO query);

    /**
     * 通过订单查询其使用的券的分享类型
     * @param orderId
     * @return
     */
    Integer selectOrderTypeByOrderId(@Param("orderId") Long orderId);

    /**
     * 更新卡券推广类型
     * @param orderId
     * @param phoneNumber
     * @param partnerNumber
     * @return
     */
    Integer updateGiftOrder(@Param("orderId") Long orderId, @Param("phoneNumber") String phoneNumber, @Param("partnerNumber") String partnerNumber);


    TeboCouponGiftOrderDTO selectGiftPackOrderByOrderId(@Param("orderId") Long orderId);

    /**
     * 通过商城订单号更新久久券状态为已占用
     * @param orderNo
     * @return
     */
    Integer updateCouponStatusForMerge(@Param("orderNo") String orderNo);

    /**
     * 通过商城订单号查询久久券
     * @param orderNo
     * @return
     */
    List<String> selectCouponByOrderNo(@Param("orderNo") String orderNo);
    /**
     * 通过商城订单号查询久久券订单号
     * @param orderNo
     * @return
     */
    String selectCouponOrderNo(@Param("orderNo") String orderNo);
}
