package com.tebo.mall.service.order;

import com.tebo.mall.api.domain.dto.TeboMallOrderDTO;
import com.tebo.mall.web.domain.dto.order.outer.TeboMallSpecialOrderAddOuterDTO;
import com.tebo.mall.web.domain.dto.order.TeboBatteryOrderRefundDTO;

import java.util.Map;

/**
 * 电池订单服务
 */
public interface TeboBatteryOrderService {

    /**
     * 创建直营商品订单（包含久久券）
     * @param addDTO
     * @return
     */
    Map<String, Object> createOrderWithCoupon(TeboMallSpecialOrderAddOuterDTO addDTO);


    /**
     * 商城直营订单支付成功后回调
     * @param mallOrder
     * @return
     */
    Boolean payBatteryOrderAfterNotify(TeboMallOrderDTO mallOrder);

    /**
     * 电池订单退款
     * @param refundDTO
     * @return
     */
    Boolean refundBatteryOrder(TeboBatteryOrderRefundDTO refundDTO);

}
