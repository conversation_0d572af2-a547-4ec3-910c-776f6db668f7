package com.tebo.mall.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tebo.common.core.constant.GiftPackUrlConstants;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.core.web.domain.AjaxResult;
import com.tebo.common.util.http.HttpTool;
import com.tebo.mall.api.domain.dto.*;
import com.tebo.mall.api.domain.view.JiFenOrderGoodsVO;
import com.tebo.mall.api.domain.view.JiFenOrderLogisticsDetailVO;
import com.tebo.mall.api.domain.view.JifenOrderLogisticsVO;
import com.tebo.mall.entity.TeboAddressDO;
import com.tebo.mall.manager.TeboAddressManger;
import com.tebo.mall.web.domain.dto.rewardsPoint.*;
import com.tebo.mall.web.domain.vo.cloudShop.*;
import com.tebo.rescue.api.RemoteCouponService;
import com.tebo.system.api.RemoteCustomerService;
import com.tebo.system.api.domain.view.WechatPrepayResponse;
import com.tebo.system.api.model.TeboConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
public class RewardsPointUtil {
    @Resource
    private RemoteCustomerService remoteCustomerService;
    @Resource
    private TeboAddressManger teboAddressManger;

    @Resource
    private RemoteCouponService remoteCouponService;

    @Value("${giftPack.cloudShopUrl}")
    private String url;

    @Value("${giftPack.storeId}")
    private String storeId;

    /**
     * 查询商品信息
     */
    public JiFenGoodsDataInfo goodsList(RewardsPointGoodsQueryDTO rewardsPointGoodsQueryDTO){
        String apiUrl = url + GiftPackUrlConstants.goodsList;//访问的接口地址
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
        List<String> storeIdList = new ArrayList<>();
        storeIdList.add(storeId);
        List<String> goodsTypeList = new ArrayList<>();
        goodsTypeList.add("2");
        List<SortItem> itemList = new ArrayList<>();
        SortItem item2 = new SortItem();
        SortItem item1 = new SortItem();
        item1.setField("recommendFlag");
        item1.setOrder(1);
        item2.setField("price");
        item2.setOrder(0);
        itemList.add(item1);
        itemList.add(item2);
        apiParams.put("storeId", storeIdList);
        apiParams.put("secondCateId", rewardsPointGoodsQueryDTO.getCateId());
        apiParams.put("goodsType", goodsTypeList);
        apiParams.put("pageSize", rewardsPointGoodsQueryDTO.getPageSize());
        apiParams.put("pageNum", rewardsPointGoodsQueryDTO.getPageNum());
        apiParams.put("sortItems", itemList);
        apiParams.put("hightLight", false);
        apiParams.put("queryString", rewardsPointGoodsQueryDTO.getKeyWord());
        Map<String,String> headers = new HashMap();
        headers.put("SystemChannel", "PC_BGSHOP");
        String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams), headers);
        if (StringUtils.isNotEmpty(resultJson)){
            JSONObject jsonObject = JSONObject.parseObject(resultJson);
            JSONObject data = (JSONObject) jsonObject.get("data");
            JiFenGoodsDataInfo jiFenGoodsDataInfo = JSONObject.parseObject(String.valueOf(data), JiFenGoodsDataInfo.class);
           return jiFenGoodsDataInfo;
        }
        return null;
    }

    /**
     * 创建积分订单
     */
    public Map<String,String> createOrder(RewardsPointOrderDTO orderDTO) {
        /**
         * 防止用户没有同步过去，再次同步用户
         */
        LoginDTO loginDTO = new LoginDTO();
        loginDTO.setUnionId(orderDTO.getUnionId());
        loginCloudShop(loginDTO);
        /**
         * 创建积分订单
         */
        String apiUrl = url + GiftPackUrlConstants.createOrder;//访问的接口地址
        TeboAddressDO addressDO = teboAddressManger.getAddressById(Long.parseLong(orderDTO.getAddressId()));
        log.info("createOrder param:{},result:{}",Long.parseLong(orderDTO.getAddressId()),addressDO == null? "空":JSONObject.toJSONString(addressDO));
        if (ObjectUtil.isEmpty(addressDO)){
            throw new ServiceException("收货地址不存在");
        }
        Map<String,String> map = new HashMap();
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
        List<Store> storeIdList = new ArrayList<>();
        Store store = new Store();
        store.setStoreId(storeId);
        storeIdList.add(store);
        List<String> goodsTypeList = new ArrayList<>();
        goodsTypeList.add("2");
        List<SortItem> itemList = new ArrayList<>();
        SortItem item = new SortItem();
        item.setField("goodsType");
        item.setOrder(0);
        itemList.add(item);
        apiParams.put("skuInfo", orderDTO.getSkuId()+",1");
        apiParams.put("secondSource","tbcx");
        apiParams.put("tnPointsOrder",true);
        if (StringUtils.isNotEmpty(orderDTO.getCouponCode())){
            apiParams.put("couponCode",orderDTO.getCouponCode());
        }
        if (ObjectUtil.isNotEmpty(orderDTO.getLotteryRecordId())){
            apiParams.put("lotteryRecordId",orderDTO.getLotteryRecordId());
        }
        if (ObjectUtil.isNotEmpty(orderDTO.getOrderNo())){
            apiParams.put("orderNo",orderDTO.getOrderNo());
        }
        apiParams.put("storeInfos",storeIdList);
        CustomerAddressDTO customerAddressDTO = new CustomerAddressDTO();
        customerAddressDTO.setName(addressDO.getConsigneeName());
        customerAddressDTO.setMobile(addressDO.getConsigneePhone());
        customerAddressDTO.setAddress(addressDO.getRegion());
        customerAddressDTO.setDetailAddress(addressDO.getRegion() + " "+addressDO.getHouseNumber()+" "+ addressDO.getDetailedAddress());
        apiParams.put("customerAddressDTO",customerAddressDTO);
        Map<String,String> headers = new HashMap();
        headers.put("SystemChannel", "PC_BGSHOP");
        headers.put("unionCode", orderDTO.getUnionId());
        String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams), headers);
        log.info("createOrder result:{},param:{}",resultJson,JSONObject.toJSONString(apiParams));
        if (StringUtils.isNotEmpty(resultJson)){
            JSONObject jsonObject = JSONObject.parseObject(resultJson);
            JSONObject data = (JSONObject) jsonObject.get("data");
            String result = data.getString("result");
            JSONArray jsonArray = (JSONArray)data.get("pointOrderInfos");
            if ("1".equals(result)){
                JSONObject successJson = (JSONObject)jsonArray.get(0);
                String wechatSecondSupplierId = data.getString("wechatSecondSupplierId");
                map.put("wechatSecondSupplierId",wechatSecondSupplierId);
                map.put("orderCode",successJson.getString("orderCode"));
                map.put("orderMoney",successJson.getString("orderMoney"));
                map.put("id",data.getString("id"));
                /**
                 * 将券设置为已使用
                 */
                if (StringUtils.isNotEmpty(orderDTO.getCouponCode())){
                    AjaxResult ajaxResult = remoteCouponService.writeOff(orderDTO.getCouponCode());
                    if (ajaxResult.isError()){
                        throw new ServiceException("卡券核销失败");
                    }
                }
            }else {
                String resultMsg = data.getString("resultMsg");
                throw new ServiceException(resultMsg);
            }
        }
        return map;
    }


    /**
     * 创建积分订单
     */
    public Map<String,String> newCreateOrder(RewardsPointOrderDTO orderDTO) {
        /**
         * 防止用户没有同步过去，再次同步用户
         */
        LoginDTO loginDTO = new LoginDTO();
        loginDTO.setUnionId(orderDTO.getUnionId());
        loginCloudShop(loginDTO);
        /**
         * 创建积分订单
         */
        String apiUrl = url + GiftPackUrlConstants.newCreateOrder;//访问的接口地址
        TeboAddressDO addressDO = teboAddressManger.getAddressById(Long.parseLong(orderDTO.getAddressId()));
        log.info("createOrder param:{},result:{}",Long.parseLong(orderDTO.getAddressId()),addressDO == null? "空":JSONObject.toJSONString(addressDO));
        if (ObjectUtil.isEmpty(addressDO)){
            throw new ServiceException("收货地址不存在");
        }
        Map<String,String> map = new HashMap();
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
        List<Store> storeIdList = new ArrayList<>();
        Store store = new Store();
        store.setStoreId(storeId);
        storeIdList.add(store);
        List<String> goodsTypeList = new ArrayList<>();
        goodsTypeList.add("2");
        List<SortItem> itemList = new ArrayList<>();
        SortItem item = new SortItem();
        item.setField("goodsType");
        item.setOrder(0);
        itemList.add(item);
        apiParams.put("skuInfo", orderDTO.getSkuId()+",1");
        apiParams.put("secondSource","tbcx");
        apiParams.put("tnPointsOrder",true);
        if (StringUtils.isNotEmpty(orderDTO.getCouponCode())){
            apiParams.put("couponCode",orderDTO.getCouponCode());
        }
        if (StringUtils.isNotEmpty(orderDTO.getTbcxCouponCode())){
            apiParams.put("tbcxCouponCode",orderDTO.getTbcxCouponCode());
        }
        if (ObjectUtil.isNotEmpty(orderDTO.getLotteryRecordId())){
            apiParams.put("lotteryRecordId",orderDTO.getLotteryRecordId());
        }
        if (ObjectUtil.isNotEmpty(orderDTO.getOrderNo())){
            apiParams.put("orderNo",orderDTO.getOrderNo());
        }
        apiParams.put("storeInfos",storeIdList);
        CustomerAddressDTO customerAddressDTO = new CustomerAddressDTO();
        customerAddressDTO.setName(addressDO.getConsigneeName());
        customerAddressDTO.setMobile(addressDO.getConsigneePhone());
        customerAddressDTO.setAddress(addressDO.getRegion());
        customerAddressDTO.setDetailAddress(addressDO.getRegion() + " "+addressDO.getHouseNumber()+" "+ addressDO.getDetailedAddress());
        apiParams.put("customerAddressDTO",customerAddressDTO);
        Map<String,String> headers = new HashMap();
        headers.put("SystemChannel", "PC_BGSHOP");
        headers.put("unionCode", orderDTO.getUnionId());
        String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams), headers);
        log.info("createOrder result:{},param:{}",resultJson,JSONObject.toJSONString(apiParams));
        if (StringUtils.isNotEmpty(resultJson)){
            JSONObject jsonObject = JSONObject.parseObject(resultJson);
            JSONObject data = (JSONObject) jsonObject.get("data");
            String result = data.getString("result");
            JSONArray jsonArray = (JSONArray)data.get("pointOrderInfos");
            if ("1".equals(result)){
                JSONObject successJson = (JSONObject)jsonArray.get(0);
                String wechatSecondSupplierId = data.getString("wechatSecondSupplierId");
                map.put("wechatSecondSupplierId",wechatSecondSupplierId);
                map.put("orderCode",successJson.getString("orderCode"));
                map.put("orderMoney",successJson.getString("orderMoney"));
                map.put("id",data.getString("id"));
                /**
                 * 将券设置为已使用
                 */
                if (StringUtils.isNotEmpty(orderDTO.getCouponCode())){
                    AjaxResult ajaxResult = remoteCouponService.writeOff(orderDTO.getCouponCode());
                    if (ajaxResult.isError()){
                        throw new ServiceException("卡券核销失败");
                    }
                }
            }else {
                String resultMsg = data.getString("resultMsg");
                throw new ServiceException(resultMsg);
            }
        }
        return map;
    }
    /**
     * 查询积分订单
     */
    public JiFenOrderDataInfo getOrderList(RewardsPointOrderQueryDTO queryDTO) {
        String apiUrl = url + GiftPackUrlConstants.getNewOrderList+"?storeId="+storeId+"&storeOrderType=4"+"&pageNum="+queryDTO.getPageNum()+"&pageSize="+queryDTO.getPageSize()+"&status="+queryDTO.getStatus();//访问的接口地址
        String resultJson = HttpTool.sendGetWithHeader(apiUrl,queryDTO.getUnionId());
        JiFenOrderDataInfo jiFenOrderDataInfo = new JiFenOrderDataInfo();
        List<JiFenOrderVO> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(resultJson)){
            JSONObject jsonObject = JSONObject.parseObject(resultJson);
            JSONObject data = (JSONObject) jsonObject.get("data");
            Integer totalPage = (Integer) data.get("recordsTotal");
            jiFenOrderDataInfo.setTotalPage(totalPage);
            JSONArray jsonArray = (JSONArray) data.get("data");
            for (int i = 0;i < jsonArray.size();i++){
              JiFenOrderVO jiFenOrderVO = new JiFenOrderVO();
              JSONObject json = (JSONObject)jsonArray.get(i);
                /**
                 * 订单编号
                 */
              jiFenOrderVO.setOrderNo(json.getString("orderCode"));
              jiFenOrderVO.setId(json.getString("id"));
              jiFenOrderVO.setCreateTime(json.getString("createTime"));
              jiFenOrderVO.setStatus(json.getString("status"));
              jiFenOrderVO.setGivePoints(json.getString("givePoints"));
              jiFenOrderVO.setWechatSecondSupplierId(json.getString("wechatSecondSupplierId"));
              JSONArray jsonArray1 = (JSONArray)json.get("orderSkus");
              JSONObject goods = (JSONObject)jsonArray1.get(0);
              JiFenOrderGoodsVO goodsVO = JSONObject.parseObject(String.valueOf(goods), JiFenOrderGoodsVO.class);
              goodsVO.setIntegralValue(json.getString("integralValue"));
                /**
                 * 订单商品
                 */
              jiFenOrderVO.setJiFenOrderGoodsVO(goodsVO);
              list.add(jiFenOrderVO);
            }
            jiFenOrderDataInfo.setData(list);
            return jiFenOrderDataInfo;
        }
         return null;
    }

    /**
     * 增加积分  {
     * "khbhoooo": "123456", //客户编号
     * "khmcoooo": "zhangsan", //客户名称
     * "zjdzzooo": 100, //变动值
     * "khzhlxoo": "", //权益类型
     * "xtbsoooo": "TBCX_XCX" //系统标识
     *
     */
    public boolean addUserIntegral(TeboIntegralDTO userIntegralDTO) {
        String apiUrl = url + GiftPackUrlConstants.addUserIntegral;//访问的接口地址
        R<TeboConsumer> consumerResult = remoteCustomerService.selectByUnionId(userIntegralDTO.getUnionId());
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
        apiParams.put("khbhoooo", userIntegralDTO.getUnionId());
        apiParams.put("khmcoooo", consumerResult.getData().getPhoneNumber());
        apiParams.put("zjdzzooo", userIntegralDTO.getIntegral());
        apiParams.put("khzhlxoo", "JF");
        apiParams.put("xtbsoooo", "TBCX_XCX");
        apiParams.put("tbcxOrderId", userIntegralDTO.getId());
        Map<String, String> headers = new HashMap();
        headers.put("SystemChannel", "PC_BGSHOP");
        String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams), headers);
        log.info("addUserIntegral result :{},param:{}",resultJson,JSONObject.toJSONString(apiParams));
        if (StringUtils.isNotEmpty(resultJson)) {
            JSONObject jsonObject = JSONObject.parseObject(resultJson);
            Integer code = (Integer) jsonObject.get("code");
            if (200 == code) {
                return true;
            }else {
                throw new ServiceException(jsonObject.getString("msg"));
            }
        }
        return false;
    }
    /**
     * 团购订单减积分
     */
    public boolean reduceGroupOrderIntegral(TeboIntegralDTO userIntegralDTO) {
        String apiUrl = url + GiftPackUrlConstants.groupOrderReduceIntegral;//访问的接口地址
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
        apiParams.put("khbhoooo", userIntegralDTO.getUnionId());
        apiParams.put("zjdzzooo", userIntegralDTO.getIntegral());
        apiParams.put("khzhlxoo", "JF");
        apiParams.put("xtbsoooo", "TBCX_XCX");
        apiParams.put("groupBuyOrderId", userIntegralDTO.getGroupBuyOrderId());
        Map<String, String> headers = new HashMap();
        headers.put("SystemChannel", "PC_BGSHOP");
        String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams), headers);
        log.info("reduceUserIntegral result :{},param:{}",resultJson,JSONObject.toJSONString(apiParams));
        if (StringUtils.isNotEmpty(resultJson)) {
            JSONObject jsonObject = JSONObject.parseObject(resultJson);
            Integer code = (Integer) jsonObject.get("code");
            if (200 == code) {
                return true;
            }else {
                throw new ServiceException(jsonObject.getString("msg"));
            }
        }
        return false;
    }

    /**
     * 退款减积分
     * @param userIntegralDTO
     * @return
     */
    public boolean reduceUserIntegral(TeboIntegralDTO userIntegralDTO) {
        String apiUrl = url + GiftPackUrlConstants.reduceUserIntegral;//访问的接口地址
        R<TeboConsumer> consumerResult = remoteCustomerService.selectByUnionId(userIntegralDTO.getUnionId());
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
        apiParams.put("khbhoooo", userIntegralDTO.getUnionId());
        apiParams.put("khmcoooo", consumerResult.getData().getPhoneNumber());
        apiParams.put("zjdzzooo", userIntegralDTO.getIntegral());
        apiParams.put("khzhlxoo", "JF");
        apiParams.put("xtbsoooo", "TBCX_XCX");
        apiParams.put("tbcxOrderId", userIntegralDTO.getId());
        Map<String, String> headers = new HashMap();
        headers.put("SystemChannel", "PC_BGSHOP");
        String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams), headers);
        log.info("reduceUserIntegral result :{},param:{}",resultJson,JSONObject.toJSONString(apiParams));
        if (StringUtils.isNotEmpty(resultJson)) {
            JSONObject jsonObject = JSONObject.parseObject(resultJson);
            Integer code = (Integer) jsonObject.get("code");
            if (200 == code) {
                return true;
            }else {
                throw new ServiceException(jsonObject.getString("msg"));
            }
        }
        return false;
    }


    /**
     * 查询用户积分
     */
    public CloudShopIntegralVo getUserIntegral(TeboCloudShopUserIntegralQueryDTO queryDTO){
        String apiUrl = url + GiftPackUrlConstants.getUserIntegral;//访问的接口地址
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
        apiParams.put("qylxszoo", "JF");
        if (queryDTO.getSource() == 1){
         apiParams.put("khbhoooo", queryDTO.getUnionId());
        }else {
         apiParams.put("phone",queryDTO.getPhoneNumber());
        }
        Map<String, String> headers = new HashMap();
        String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams), headers);
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        Integer code = (Integer) jsonObject.get("code");
        CloudShopIntegralVo cloudShopIntegralVO = new CloudShopIntegralVo();
        if (200 == code) {
            JSONArray array = (JSONArray) jsonObject.get("data");
            if (array.size() > 0){
                JSONObject json = (JSONObject)array.get(0);
                Integer jifen = (Integer) json.get("syedoooo");
                cloudShopIntegralVO.setIntegral(jifen);
                return cloudShopIntegralVO;
            }
        }
        return cloudShopIntegralVO;
    }

    /**
     * 积分流水
     * @param {
     * "khbhoooo": "123456", //客户编号
     * "khzhlxoo": "JF", //权益类型
     * "zjldlxoo": "", //权益变动类型
     * "pageNum": 1, //页码
     * "pageSize": 10 //条数
     * }
     */
    public JiFenDataInfoVO getUserIntegralRecordList(TeboUserIntegralQueryDTO queryDTO) {
        String apiUrl = url + GiftPackUrlConstants.queryRightChangeDetailList;//访问的接口地址
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
        apiParams.put("khbhoooo", queryDTO.getUnionId());
        apiParams.put("khzhlxoo", "JF");
        apiParams.put("pageNum",queryDTO.getPageNum());
        apiParams.put("pageSize", queryDTO.getPageSize());
        Map<String, String> headers = new HashMap();
        headers.put("SystemChannel", "PC_BGSHOP");
        String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams), headers);
        if (StringUtils.isNotEmpty(resultJson)) {
            JSONObject jsonObject = JSONObject.parseObject(resultJson);
            Integer code = (Integer) jsonObject.get("code");
            if (200 == code) {
                JSONObject jsonObject1 = JSONObject.parseObject(resultJson);
                JSONObject data = (JSONObject) jsonObject1.get("data");
                JiFenDataInfoVO jiFenDataInfoVO = JSONObject.parseObject(String.valueOf(data), JiFenDataInfoVO.class);
                return jiFenDataInfoVO;
            }
        }
        return null;
    }

    public boolean loginCloudShop(LoginDTO loginDTO){
        String apiUrl = url + GiftPackUrlConstants.login;//访问的接口地址
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
        apiParams.put("mobile",loginDTO.getMobile());
        apiParams.put("userName",loginDTO.getUnionId());
        Map<String, String> headers = new HashMap();
        headers.put("SystemChannel", "PC_BGSHOP");
        String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams), headers);
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        String code = (String) jsonObject.get("code");
        if ("200".equals(code)) {
            return true;
        }
        return false;
    }

    /**
     * spu商品详情
     */
    public JiFenGoodsDetailVO goodsSpuDetail(String skuId,String unionId) {
        String apiUrl = String.format(url + GiftPackUrlConstants.goodsDetail,skuId);//访问的接口地址
        String resultJson = HttpTool.sendGetWithHeader(apiUrl,unionId);
        JiFenGoodsDetailVO jiFenGoodsDetailVO = JSONObject.parseObject(resultJson, JiFenGoodsDetailVO.class);
        if (ObjectUtil.isNotEmpty(jiFenGoodsDetailVO)){
            jiFenGoodsDetailVO.setPreDiscountAmount(jiFenGoodsDetailVO.getCash());
        }
        return jiFenGoodsDetailVO;
    }

    /**
     * spu商品规格
     */
    public List<GoodsSpecVO> getGoodsSpuSpecs(String spuId,String unionId) {
        String apiUrl = String.format(url + GiftPackUrlConstants.goodsSpecs,spuId);//访问的接口地址
        String resultJson = HttpTool.sendGetWithHeader(apiUrl,unionId);
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        JSONArray jsonArray = (JSONArray)jsonObject.get("data");
        List<GoodsSpecVO> specList = JSONArray.parseArray(JSONObject.toJSONString(jsonArray),GoodsSpecVO.class);
        return specList ;
    }
    /**
     * sku商品详情
     */
    public JiFenGoodsDetailVO goodsSkuDetail(String skuId, String unionId) {
        String apiUrl = String.format(url + GiftPackUrlConstants.goodsSkuDetail,skuId);
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
        apiParams.put("id",skuId);
        Map<String, String> headers = new HashMap();
        headers.put("SystemChannel", "PC_BGSHOP");
        headers.put("unionCode", unionId);
        //访问的接口地址
        String resultJson =  HttpTool.sendPost(apiUrl,JSONObject.toJSONString(apiParams),headers);
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        JSONObject result = (JSONObject)jsonObject.get("data");
        JiFenGoodsDetailVO jiFenGoodsDetailVO = JSONObject.parseObject(JSONObject.toJSONString(result), JiFenGoodsDetailVO.class);
        return jiFenGoodsDetailVO;
    }

    /**
     * 订单详情
     */
    public JiFenOrderVO getOrderDetail(String orderId,String unionId) {
        String apiUrl = String.format(url + GiftPackUrlConstants.getOrderDetail,orderId);
        //访问的接口地址
        String resultJson =  HttpTool.sendGetWithHeader(apiUrl,unionId);
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        JiFenOrderVO jiFenOrderVO = new JiFenOrderVO();
        jiFenOrderVO.setOrderNo(jsonObject.getString("orderCode"));
        jiFenOrderVO.setId(jsonObject.getString("id"));
        jiFenOrderVO.setCreateTime(jsonObject.getString("createTime"));
        jiFenOrderVO.setPayTime(jsonObject.getString("payTime"));
        jiFenOrderVO.setStatus(jsonObject.getString("status"));
        jiFenOrderVO.setCouponPrice(jsonObject.getString("couponPrice"));
        jiFenOrderVO.setWechatSecondSupplierId(jsonObject.getString("wechatSecondSupplierId"));
        JSONArray skuArray = (JSONArray)jsonObject.get("orderSkus");
        JSONObject goods = (JSONObject)skuArray.get(0);
        JiFenOrderGoodsVO goodsVO = JSONObject.parseObject(String.valueOf(goods), JiFenOrderGoodsVO.class);
        goodsVO.setIntegralValue(jsonObject.getString("integralValue"));
        /**
         * 订单商品
         */
        jiFenOrderVO.setJiFenOrderGoodsVO(goodsVO);
        /**
         * 物流信息
         */
        JifenOrderLogisticsVO jifenOrderLogisticsVO = new JifenOrderLogisticsVO();
        jiFenOrderVO.setJifenOrderLogisticsVO(jifenOrderLogisticsVO);
        /**
         * 收货人信息
         */
        JSONObject orderAttr = (JSONObject)jsonObject.get("orderAttr");
        if (orderAttr != null){
            jifenOrderLogisticsVO.setReceiptName(orderAttr.getString("receiptName"));
            jifenOrderLogisticsVO.setReceiptMobile(orderAttr.getString("receiptMobile"));
            jifenOrderLogisticsVO.setReceiptAddress(orderAttr.getString("receiptAddress"));
            jifenOrderLogisticsVO.setReceiptDetailAddress(orderAttr.getString("receiptDetailAddress"));
        }
        JSONArray logisticsInfoList = (JSONArray)jsonObject.get("logisticsInfoList");
        if (logisticsInfoList != null && logisticsInfoList.size() >0){
            JSONObject logisticsInfo = (JSONObject)logisticsInfoList.get(0);
            jifenOrderLogisticsVO.setWaybillCode(logisticsInfo.getString("waybillCode"));
            jifenOrderLogisticsVO.setLogisticsCompany(logisticsInfo.getString("logisticsCompany"));
        }
        JSONObject queryTrackResp = (JSONObject)jsonObject.get("queryTrackResp");
        if (queryTrackResp != null){
            JSONArray jsonArrayWuLiu = (JSONArray)queryTrackResp.get("data");
            List<JiFenOrderLogisticsDetailVO> jiFenOrderLogisticsDetailVOList = JSONArray.parseArray(String.valueOf(jsonArrayWuLiu), JiFenOrderLogisticsDetailVO.class);
            jifenOrderLogisticsVO.setLogisticsDetailList(jiFenOrderLogisticsDetailVOList);
            jiFenOrderVO.setJifenOrderLogisticsVO(jifenOrderLogisticsVO);
        }
        return jiFenOrderVO;
    }

    /**
     * 商品分类
     */
    public List<JiFenCategoryVO> goodsCategory(String unionId) {
        String apiUrl = String.format(url + GiftPackUrlConstants.goodsCategory,storeId);
        Map<String, String> headers = new HashMap();
        headers.put("SystemChannel", "PC_BGSHOP");
        headers.put("unionCode", unionId);
        //访问的接口地址
        String resultJson =  HttpTool.sendGetWithHeader(apiUrl,unionId);
        List<JiFenCategoryVO> jiFenOrderLogisticsDetailVOList = JSONArray.parseArray(resultJson, JiFenCategoryVO.class);
        return jiFenOrderLogisticsDetailVOList;
    }

    /**
     * 收货
     */
    public ReceiveGoodsV0 receiveGoods(String orderId,String unionId) {
        String apiUrl = String.format(url + GiftPackUrlConstants.receiveGoods+"?orderId="+orderId);
        Map<String, String> headers = new HashMap();
        headers.put("SystemChannel", "PC_BGSHOP");
        headers.put("unionCode", unionId);
        //访问的接口地址
        String resultJson =  HttpTool.sendGetWithHeader(apiUrl,unionId);
        log.info("receiveGoods result :{},param:{}",resultJson,apiUrl);
        ReceiveGoodsV0 receiveGoodsV0 = new ReceiveGoodsV0();
        receiveGoodsV0.setReceiveGoods(false);
        if (StringUtils.isEmpty(resultJson)){
            return receiveGoodsV0;
        }
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        String code = jsonObject.getString("code");
        if ("200".equals(code)){
            receiveGoodsV0.setReceiveGoods(true);
        }else {
            throw new ServiceException(jsonObject.getString("message"));
        }
        return receiveGoodsV0;
    }

    /**
     * 给非会员增加积分鼓励消费
     * @param userIntegralDTO
     * @return
     */
    public boolean addNonVipUserIntegral(TeboIntegralDTO userIntegralDTO, String activityFrom) {
        String apiUrl = url + GiftPackUrlConstants.addNonVipUserIntegral;//访问的接口地址
        R<TeboConsumer> consumerResult = remoteCustomerService.selectByUnionId(userIntegralDTO.getUnionId());
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
        apiParams.put("khbhoooo", userIntegralDTO.getUnionId());
        apiParams.put("khmcoooo", consumerResult.getData().getPhoneNumber());
        apiParams.put("zjdzzooo", userIntegralDTO.getIntegral());
        apiParams.put("khzhlxoo", "JF");
        apiParams.put("xtbsoooo", "TBCX_XCX");
        // 久久会员日抽奖积分需要传活动来源，云门店需要特殊处理
        if (StrUtil.isNotBlank(activityFrom)) {
            apiParams.put("lotteryId", userIntegralDTO.getId()); // 领奖记录id
            apiParams.put("hdlyoooo", activityFrom); // HDZJ
        }
        Map<String, String> headers = new HashMap();
        headers.put("SystemChannel", "PC_BGSHOP");
        String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams), headers);
        log.info("addNonVipUserIntegral result :{},param:{}",resultJson,JSONObject.toJSONString(apiParams));
        if (StringUtils.isNotEmpty(resultJson)) {
            JSONObject jsonObject = JSONObject.parseObject(resultJson);
            Integer code = (Integer) jsonObject.get("code");
            if (200 == code) {
                return true;
            }else {
                throw new ServiceException(jsonObject.getString("msg"));
            }
        }
        return false;
    }

    /**
     * 超服店分佣
     */
    public boolean saleCardAmount(CouponCommissionShareDTO saleCardAmount) {
        String apiUrl = url + GiftPackUrlConstants.saleCardAmount;//访问的接口地址
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
        apiParams.put("terminalnodeId",saleCardAmount.getCloudShopId());
        apiParams.put("saleCardOrderCode",saleCardAmount.getOrderNo());
        apiParams.put("saleCardOrderId",saleCardAmount.getOrderId());
        apiParams.put("cardUserId",saleCardAmount.getUnionId());
        apiParams.put("cardPhone",saleCardAmount.getPhoneNumber());
        apiParams.put("cardTypeId",saleCardAmount.getPacketId());
        Map<String, String> headers = new HashMap();
        headers.put("SystemChannel", "PC_BGSHOP");
        String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams), headers);
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        String code = (String) jsonObject.get("code");
        log.info("saleCardAmount result:{},param:{}",resultJson,JSONObject.toJSONString(apiParams));
        if ("200".equals(code)) {
            return true;
        }
        return false;
    }

    /**
     * 解冻超服店分佣
     */
    public boolean thawSaleCardAmount(CouponCommissionShareDTO saleCardAmount) {
        String apiUrl = url + GiftPackUrlConstants.thawSaleCardAmount;//访问的接口地址
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
        apiParams.put("saleCardOrderId",saleCardAmount.getOrderId());
        apiParams.put("saleCardOrderCode",saleCardAmount.getOrderNo());
        Map<String, String> headers = new HashMap();
        headers.put("SystemChannel", "PC_BGSHOP");
        String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams), headers);
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        String code = (String) jsonObject.get("code");
        log.info("thawSaleCardAmount result:{},param:{}",resultJson,JSONObject.toJSONString(apiParams));
        if ("200".equals(code)) {
            return true;
        }
        return false;
    }

    /**
     * 根据久久卡订单号查询大米订单信息
     * @param orderNo
     * @return
     */
    public JiFenOrderVO getOrderByOrderNo(String orderNo,String unionId) {
        String apiUrl = String.format(url + GiftPackUrlConstants.getOrderByOrderNo,orderNo);
        //访问的接口地址
        String resultJson =  HttpTool.sendGetWithHeader(apiUrl,unionId);
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        JiFenOrderVO jiFenOrderVO = new JiFenOrderVO();
        jiFenOrderVO.setOrderNo(jsonObject.getString("orderCode"));
        jiFenOrderVO.setId(jsonObject.getString("id"));
        jiFenOrderVO.setCreateTime(jsonObject.getString("createTime"));
        jiFenOrderVO.setPayTime(jsonObject.getString("payTime"));
        jiFenOrderVO.setStatus(jsonObject.getString("status"));
        jiFenOrderVO.setWechatSecondSupplierId(jsonObject.getString("wechatSecondSupplierId"));
        JSONArray skuArray = (JSONArray)jsonObject.get("orderSkus");
        JSONObject goods = (JSONObject)skuArray.get(0);
        JiFenOrderGoodsVO goodsVO = JSONObject.parseObject(String.valueOf(goods), JiFenOrderGoodsVO.class);
        goodsVO.setIntegralValue(jsonObject.getString("integralValue"));
        /**
         * 订单商品
         */
        jiFenOrderVO.setJiFenOrderGoodsVO(goodsVO);
        /**
         * 物流信息
         */
        JifenOrderLogisticsVO jifenOrderLogisticsVO = new JifenOrderLogisticsVO();
        jiFenOrderVO.setJifenOrderLogisticsVO(jifenOrderLogisticsVO);
        /**
         * 收货人信息
         */
        JSONObject orderAttr = (JSONObject)jsonObject.get("orderAttr");
        if (orderAttr != null){
            jifenOrderLogisticsVO.setReceiptName(orderAttr.getString("receiptName"));
            jifenOrderLogisticsVO.setReceiptMobile(orderAttr.getString("receiptMobile"));
            jifenOrderLogisticsVO.setReceiptAddress(orderAttr.getString("receiptAddress"));
            jifenOrderLogisticsVO.setReceiptDetailAddress(orderAttr.getString("receiptDetailAddress"));
        }
        JSONArray logisticsInfoList = (JSONArray)jsonObject.get("logisticsInfoList");
        if (logisticsInfoList != null && logisticsInfoList.size() >0){
            JSONObject logisticsInfo = (JSONObject)logisticsInfoList.get(0);
            jifenOrderLogisticsVO.setWaybillCode(logisticsInfo.getString("waybillCode"));
            jifenOrderLogisticsVO.setLogisticsCompany(logisticsInfo.getString("logisticsCompany"));
        }
        JSONObject queryTrackResp = (JSONObject)jsonObject.get("queryTrackResp");
        if (queryTrackResp != null){
            JSONArray jsonArrayWuLiu = (JSONArray)queryTrackResp.get("data");
            List<JiFenOrderLogisticsDetailVO> jiFenOrderLogisticsDetailVOList = JSONArray.parseArray(String.valueOf(jsonArrayWuLiu), JiFenOrderLogisticsDetailVO.class);
            jifenOrderLogisticsVO.setLogisticsDetailList(jiFenOrderLogisticsDetailVOList);
            jiFenOrderVO.setJifenOrderLogisticsVO(jifenOrderLogisticsVO);
        }
        return jiFenOrderVO;
    }

    /**
     * 云门店电池核销
     * @param batteryVerificationDTO
     */
    public boolean batteryVerification(TeboBatteryVerificationDTO batteryVerificationDTO) {
        String apiUrl = String.format(url + GiftPackUrlConstants.batteryVerification);
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
        apiParams.put("cloudShopId",batteryVerificationDTO.getCloudShopId());
        apiParams.put("accountId",batteryVerificationDTO.getAccountId());
        apiParams.put("phoneNumber",batteryVerificationDTO.getPhoneNumber());
        apiParams.put("couponCode",batteryVerificationDTO.getCouponCode());
        apiParams.put("batteryCode",batteryVerificationDTO.getBatteryCode());
        Map<String, String> headers = new HashMap();
        headers.put("SystemChannel", "PC_BGSHOP");
        String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams), headers);
        log.info("batteryVerification result:{},param:{}",resultJson,JSONObject.toJSONString(apiParams));
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        Integer code = (Integer) jsonObject.get("code");
        if (code == 200) {
            return true;
        }else {
            throw new ServiceException(jsonObject.getString("msg"));
        }
    }

    /**
     * 专供品抵用金
     */
    public boolean placeOrderVerified(TeboSpecialGoodsVerificationDTO dto) {
        String apiUrl = String.format(url + GiftPackUrlConstants.placeOrderVerified);
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
        apiParams.put("cloudShopId",dto.getCloudShopId());
        apiParams.put("accountId",dto.getAccountId());
        apiParams.put("phoneNumber",dto.getPhoneNumber());
        apiParams.put("couponCode",dto.getCouponCode());
        apiParams.put("orderCode",dto.getOrderCode());
        Map<String, String> headers = new HashMap();
//        headers.put("SystemChannel", "PC_BGSHOP");
        String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams), headers);
        log.info("placeOrderVerified result:{},param:{}",resultJson,JSONObject.toJSONString(apiParams));
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        Integer code = (Integer) jsonObject.get("code");
        if (code == 200) {
            return true;
        }else {
            throw new ServiceException(jsonObject.getString("msg"));
        }
    }
    /**
     * 专供品抵用金
     */
    public boolean thawSaleCardAmountOrder(String orderNo) {
        String apiUrl = String.format(url + GiftPackUrlConstants.thawSaleCardAmountOrder);
        String resultJson = HttpTool.sendGet(apiUrl + orderNo);
        log.info("placeOrderVerified result:{},param:{}",resultJson,orderNo);
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        if ("200".equals(jsonObject.get("code"))) {
            return true;
        } else {
            throw new ServiceException(jsonObject.getString("msg"));
        }
    }

    /**
     * 汇付支付
     */
    public WechatPrepayResponse huiFuPayOrder(HuiFuOrderDTO huiFuOrderDTO) {
        String apiUrl = url + GiftPackUrlConstants.huiFuPayOrder;//访问的接口地址
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
        apiParams.put("orderId",huiFuOrderDTO.getOrderId());
        apiParams.put("subOpenid",huiFuOrderDTO.getSubOpenid());
        apiParams.put("unionId",huiFuOrderDTO.getUnionId());
        Map<String, String> headers = new HashMap();
        headers.put("SystemChannel", "PC_BGSHOP");
        String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams), headers);
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        String code = (String) jsonObject.get("code");
        log.info("huiFuPayOrder result:{},param:{}",resultJson,JSONObject.toJSONString(apiParams));
        JSONObject result = (JSONObject)jsonObject.get("data");
        WechatPrepayResponse wechatPrepayResponse = JSONObject.parseObject(JSONObject.toJSONString(result), WechatPrepayResponse.class);
        if ("R-00000".equals(code)) {
            wechatPrepayResponse.setUniqueId(System.currentTimeMillis());
            wechatPrepayResponse.setPackageVal(wechatPrepayResponse.getPackageValue());
            return wechatPrepayResponse;
        }else {
            throw new ServiceException("支付异常");
        }
    }


    /**
     * 云门店电池核销
     * @param
     */
    public void drawList(TeboShopCouponDTO shopCouponDTO) {
        String apiUrl = String.format(url + GiftPackUrlConstants.batteryVerification);
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
    //    apiParams.put("cloudShopId",batteryVerificationDTO.getCloudShopId());
    //    apiParams.put("accountId",batteryVerificationDTO.getAccountId());
    //    apiParams.put("phoneNumber",batteryVerificationDTO.getPhoneNumber());
     //   apiParams.put("couponCode",batteryVerificationDTO.getCouponCode());
    //    apiParams.put("batteryCode",batteryVerificationDTO.getBatteryCode());
        Map<String, String> headers = new HashMap();
        headers.put("SystemChannel", "PC_BGSHOP");
        String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams), headers);
        log.info("batteryVerification result:{},param:{}",resultJson,JSONObject.toJSONString(apiParams));
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        Integer code = (Integer) jsonObject.get("code");

    }

    /**
     * 校验是否是玖玖电池
     * @param
     */
    public void checkBatteryCode(String batteryCode) {
        String apiUrl = String.format(url + "/prod-api/market/scene/tnShop/redPacket/specialVerified");
        Map<String, Object> apiParams = new TreeMap<String, Object>();//接口参数
        apiParams.put("batteryCode", batteryCode);
        String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams));
        log.info("getBatterycodeInfo batteryCode {} resultJson:{}", batteryCode, resultJson);
        JSONObject jsonObject = JSONObject.parseObject(resultJson);
        Integer code = (Integer) jsonObject.get("code");
        if (200 != code) {
            throw new ServiceException(jsonObject.getString("msg"));
        }
    }
    public static void main(String[] args) {
    }
}