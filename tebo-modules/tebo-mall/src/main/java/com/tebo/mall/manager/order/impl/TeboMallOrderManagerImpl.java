package com.tebo.mall.manager.order.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.security.util.MaintainerOnlineUserUtil;
import com.tebo.common.util.DataUtils;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.mall.api.domain.view.TeboGoodsVO;
import com.tebo.mall.domain.TeboOrderGoodsCategoryGroup;
import com.tebo.mall.domain.TeboOrderGoodsCodeGroup;
import com.tebo.mall.entity.TeboMallOrderDO;
import com.tebo.mall.enums.MallOrderStatusEnum;
import com.tebo.mall.manager.goods.TeboGoodsManager;
import com.tebo.mall.manager.order.TeboMallOrderGoodsManager;
import com.tebo.mall.manager.order.TeboMallOrderManager;
import com.tebo.mall.mapper.TeboMallOrderMapper;
import com.tebo.mall.service.mq.DisMqService;
import com.tebo.mall.web.domain.dto.goods.TeboGoodsQueryDTO;
import com.tebo.mall.web.domain.dto.order.inner.TeboMallOrderAddDTO;
import com.tebo.mall.web.domain.dto.order.inner.TeboOrderGoodsUpdateDTO;
import com.tebo.mall.web.domain.dto.order.inner.TeboTenantOrderDTO;
import com.tebo.mall.web.domain.dto.order.outer.TeboMallOrderQueryInnerDTO;
import com.tebo.mall.web.domain.vo.WriteOffOrderResultVO;
import com.tebo.mall.web.domain.vo.order.TeboMallOrderGoodsVO;
import com.tebo.mall.web.domain.vo.order.TeboMallOrderVO;
import com.tebo.rescue.api.RemoteQueueOrderService;
import com.tebo.rescue.api.domain.dto.ServiceOrderQueryDTO;
import com.tebo.rescue.api.domain.dto.TeboOrderGoodsIdGroup;
import com.tebo.system.api.RemoteAccountService;
import com.tebo.system.api.model.TeboAccountInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TeboMallOrderManagerImpl implements TeboMallOrderManager {
    @Resource
    private TeboMallOrderMapper orderMapper;
    @Autowired
    private TeboMallOrderGoodsManager mallOrderGoodsManager;

    @Autowired
    private RemoteAccountService remoteAccountService;

    @Autowired
    private DisMqService disMqService;

    @Autowired
    private RemoteQueueOrderService remoteQueueOrderService;

    @Autowired
    private TeboGoodsManager teboGoodsManager;


    @Override
    public List<TeboMallOrderVO> getOrderList(TeboMallOrderQueryInnerDTO orderDTO) {
        List<TeboMallOrderDO> list = orderMapper.getOrderList(orderDTO);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        return BeanConvert.copyList(list, TeboMallOrderVO::new);
    }

    @Override
    public int createOrder(TeboMallOrderAddDTO addDTO) {
        TeboMallOrderDO teboMallOrderDO = new TeboMallOrderDO();
        BeanConvert.copy(addDTO, teboMallOrderDO);
        if (ObjectUtil.isNotEmpty(addDTO.getOrderAmount())) {
            teboMallOrderDO.setOrderAmount(Integer.parseInt(addDTO.getOrderAmount()));
        }
        int insert = orderMapper.insert(teboMallOrderDO);

        return insert;
    }

    @Override
    public Integer orderVerification(String orderNo, String batteryCode) {
        TeboMallOrderDO mallOrderDO = getDetailByNo(orderNo);
        if (Objects.isNull(mallOrderDO)) {
            throw new ServiceException("单号未匹配到订单");
        }
        if (MallOrderStatusEnum.TO_BO_PICKER_UP.getCode() != mallOrderDO.getOrderStatus()) {
            throw new ServiceException("只有待提货状态的订单可以核销");
        }
        if (mallOrderDO.getChannel() == 4) {
            return 1;
        }
        TeboMallOrderDO update = new TeboMallOrderDO();
        update.setId(mallOrderDO.getId());
        Long userId = MaintainerOnlineUserUtil.getUserId();
        R<TeboAccountInfoVO> accountR = remoteAccountService.getAccountInfoById(userId);
        TeboAccountInfoVO accountInfoVO = accountR.getData();
        if (Objects.nonNull(accountInfoVO)) {
            update.setVerificationName(accountInfoVO.getAccountName());
            update.setVerificationAccount(accountInfoVO.getUserName());
            update.setVerificationAccountId(accountInfoVO.getId());
        }
        if (!ObjectUtil.isEmpty(batteryCode)) {
            update.setBatteryCode(batteryCode);
        }
        update.setOrderStatus(MallOrderStatusEnum.FINISH.getCode());
        update.setVerificationTime(LocalDateTime.now());
        int i = orderMapper.updateById(update);
        disMqService.recordWriteOff(mallOrderDO.getId());
        return i;
    }

    @Override
    public TeboMallOrderVO getDetailById(Long orderId) {
        TeboMallOrderDO teboMallOrderDO = orderMapper.selectById(orderId);
        int count = 1;
        while (count <= 5 && ObjectUtil.isEmpty(teboMallOrderDO)) {
            teboMallOrderDO = orderMapper.selectById(orderId);
            count++;
            if (ObjectUtil.isNotEmpty(teboMallOrderDO)){
                break;
            }
        }
        if (ObjectUtil.isEmpty(teboMallOrderDO)) {
            return null;
        }
        TeboMallOrderVO orderVO = new TeboMallOrderVO();
        BeanConvert.copy(teboMallOrderDO, orderVO);
        return orderVO;
    }

    @Override
    public TeboMallOrderDO getDetailByNo(String orderNo) {
        Assert.notNull(orderNo, "订单编号不能为空");
        TeboMallOrderDO teboMallOrderDO = orderMapper.getByOrderNo(orderNo);
        int count = 1;
        while (count <= 5 && ObjectUtil.isEmpty(teboMallOrderDO)) {
            teboMallOrderDO = orderMapper.getByOrderNo(orderNo);
            count++;
            if (ObjectUtil.isNotEmpty(teboMallOrderDO)){
                break;
            }
        }

        return teboMallOrderDO;
    }

    @Override
    public void updateMallOrderStatus(TeboOrderGoodsUpdateDTO updateDTO) {
        if (ObjectUtil.isEmpty(updateDTO)) {
            return;
        }
        if (ObjectUtil.isNotEmpty(updateDTO.getId())) {
            updateDTO.setIdList(Lists.newArrayList(updateDTO.getId()));
        }
        orderMapper.updateMallOrderStatus(updateDTO);
    }

    @Override
    public List<TeboMallOrderVO> getPayTimeOutOrder(LocalDateTime localDateTime) {
        List<TeboMallOrderDO> list = orderMapper.getPayTimeOutOrder(localDateTime);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        return BeanConvert.copyList(list, TeboMallOrderVO::new);
    }

    @Override
    public List<TeboMallOrderVO> getExtendedWarrantyOrderOrder(LocalDateTime localDateTime) {
        List<TeboMallOrderDO> list = orderMapper.getExtendedWarrantyOrderOrder(localDateTime);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        return BeanConvert.copyList(list, TeboMallOrderVO::new);
    }

    @Override
    public List<TeboOrderGoodsCodeGroup> getOrderGoodsCodeGroup(TeboMallOrderQueryInnerDTO queryDTO) {
        queryDTO.setOrderStatusList(MallOrderStatusEnum.getPayedStatusList());
        List<TeboMallOrderDO> orderList = orderMapper.getOrderList(queryDTO);

        //查询工单的
        List<TeboOrderGoodsIdGroup> serviceOrderGoodsGroup = getServiceOrderGoodsGroup(queryDTO);
        List<TeboOrderGoodsCodeGroup> codeGroups = BeanConvert.copyList(serviceOrderGoodsGroup, TeboOrderGoodsCodeGroup::new);

        Map<String, List<TeboOrderGoodsCodeGroup>> goodGroupAll = new HashMap<>();
        if (CollectionUtil.isNotEmpty(orderList)) {
            List<Long> orderIds = orderList.stream().map(TeboMallOrderDO::getId).collect(Collectors.toList());

            List<TeboOrderGoodsCodeGroup> allCodeGroupByOrderIds = new ArrayList<>();

            DataUtils.splitHandle(orderIds, (splitIds) -> {
                List<TeboOrderGoodsCodeGroup> codeGroupByOrderIds = mallOrderGoodsManager.getOrderGoodsCodeGroupByOrderIds(splitIds);
                //把工单的数据加到商城单中
                if (CollectionUtil.isNotEmpty(codeGroupByOrderIds)) {
                    allCodeGroupByOrderIds.addAll(codeGroupByOrderIds);

                }
            }, 400);

            if (CollectionUtil.isNotEmpty(codeGroups)) {
                allCodeGroupByOrderIds.addAll(codeGroups);
            }

            Map<String, List<TeboOrderGoodsCodeGroup>> goodGroup = DataUtils.listToGroup(allCodeGroupByOrderIds, TeboOrderGoodsCodeGroup::getGoodsNo);
            for (String goodsNo : goodGroup.keySet()) {
                if (!goodGroupAll.containsKey(goodsNo)) {
                    goodGroupAll.put(goodsNo, goodGroup.get(goodsNo));
                } else {
                    List<TeboOrderGoodsCodeGroup> goodsCodeGroups = goodGroupAll.get(goodsNo);
                    goodsCodeGroups.addAll(goodGroup.get(goodsNo));
                }
            }
        }


//        List<TeboOrderGoodsCodeGroup> codeGroupByOrderIds = mallOrderGoodsManager.getOrderGoodsCodeGroupByOrderIds(orderIds);
        List<TeboOrderGoodsCodeGroup> resultList = new ArrayList<>();
        for (List<TeboOrderGoodsCodeGroup> valueList : goodGroupAll.values()) {
            if (CollectionUtil.isEmpty(valueList)) {
                continue;
            }
            TeboOrderGoodsCodeGroup item = new TeboOrderGoodsCodeGroup();
            TeboOrderGoodsCodeGroup first = valueList.get(0);
            item.setGoodsNo(first.getGoodsNo());
            item.setGoodsName(first.getGoodsName());
            long allCount = valueList.stream().mapToLong(TeboOrderGoodsCodeGroup::getAllCount).reduce(0, Long::sum);
            item.setAllCount(allCount);
            int allAmount = valueList.stream().mapToInt(TeboOrderGoodsCodeGroup::getAllAmount).reduce(0, Integer::sum);
            item.setAllAmount(allAmount);
            resultList.add(item);
        }

        return resultList.stream().sorted(Comparator.comparing(TeboOrderGoodsCodeGroup::getAllAmount).reversed()).collect(Collectors.toList());
    }

    /**
     * 获取工单聚合数据
     *
     * @param queryDTO
     */
    private List<TeboOrderGoodsIdGroup> getServiceOrderGoodsGroup(TeboMallOrderQueryInnerDTO queryDTO) {
        ServiceOrderQueryDTO serviceOrderQueryDTO = new ServiceOrderQueryDTO();
        serviceOrderQueryDTO.setCreateTimeStartSecond(queryDTO.getCreateTimeStartSecond());
        serviceOrderQueryDTO.setCreateTimeEndSecond(queryDTO.getCreateTimeEndSecond());
        List<TeboOrderGoodsIdGroup> goodsIdGroups = remoteQueueOrderService.orderGoodsIdGroup(serviceOrderQueryDTO).getData();
        //把商品编码补上
        if (CollectionUtil.isNotEmpty(goodsIdGroups)) {
            List<Long> goodIds = goodsIdGroups.stream().map(TeboOrderGoodsIdGroup::getGoodsId).collect(Collectors.toList());
            TeboGoodsQueryDTO goodsQueryDTO = new TeboGoodsQueryDTO();
            goodsQueryDTO.setGoodsIdList(goodIds);

            List<TeboGoodsVO> teboGoodsVOS = teboGoodsManager.listGoods(goodsQueryDTO);
            Map<Long, TeboGoodsVO> teboGoodsVOMap = DataUtils.listToMap(teboGoodsVOS, TeboGoodsVO::getId);

            for (TeboOrderGoodsIdGroup goodsIdGroup : goodsIdGroups) {
                Long goodsId = goodsIdGroup.getGoodsId();
                TeboGoodsVO teboGoodsVO = teboGoodsVOMap.get(goodsId);
                if (Objects.nonNull(teboGoodsVO)) {
                    goodsIdGroup.setGoodsNo(teboGoodsVO.getGoodsNo());
                    goodsIdGroup.setCategoryName(teboGoodsVO.getSecondCategoryName());
                }

            }
        }
        return goodsIdGroups.stream().filter(e-> StringUtils.isNotEmpty(e.getGoodsNo())).collect(Collectors.toList());
    }

    @Override
    public List<TeboOrderGoodsCategoryGroup> getOrderGoodsCategoryGroup(TeboMallOrderQueryInnerDTO queryDTO) {

        queryDTO.setOrderStatusList(MallOrderStatusEnum.getPayedStatusList());
        List<TeboMallOrderDO> orderList = orderMapper.getOrderList(queryDTO);


        //查询工单的
        List<TeboOrderGoodsIdGroup> serviceOrderGoodsGroup = getServiceOrderGoodsGroup(queryDTO);
        List<TeboOrderGoodsCategoryGroup> codeGroups = BeanConvert.copyList(serviceOrderGoodsGroup, TeboOrderGoodsCategoryGroup::new);

        Map<String, List<TeboOrderGoodsCategoryGroup>> goodGroupAll = new HashMap<>();
        if (CollectionUtil.isNotEmpty(orderList)) {
            List<Long> orderIds = orderList.stream().map(TeboMallOrderDO::getId).collect(Collectors.toList());

            List<TeboOrderGoodsCategoryGroup> allCategoryGroupByOrderIds = new ArrayList<>();
            allCategoryGroupByOrderIds.addAll(codeGroups);

            DataUtils.splitHandle(orderIds, (splitIds) -> {
                List<TeboOrderGoodsCategoryGroup> categoryGroupByOrderIds = mallOrderGoodsManager.getOrderGoodsCategoryGroupByOrderIds(splitIds);
                if (CollectionUtil.isNotEmpty(categoryGroupByOrderIds)) {
                    allCategoryGroupByOrderIds.addAll(categoryGroupByOrderIds);
                }
            }, 400);


            Map<String, List<TeboOrderGoodsCategoryGroup>> goodGroup = DataUtils.listToGroup(allCategoryGroupByOrderIds, TeboOrderGoodsCategoryGroup::getCategoryName);
            for (String key : goodGroup.keySet()) {
                if (!goodGroupAll.containsKey(key)) {
                    goodGroupAll.put(key, goodGroup.get(key));
                } else {
                    List<TeboOrderGoodsCategoryGroup> goodsCategoryGroups = goodGroupAll.get(key);
                    goodsCategoryGroups.addAll(goodGroup.get(key));
                }
            }
        }


//        List<TeboOrderGoodsCodeGroup> codeGroupByOrderIds = mallOrderGoodsManager.getOrderGoodsCodeGroupByOrderIds(orderIds);
        List<TeboOrderGoodsCategoryGroup> resultList = new ArrayList<>();
        for (List<TeboOrderGoodsCategoryGroup> valueList : goodGroupAll.values()) {
            if (CollectionUtil.isEmpty(valueList)) {
                continue;
            }
            TeboOrderGoodsCategoryGroup item = new TeboOrderGoodsCategoryGroup();
            TeboOrderGoodsCategoryGroup first = valueList.get(0);
            item.setCategoryName(first.getCategoryName());
            long allCount = valueList.stream().mapToLong(TeboOrderGoodsCategoryGroup::getAllCount).reduce(0, Long::sum);
            item.setAllCount(allCount);
            int allAmount = valueList.stream().mapToInt(TeboOrderGoodsCategoryGroup::getAllAmount).reduce(0, Integer::sum);
            item.setAllAmount(allAmount);
            resultList.add(item);
        }

        return resultList.stream().sorted(Comparator.comparing(TeboOrderGoodsCategoryGroup::getAllAmount).reversed()).collect(Collectors.toList());
    }

    @Override
    public List<TeboMallOrderGoodsVO> getOrderList(TeboTenantOrderDTO orderDTO) {
        List<TeboMallOrderDO> list = orderMapper.getMallOrderList(orderDTO);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        return BeanConvert.copyList(list, TeboMallOrderGoodsVO::new);
    }


}