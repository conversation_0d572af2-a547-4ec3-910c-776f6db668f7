package com.tebo.mall.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 专供品门店安装费表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Getter
@Setter
@TableName("tebo_goods_order_split")
public class TeboGoodsOrderSplitDO extends Model<TeboGoodsOrderSplitDO> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 订单id
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 金额
     */
    @TableField("amount")
    private Integer amount;

    /**
     * 钱包id
     */
    @TableField("wallet_id")
    private Long walletId;

    /**
     * 类型1商2门店3门店抵用金4久久券
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 状态1未打款2已打款
     */
    @TableField("`status`")
    private Integer status;


    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
