package com.tebo.mall.mapper;

import com.tebo.mall.entity.TeboGoodsAttributeDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.mall.web.domain.dto.TeboAttributeQueryDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 商城属性表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Mapper
public interface TeboGoodsAttributeMapper extends TeboBaseMapper<TeboGoodsAttributeDO> {
    List<TeboGoodsAttributeDO> list(TeboAttributeQueryDTO queryDTO);
}
