package com.tebo.mall.service.goods.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.tebo.common.core.exception.GlobalException;

import com.tebo.common.security.util.OnlineUserUtil;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.CodeGenerator;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.mall.api.domain.view.*;
import com.tebo.mall.domain.TeboSupplyDistrictGoodsVO;
import com.tebo.mall.entity.*;


import com.tebo.mall.manager.TeboGoodsBrandManager;
import com.tebo.mall.mapper.TeboSpecialSupplyDistrictGoodsMapper;
import com.tebo.mall.mapper.special.TeboSpecialSupplyGoodsMapper;
import com.tebo.mall.redisson.queue.RedisDelayQueueEnum;
import com.tebo.mall.service.category.TeboGoodsCategoryService;
import com.tebo.mall.service.goods.TeboGoodsPicService;
import com.tebo.mall.service.goods.special.TeboSpecialSupplyGoodsService;
import com.tebo.mall.web.domain.dto.goods.special.*;

import com.tebo.mall.web.domain.vo.TeboGoodsCategoryVO;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class TeBoSpecialSupplyGoodsServiceImpl implements TeboSpecialSupplyGoodsService {
    @Resource
    private TeboSpecialSupplyGoodsMapper teboSpecialSupplyGoodsMapper;
    @Resource
    private TeboSpecialSupplyDistrictGoodsMapper teboSpecialSupplyDistrictGoodsMapper;
    @Resource
    private TeboGoodsPicService teboGoodsPicService;
    @Resource
    private TeboGoodsCategoryService teboGoodsCategoryService;

    @Resource
    private TeboGoodsBrandManager teboGoodsBrandManager;

    @Transactional
    @Override
    public Boolean addGoods(TeboSpecialSupplyGoodsDTO goodsDTO) {
        if (goodsDTO == null) {
            throw new GlobalException("参数不能为空");
        }
        List<Long> goodsIdList = new ArrayList<>();
        TeboSpecialSupplyGoodsDO specialSupplyGoodsDO = new TeboSpecialSupplyGoodsDO();
        if (ObjectUtil.isEmpty(goodsDTO.getGoodsNo())) {
            goodsDTO.setGoodsNo(CodeGenerator.generateSpecialGoodsCode());
        }
        BeanConvert.copy(goodsDTO, specialSupplyGoodsDO);
        specialSupplyGoodsDO.setId(SnowFlakeUtil.nextId());
        goodsIdList.add(specialSupplyGoodsDO.getId());
        //批量插入商品
         teboSpecialSupplyGoodsMapper.insert(specialSupplyGoodsDO);
        //插入附属图片
        if (CollectionUtil.isNotEmpty(goodsDTO.getGoodsPicList()) && CollectionUtil.isNotEmpty(goodsIdList) ){
            teboGoodsPicService.batchInsert(goodsIdList,goodsDTO.getGoodsPicList());
        }
        return true;
    }
    @Override
    @Transactional
    public Boolean updateGoods(TeboSpecialSupplyGoodsDTO goodsDTO) {
        TeboSpecialSupplyGoodsDO specialSupplyGoodsDO = teboSpecialSupplyGoodsMapper.selectById(goodsDTO.getId());
        BeanConvert.copy(goodsDTO, specialSupplyGoodsDO);
        teboSpecialSupplyGoodsMapper.updateById(specialSupplyGoodsDO);
        //修改商品图片
        teboGoodsPicService.updateGoodsPic(goodsDTO.getId(),goodsDTO.getGoodsPicList());
        return true ;
    }
    @Override
    public List<TeboSpecialSupplyGoodsVO> listGoods(TeboSpecialSupplyGoodsQueryDTO queryDTO) {
        List<TeboSpecialSupplyGoodsVO> list = teboSpecialSupplyGoodsMapper.listGoods(queryDTO);
        if (CollectionUtil.isEmpty(list)){
            return null;
        }
        return list;
    }

    @Override
    public TeboSpecialSupplyGoodsVO detailGoods(Long id) {
        if (id == null) {
            throw new GlobalException("参数不能为空");
        }
        TeboSpecialSupplyGoodsDO teboSpecialSupplyGoodsDO = teboSpecialSupplyGoodsMapper.selectById(id);
        if (teboSpecialSupplyGoodsDO == null) {
            return null;
        }
        TeboSpecialSupplyGoodsVO teboSpecialSupplyGoodsVO = new TeboSpecialSupplyGoodsVO();
        BeanConvert.copy(teboSpecialSupplyGoodsDO,teboSpecialSupplyGoodsVO);
        TeboGoodsVO teboBrandVo = teboGoodsBrandManager.selectById(teboSpecialSupplyGoodsDO.getBrandId());
        if (ObjectUtil.isNotEmpty(teboBrandVo)){
            teboSpecialSupplyGoodsVO.setBrandName(teboBrandVo.getBrandName());
        }
        /**
         * 一级分类
         */
        TeboGoodsCategoryVO categoryVO = teboGoodsCategoryService.getCategoryById(teboSpecialSupplyGoodsVO.getCategoryId());
        if (ObjectUtil.isNotEmpty(categoryVO)){
            teboSpecialSupplyGoodsVO.setCategoryName(categoryVO.getCategoryName());
        }

        /**
         * 二级分类
         */
        TeboGoodsCategoryVO secondCategoryVo = teboGoodsCategoryService.getCategoryById(teboSpecialSupplyGoodsVO.getSecondCategoryId());
        if (ObjectUtil.isNotEmpty(secondCategoryVo)){
            teboSpecialSupplyGoodsVO.setSecondCategoryName(secondCategoryVo.getCategoryName());
        }

        /**
         * 商品附属图片
         */
        List<TeboGoodsPicVO> picList = teboGoodsPicService.getGoodsPicList(id);
        if (CollectionUtil.isNotEmpty(picList)){
            teboSpecialSupplyGoodsVO.setPicList(picList);
        }
        return teboSpecialSupplyGoodsVO;
    }
    @Override
    public Boolean authorizedDistrictGoods(TeboSpecialSupplyDistrictGoodsDTO specialSupplyGoodsDTO){
        List<String> districtList = specialSupplyGoodsDTO.getDistrictList();
        List<TeboSpecialSupplyGoodsDetailDTO> authorizedGoods = specialSupplyGoodsDTO.getGoodsList();
        List<TeboSpecialSupplyDistrictGoodsDO> list = new ArrayList<>();
        /**
         * 删除原来的数据
         */
        authorizedGoods.forEach(item ->{
            TeboSpecialSupplyDistrictGoodsQueryDTO queryDTO = new TeboSpecialSupplyDistrictGoodsQueryDTO();
            queryDTO.setDistrictList(districtList);
            TeboSpecialSupplyGoodsDO goodsDO = teboSpecialSupplyGoodsMapper.selectById(item.getGoodsId());
            queryDTO.setGoodsNo(goodsDO.getGoodsNo());
            List<TeboSpecialSupplyDistrictGoodsDO> goodsList = teboSpecialSupplyDistrictGoodsMapper.list(queryDTO);
            if (CollectionUtil.isNotEmpty(goodsList)){
                goodsList.forEach(goods ->{
                    teboSpecialSupplyDistrictGoodsMapper.deleteById(goods);
                });
            }
        });
        districtList.stream().forEach(item ->{
            List<TeboSpecialSupplyGoodsDetailDTO> goodsDetailList = specialSupplyGoodsDTO.getGoodsList();
            goodsDetailList.forEach(goods ->{
                TeboSpecialSupplyDistrictGoodsDO teboSpecialSupplyDistrictGoodsDO = new TeboSpecialSupplyDistrictGoodsDO();
                // 拆分省市编码
                String[] districtArr = item.split("-");
                teboSpecialSupplyDistrictGoodsDO.setDistrict(item);
                teboSpecialSupplyDistrictGoodsDO.setProvinceDistrict(districtArr[0]);
                teboSpecialSupplyDistrictGoodsDO.setCityDistrict(districtArr[1]);
                TeboSpecialSupplyGoodsDO teboSpecialSupplyGoodsDO = teboSpecialSupplyGoodsMapper.selectById(goods.getGoodsId());
                if (ObjectUtil.isNotEmpty(teboSpecialSupplyGoodsDO)){
                    BeanConvert.copy(teboSpecialSupplyGoodsDO,teboSpecialSupplyDistrictGoodsDO);
                }
                teboSpecialSupplyDistrictGoodsDO.setSalePrice(MoneyUtil.yuanToFen(goods.getSalePrice()));
                teboSpecialSupplyDistrictGoodsDO.setTradeInPrice(MoneyUtil.yuanToFen(goods.getTradeInPrice()));
                teboSpecialSupplyDistrictGoodsDO.setOriginPrice(teboSpecialSupplyGoodsDO.getOriginPrice());
                teboSpecialSupplyDistrictGoodsDO.setAuthorizationTime(LocalDateTime.now());
                teboSpecialSupplyDistrictGoodsDO.setId(SnowFlakeUtil.nextId());
                teboSpecialSupplyDistrictGoodsDO.setCreateTime(LocalDateTime.now());
                teboSpecialSupplyDistrictGoodsDO.setUpdateTime(LocalDateTime.now());
                teboSpecialSupplyDistrictGoodsDO.setCreateBy(OnlineUserUtil.getUsername());
                list.add(teboSpecialSupplyDistrictGoodsDO);
            });
        });
        if (CollectionUtil.isNotEmpty(list)){
            teboSpecialSupplyDistrictGoodsMapper.batchInsert(list);
        }
        // todo 授权地区同时同步数据到合伙人
//        Map<String, String> param = new HashMap<>();
//        param.put("orderId", innerAddDto.getId().toString());
//        param.put("remark", "商城订单支付超时，自动取消订单");
//        // 添加订单支付超时，自动取消订单延迟队列。为了测试效果，延迟10秒钟
//        redisDelayQueueUtil.addDelayQueue(param, 15, TimeUnit.MINUTES, RedisDelayQueueEnum.TEBO_MALL_ORDER_PAYMENT_TIMEOUT.getCode());
        return true;
    }

    @Override
    public void deleteById(Long id) {
        TeboSpecialSupplyGoodsDO goodsDO = teboSpecialSupplyGoodsMapper.selectById(id);
        if (ObjectUtil.isNotEmpty(goodsDO)){
            teboSpecialSupplyGoodsMapper.deleteById(id);
            // 删除授权的商品
            teboSpecialSupplyDistrictGoodsMapper.delGoodsRelate(goodsDO.getGoodsNo());
        }

    }

    @Override
    public List<TeboSupplyDistrictGoodsVO> authorizeList(TeboSpecialSupplyGoodsQueryDTO queryDTO) {
        TeboSpecialSupplyDistrictGoodsQueryDTO query = new TeboSpecialSupplyDistrictGoodsQueryDTO();
        if (!ObjectUtils.isEmpty(queryDTO.getDistrict())) {
            query.setDistrictList(Collections.singletonList(queryDTO.getDistrict()));
        }
        if (!ObjectUtils.isEmpty(queryDTO.getGoodsNo())) {
            query.setGoodsNo(queryDTO.getGoodsNo());
        }
        if (!ObjectUtils.isEmpty(queryDTO.getGoodsName())) {
            query.setGoodsName(queryDTO.getGoodsName());
        }
        List<TeboSpecialSupplyDistrictGoodsVO> goodsList = teboSpecialSupplyDistrictGoodsMapper.listNew(query);
        if (CollectionUtils.isEmpty(goodsList)) {
            return Collections.emptyList();
        }
        return BeanConvert.copyList(goodsList, TeboSupplyDistrictGoodsVO::new);
    }
}
