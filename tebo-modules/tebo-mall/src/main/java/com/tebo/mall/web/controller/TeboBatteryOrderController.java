package com.tebo.mall.web.controller;

import com.tebo.common.core.domain.R;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.mall.service.order.TeboBatteryOrderService;
import com.tebo.mall.web.domain.dto.order.TeboBatteryOrderRefundDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 电池订单控制器
 */
@Slf4j
@RestController
@RequestMapping("/battery/order")
public class TeboBatteryOrderController extends BaseController {

    @Resource
    private TeboBatteryOrderService teboBatteryOrderService;

    /**
     * 电池订单退款
     */
    @PostMapping("/refund")
    public R<Boolean> refundBatteryOrder(@Validated @RequestBody TeboBatteryOrderRefundDTO refundDTO) {
        log.info("电池订单退款请求：{}", refundDTO);
        try {
            Boolean result = teboBatteryOrderService.refundBatteryOrder(refundDTO);
            return R.ok(result, "退款申请提交成功");
        } catch (Exception e) {
            log.error("电池订单退款失败", e);
            return R.fail("退款失败：" + e.getMessage());
        }
    }
}
