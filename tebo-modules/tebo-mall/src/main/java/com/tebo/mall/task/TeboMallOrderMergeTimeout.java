package com.tebo.mall.task;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.mall.entity.TeboGoodsOrderCouponDO;
import com.tebo.mall.entity.TeboGoodsOrderSplitDO;
import com.tebo.mall.entity.TeboMallOrderDO;
import com.tebo.mall.mapper.TeboGoodsOrderCouponMapper;
import com.tebo.mall.mapper.TeboGoodsOrderSplitMapper;
import com.tebo.mall.mapper.TeboMallOrderMapper;
import com.tebo.mall.redisson.queue.RedisDelayQueueHandle;
import com.tebo.mall.util.DistributedLock;
import com.tebo.mall.util.TeboMallCommonCacheConstant;
import com.tebo.rescue.api.domain.dto.OrderCouponDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> ZhangFan
 * @date : 2024/3/9 14:14
 * @Desc : 商城合并下单订单卡券处理
 */
@Slf4j
@Component
public class TeboMallOrderMergeTimeout implements RedisDelayQueueHandle<Map> {
    @Resource
    private DistributedLock distributedLock;

    @Resource
    private TeboMallOrderMapper mallOrderMapper;
    @Resource
    private TeboGoodsOrderCouponMapper teboGoodsOrderCouponMapper;

    @Override
    public void execute(Map map) {
        if (!distributedLock.tryLock(TeboMallCommonCacheConstant.getScheduledTaskPrefix("TeboMallOrderMergeTimeout:execute"), 10, 30)) {
            return;
        }
        log.info("TeboMallOrderMergeTimeout execute  {}", map);
        String orderNo = (String) map.get("orderNo");
        Long orderId = (Long) map.get("orderId");
        if (ObjectUtil.isEmpty(orderNo) || ObjectUtil.isEmpty(orderId)) {
            return;
        }
        TeboMallOrderDO mallOrderDO = mallOrderMapper.selectById(orderId);
        if (ObjectUtil.isEmpty(mallOrderDO)) {
            return;
        }
        // 将优惠券状态设置为占用
        mallOrderMapper.updateCouponStatusForMerge(orderNo);
        List<String> couponList = mallOrderMapper.selectCouponByOrderNo(orderNo);
        log.info("TeboMallOrderMergeTimeout execute  couponList {}", couponList);
        // 插入订单关联的优惠券表
        TeboGoodsOrderCouponDO couponDO = new TeboGoodsOrderCouponDO();
        // 包含两种券，金额剪掉指定金额
        couponDO.setId(SnowFlakeUtil.nextId());
        couponDO.setOrderId(orderId);
        couponDO.setOrderNo(mallOrderMapper.selectCouponOrderNo(orderNo));
        couponDO.setUniqueCode(couponList.get(0));
        couponDO.setAmount(3000);
        teboGoodsOrderCouponMapper.insert(couponDO);
        couponDO.setId(SnowFlakeUtil.nextId());
        couponDO.setUniqueCode(couponList.get(1));
        couponDO.setAmount(2000);
        teboGoodsOrderCouponMapper.insert(couponDO);
        log.info("TeboMallOrderMergeTimeout execute  end");
    }
}
