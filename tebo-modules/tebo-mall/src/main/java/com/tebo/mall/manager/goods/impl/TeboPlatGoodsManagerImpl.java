package com.tebo.mall.manager.goods.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.mall.entity.TeboPlatGoodsDO;
import com.tebo.mall.manager.goods.TeboPlatGoodsManager;
import com.tebo.mall.mapper.goods.TeboPlatGoodsMapper;
import com.tebo.mall.web.domain.dto.goods.TeboPlatGoodsDTO;
import com.tebo.mall.web.domain.dto.goods.TeboPlatGoodsQueryDTO;
import com.tebo.mall.web.domain.vo.TeboPlatGoodsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class TeboPlatGoodsManagerImpl implements TeboPlatGoodsManager {
    @Resource
    private TeboPlatGoodsMapper teboPlatGoodsMapper;

    @Override
    public Long insert(TeboPlatGoodsDTO platGoodsDTO) {
        TeboPlatGoodsDO platGoodsDO = new TeboPlatGoodsDO();
        BeanConvert.copy(platGoodsDTO, platGoodsDO);
        platGoodsDO.setId(SnowFlakeUtil.nextId());
        platGoodsDO.setOriginPrice(StringUtils.isEmpty(platGoodsDTO.getOriginPrice()) ? null : MoneyUtil.yuanToFen(platGoodsDTO.getOriginPrice().toString()));
        if(ObjectUtil.isNotEmpty(platGoodsDTO.getPreDiscountAmount())){
            platGoodsDO.setPreDiscountAmount(MoneyUtil.yuanToFen(platGoodsDTO.getPreDiscountAmount()));
        }
        if(ObjectUtil.isNotEmpty(platGoodsDTO.getDisCountAmount())){
            platGoodsDO.setDisCountAmount(MoneyUtil.yuanToFen(platGoodsDTO.getDisCountAmount()));
        }
        teboPlatGoodsMapper.insert(platGoodsDO);
        return platGoodsDO.getId();
    }
    @Override
    public int updateGoodsInfo(TeboPlatGoodsDTO platGoodsDTO) {
        if (platGoodsDTO == null){
            return 0;
        }
        TeboPlatGoodsDO platFormGoodsDO = new TeboPlatGoodsDO();
        BeanConvert.copy(platGoodsDTO,platFormGoodsDO);
        platFormGoodsDO.setOriginPrice(StringUtils.isEmpty(platGoodsDTO.getOriginPrice()) ? null : MoneyUtil.yuanToFen(platGoodsDTO.getOriginPrice().toString()));
        if (ObjectUtil.isNotEmpty(platGoodsDTO.getPreDiscountAmount())){
            platFormGoodsDO.setPreDiscountAmount(MoneyUtil.yuanToFen(platGoodsDTO.getPreDiscountAmount()));
        }
        if (ObjectUtil.isNotEmpty(platGoodsDTO.getDisCountAmount())){
            platFormGoodsDO.setDisCountAmount(MoneyUtil.yuanToFen(platGoodsDTO.getDisCountAmount()));
        }
        return teboPlatGoodsMapper.updateById(platFormGoodsDO);
    }
    @Override
    public List<TeboPlatGoodsVO> listGoods(TeboPlatGoodsQueryDTO queryDTO) {
        queryDTO.setDelFlag(0);
        List<TeboPlatGoodsVO> list = teboPlatGoodsMapper.listGoods(queryDTO);
        return list;
    }
    @Override
    public TeboPlatGoodsDO selectById(Long id) {
        TeboPlatGoodsDO teboPlatGoodsDO = teboPlatGoodsMapper.selectById(id);
        return teboPlatGoodsDO;
    }
    @Override
    public int deleteById(Long id) {
        return teboPlatGoodsMapper.deleteByIds(id);
    }

    @Override
    public List<TeboPlatGoodsVO> getPlatGoodsByUpdateTime(LocalDateTime time) {
        List<TeboPlatGoodsDO> list = teboPlatGoodsMapper.getPlatGoodsByUpdateTime(time);
        if (CollectionUtil.isEmpty(list)){
            return null;
        }
      return BeanConvert.copyList(list,TeboPlatGoodsVO::new);
    }
}