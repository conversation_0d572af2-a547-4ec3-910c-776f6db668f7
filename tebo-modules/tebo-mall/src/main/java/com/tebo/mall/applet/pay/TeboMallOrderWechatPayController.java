package com.tebo.mall.applet.pay;

import com.tebo.common.core.domain.R;
import com.tebo.mall.api.domain.dto.TeboMallOrderDTO;
import com.tebo.mall.service.order.TeboBatteryOrderService;
import com.tebo.mall.service.pay.TeboMallOrderPayService;
import com.tebo.system.api.domain.view.WechatPrepayResponse;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 商城订单支付
 */
@RestController
@RequestMapping("/order/wechat/pay")
public class TeboMallOrderWechatPayController {
    @Resource
    private TeboMallOrderPayService payService;
    @Resource
    private TeboBatteryOrderService teboBatteryOrderService;

    /**
     * 商城订单支付成功后回调
     *
     */
    @PostMapping("/payMallOrderAfterNotify")
    public R<Boolean> payOrderAfterNotify(@RequestBody TeboMallOrderDTO mallOrder) {
        return R.ok(payService.payOrderAfterNotify(mallOrder));
    }

    /**
     * 商城直营订单支付成功后回调-new
     *
     */
    @PostMapping("/payBatteryOrderAfterNotify")
    public R<Boolean> payBatteryOrderAfterNotify(@RequestBody TeboMallOrderDTO mallOrder) {
        return R.ok(teboBatteryOrderService.payBatteryOrderAfterNotify(mallOrder));
    }

    /**
     * 商城保费订单支付成功后更新订单状态
     *
     */
    @PostMapping("/payWarrantyExtensionOrderAfterNotify")
    public R<Boolean> payWarrantyExtensionOrderAfterNotify(@RequestBody TeboMallOrderDTO mallOrder) {
        return R.ok(payService.payWarrantyExtensionOrderAfterNotify(mallOrder));
    }

    /**
     * 商城订单预支付
     * @param orderId
     * @return
     */
    @RequestMapping("/mallOrderPrePay/{orderId}")
    public R<WechatPrepayResponse> mallOrderPrePay(@PathVariable("orderId") Long orderId) {
        return payService.mallOrderPrePay(orderId);
    }

    /**
     * 商城专供品订单预支付
     * @param orderId
     * @return
     */
    @RequestMapping("/mallSpecialOrderPrePay")
    public R<WechatPrepayResponse> mallSpecialOrderPrePay(@RequestParam("orderId") Long orderId,@RequestParam("unionId") String unionId) {
        return payService.mallSpecialOrderPrePay(orderId,unionId);
    }

    /**
     * 保险单预支付
     * @param orderId
     * @return
     */
    @RequestMapping("/insuranceOrderPrePay/{orderId}")
    public R<WechatPrepayResponse> insuranceOrderPrePay(@PathVariable("orderId") Long orderId,@RequestParam(value = "openId",required = false) String openId) {
        return payService.insuranceOrderPrePay(orderId,openId);
    }
}
