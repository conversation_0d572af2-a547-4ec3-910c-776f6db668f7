package com.tebo.mall.service.goods.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.mall.domain.dto.TeboTenantGoodsInfoDTO;
import com.tebo.mall.domain.view.TeboTenantGoodsInfoVO;
import com.tebo.mall.domain.view.TeboTenantGoodsPriceInfoVO;
import com.tebo.mall.entity.TeboSpecialSupplyGoodsDO;
import com.tebo.mall.entity.TeboTenantGoodsInfoDO;
import com.tebo.mall.entity.TeboTenantGoodsNoPriceDO;
import com.tebo.mall.mapper.TeboTenantGoodsInfoMapper;
import com.tebo.mall.mapper.TeboTenantGoodsNoPriceMapper;
import com.tebo.mall.mapper.special.TeboSpecialSupplyGoodsMapper;
import com.tebo.mall.service.goods.TeboSupplyGoodsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TeboSupplyGoodsServiceImpl implements TeboSupplyGoodsService {

    @Resource
    private TeboTenantGoodsInfoMapper teboTenantGoodsInfoMapper;
    @Resource
    private TeboTenantGoodsNoPriceMapper teboTenantGoodsNoPriceMapper;
    @Resource
    private TeboSpecialSupplyGoodsMapper teboSpecialSupplyGoodsMapper;

    @Override
    public List<TeboTenantGoodsInfoVO> selectTenantForWebList(TeboTenantGoodsInfoDTO queryDTO) {
        LambdaQueryWrapper<TeboTenantGoodsInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        if (!ObjectUtils.isEmpty(queryDTO.getRemark())) {
            // 合伙人编号、名称、手机号模糊查询
            queryWrapper.like(TeboTenantGoodsInfoDO::getTenantCode, queryDTO.getRemark())
                    .or().like(TeboTenantGoodsInfoDO::getTenantName, queryDTO.getRemark())
                    .or().like(TeboTenantGoodsInfoDO::getPhoneNumber, queryDTO.getRemark())
            ;
        }
        if (!ObjectUtils.isEmpty(queryDTO.getTenantDistant())) {
            queryWrapper.like(TeboTenantGoodsInfoDO::getTenantDistant, queryDTO.getTenantDistant());
        }
        if (!ObjectUtils.isEmpty(queryDTO.getStatus())) {
            queryWrapper.like(TeboTenantGoodsInfoDO::getStatus, queryDTO.getStatus());
        }
        queryWrapper.orderByDesc(TeboTenantGoodsInfoDO::getCreateTime);
        List<TeboTenantGoodsInfoDO> tenantGoodsInfoDOList = teboTenantGoodsInfoMapper.selectList(queryWrapper);
        return BeanConvert.copyList(tenantGoodsInfoDOList, TeboTenantGoodsInfoVO::new);
    }

    @Override
    public List<TeboTenantGoodsPriceInfoVO> selectGoodsPriceInfo(Long tenantId) {
        // 先查专供品列表
        LambdaQueryWrapper<TeboSpecialSupplyGoodsDO> goodsWrapper = new LambdaQueryWrapper<>();
        goodsWrapper.eq(TeboSpecialSupplyGoodsDO::getDelFlag, 0);
        goodsWrapper.orderByDesc(TeboSpecialSupplyGoodsDO::getCreateTime);
        List<TeboSpecialSupplyGoodsDO> goodsDOList = teboSpecialSupplyGoodsMapper.selectList(goodsWrapper);
        if (CollectionUtils.isEmpty(goodsDOList)) {
            return Collections.emptyList();
        }
        List<TeboTenantGoodsPriceInfoVO> result = BeanConvert.copyList(goodsDOList, TeboTenantGoodsPriceInfoVO::new);
        // 再查专供品在这个商下的价格
        LambdaQueryWrapper<TeboTenantGoodsNoPriceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeboTenantGoodsNoPriceDO::getTenantId, tenantId);
        List<TeboTenantGoodsNoPriceDO> tenantGoodsNoPriceDOList = teboTenantGoodsNoPriceMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(tenantGoodsNoPriceDOList)) {
            return result;
        }
        Map<String, TeboTenantGoodsNoPriceDO> priceDOMap = tenantGoodsNoPriceDOList.stream().collect(Collectors.toMap(TeboTenantGoodsNoPriceDO::getGoodsNo, item -> item));
        result = new ArrayList<>();
        for (TeboSpecialSupplyGoodsDO item : goodsDOList) {
            TeboTenantGoodsPriceInfoVO vo = new TeboTenantGoodsPriceInfoVO();
            BeanConvert.copy(item, vo);
            if (priceDOMap.containsKey(item.getGoodsNo())) {
                // 拼接商品的服务费
                vo.setShopFee(priceDOMap.get(item.getGoodsNo()).getShopFee());
            }
            result.add(vo);
        }
        return result;
    }

    @Transactional
    @Override
    public Boolean batchUpdateShopFee(List<TeboTenantGoodsInfoDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Boolean.FALSE;
        }
        Long walletId = teboTenantGoodsNoPriceMapper.selectWalletIdByNewTenantId(dtoList.get(0).getTenantId());
        if (ObjectUtils.isEmpty(walletId)) {
            throw new RuntimeException("供应商未创建钱包，不可设置");
        }
        TeboTenantGoodsNoPriceDO orderDO = new TeboTenantGoodsNoPriceDO();
        dtoList.forEach(item -> {
            LambdaQueryWrapper<TeboTenantGoodsNoPriceDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TeboTenantGoodsNoPriceDO::getGoodsNo, item.getGoodsNo());
            wrapper.eq(TeboTenantGoodsNoPriceDO::getTenantId, item.getTenantId());
            teboTenantGoodsNoPriceMapper.delete(wrapper);
            orderDO.setId(SnowFlakeUtil.nextId());
            orderDO.setTenantId(item.getTenantId());
            orderDO.setWalletId(walletId);
            orderDO.setGoodsNo(item.getGoodsNo());
            orderDO.setShopFee(item.getShopFee());
            teboTenantGoodsNoPriceMapper.insert(orderDO);
        });
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateInfo(TeboTenantGoodsInfoDTO dto) {
        LambdaUpdateWrapper<TeboTenantGoodsInfoDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TeboTenantGoodsInfoDO::getId, dto.getId());
        updateWrapper.set(TeboTenantGoodsInfoDO::getStatus, dto.getStatus());
        teboTenantGoodsInfoMapper.update(updateWrapper);
        return Boolean.TRUE;
    }

}
