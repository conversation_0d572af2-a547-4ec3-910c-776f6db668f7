package com.tebo.mall.web.domain.dto.goods.special;

import com.tebo.common.core.web.page.PageDomain;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class TeboSpecialSupplyDistrictGoodsQueryDTO extends PageDomain implements Serializable {

    /**
     * 地区
     */
    private String district;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商品名称
     */
    private String goodsName;

    private List<String> districtList;

    /**
     * 经度
     */
    private String lgt;

    /**
     * 纬度
     */
    private String lnt;

    /**
     * 商品id
     */
    private List<Long> goodsIdList;
}
