package com.tebo.mall.service.category;

import com.tebo.mall.web.domain.dto.category.TeboGoodsCategoryDTO;
import com.tebo.mall.web.domain.dto.category.TeboCategoryQueryDTO;
import com.tebo.mall.web.domain.vo.TeboGoodsCategoryVO;

import java.util.List;

/**
 * @Author：zhengmk
 * @Date 2023/12/19 13:18
 */

public interface TeboGoodsCategoryService {

    /**
     * 类目列表
     */
    public List<TeboGoodsCategoryVO> listCategory(TeboCategoryQueryDTO queryDTO);

    /**
     * 师傅端可选择的类目列表
     */
  //  public List<TeboGoodsCategoryVO> appletSelectableCategoryList(TeboCategoryQueryDTO teboCategoryQueryDTO);

    /**
     * 用户端类目展示
     * @param teboCategoryQueryDTO
     * @return
     */
    public List<TeboGoodsCategoryVO> userCategoryList(TeboCategoryQueryDTO teboCategoryQueryDTO);

    List<TeboGoodsCategoryVO> allCategoryList();

    /**
     * 可选择的类目列表
     */
    public List<TeboGoodsCategoryVO> selectableCategoryList(Long parentId);
    /**
     * 类目详情
     */
    public TeboGoodsCategoryVO getCategoryById(Long id);

    /**
     * 添加类目
     */
    public Boolean addCategory(TeboGoodsCategoryDTO categoryDTO);

    /**
     * 修改类目
     */
    public Boolean updateCategory(TeboGoodsCategoryDTO categoryDTO);

    /**
     * 删除类目
     */
    public Boolean deleteCategory(Long id);


    List<TeboGoodsCategoryVO> builTree(TeboCategoryQueryDTO teboCategoryQueryDTO);

    /**
     * 推荐的二级目录
     */

    public List<TeboGoodsCategoryVO> recommendUserCategoryList();


}
