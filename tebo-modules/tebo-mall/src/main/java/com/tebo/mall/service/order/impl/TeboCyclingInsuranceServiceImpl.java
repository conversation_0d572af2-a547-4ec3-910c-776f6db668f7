package com.tebo.mall.service.order.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.core.web.domain.AjaxResult;
import com.tebo.common.util.applet.AppletUtil;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.mall.api.domain.dto.cycling.TeboCyclingOrderDTO;
import com.tebo.mall.entity.TeboCyclyingInsuranceOrderDO;
import com.tebo.mall.manager.order.TeboCyclyingInsuranceOrderManager;
import com.tebo.mall.service.order.TeboCyclingInsuranceService;
import com.tebo.mall.util.CyclingInsurancePolicyUtil;

import com.tebo.mall.web.domain.dto.cycle.TeboCycleOrderDTO;
import com.tebo.mall.web.domain.dto.cycle.YuHaoOrderUrlVO;
import com.tebo.mall.web.domain.vo.cycling.CycleInsuranceOrderVO;
import com.tebo.mall.web.domain.vo.cycling.TeboCycleInsuranceOrderVO;
import com.tebo.rescue.api.RemoteCouponService;
import com.tebo.rescue.api.RemoteFundUnfreezeService;
import com.tebo.rescue.api.domain.dto.OrderCouponDTO;
import com.tebo.rescue.api.domain.dto.TeboCouponCustomerDTO;
import com.tebo.rescue.api.domain.dto.UnfreezeFundsQueryDTO;
import com.tebo.system.api.RemoteCustomerService;
import com.tebo.system.api.model.TeboConsumer;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.JsonAggregateOnNullType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;

@Service
@Slf4j
public class TeboCyclingInsuranceServiceImpl implements TeboCyclingInsuranceService {
    @Resource
    private CyclingInsurancePolicyUtil cyclingInsurancePolicyUtil;
    @Resource
    private RemoteCouponService remoteCouponService;
    @Resource
    private TeboCyclyingInsuranceOrderManager orderManager;

    /**
     * 誉好投保跳转地址
     */
    @Value("${insurance.cycling.yuHaoOrderUrl}")
    private String yuHaoOrderUrl;

    @Resource
    private RemoteCustomerService remoteCustomerService;

    @Resource
    private RemoteFundUnfreezeService fundUnfreezeService;

    @Resource
    private HttpServletRequest request;


    @Override
    public void synchronizeOrder(TeboCyclingOrderDTO cycleOrderDTO) {
        if (StringUtils.isEmpty(cycleOrderDTO.getOrderPhoneNumber())){
            String unionId = AppletUtil.getUnionIdByRequest(request);
            /**
             * 权益人手机号码
             */
            TeboConsumer stakeholderConsumer = remoteCustomerService.selectByUnionId(unionId).getData();
            cycleOrderDTO.setOrderPhoneNumber(stakeholderConsumer.getPhoneNumber());
        }
        cyclingInsurancePolicyUtil.synchronizeOrder(cycleOrderDTO);
    }

    @Override
    public void synchronizeOriginators(TeboCyclingOrderDTO cycleOrderDTO) {
         cyclingInsurancePolicyUtil.synchronizeOriginators(cycleOrderDTO);
    }

    @Override
    public void refund(TeboCyclingOrderDTO cycleOrderDTO) {
        cyclingInsurancePolicyUtil.refund(cycleOrderDTO);
    }

    @Override
    public CycleInsuranceOrderVO getOrderDetail(TeboCycleOrderDTO cycleOrderDTO) {
        return cyclingInsurancePolicyUtil.getOrderDetail(cycleOrderDTO);
    }

    @Override
    public void updateOrderStatus(TeboCycleOrderDTO cycleOrderDTO) {
        R<TeboCouponCustomerDTO> couponCustomerDTOR = remoteCouponService.getDetailByCouponCode(cycleOrderDTO.getOrderCode());
       switch (cycleOrderDTO.getOrderStatus()){
           /**
            * 核销,将券设置为已使用
            */
           case 1:
               if (ObjectUtil.isNotEmpty(couponCustomerDTOR) && ObjectUtil.isNotEmpty(couponCustomerDTOR.getData())){
                   cycleOrderDTO.setCouponOrderNo(couponCustomerDTOR.getData().getOrderNo());
               }
               orderManager.insertOrder(cycleOrderDTO);
               AjaxResult result = remoteCouponService.writeOff(cycleOrderDTO.getOrderCode());
               if (result.isError()){
                   throw new ServiceException("卡券核销失败");
               }
               break;
           /**
            * 将券设置为未使用,将订单状态设置为退款
            */
           case 2:
               /**
                * 更新订单状态
                */
               TeboCyclyingInsuranceOrderDO insuranceOrderDO = orderManager.getOrderByCouponCode(cycleOrderDTO.getOrderCode());
               if (ObjectUtil.isNotEmpty(insuranceOrderDO)){
                   log.info("投保失败,投保信息为：{}", JSONObject.toJSONString(insuranceOrderDO));
                   orderManager.deleteOrderById(insuranceOrderDO.getId());
               }
               /**
                * 更新券状态
                */
               OrderCouponDTO orderCouponDTO = new OrderCouponDTO();
               orderCouponDTO.setCouponCode(cycleOrderDTO.getOrderCode());
               remoteCouponService.updateCouponStatusNotUsed(orderCouponDTO);
               break;
           /**
            * 将订单状态设置为已生效
            */
           case 3:
               TeboCyclyingInsuranceOrderDO orderDO = orderManager.getOrderByCouponCode(cycleOrderDTO.getOrderCode());
               if (ObjectUtil.isEmpty(orderDO)){
                   log.error("get cycle insurance error param :{}",cycleOrderDTO.getOrderCode());
                  return;
               }
               BeanConvert.copy(cycleOrderDTO,orderDO);
               orderDO.setUpdateTime(LocalDateTime.now());
               orderManager.updateById(orderDO);
               /**
                * 骑行卡激活
                */
               if (ObjectUtil.isNotEmpty(couponCustomerDTOR) && ObjectUtil.isNotEmpty(couponCustomerDTOR.getData())){
                   TeboCouponCustomerDTO teboCouponCustomerDTO = couponCustomerDTOR.getData();
                   String orderNo = teboCouponCustomerDTO.getOrderNo();
                   remoteCouponService.updateOrderStatus(orderNo);
                   UnfreezeFundsQueryDTO unfreezeFundsQueryDTO = new UnfreezeFundsQueryDTO();
                   unfreezeFundsQueryDTO.setOrderNo(orderNo);
                   unfreezeFundsQueryDTO.setType(3);
                   fundUnfreezeService.cycleCommission(unfreezeFundsQueryDTO);
               }
               break;
       }
    }

    @Override
    public TeboCycleInsuranceOrderVO getOrderByCouponCode(String couponCode) {
        TeboCyclyingInsuranceOrderDO insuranceOrderDO =  orderManager.getOrderByCouponCode(couponCode);
        if (ObjectUtil.isEmpty(insuranceOrderDO)){
            return new TeboCycleInsuranceOrderVO();
        }
        TeboCycleInsuranceOrderVO cycleInsuranceOrderVO = new TeboCycleInsuranceOrderVO();
        return BeanConvert.copy(insuranceOrderDO,cycleInsuranceOrderVO);
    }
    @Override
    public YuHaoOrderUrlVO getYuHaoOrderUrl(String couponCode) throws Exception {
        YuHaoOrderUrlVO yuHaoOrderUrlVO = new YuHaoOrderUrlVO();
        String url = yuHaoOrderUrl+ couponCode;
        yuHaoOrderUrlVO.setUrl(url);
        return yuHaoOrderUrlVO;
    }
}