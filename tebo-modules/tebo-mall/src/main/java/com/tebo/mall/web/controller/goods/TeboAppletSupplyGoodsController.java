package com.tebo.mall.web.controller.goods;

import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.domain.AjaxResult;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.common.security.util.OnlineUserUtil;
import com.tebo.common.util.applet.AppletUtil;
import com.tebo.common.util.position.AutoNaviPositionUtil;
import com.tebo.mall.api.domain.view.TeboSpecialSupplyDistrictGoodsVO;
import com.tebo.mall.service.goods.special.TeboSpecialSupplyDistrictGoodsService;
import com.tebo.mall.web.domain.dto.goods.special.TeboSpecialSupplyDistrictGoodsQueryDTO;
import com.tebo.system.api.RemotePartnerService;
import com.tebo.system.api.RemoteShopService;
import com.tebo.system.api.domain.view.TeboAreaVO;
import com.tebo.system.api.domain.view.TeboPartnerInfoVO;
import com.tebo.system.api.model.TeboShop;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@RestController
@Slf4j
@RequestMapping("/tebo/applet/supply/goods")
public class TeboAppletSupplyGoodsController  extends BaseController {
    @Resource
    private TeboSpecialSupplyDistrictGoodsService districtGoodsService;
    @Resource
    private RemoteShopService remoteShopService;
    @Resource
    private RemotePartnerService remotePartnerService;

    /**
     * 校验绑定关系是否存在
     * @param shopId
     * @param request
     * @return
     */
    @GetMapping("/checkReferenceShop")
    public R checkReferenceShop(@RequestParam Long shopId, HttpServletRequest request) {
        // 获取当前用户绑定的超服店id
        return R.ok(districtGoodsService.getShopIdByUnionId(AppletUtil.getUnionIdByRequest(request)));
    }


    /**
     * 查询用户绑定门店信息
     * 取消高德地图调用
     */
    @GetMapping("/getShopIdByUnionId")
    public R<?> getShopIdByUnionId(HttpServletRequest request) {
        // 获取当前用户绑定的超服店id
        Long shopIdByUnionId = districtGoodsService.getShopIdByUnionId(AppletUtil.getUnionIdByRequest(request));
        if (ObjectUtils.isEmpty(shopIdByUnionId)) {
            return R.ok("");
        }
        log.info("getShopIdByUnionId [] {}", shopIdByUnionId);
        // 25.5.14逻辑 如果找到当前用户绑定超服店，则直接查询该店专供品
        R<TeboShop> res = remoteShopService.getShopInfo(shopIdByUnionId);
        if (res.getCode() != 200) {
            return R.ok("");
        }
        TeboShop shop = res.getData();
        // 非超服店，直接返回为空
        Map<String,Object> result = new HashMap<>();
        result.put("id",shop.getId());
        result.put("shopName",shop.getShopName());
        result.put("phoneNumber",shop.getPhoneNumber());
        result.put("address",shop.getAddress());
        result.put("latitude",shop.getLatitude());
        result.put("longitude",shop.getLongitude());
        return R.ok(result);
    }

    /**
     * 根据门店查询是否有专供品列表
     * 取消高德地图调用
     *  TeboAppletSpecialSupplyGoodsController list
     */
    @GetMapping("/getList")
    public TableDataInfo getList(@RequestParam Long shopId, HttpServletRequest request) {
//        // 获取当前用户绑定的超服店id
//        Long shopIdByUnionId = districtGoodsService.getShopIdByUnionId(AppletUtil.getUnionIdByRequest(request));
//        // 25.5.14逻辑 如果找到当前用户绑定超服店，则直接查询该店专供品
//        // 如果未找到，则直接使用当前门店查询专供品
//        if (!ObjectUtils.isEmpty(shopIdByUnionId)) {
//            shopId = shopIdByUnionId;
//        }
        R<TeboShop> res = remoteShopService.getShopInfo(shopId);
        if (res.getCode() != 200) {
            return getDataTable(Collections.emptyList());
        }
        TeboShop shop = res.getData();
        // 非超服店，直接返回为空
        if (shop.getVip() != 1) {
            return getDataTable(Collections.emptyList());
        }
        // 查所属合伙人的经营区域
        R<String> partnerRes = remoteShopService.selectInfoById(shopId);
        if (partnerRes.getCode() != 200) {
            return getDataTable(Collections.emptyList());
        }
        String district = partnerRes.getData();
        // 如果合伙人经营区域为空，也返回为空
        if (StringUtils.isEmpty(district)) {
            return getDataTable(Collections.emptyList());
        }
        String [] districtArray = district.split(",");
        TeboSpecialSupplyDistrictGoodsQueryDTO queryDTO = new TeboSpecialSupplyDistrictGoodsQueryDTO();
        queryDTO.setDistrict(districtArray[0] + "-" + districtArray[1]);
        List<TeboSpecialSupplyDistrictGoodsVO> goodsList = districtGoodsService.listGoods(queryDTO);
        if (CollectionUtils.isEmpty(goodsList)) {
            return getDataTable(Collections.emptyList());
        }
        // 结果按照商品编号排序（从小到大）
        return getDataTable(goodsList.stream().sorted(Comparator
                .comparingDouble((TeboSpecialSupplyDistrictGoodsVO g) -> extractCapacity(g.getGoodsName()))
                .thenComparingInt(g -> extractVoltage(g.getGoodsName()))
        ).collect(Collectors.toList()));
    }

    private double extractCapacity(String goodsName) {
        Matcher matcher = Pattern.compile("(\\d+\\.?\\d*)(?i)ah").matcher(goodsName);
        return matcher.find() ? Double.parseDouble(matcher.group(1)) : 0.0;
    }

    // 提取电压（V），支持大小写（如 12V/24v）
    private int extractVoltage(String goodsName) {
        Matcher matcher = Pattern.compile("(\\d+)(?i)v").matcher(goodsName);
        return matcher.find() ? Integer.parseInt(matcher.group(1)) : 0;
    }
}
