package com.tebo.mall.service.goods.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.tebo.common.util.DataUtils;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.mall.api.domain.view.TeboAttributeRelationVO;
import com.tebo.mall.api.domain.view.TeboAttributeValueRelationVO;
import com.tebo.mall.entity.TeboGoodsAttributeValueDO;
import com.tebo.mall.manager.TeboGoodsAttributeValueManger;
import com.tebo.mall.manager.goods.TeboGoodsAttributeRelationManager;
import com.tebo.mall.service.goods.TeboGoodsAttributeRelationService;
import com.tebo.mall.web.domain.dto.TeboAttributeValueQueryDTO;
import com.tebo.mall.web.domain.dto.goods.TeboGoodsAttributeDTO;
import com.tebo.mall.web.domain.dto.goods.TeboGoodsAttributeValueDTO;
import com.tebo.mall.web.domain.dto.goods.TeboGoodsShopDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TeboGoodsAttributeRelationServiceImpl implements TeboGoodsAttributeRelationService {
    @Resource
    private TeboGoodsAttributeRelationManager teboGoodsAttributeManager;
    @Resource
    private TeboGoodsAttributeValueManger valueManger;

    @Override
    public void addGoodsAttribute(List<Long> goodsIdList,List<TeboGoodsAttributeDTO> goodsAttributeList) {
        List<TeboGoodsAttributeValueDTO> valueDTOList = new ArrayList<>();
        List<TeboGoodsAttributeDTO> attributeList = new ArrayList<>();
        goodsIdList.forEach(goodsId ->{
            goodsAttributeList.forEach(item -> {
                TeboGoodsAttributeDTO teboGoodsAttributeDTO = new TeboGoodsAttributeDTO();
                BeanConvert.copy(item,teboGoodsAttributeDTO);
                teboGoodsAttributeDTO.setId(SnowFlakeUtil.nextId());
                teboGoodsAttributeDTO.setGoodsId(goodsId);
                if (CollectionUtil.isNotEmpty(item.getSelectedValueIdList())){
                    item.getSelectedValueIdList().forEach(value -> {
                        TeboGoodsAttributeValueDTO valueDto = new TeboGoodsAttributeValueDTO();
                        valueDto.setId(SnowFlakeUtil.nextId());
                        valueDto.setAttributeId(item.getAttributeId());
                        valueDto.setAttributeValueId(value);
                        valueDto.setGoodsId(goodsId);
                        valueDTOList.add(valueDto);
                    });
                    attributeList.add(teboGoodsAttributeDTO);
                }
            });
        });
        if (CollectionUtil.isNotEmpty(attributeList)){
            teboGoodsAttributeManager.batchInsertGoodsAttribute(attributeList) ;
        }
        if (CollectionUtil.isNotEmpty(valueDTOList)){
            teboGoodsAttributeManager.batchInsertGoodsAttributeValue(valueDTOList);
        }
    }
    @Override
    @Transactional
    public void updateGoodsAttribute(List<Long> goodsIdList,TeboGoodsShopDTO teboGoodsShopDTO) {
        //删除商品属性
        teboGoodsAttributeManager.deleteGoodsAttribute(teboGoodsShopDTO.getId());
        if (CollectionUtil.isNotEmpty(teboGoodsShopDTO.getGoodsAttributeList())){
          //添加商品属性
          addGoodsAttribute(goodsIdList,teboGoodsShopDTO.getGoodsAttributeList());
        }
    }

    @Override
    public List<TeboAttributeRelationVO> listGoodsAttribute(TeboGoodsAttributeDTO teboGoodsAttributeDTO) {
        List<TeboAttributeRelationVO> list = teboGoodsAttributeManager.listGoodsAttribute(teboGoodsAttributeDTO);
        if (CollectionUtil.isEmpty(list)){
            return null;
        }
        List<Long> attributeList = list.stream().map(item -> item.getAttributeId()).collect(Collectors.toList());
        TeboAttributeValueQueryDTO valueQueryDTO = new TeboAttributeValueQueryDTO();
        valueQueryDTO.setAttributeIds(attributeList);
        List<TeboGoodsAttributeValueDO> attributeValueDOS = valueManger.selectAttributeValue(valueQueryDTO);
        Map<Long, List<TeboGoodsAttributeValueDO>> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(attributeValueDOS)) {
            map = DataUtils.listToGroup(attributeValueDOS, TeboGoodsAttributeValueDO::getAttributeId);
        }
        Map<Long, List<TeboGoodsAttributeValueDO>> finalMap = map;
        list.forEach(item ->{
            TeboGoodsAttributeDTO query = new TeboGoodsAttributeDTO();
            query.setGoodsId(item.getGoodsId());
            query.setAttributeId(item.getAttributeId());
            List<TeboAttributeValueRelationVO> valueList = teboGoodsAttributeManager.getGoodsAttributeValueList(query);
            List<Long> selectedValueIdList = valueList.stream().map(value -> value.getAttributeValueId()).collect(Collectors.toList());
            item.setSelectedValueIdList(selectedValueIdList);
            List<TeboGoodsAttributeValueDO> valueDOS = finalMap.get(item.getAttributeId());
            List<TeboAttributeValueRelationVO> valueRelationList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(valueDOS)){
                valueDOS.forEach(valueDO ->{
                    TeboAttributeValueRelationVO valueRelationVO = new TeboAttributeValueRelationVO();
                    valueRelationVO.setAttributeId(valueDO.getAttributeId());
                    valueRelationVO.setAttributeValue(valueDO.getAttributeValue());
                    valueRelationVO.setAttributeValueId(valueDO.getId());
                    valueRelationList.add(valueRelationVO);
                });
                item.setAllValueList(valueRelationList);
            }
            item.setValueList(valueList);
        });
        return list;
    }
}