package com.tebo.mall.manager.goods;

import com.tebo.mall.web.domain.dto.goods.TeboPlatGoodsPicDTO;
import com.tebo.mall.web.domain.vo.TeboPlatGoodsPicVO;

import java.util.List;

public interface TeboPlatGoodsPicManager {
    /**
     * 批量插入图片
     * @param teboGoodsPicDTO
     * @return
     */
    void batchInsert(List<TeboPlatGoodsPicDTO> teboGoodsPicDTO);

    /**
     * 查询商品附属图
     * @param goodsId
     * @return
     */
    List<TeboPlatGoodsPicVO> getGoodsPicList(Long goodsId);

    /**
     * 删除商品附属图
     * @param goodsId
     */
    void batchDelete(Long goodsId);

    List<TeboPlatGoodsPicVO> getGoodsPicListByGoodsNo(String goodsNo);
}