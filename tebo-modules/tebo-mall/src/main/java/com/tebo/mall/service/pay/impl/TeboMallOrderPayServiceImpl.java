package com.tebo.mall.service.pay.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.enums.PayWechatOrderBusinessTypeEnum;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.mall.api.RemoteCommissionService;
import com.tebo.mall.api.domain.dto.CouponCommissionShareDTO;
import com.tebo.mall.api.domain.dto.TeboMallOrderDTO;
import com.tebo.mall.api.domain.dto.TeboSpecialGoodsVerificationDTO;
import com.tebo.mall.api.domain.view.TeboGoodsVO;
import com.tebo.mall.api.enums.MallOrderChannelEnum;
import com.tebo.mall.domain.dto.TeboCouponGiftOrderDTO;
import com.tebo.mall.entity.*;
import com.tebo.mall.enums.InsuranceMallOrderStatusEnum;
import com.tebo.mall.enums.MallOrderStatusEnum;
import com.tebo.mall.enums.TeboMallOrderTypeEnum;
import com.tebo.mall.generator.TeboNumberGenerator;
import com.tebo.mall.manager.goods.TeboGoodsManager;
import com.tebo.mall.manager.order.TeboInsurancePolicyOrderManager;
import com.tebo.mall.manager.order.TeboMallOrderGoodsManager;
import com.tebo.mall.manager.order.TeboMallOrderManager;
import com.tebo.mall.mapper.TeboGoodsOrderCouponMapper;
import com.tebo.mall.mapper.TeboGoodsOrderSplitMapper;
import com.tebo.mall.mapper.TeboMallOrderMapper;
import com.tebo.mall.mapper.TeboTenantGoodsNoPriceMapper;
import com.tebo.mall.redisson.queue.RedisDelayQueueEnum;
import com.tebo.mall.redisson.queue.RedisDelayQueueUtil;
import com.tebo.mall.service.order.TeboInsurancePolicyService;
import com.tebo.mall.service.pay.TeboMallOrderPayService;
import com.tebo.mall.service.rewardsPoint.RewardsPointService;
import com.tebo.mall.util.RewardsPointUtil;
import com.tebo.mall.web.domain.dto.TeboInsurancePolicyDTO;
import com.tebo.mall.web.domain.dto.order.inner.TeboMallOrderQueryDTO;
import com.tebo.mall.web.domain.dto.order.inner.TeboOrderGoodsUpdateDTO;
import com.tebo.mall.web.domain.vo.order.TeboMallOrderVO;
import com.tebo.mall.web.domain.vo.order.TeboOrderGoodsVO;
import com.tebo.rescue.api.RemoteCouponService;
import com.tebo.rescue.api.RemoteQueueOrderService;
import com.tebo.rescue.api.domain.dto.ServiceOrderDTO;
import com.tebo.rescue.api.domain.dto.TeboCouponCustomerDTO;
import com.tebo.rescue.api.domain.dto.TeboCouponMallOrderDTO;
import com.tebo.system.api.*;
import com.tebo.rescue.api.domain.dto.OrderCouponDTO;
import com.tebo.system.api.domain.dto.PayOrderDTO;
import com.tebo.system.api.domain.dto.wallet.TeboWalletPlanDTO;
import com.tebo.system.api.domain.dto.wallet.TeboWalletPlanDetailDTO;
import com.tebo.system.api.domain.view.PayWechatVO;
import com.tebo.system.api.domain.view.TeboPartnerInfoVO;
import com.tebo.system.api.domain.view.WechatPrepayResponse;
import com.tebo.system.api.model.TeboConsumer;
import com.tebo.system.api.model.TeboShop;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class TeboMallOrderPayServiceImpl implements TeboMallOrderPayService {
    @Resource
    private RemotePayService remotePayService;
    @Resource
    private RemoteCustomerService remoteCustomerService;
    @Resource
    private RemoteCouponService remoteCouponService;
    @Resource
    private RemoteShopService shopService;

    @Resource
    private TeboMallOrderManager teboMallOrderManager;

    @Resource
    private TeboMallOrderMapper orderMapper;
    @Resource
    private TeboInsurancePolicyOrderManager insurancePolicyOrderManager;
    @Resource
    private TeboMallOrderGoodsManager orderGoodsManager;
    @Resource
    private TeboGoodsManager teboGoodsManager;
    @Resource
    private TeboInsurancePolicyService insurancePolicyService;
    @Resource
    private RemoteQueueOrderService remoteQueueOrderService;
    @Resource
    private TeboTenantGoodsNoPriceMapper teboTenantGoodsNoPriceMapper;
    @Resource
    private TeboGoodsOrderSplitMapper teboGoodsOrderSplitMapper;
    @Resource
    private TeboGoodsOrderCouponMapper teboGoodsOrderCouponMapper;

    @Value("${partner.secondCategoryId}")
    private String secondCategoryId;
    @Resource
    private RemoteWalletService remoteWalletService;
    @Resource
    private RewardsPointUtil rewardsPointUtil;
    @Resource
    private RedisDelayQueueUtil redisDelayQueueUtil;
    @Resource
    private RemotePartnerService remotePartnerService;
    @Resource
    private RewardsPointService rewardsPointService;
    @Autowired
    private TeboMallOrderMapper teboMallOrderMapper;

    //    @Transactional
    @Override
    public Boolean payOrderAfterNotify(TeboMallOrderDTO mallOrder) {
        log.info("mall order pay notify param :{}", JSONObject.toJSONString(mallOrder));
        if (StringUtils.isEmpty(mallOrder.getOrderNo())){
            throw new ServiceException("订单编号为空");
        }
        TeboMallOrderDO teboMallOrderDO = teboMallOrderManager.getDetailByNo(mallOrder.getOrderNo());
        if (ObjectUtil.isEmpty(teboMallOrderDO)){
            throw new ServiceException("订单不存在");
        }
        teboMallOrderDO.setOrderStatus(MallOrderStatusEnum.TO_BO_PICKER_UP.getCode());
        TeboOrderGoodsUpdateDTO updateDTO = new TeboOrderGoodsUpdateDTO();
        BeanConvert.copy(teboMallOrderDO,updateDTO);
        updateDTO.setPayTime(LocalDateTime.now());
        teboMallOrderManager.updateMallOrderStatus(updateDTO);
        // 专供品订单，创建分账计划
        if (mallOrder.getOrderNo().startsWith("TBCXA")) {
            TeboMallOrderQueryDTO queryDTO = new TeboMallOrderQueryDTO();
            queryDTO.setOrderIdList(Collections.singletonList(teboMallOrderDO.getId()));
            List<TeboOrderGoodsVO> goodsList = orderGoodsManager.getOrderGoodsList(queryDTO);
            if (CollectionUtils.isEmpty(goodsList)) {
                return true;
            }
            Long tenantId = teboMallOrderDO.getTenantId();
            LambdaQueryWrapper<TeboTenantGoodsNoPriceDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TeboTenantGoodsNoPriceDO::getTenantId, tenantId);
            queryWrapper.eq(TeboTenantGoodsNoPriceDO::getGoodsNo, goodsList.get(0).getGoodsNo());
            TeboTenantGoodsNoPriceDO tenantGoods = teboTenantGoodsNoPriceMapper.selectOne(queryWrapper);
            if (ObjectUtil.isEmpty(tenantGoods)) {
                return true;
            }
            // 门店应分安装金额
            Integer shopAmount = tenantGoods.getShopFee();
            // 商应分到金额 25.4.3 新增 18块低佣金
//            Integer tenantAmount = teboMallOrderDO.getOrderAmount() - shopAmount - 1800;
            // 25.6.19 取消门店低佣金提取
            Integer tenantAmount = teboMallOrderDO.getOrderAmount() - shopAmount;
            // 插入分账详情
            TeboGoodsOrderSplitDO teboGoodsOrderSplitDO = new TeboGoodsOrderSplitDO();
            teboGoodsOrderSplitDO.setId(SnowFlakeUtil.nextId());
            teboGoodsOrderSplitDO.setOrderId(teboMallOrderDO.getId());
            teboGoodsOrderSplitDO.setOrderNo(teboMallOrderDO.getOrderNo());
            // 发送延迟消息修改商的费用
            Map<String, Object> param = new HashMap<>();
            param.put("orderNo", teboMallOrderDO.getOrderNo());
            param.put("orderId", teboMallOrderDO.getId());
            redisDelayQueueUtil.addDelayQueue(param, 3, TimeUnit.SECONDS, RedisDelayQueueEnum.TEBO_MALL_ORDER_FEE_TIMEOUT.getCode());
            teboGoodsOrderSplitDO.setAmount(tenantAmount);
            teboGoodsOrderSplitDO.setWalletId(tenantGoods.getWalletId());
            teboGoodsOrderSplitDO.setType(1);
            // 商的分账计划
            teboGoodsOrderSplitMapper.insert(teboGoodsOrderSplitDO);
            teboGoodsOrderSplitDO.setId(SnowFlakeUtil.nextId());
            teboGoodsOrderSplitDO.setAmount(shopAmount);
            R<TeboShop> shopResult = shopService.getShopInfo(teboMallOrderDO.getShopId());
            if (shopResult.getCode() != 200 || ObjectUtil.isEmpty(shopResult)) {
                teboGoodsOrderSplitDO.setWalletId(0L);
            }else {
                teboGoodsOrderSplitDO.setWalletId(shopResult.getData().getWalletId());
            }
            teboGoodsOrderSplitDO.setType(2);
            // 门店安装费的分账计划
            teboGoodsOrderSplitMapper.insert(teboGoodsOrderSplitDO);
            teboGoodsOrderSplitDO.setId(SnowFlakeUtil.nextId());
            teboGoodsOrderSplitDO.setAmount(1800);
            // 25.6.19 取消门店低佣金提取
            teboGoodsOrderSplitDO.setAmount(0);
            teboGoodsOrderSplitDO.setType(3);
            // 门店低佣金的分账计划
            teboGoodsOrderSplitMapper.insert(teboGoodsOrderSplitDO);
            // 7.22 新逻辑，如果核销的卡券类型为自主购买，则改为门店推广，实行退60或奖励 60
            changeCouponType(teboMallOrderDO.getId(), teboMallOrderDO.getShopId(), teboMallOrderDO.getUnionid());
//            // 新的分佣逻辑, 分佣 13.9
            // 25.7.17 改为核销后分佣 13.9
//            // 专供品订单，更新优惠券为未使用
//            LambdaQueryWrapper<TeboGoodsOrderCouponDO> couponWrapper = new LambdaQueryWrapper<>();
//            couponWrapper.eq(TeboGoodsOrderCouponDO::getOrderId, teboMallOrderDO.getId());
//            couponWrapper.eq(TeboGoodsOrderCouponDO::getAmount, 3000);
//            List<TeboGoodsOrderCouponDO> couponDOList = teboGoodsOrderCouponMapper.selectList(couponWrapper);
//            // 卡券不为空，则有13.9分佣
//            if (!ObjectUtils.isEmpty(couponDOList)) {
//                // 所属门店钱包
//                LambdaQueryWrapper<TeboGoodsOrderSplitDO> splitWrapper = new LambdaQueryWrapper<>();
//                splitWrapper.eq(TeboGoodsOrderSplitDO::getOrderId, teboMallOrderDO.getId());
//                splitWrapper.eq(TeboGoodsOrderSplitDO::getType, 2);
//                TeboGoodsOrderSplitDO splitDO = teboGoodsOrderSplitMapper.selectOne(splitWrapper);
//                if (ObjectUtils.isEmpty(splitDO)) {
//                    return true;
//                }
//                TeboWalletPlanDetailDTO detail = new TeboWalletPlanDetailDTO();
//                detail.setWalletId(splitDO.getWalletId());
//                detail.setAmount(1390);
//                detail.setFlowType(14);
//                detail.setDesc(teboMallOrderDO.getCustomerPhone()+ "-玖玖电池款");
//                TeboWalletPlanDTO teboWalletPlanDTO = new TeboWalletPlanDTO();
//                teboWalletPlanDTO.setOrderNo(couponDOList.get(0).getOrderNo());
//                teboWalletPlanDTO.setDetails(Collections.singletonList(detail));
//                remoteWalletService.splitPlan(teboWalletPlanDTO);
//                remoteWalletService.startSplitPlan(teboWalletPlanDTO);
//                // 调用柯峰接口
//                TeboSpecialGoodsVerificationDTO dto = new TeboSpecialGoodsVerificationDTO();
//                R<TeboShop> shopRes = shopService.getShopInfo(teboMallOrderDO.getShopId());
//                if (shopRes.getCode() == 200 && ObjectUtil.isNotEmpty(shopRes.getData())) {
//                    dto.setCloudShopId(shopRes.getData().getCloudShopId());
//                    dto.setCouponCode(couponDOList.get(0).getUniqueCode());
//                    dto.setOrderCode(teboMallOrderDO.getOrderNo());
//                    dto.setAccountId(teboMallOrderDO.getUnionid());
//                    dto.setPhoneNumber(teboMallOrderDO.getCustomerPhone());
//                    rewardsPointUtil.placeOrderVerified(dto);
//                    TeboCouponMallOrderDTO orderDTO = new TeboCouponMallOrderDTO();
//                    orderDTO.setCouponCode(couponDOList.get(0).getUniqueCode());
//                    orderDTO.setShopId(teboMallOrderDO.getShopId());
//                    orderDTO.setShopName(teboMallOrderDO.getShopName());
//                    orderDTO.setShopPhone(shopRes.getData().getPhoneNumber());
//                    orderDTO.setBatteryType("玖玖电池");
//                    orderDTO.setBatteryModel("玖玖");
//                    orderDTO.setBatteryNum(1);
//                    orderDTO.setBatteryCode(teboMallOrderDO.getOrderNo());
//                    remoteQueueOrderService.signRecordNew(orderDTO);
//                }
//
//            }
        }
        return true;
    }


    private void changeCouponType(Long orderId, Long shopId, String unionId) {
        log.info("changeCouponType orderId {} shopId {} unionId {}", orderId, shopId, unionId);
        Integer type = orderMapper.selectOrderTypeByOrderId(orderId);
        log.info("changeCouponType type {}", type);
        // 不是自主购买的，忽略掉
        if (type != 0) {
            log.info("changeCouponType type no");
            return;
        }
        // 如果卡券是自主购买，则需要手动处理下
        // 将购买记录设置为门店分享
        // 调用冻结 60
        R<TeboShop> shopR = shopService.getShopInfo(shopId);
        if (ObjectUtil.isNotEmpty(shopR) && ObjectUtil.isNotEmpty(shopR.getData())) {
            TeboShop teboShop = shopR.getData();
            String phoneNumber = teboShop.getPhoneNumber();
            Long tenantId = teboShop.getTenantId();
            if (ObjectUtil.isNotEmpty(tenantId)) {
                R<TeboPartnerInfoVO> partnerInfoVOR = remotePartnerService.getById(tenantId);
                TeboPartnerInfoVO teboPartnerInfoVO = partnerInfoVOR.getData();
                if (ObjectUtil.isNotEmpty(teboPartnerInfoVO)) {
                    String partnerNumber = teboPartnerInfoVO.getLinkPhone();
                    log.info("changeCouponType orderId {} phoneNumber {} partnerNumber {}", orderId, phoneNumber, partnerNumber);
                    // 更新推广门店和上级电话
                    orderMapper.updateGiftOrder(orderId, phoneNumber, partnerNumber);
                }
            }
            // 调用柯峰返款接口
            R<TeboConsumer> customerR = remoteCustomerService.selectByUnionId(unionId);
            TeboCouponGiftOrderDTO orderDTO = teboMallOrderMapper.selectGiftPackOrderByOrderId(orderId);
            CouponCommissionShareDTO commissionShareDTO = new CouponCommissionShareDTO();
            commissionShareDTO.setUnionId(unionId);
            commissionShareDTO.setOrderNo(orderDTO.getOrderNo());
            commissionShareDTO.setOrderId(orderDTO.getId());
            commissionShareDTO.setPhoneNumber(customerR.getData().getPhoneNumber());
            commissionShareDTO.setPacketId(orderDTO.getPackId());
            /**
             * 根据推广人手机号查询推广人门店id
             */
            if (ObjectUtil.isNotEmpty(shopR.getData())){
                commissionShareDTO.setCloudShopId(shopR.getData().getCloudShopId());
            }
            log.info("payGiftPackAfterNotify saleCardAmount param :{}",JSONObject.toJSONString(commissionShareDTO));
            rewardsPointService.saleCardAmount(commissionShareDTO);
        }
    }

    @Override
    public Boolean payWarrantyExtensionOrderAfterNotify(TeboMallOrderDTO mallOrder) {
        log.info("mall order pay notify param :{}", JSONObject.toJSONString(mallOrder));
        if (StringUtils.isEmpty(mallOrder.getOrderNo())){
            throw new ServiceException("订单编号为空");
        }
        TeboInsurancePolicyOrderDO teboInsurancePolicyOrderDO = insurancePolicyOrderManager.getDetailByNo(mallOrder.getOrderNo());
        if (ObjectUtil.isEmpty(teboInsurancePolicyOrderDO)){
            throw new ServiceException("订单不存在");
        }
        teboInsurancePolicyOrderDO.setOrderStatus(InsuranceMallOrderStatusEnum.PENDING_EFFECTIVENESS.getCode());
        teboInsurancePolicyOrderDO.setPayTime(LocalDateTime.now());
        insurancePolicyOrderManager.updateById(teboInsurancePolicyOrderDO);
        /**
         * 如果是商城订单,保费支付成功以后更新商城订单的channel为 4 延保订单
         */
        if (teboInsurancePolicyOrderDO.getSource() == 1){
            TeboOrderGoodsUpdateDTO updateDTO = new TeboOrderGoodsUpdateDTO();
            updateDTO.setId(teboInsurancePolicyOrderDO.getParentOrderId());
            updateDTO.setChannel(MallOrderChannelEnum.WARRANTY_EXTENSION.getCode());
            teboMallOrderManager.updateMallOrderStatus(updateDTO);
        }
        /**
         * 如果是工单,支付成功回调调用誉好接口生成保单
         */
        if (teboInsurancePolicyOrderDO.getSource() == 2){
            /**
             * 将工单类型修改成保险单
             */
            R<ServiceOrderDTO>  serviceOrderDTOResult = remoteQueueOrderService.getServiceOrderById(teboInsurancePolicyOrderDO.getParentOrderId());
            ServiceOrderDTO serviceOrderDTO = serviceOrderDTOResult.getData();
            remoteQueueOrderService.updateOrderType(serviceOrderDTO.getOrderNo());
            TeboInsurancePolicyDTO teboInsurancePolicyDTO = new TeboInsurancePolicyDTO();
            teboInsurancePolicyDTO.setProductCode(teboInsurancePolicyOrderDO.getProductCode());
            teboInsurancePolicyDTO.setBatteryCodeImageUrl(teboInsurancePolicyOrderDO.getBatteryCodeImageUrl());
            teboInsurancePolicyDTO.setBatteryImageUrl(teboInsurancePolicyOrderDO.getBatteryImageUrl());
            teboInsurancePolicyDTO.setOrderId(teboInsurancePolicyOrderDO.getParentOrderId());
            teboInsurancePolicyDTO.setProductCode(teboInsurancePolicyOrderDO.getProductCode());
            insurancePolicyService.createServiceOrderInsurancePolicyOrder(teboInsurancePolicyDTO);
        }
        return true;
    }

    @Override
    public R<WechatPrepayResponse> mallOrderPrePay(Long orderId) {
        TeboMallOrderVO teboMallOrderVO = teboMallOrderManager.getDetailById(orderId);
        if (ObjectUtil.isEmpty(teboMallOrderVO)){
            throw new GlobalException("订单不存在");
        }
        if (teboMallOrderVO.getOrderStatus() != 1) {
            throw new GlobalException("只有待支付订单能支付");
        }
        R<TeboConsumer> result = remoteCustomerService.selectByUnionId(teboMallOrderVO.getUnionid());
        if (ObjectUtil.isEmpty(result) || result.getCode() != 200 || ObjectUtil.isEmpty(result.getData()) ){
            throw new GlobalException(result.getMsg());
        }
        PayOrderDTO payOrderDTO = new PayOrderDTO();
        payOrderDTO.setSource(1);
        payOrderDTO.setChannel(teboMallOrderVO.getChannel());
        payOrderDTO.setBusinessType(PayWechatOrderBusinessTypeEnum.MALL_ORDER.getCode());
        payOrderDTO.setOrderNo(teboMallOrderVO.getOrderNo());
        payOrderDTO.setAmount(MoneyUtil.yuanToFen(teboMallOrderVO.getOrderAmount()));
        payOrderDTO.setOpenId(result.getData().getOpenid());
        payOrderDTO.setDescription("商城订单支付");
        payOrderDTO.setShopId(teboMallOrderVO.getShopId());
        payOrderDTO.setShopName(teboMallOrderVO.getShopName());
        payOrderDTO.setTenantId(teboMallOrderVO.getTenantId());
        payOrderDTO.setReceiver(getReceiverTypeByOrderId(orderId));
        log.info("payOrderDTO={}", JSON.toJSONString(payOrderDTO));
        return remotePayService.wechatPrePay(payOrderDTO);
    }

    @Override
    public R<WechatPrepayResponse> mallSpecialOrderPrePay(Long orderId,String unionId) {
        TeboMallOrderDO mallOrderDO = orderMapper.selectById(orderId);
        int count = 1;
        while (count <= 5 && ObjectUtil.isEmpty(mallOrderDO)) {
            mallOrderDO = orderMapper.selectById(orderId);
            count++;
            if (ObjectUtil.isNotEmpty(mallOrderDO)){
                break;
            }
        }
        if (ObjectUtil.isEmpty(mallOrderDO)){
            throw new GlobalException("订单不存在");
        }
        if (mallOrderDO.getOrderStatus() != 1) {
            throw new GlobalException("只有待支付订单能支付");
        }
        R<TeboConsumer> result = remoteCustomerService.selectByUnionId(mallOrderDO.getUnionid());
        if (ObjectUtil.isEmpty(result) || result.getCode() != 200 || ObjectUtil.isEmpty(result.getData()) ){
            throw new GlobalException(result.getMsg());
        }
        TeboConsumer consumer = remoteCustomerService.selectByUnionId(unionId).getData();
        PayOrderDTO payOrderDTO = new PayOrderDTO();
        payOrderDTO.setSource(1);
        payOrderDTO.setBusinessType(11);
        payOrderDTO.setOrderNo(TeboNumberGenerator.generateMallSpecialOrderNo() + "A");
        mallOrderDO.setOrderNo(payOrderDTO.getOrderNo());
        orderMapper.updateById(mallOrderDO);
        payOrderDTO.setAmount(mallOrderDO.getOrderAmount());
        payOrderDTO.setOpenId(consumer.getOpenid());
        payOrderDTO.setUnionId(unionId);
        payOrderDTO.setDescription("泰博出行_" + mallOrderDO.getOrderNo());
        payOrderDTO.setReceiver(3);
        log.info("groupOrderPrePay={}", JSON.toJSONString(payOrderDTO));
        return remotePayService.appletPay(payOrderDTO);
    }

    @Override
    public Integer getReceiverTypeByOrderId(Long orderId) {
        TeboMallOrderDO mallOrderDO = orderMapper.selectById(orderId);
        List<TeboOrderGoodsVO> goodsList = orderGoodsManager.getOrderGoodsByOrderId(orderId);
        if (CollectionUtils.isEmpty(goodsList)) {
            throw new GlobalException("订单商品不存在");
        }
        if (mallOrderDO.getOrderType() == 12){
            return 1;
        }
        Long goodId = goodsList.get(0).getGoodsId();

        TeboGoodsVO teboGoodsVO = teboGoodsManager.selectById(goodId);
        if (ObjectUtil.isEmpty(teboGoodsVO)){
            throw new GlobalException("订单商品不存在");
        }
        List<String> secondCategoryList = Arrays.asList(secondCategoryId.split(","));
        // 未找到系统配置，默认支付到门店
        if (CollectionUtils.isEmpty(secondCategoryList)){
            return 1;
        }
        if (secondCategoryList.contains(teboGoodsVO.getSecondCategoryId().toString())){
            return 2;
        }else {
            return 1;
        }
    }

    @Override
    public R<WechatPrepayResponse> insuranceOrderPrePay(Long orderId,String openId) {
        TeboInsurancePolicyOrderDO insurancePolicyOrderDO = insurancePolicyOrderManager.selectById(orderId);
        if (ObjectUtil.isEmpty(insurancePolicyOrderDO)){
            throw new GlobalException("订单不存在");
        }
        if (insurancePolicyOrderDO.getOrderStatus() != 1) {
            throw new GlobalException("只有待支付订单能支付");
        }
        PayOrderDTO payOrderDTO = new PayOrderDTO();
        payOrderDTO.setSource(1);
        if (insurancePolicyOrderDO.getSource() == 1){
            TeboMallOrderVO teboMallOrderVO = teboMallOrderManager.getDetailById(insurancePolicyOrderDO.getParentOrderId());
            R<TeboConsumer> result = remoteCustomerService.selectByUnionId(teboMallOrderVO.getUnionid());
            if (ObjectUtil.isEmpty(result) || result.getCode() != 200 || ObjectUtil.isEmpty(result.getData()) ){
                throw new GlobalException(result.getMsg());
            }
            payOrderDTO.setChannel(teboMallOrderVO.getChannel());
            payOrderDTO.setOpenId(result.getData().getOpenid());
            payOrderDTO.setShopId(teboMallOrderVO.getShopId());
            payOrderDTO.setShopName(teboMallOrderVO.getShopName());
            payOrderDTO.setTenantId(teboMallOrderVO.getTenantId());
        }else {
            payOrderDTO.setChannel(4);
            R<ServiceOrderDTO> serviceOrderDTOR = remoteQueueOrderService.getServiceOrderById(insurancePolicyOrderDO.getParentOrderId());
            payOrderDTO.setOpenId(openId);
            payOrderDTO.setShopId(serviceOrderDTOR.getData().getShopId());
            payOrderDTO.setShopName(serviceOrderDTOR.getData().getShopName());
            payOrderDTO.setTenantId(serviceOrderDTOR.getData().getTenantId());
        }
        payOrderDTO.setBusinessType(PayWechatOrderBusinessTypeEnum.WARRANTY_EXTENSION_ORDER.getCode());
        payOrderDTO.setOrderNo(insurancePolicyOrderDO.getOrderNo());
        payOrderDTO.setAmount(insurancePolicyOrderDO.getOrderAmount());
        payOrderDTO.setDescription("保费订单支付");
        payOrderDTO.setReceiver(1);
        log.info("payOrderDTO={}", JSON.toJSONString(payOrderDTO));
        return remotePayService.wechatPrePay(payOrderDTO);
    }
}
