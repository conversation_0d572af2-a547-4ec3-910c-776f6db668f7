package com.tebo.mall.service.goods.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.mall.api.domain.view.TeboSpecialSupplyDistrictGoodsVO;
import com.tebo.mall.entity.TeboSpecialSupplyDistrictGoodsDO;
import com.tebo.mall.entity.TeboSpecialSupplyGoodsDO;
import com.tebo.mall.mapper.TeboSpecialSupplyDistrictGoodsMapper;
import com.tebo.mall.mapper.special.TeboSpecialSupplyGoodsMapper;
import com.tebo.mall.service.goods.special.TeboSpecialSupplyDistrictGoodsService;
import com.tebo.mall.web.domain.dto.goods.special.TeboSpecialSupplyDistrictGoodsQueryDTO;
import com.tebo.system.api.RemoteQrCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TeboSpecialSupplyDistrictGoodsServiceImpl implements TeboSpecialSupplyDistrictGoodsService {
    @Resource
    private TeboSpecialSupplyDistrictGoodsMapper teboSpecialSupplyDistrictGoodsMapper;

    @Resource
    private RemoteQrCodeService remoteQrCodeService;
    @Resource
    private TeboSpecialSupplyGoodsMapper teboSpecialSupplyGoodsMapper;

    @Override
    public List<TeboSpecialSupplyDistrictGoodsVO> listGoods(TeboSpecialSupplyDistrictGoodsQueryDTO queryDTO) {
        List<TeboSpecialSupplyDistrictGoodsDO> list = teboSpecialSupplyDistrictGoodsMapper.list(queryDTO);
        if (CollectionUtil.isEmpty(list)){
            return null;
        }
        // 获取商品编码
        List<String> goodsNoList = list.stream().map(TeboSpecialSupplyDistrictGoodsDO::getGoodsNo).collect(Collectors.toList());
        LambdaQueryWrapper<TeboSpecialSupplyGoodsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TeboSpecialSupplyGoodsDO::getGoodsNo, goodsNoList);
        List<TeboSpecialSupplyGoodsDO> goodsDOList = teboSpecialSupplyGoodsMapper.selectList(wrapper);
        Map<String, TeboSpecialSupplyGoodsDO> goodsDOMap = goodsDOList.stream().collect(Collectors.toMap(TeboSpecialSupplyGoodsDO::getGoodsNo, v -> v));
        List<TeboSpecialSupplyDistrictGoodsVO> finalResult = new ArrayList<>();
        list.forEach(item ->{
            TeboSpecialSupplyDistrictGoodsVO teboSpecialSupplyDistrictGoodsVO = new TeboSpecialSupplyDistrictGoodsVO();
            BeanConvert.copy(item,teboSpecialSupplyDistrictGoodsVO);
            if (!ObjectUtils.isEmpty(item.getOriginPrice())){
                teboSpecialSupplyDistrictGoodsVO.setOriginPrice(MoneyUtil.fenToYuan(item.getOriginPrice()));
            }
            if (!ObjectUtils.isEmpty(item.getSalePrice())){
                teboSpecialSupplyDistrictGoodsVO.setSalePrice(MoneyUtil.fenToYuan(item.getSalePrice()));
            }
            if (!ObjectUtils.isEmpty(item.getTradeInPrice())){
                teboSpecialSupplyDistrictGoodsVO.setTradeInPrice(MoneyUtil.fenToYuan(item.getTradeInPrice()));
            }
            TeboSpecialSupplyGoodsDO goodsDO = goodsDOMap.get(item.getGoodsNo());
            if (!ObjectUtil.isEmpty(goodsDO)){
                teboSpecialSupplyDistrictGoodsVO.setMainPic(goodsDO.getMainPic());
                teboSpecialSupplyDistrictGoodsVO.setGoodsName(goodsDO.getGoodsName());
            }
            finalResult.add(teboSpecialSupplyDistrictGoodsVO);
        });
        return finalResult;
    }

    @Override
    public TeboSpecialSupplyDistrictGoodsVO getGoodsDetail(Long id) {
        TeboSpecialSupplyDistrictGoodsDO teboSpecialSupplyDistrictGoodsDO = teboSpecialSupplyDistrictGoodsMapper.selectById(id);
        int count = 1;
        while (count <= 5 && ObjectUtil.isEmpty(teboSpecialSupplyDistrictGoodsDO)) {
            teboSpecialSupplyDistrictGoodsDO = teboSpecialSupplyDistrictGoodsMapper.selectById(id);
            count++;
            if (ObjectUtil.isNotEmpty(teboSpecialSupplyDistrictGoodsDO)){
                break;
            }
        }
        if (ObjectUtils.isEmpty(teboSpecialSupplyDistrictGoodsDO)){
            return null;
        }
        LambdaQueryWrapper<TeboSpecialSupplyGoodsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TeboSpecialSupplyGoodsDO::getGoodsNo, teboSpecialSupplyDistrictGoodsDO.getGoodsNo());
        List<TeboSpecialSupplyGoodsDO> goodsDOList = teboSpecialSupplyGoodsMapper.selectList(wrapper);

        if (!CollectionUtil.isEmpty(goodsDOList)){
            TeboSpecialSupplyGoodsDO goodsDO = goodsDOList.get(0);
            teboSpecialSupplyDistrictGoodsDO.setGoodsName(goodsDO.getGoodsName());
            teboSpecialSupplyDistrictGoodsDO.setMainPic(goodsDO.getMainPic());
            teboSpecialSupplyDistrictGoodsDO.setGoodsDetail(goodsDO.getGoodsDetail());
        }
        R<String> qrCode = remoteQrCodeService.generateGoodsCode(id);
        TeboSpecialSupplyDistrictGoodsVO teboSpecialSupplyDistrictGoodsVO = new TeboSpecialSupplyDistrictGoodsVO();
        BeanConvert.copy(teboSpecialSupplyDistrictGoodsDO,teboSpecialSupplyDistrictGoodsVO);
        if (!ObjectUtils.isEmpty(teboSpecialSupplyDistrictGoodsDO.getOriginPrice())){
            teboSpecialSupplyDistrictGoodsVO.setOriginPrice(MoneyUtil.fenToYuan(teboSpecialSupplyDistrictGoodsDO.getOriginPrice()));
        }
        if (!ObjectUtils.isEmpty(teboSpecialSupplyDistrictGoodsDO.getTradeInPrice())){
            teboSpecialSupplyDistrictGoodsVO.setTradeInPrice(MoneyUtil.fenToYuan(teboSpecialSupplyDistrictGoodsDO.getTradeInPrice()));
        }
        if (!ObjectUtils.isEmpty(teboSpecialSupplyDistrictGoodsDO.getSalePrice())){
            teboSpecialSupplyDistrictGoodsVO.setSalePrice(MoneyUtil.fenToYuan(teboSpecialSupplyDistrictGoodsDO.getSalePrice()));
        }
        if (ObjectUtil.isNotEmpty(qrCode) && StringUtils.isNotEmpty(qrCode.getData())){
            teboSpecialSupplyDistrictGoodsVO.setGoodsQrCode(qrCode.getData());
        }
        return teboSpecialSupplyDistrictGoodsVO;
    }

    @Override
    public int goodsCount(TeboSpecialSupplyDistrictGoodsQueryDTO queryDTO) {
        return teboSpecialSupplyDistrictGoodsMapper.goodsCount(queryDTO);
    }

    @Override
    public Long getShopIdByUnionId(String unionId) {
        return teboSpecialSupplyDistrictGoodsMapper.getShopIdByUnionId(unionId);
    }


}