package com.tebo.mall.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tebo.common.core.utils.DateUtils;
import com.tebo.common.util.DataUtils;
import com.tebo.mall.entity.TeboGoodsOrderCouponDO;
import com.tebo.mall.entity.TeboInsurancePolicyOrderDO;
import com.tebo.mall.enums.MallOrderCancelTypeEnum;
import com.tebo.mall.enums.MallOrderStatusEnum;
import com.tebo.mall.manager.order.TeboInsurancePolicyOrderManager;
import com.tebo.mall.manager.order.TeboMallOrderManager;
import com.tebo.mall.mapper.TeboGoodsOrderCouponMapper;
import com.tebo.mall.redisson.queue.RedisDelayQueueHandle;
import com.tebo.mall.service.order.TeboMallOrderService;
import com.tebo.mall.util.DistributedLock;
import com.tebo.mall.util.TeboMallCommonCacheConstant;
import com.tebo.mall.web.domain.dto.TeboInsurancePolicyQueryDTO;
import com.tebo.mall.web.domain.dto.order.inner.TeboOrderGoodsUpdateDTO;
import com.tebo.mall.web.domain.dto.order.outer.TeboMallOrderQueryInnerDTO;
import com.tebo.mall.web.domain.vo.order.TeboInsurancePolicyOrderVO;
import com.tebo.mall.web.domain.vo.order.TeboMallOrderVO;
import com.tebo.rescue.api.RemoteCouponService;
import com.tebo.rescue.api.domain.dto.OrderCouponDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ZhangFan
 * @date : 2024/3/9 14:14
 * @Desc : 商城订单超时处理
 */
@Slf4j
@Component
public class TeboMallOrderPayTimeout implements RedisDelayQueueHandle<Map> {
    @Resource
    private TeboMallOrderManager teboMallOrderManager;
    @Resource
    private TeboMallOrderService orderService;
    @Resource
    private TeboInsurancePolicyOrderManager teboInsurancePolicyOrderManager;
    @Resource
    private DistributedLock distributedLock;
    @Resource
    private TeboGoodsOrderCouponMapper teboGoodsOrderCouponMapper;
    @Resource
    private RemoteCouponService remoteCouponService;

    @Override
    public void execute(Map map) {
        if (!distributedLock.tryLock(TeboMallCommonCacheConstant.getScheduledTaskPrefix("TeboMallOrderPayTimeout:execute"), 10, 30)) {
            return;
        }
        log.info("商城订单支付超时延迟消息:{}", map);
        Object orderId = (Object)map.get("orderId");
        if (ObjectUtil.isEmpty(orderId)){
           return;
        }
        TeboMallOrderVO orderVO = teboMallOrderManager.getDetailById(Long.parseLong(orderId.toString()));
        if (orderVO!= null && orderVO.getOrderStatus() != 1){
            return;
        }
        TeboOrderGoodsUpdateDTO updateDTO = new TeboOrderGoodsUpdateDTO();
        updateDTO.setId(Long.parseLong(orderId.toString()));
        updateDTO.setOrderStatus(MallOrderStatusEnum.CANCEL.getCode());
        updateDTO.setCancelType(MallOrderCancelTypeEnum.TIME_OUT_CANCEL.getCode());
        updateDTO.setCancelTime(LocalDateTime.now());
        teboMallOrderManager.updateMallOrderStatus(updateDTO);
        if (orderVO.getOrderNo().startsWith("TBCXA")) {
            // 专供品订单，更新优惠券为未使用
            LambdaQueryWrapper<TeboGoodsOrderCouponDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TeboGoodsOrderCouponDO::getOrderId, orderVO.getId());
            List<TeboGoodsOrderCouponDO> couponDOList = teboGoodsOrderCouponMapper.selectList(queryWrapper);
            log.info("商城订单支付超时延迟消息 couponDOList:{}", couponDOList);
            if (ObjectUtils.isEmpty(couponDOList)) {
                return;
            }
            // 清除券的关联
            teboGoodsOrderCouponMapper.deleteBatchIds(couponDOList.stream().map(TeboGoodsOrderCouponDO::getId).collect(Collectors.toList()));
            List<String> couponCodeList = couponDOList.stream().map(TeboGoodsOrderCouponDO::getUniqueCode).collect(Collectors.toList());
            log.info("商城订单支付超时延迟消息 couponCodeList:{}", couponCodeList);
            couponCodeList.stream().forEach(couponCode -> {
                // 更新券状态
                OrderCouponDTO orderCouponDTO = new OrderCouponDTO();
                orderCouponDTO.setCouponCode(couponCode);
                log.info("商城订单支付超时延迟消息 orderCouponDTO:{}", orderCouponDTO);
                remoteCouponService.updateCouponStatusNotUsed(orderCouponDTO);
                // 更新占用状态
                orderCouponDTO.setOccStatus(1);
                orderCouponDTO.setOccNumber("");
                log.info("商城订单支付超时延迟消息 orderCouponDTO:{}", orderCouponDTO);
                remoteCouponService.updateCouponOccStatus(orderCouponDTO);
            });
        }
    }

    /**
     * 兜底方案
     * 每十五分钟执行一次，将延迟消息未消费的订单取消
     */
    @Scheduled(cron = "0 0/15 * * * *")
    public void mallOrderCancelTask(){
        if (!distributedLock.tryLock(TeboMallCommonCacheConstant.getScheduledTaskPrefix("TeboMallOrderPayTimeout:mallOrderCancelTask"), 10, 30)) {
            return;
        }
        /**
         * 查询超时15分钟的订单
         */
        LocalDateTime compareTime = LocalDateTime.now().minusMinutes(15);
        List<TeboMallOrderVO> list = teboMallOrderManager.getPayTimeOutOrder(compareTime);
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<Long> orderIdList = list.stream().map(item -> item.getId()).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(orderIdList)) {
            return;
        }
        TeboOrderGoodsUpdateDTO updateDTO = new TeboOrderGoodsUpdateDTO();
        updateDTO.setIdList(orderIdList);
        updateDTO.setOrderStatus(MallOrderStatusEnum.CANCEL.getCode());
        updateDTO.setCancelType(MallOrderCancelTypeEnum.TIME_OUT_CANCEL.getCode());
        updateDTO.setCancelTime(LocalDateTime.now());
        teboMallOrderManager.updateMallOrderStatus(updateDTO);
        list.stream().forEach(item -> {
            if (item.getOrderNo().startsWith("TBCXA")) {
                // 专供品订单，更新优惠券为未使用
                LambdaQueryWrapper<TeboGoodsOrderCouponDO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TeboGoodsOrderCouponDO::getOrderId, item.getId());
                List<TeboGoodsOrderCouponDO> couponDOList = teboGoodsOrderCouponMapper.selectList(queryWrapper);
                log.info("couponDOList:{}", couponDOList);
                if (ObjectUtils.isEmpty(couponDOList)) {
                    return;
                }
                // 清除券的关联
                teboGoodsOrderCouponMapper.deleteBatchIds(couponDOList.stream().map(TeboGoodsOrderCouponDO::getId).collect(Collectors.toList()));
                List<String> couponCodeList = couponDOList.stream().map(TeboGoodsOrderCouponDO::getUniqueCode).collect(Collectors.toList());
                log.info("couponCodeList:{}", couponCodeList);
                couponCodeList.stream().forEach(couponCode -> {
                    // 更新券状态
                    OrderCouponDTO orderCouponDTO = new OrderCouponDTO();
                    orderCouponDTO.setCouponCode(couponCode);
                    // 更新占用状态
                    orderCouponDTO.setOccStatus(1);
                    orderCouponDTO.setOccNumber("");
                    log.info("mallOrderCancelTask orderCouponDTO:{}", orderCouponDTO);
                    remoteCouponService.updateCouponOccStatus(orderCouponDTO);
                });
            }
        });
    }
}
