package com.tebo.mall.web.domain.dto.order.inner;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tebo.common.core.web.page.PageDomain;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class TeboMallOrderQueryDTO extends PageDomain implements Serializable {

    private Long tenantId;
    /**
     * 门店id
     */
    private Long shopId;

    /**
     * unionid
     */

    private String unionid;

    /**
     * 订单状态 1:待支付 2:待提货,3 已完成  4 已评价 10:已取消
     */
    private Integer orderStatus;

    /**
     * 服务类型 1上门 2 到店
     */
    private Integer serviceType;

    /**
     * 下单时间
     */
    private String orderTime;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 订单id集合
     */

    private List<Long> orderIdList;



    /**
     * 核销人员名字
     */
    private String verificationNameLike;



    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate createTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate createTimeEnd;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTimeStartSecond;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTimeEndSecond;


    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate verificationTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate verificationTimeEnd;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime verificationTimeStartSecond;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime verificationTimeEndSecond;

    /**
     * 渠道
     * MallOrderChannelEnum
     */
    private Integer channel;

    /**
     * 订单类型 12 专供品订单
     * */
    private Integer orderType;

    /**
     * 门店地址
     */
    private String shopAddress;

    /**
     * 电池码信息
     */
    private String batteryNumber;

    private String areaName;


    private List<Integer> orderStatusList;
}
