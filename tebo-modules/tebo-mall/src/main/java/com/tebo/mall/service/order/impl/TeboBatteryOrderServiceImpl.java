package com.tebo.mall.service.order.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.mall.api.domain.dto.TeboMallOrderDTO;
import com.tebo.mall.entity.*;
import com.tebo.mall.enums.MallOrderStatusEnum;
import com.tebo.mall.enums.TeboGoodsCategoryEnum;
import com.tebo.mall.manager.order.TeboMallOrderGoodsManager;
import com.tebo.mall.manager.order.TeboMallOrderManager;
import com.tebo.mall.mapper.TeboGoodsOrderCouponMapper;
import com.tebo.mall.mapper.TeboGoodsOrderSplitMapper;
import com.tebo.mall.mapper.TeboSpecialSupplyDistrictGoodsMapper;
import com.tebo.mall.mapper.TeboTenantGoodsNoPriceMapper;
import com.tebo.mall.mapper.TeboBatteryOrderRefundMapper;
import com.tebo.mall.mapper.TeboMallOrderMapper;
import com.tebo.mall.mapper.category.TeboGoodsCategoryMapper;
import com.tebo.mall.domain.dto.TeboCouponGiftOrderDTO;
import com.tebo.mall.redisson.queue.RedisDelayQueueEnum;
import com.tebo.mall.redisson.queue.RedisDelayQueueUtil;
import com.tebo.mall.service.order.TeboBatteryOrderService;
import com.tebo.mall.util.DistributedLock;
import com.tebo.mall.web.domain.dto.order.inner.TeboMallOrderAddDTO;
import com.tebo.mall.web.domain.dto.order.inner.TeboMallOrderQueryDTO;
import com.tebo.mall.web.domain.dto.order.inner.TeboOrderGoodsAddDTO;
import com.tebo.mall.web.domain.dto.order.inner.TeboOrderGoodsUpdateDTO;
import com.tebo.mall.web.domain.dto.order.outer.TeboMallSpecialOrderAddOuterDTO;
import com.tebo.mall.web.domain.dto.order.TeboBatteryOrderRefundDTO;
import com.tebo.mall.web.domain.vo.order.TeboOrderGoodsVO;
import com.tebo.rescue.api.RemoteCouponService;
import com.tebo.rescue.api.domain.dto.OrderCouponDTO;
import com.tebo.rescue.api.domain.dto.TeboCouponCustomerDTO;
import com.tebo.rescue.api.domain.dto.TeboRemoteGiftOrderDTO;
import com.tebo.system.api.RemoteCustomerService;
import com.tebo.system.api.RemoteShopService;
import com.tebo.system.api.model.TeboConsumer;
import com.tebo.system.api.model.TeboShop;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 电池订单服务
 */
@Slf4j
@Service
public class TeboBatteryOrderServiceImpl implements TeboBatteryOrderService {
    @Resource
    private RemoteCustomerService customerService;
    @Resource
    private RemoteShopService remoteShopService;
    @Resource
    private RemoteCouponService remoteCouponService;

    @Resource
    private TeboSpecialSupplyDistrictGoodsMapper districtGoodsMapper;
    @Resource
    private TeboTenantGoodsNoPriceMapper teboTenantGoodsNoPriceMapper;

    @Resource
    private TeboGoodsCategoryMapper teboGoodsCategoryMapper;
    @Resource
    private TeboGoodsOrderCouponMapper teboGoodsOrderCouponMapper;
    @Resource
    private TeboMallOrderManager teboMallOrderManager;
    @Resource
    private TeboMallOrderGoodsManager orderGoodsManager;
    @Resource
    private TeboGoodsOrderSplitMapper teboGoodsOrderSplitMapper;

    @Resource
    private RedisDelayQueueUtil redisDelayQueueUtil;

    @Resource
    private DistributedLock distributedLock;

    @Resource
    private TeboBatteryOrderRefundMapper teboBatteryOrderRefundMapper;

    @Resource
    private TeboMallOrderMapper teboMallOrderMapper;

    @Override
    public Map<String, Object> createOrderWithCoupon(TeboMallSpecialOrderAddOuterDTO outDTO) {
        log.info("[TeboBatteryOrderService] [createOrderWithCoupon] dto {}", outDTO);
        TeboMallOrderAddDTO innerAddDto = new TeboMallOrderAddDTO();
        BeanConvert.copy(outDTO, innerAddDto);
        innerAddDto.setId(SnowFlakeUtil.nextId());
        innerAddDto.setOrderStatus(MallOrderStatusEnum.TO_BE_PAID.getCode());
        /**
         * 根据unionid查询用户信息
         */
        R<TeboConsumer> consumerRes = customerService.selectByUnionId(outDTO.getUnionid());
        if (consumerRes.getCode() != 200) {
            throw new ServiceException(consumerRes.getMsg());
        }
        if (!ObjectUtils.isEmpty(consumerRes.getData())) {
            TeboConsumer consumer = consumerRes.getData();
            innerAddDto.setCustomerName(consumer.getNickName());
            innerAddDto.setCustomerPhone(consumer.getPhoneNumber());
        }
        /**
         * 根据shopId查询门店信息
         */
        R<TeboShop> shopRes = remoteShopService.getShopInfo(outDTO.getShopId());
        if (shopRes.getCode() != 200 || ObjectUtil.isEmpty(shopRes)) {
            throw new GlobalException("店铺信息不存在");
        }
        TeboShop teboShop = shopRes.getData();
        if (ObjectUtils.nullSafeEquals(teboShop.getVip(), 0)) {
            throw new ServiceException("当前门店为非超服店，无法购买");
        }
        innerAddDto.setShopName(teboShop.getShopName());
        innerAddDto.setTenantId(teboShop.getTenantId());
        innerAddDto.setTenantName(teboShop.getTenantName());
        innerAddDto.setShopAddress(teboShop.getAddress());
        /**
         * 商品信息（todo 查询还用什么用？）
         */
        TeboSpecialSupplyDistrictGoodsDO districtGoodsDO = districtGoodsMapper.selectById(outDTO.getGoodsId());
        if (ObjectUtils.isEmpty(districtGoodsDO)) {
            throw new ServiceException("未查到商品信息，无法下单！");
        }
        // 判断专供品是否配置服务费
        String goodsNo = districtGoodsDO.getGoodsNo();
        Long tenantId = teboShop.getTenantId();
        LambdaQueryWrapper<TeboTenantGoodsNoPriceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeboTenantGoodsNoPriceDO::getTenantId, tenantId)
                .eq(TeboTenantGoodsNoPriceDO::getGoodsNo, goodsNo);
        Long goodsCount = teboTenantGoodsNoPriceMapper.selectCount(queryWrapper);
        if (goodsCount == 0) {
            throw new GlobalException("玖玖电池未维护服务费，暂不支持此交易！");
        }
        //商品总数
        int totalGoodsNumber = 1;
        innerAddDto.setTotalGoodsNumber(totalGoodsNumber);
        List<TeboOrderGoodsAddDTO> innerOrderGoodsList = new ArrayList<>();
        TeboOrderGoodsAddDTO goodsAddInnerDTO = new TeboOrderGoodsAddDTO();
        goodsAddInnerDTO.setOrderId(innerAddDto.getId());
        goodsAddInnerDTO.setGoodsId(outDTO.getGoodsId());
        if (ObjectUtil.isNotEmpty(districtGoodsDO)) {
            BeanConvert.copy(districtGoodsDO, goodsAddInnerDTO);
        }
        goodsAddInnerDTO.setCreateTime(null);
        goodsAddInnerDTO.setGoodsNumber(1);
//        TeboGoodsBrandDO teboGoodsBrandDO = teboGoodsBrandMapper.selectById(districtGoodsDO.getBrandId());
        goodsAddInnerDTO.setBrandName("天能");
        TeboGoodsCategoryDO teboGoodsCategoryDO = teboGoodsCategoryMapper.selectById(districtGoodsDO.getCategoryId());
        goodsAddInnerDTO.setCategoryName(teboGoodsCategoryDO.getCategoryName());
        TeboGoodsCategoryDO secondCategoryDO = teboGoodsCategoryMapper.selectById(districtGoodsDO.getSecondCategoryId());
        goodsAddInnerDTO.setSecondCategoryName(secondCategoryDO.getCategoryName());
        if (ObjectUtil.isNotEmpty(districtGoodsDO.getBusinessCategory())) {
            goodsAddInnerDTO.setBusinessCategory(TeboGoodsCategoryEnum.getGoodsCategoryEnum(districtGoodsDO.getBusinessCategory()));
        }
        /**
         * 商品价格
         */
        if (outDTO.getPriceType() == 1) {
            goodsAddInnerDTO.setPrice(districtGoodsDO.getTradeInPrice());
        } else if (outDTO.getPriceType() == 2) {
            goodsAddInnerDTO.setPrice(districtGoodsDO.getSalePrice());
        }
        goodsAddInnerDTO.setId(SnowFlakeUtil.nextId());
        goodsAddInnerDTO.setPriceType(outDTO.getPriceType());
        innerOrderGoodsList.add(goodsAddInnerDTO);
        //订单金额
        Integer orderAmount = goodsAddInnerDTO.getPrice();
        // 优惠券金额
        Integer parAmount = 5000;
        // 最终的订单金额
        orderAmount -= parAmount;
        // todo 下面的判断逻辑不需要了，但是支付成功后需要插入两张订单关联的券
        innerAddDto.setOrderAmount(orderAmount.toString());
        innerAddDto.setOrderAmount("1");
        innerAddDto.setParValue(parAmount);
        innerAddDto.setOrderNo(innerAddDto.getId().toString());
        innerAddDto.setOrderType(12);
        innerAddDto.setServiceType(1);
        innerAddDto.setTotalGoodsNumber(1);
        //创建订单
        teboMallOrderManager.createOrder(innerAddDto);
        //创建订单商品
        if (CollectionUtil.isNotEmpty(innerOrderGoodsList)) {
            orderGoodsManager.createOrderGoods(innerOrderGoodsList);
        }
        Map<String, String> param = new HashMap<>();
        param.put("orderId", innerAddDto.getId().toString());
        param.put("remark", "商城订单支付超时，自动取消订单");
        // 添加订单支付超时，自动取消订单延迟队列。为了测试效果，延迟10秒钟
        redisDelayQueueUtil.addDelayQueue(param, 5, TimeUnit.MINUTES, RedisDelayQueueEnum.TEBO_MALL_ORDER_PAYMENT_TIMEOUT.getCode());
        Map<String, Object> result = new HashMap<>();
        result.put("orderId", innerAddDto.getId());
        result.put("needPay", 1);
        return result;
    }

    @Override
    public Boolean payBatteryOrderAfterNotify(TeboMallOrderDTO mallOrder) {
        log.info("payBatteryOrderAfterNotify param :{}", mallOrder);
        if (StringUtils.isEmpty(mallOrder.getOrderNo())){
            throw new ServiceException("订单编号为空");
        }
        TeboMallOrderDO teboMallOrderDO = teboMallOrderManager.getDetailByNo(mallOrder.getOrderNo());
        if (ObjectUtil.isEmpty(teboMallOrderDO)){
            throw new ServiceException("订单不存在");
        }
        teboMallOrderDO.setOrderStatus(MallOrderStatusEnum.TO_BO_PICKER_UP.getCode());
        TeboOrderGoodsUpdateDTO updateDTO = new TeboOrderGoodsUpdateDTO();
        updateDTO.setId(teboMallOrderDO.getId());
        updateDTO.setPayTime(LocalDateTime.now());
        updateDTO.setOrderStatus(MallOrderStatusEnum.TO_BO_PICKER_UP.getCode());
        teboMallOrderManager.updateMallOrderStatus(updateDTO);
        // 专供品订单，创建分账计划
        TeboMallOrderQueryDTO queryDTO = new TeboMallOrderQueryDTO();
        queryDTO.setOrderIdList(Collections.singletonList(teboMallOrderDO.getId()));
        List<TeboOrderGoodsVO> goodsList = orderGoodsManager.getOrderGoodsList(queryDTO);
        if (CollectionUtils.isEmpty(goodsList)) {
            return true;
        }
        Long tenantId = teboMallOrderDO.getTenantId();
        LambdaQueryWrapper<TeboTenantGoodsNoPriceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeboTenantGoodsNoPriceDO::getTenantId, tenantId);
        queryWrapper.eq(TeboTenantGoodsNoPriceDO::getGoodsNo, goodsList.get(0).getGoodsNo());
        TeboTenantGoodsNoPriceDO tenantGoods = teboTenantGoodsNoPriceMapper.selectOne(queryWrapper);
        if (ObjectUtil.isEmpty(tenantGoods)) {
            return true;
        }
        // 门店应分安装金额
        Integer shopAmount = tenantGoods.getShopFee();
        // 商应分到金额 25.7.24 合并下单的去掉一张久久券 49.9
        Integer tenantAmount = teboMallOrderDO.getOrderAmount() - 4990 - shopAmount;
        // 插入分账详情
        TeboGoodsOrderSplitDO teboGoodsOrderSplitDO = new TeboGoodsOrderSplitDO();
        teboGoodsOrderSplitDO.setId(SnowFlakeUtil.nextId());
        teboGoodsOrderSplitDO.setOrderId(teboMallOrderDO.getId());
        teboGoodsOrderSplitDO.setOrderNo(teboMallOrderDO.getOrderNo());
        // 发送延迟消息修改商的费用
        Map<String, Object> param = new HashMap<>();
        param.put("orderNo", teboMallOrderDO.getOrderNo());
        param.put("orderId", teboMallOrderDO.getId());
        // todo 这里也要修改
        redisDelayQueueUtil.addDelayQueue(param, 3, TimeUnit.SECONDS, RedisDelayQueueEnum.TEBO_MALL_ORDER_FEE_TIMEOUT.getCode());
        teboGoodsOrderSplitDO.setAmount(tenantAmount);
        teboGoodsOrderSplitDO.setWalletId(tenantGoods.getWalletId());
        teboGoodsOrderSplitDO.setType(1);
        // 商的分账计划
        teboGoodsOrderSplitMapper.insert(teboGoodsOrderSplitDO);
        teboGoodsOrderSplitDO.setId(SnowFlakeUtil.nextId());
        teboGoodsOrderSplitDO.setAmount(shopAmount);
//            R<TeboShop> shopResult = shopService.getShopInfo(teboMallOrderDO.getShopId());
//            if (shopResult.getCode() != 200 || ObjectUtil.isEmpty(shopResult)) {
//                teboGoodsOrderSplitDO.setWalletId(0L);
//            }else {
//                teboGoodsOrderSplitDO.setWalletId(shopResult.getData().getWalletId());
//            }
        teboGoodsOrderSplitDO.setType(2);
        // 门店安装费的分账计划
        teboGoodsOrderSplitMapper.insert(teboGoodsOrderSplitDO);
        teboGoodsOrderSplitDO.setId(SnowFlakeUtil.nextId());
//        teboGoodsOrderSplitDO.setAmount(1800);
        // 25.6.19 取消门店低佣金提取
        teboGoodsOrderSplitDO.setAmount(0);
        teboGoodsOrderSplitDO.setType(3);
        // 门店低佣金的分账计划
        teboGoodsOrderSplitMapper.insert(teboGoodsOrderSplitDO);
        teboGoodsOrderSplitDO.setId(SnowFlakeUtil.nextId());
        teboGoodsOrderSplitDO.setAmount(4990);
        teboGoodsOrderSplitDO.setType(4);
        // 久久券的分账计划
        teboGoodsOrderSplitMapper.insert(teboGoodsOrderSplitDO);
//            // 新的分佣逻辑, 分佣 13.9
        // 25.7.17 改为核销后分佣 13.9
        // todo 久久券处理逻辑
        TeboRemoteGiftOrderDTO orderDTO = new TeboRemoteGiftOrderDTO();
        orderDTO.setUnionId(teboMallOrderDO.getUnionid());
        orderDTO.setShopId(teboMallOrderDO.getShopId());
        orderDTO.setDistrict(teboMallOrderDO.getAreaName());
        orderDTO.setBatteryOrderNo(teboMallOrderDO.getOrderNo());
        remoteCouponService.createMergeOrder(orderDTO);
        // 创建久久券订单
        // 延迟将久久券订单的券标记为已占用和增加券使用记录
        redisDelayQueueUtil.addDelayQueue(param, 10, TimeUnit.SECONDS, RedisDelayQueueEnum.TEBO_MALL_ORDER_MERGE_TIMEOUT.getCode());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean refundBatteryOrder(TeboBatteryOrderRefundDTO refundDTO) {
        log.info("[TeboBatteryOrderService] [refundBatteryOrder] dto {}", refundDTO);

        String orderNo = refundDTO.getOrderNo();
        String lockKey = "battery_order_refund_" + orderNo;

        // 使用分布式锁防止并发
        if (!distributedLock.tryLock(lockKey)) {
            throw new ServiceException("订单正在处理中，请稍后重试");
        }

        try {
            // 1. 校验订单号格式
            if (!validateOrderNo(orderNo)) {
                throw new ServiceException("订单号格式不正确，必须以TBCXA开头，以A结尾");
            }

            // 2. 查询电池订单
            TeboMallOrderDO batteryOrder = teboMallOrderManager.getDetailByNo(orderNo);
            if (ObjectUtil.isEmpty(batteryOrder)) {
                throw new ServiceException("电池订单不存在");
            }

            // 3. 校验订单状态
            if (!MallOrderStatusEnum.TO_BO_PICKER_UP.getCode().equals(batteryOrder.getOrderStatus())) {
                throw new ServiceException("订单状态不允许退款");
            }

            // 4. 检查是否已经退款
            if (isAlreadyRefunded(orderNo)) {
                throw new ServiceException("订单已经退款，请勿重复操作");
            }

            // 5. 校验电池订单未核销
            if (isBatteryOrderWrittenOff(batteryOrder)) {
                throw new ServiceException("电池订单已核销，不能退款");
            }

            // 6. 查询关联的久久券订单
            String giftPackOrderNo = getGiftPackOrderNo(orderNo);
            if (StringUtils.isEmpty(giftPackOrderNo)) {
                throw new ServiceException("未找到关联的久久券订单");
            }

            // 7. 校验大米券未使用
            if (!validateRiceCouponNotUsed(giftPackOrderNo)) {
                throw new ServiceException("大米券已使用，不能退款");
            }

            // 8. 执行退款处理
            return processRefund(batteryOrder, giftPackOrderNo, refundDTO);

        } finally {
            distributedLock.unlock(lockKey);
        }
    }

    /**
     * 校验订单号格式
     */
    private boolean validateOrderNo(String orderNo) {
        return StringUtils.isNotEmpty(orderNo) && orderNo.startsWith("TBCXA") && orderNo.endsWith("A");
    }

    /**
     * 检查是否已经退款
     */
    private boolean isAlreadyRefunded(String orderNo) {
        TeboBatteryOrderRefundDO refundRecord = teboBatteryOrderRefundMapper.selectByBatteryOrderNo(orderNo);
        return refundRecord != null;
    }

    /**
     * 校验电池订单未核销
     */
    private boolean isBatteryOrderWrittenOff(TeboMallOrderDO batteryOrder) {
        // 检查订单状态是否为已完成，如果是则表示已核销
        return MallOrderStatusEnum.FINISH.getCode().equals(batteryOrder.getOrderStatus()) ||
               MallOrderStatusEnum.REVIEWED.getCode().equals(batteryOrder.getOrderStatus());
    }

    /**
     * 获取关联的久久券订单号
     */
    private String getGiftPackOrderNo(String batteryOrderNo) {
        try {
            // 先根据电池订单号获取电池订单ID
            TeboMallOrderDO batteryOrder = teboMallOrderManager.getDetailByNo(batteryOrderNo);
            if (ObjectUtil.isEmpty(batteryOrder)) {
                return null;
            }

            // 查询久久券订单
            TeboCouponGiftOrderDTO giftOrderDTO = teboMallOrderMapper.selectGiftPackOrderByOrderId(batteryOrder.getId());
            return giftOrderDTO != null ? giftOrderDTO.getOrderNo() : null;
        } catch (Exception e) {
            log.error("获取久久券订单号失败", e);
            return null;
        }
    }

    /**
     * 校验大米券未使用
     */
    private boolean validateRiceCouponNotUsed(String giftPackOrderNo) {
        try {
            // 查询大米券状态
            R<Boolean> result = remoteCouponService.checkBatteryCode(giftPackOrderNo);
            if (result.getCode() != 200) {
                log.error("校验大米券状态失败: {}", result.getMsg());
                return false;
            }
            // 如果返回true表示已使用，false表示未使用
            return !result.getData();
        } catch (Exception e) {
            log.error("校验大米券状态异常", e);
            return false;
        }
    }

    /**
     * 执行退款处理
     */
    private Boolean processRefund(TeboMallOrderDO batteryOrder, String giftPackOrderNo, TeboBatteryOrderRefundDTO refundDTO) {
        try {
            // 1. 更新电池订单状态为已取消
            updateBatteryOrderStatus(batteryOrder);

            // 2. 处理久久券订单和券状态
            processGiftPackOrderRefund(giftPackOrderNo);

            // 3. 调用退款接口
            String wechatRefundNo = callRefundApi(batteryOrder);

            // 4. 记录退款记录
            recordRefund(batteryOrder.getOrderNo(), giftPackOrderNo, batteryOrder.getOrderAmount(),
                        wechatRefundNo, refundDTO);

            log.info("电池订单退款成功，订单号：{}", batteryOrder.getOrderNo());
            return true;

        } catch (Exception e) {
            log.error("退款处理失败，订单号：{}", batteryOrder.getOrderNo(), e);
            throw new ServiceException("退款处理失败：" + e.getMessage());
        }
    }

    /**
     * 更新电池订单状态
     */
    private void updateBatteryOrderStatus(TeboMallOrderDO batteryOrder) {
        TeboOrderGoodsUpdateDTO updateDTO = new TeboOrderGoodsUpdateDTO();
        updateDTO.setId(batteryOrder.getId());
        updateDTO.setOrderStatus(MallOrderStatusEnum.CANCEL.getCode());
        updateDTO.setUpdateTime(LocalDateTime.now());
        teboMallOrderManager.updateMallOrderStatus(updateDTO);
    }

    /**
     * 处理久久券订单和券状态
     */
    private void processGiftPackOrderRefund(String giftPackOrderNo) {
        try {
            // 1. 更新久久券订单状态为已退款
            R<Integer> updateResult = remoteCouponService.updateOrderStatus(giftPackOrderNo);
            if (updateResult.getCode() != 200) {
                throw new ServiceException("更新久久券订单状态失败：" + updateResult.getMsg());
            }

            // 2. 更新券状态为已退款
            OrderCouponDTO orderCouponDTO = new OrderCouponDTO();
            orderCouponDTO.setOrderNo(giftPackOrderNo);
            orderCouponDTO.setStatus(20); // 20表示已退款

            R<Integer> couponResult = remoteCouponService.updateCouponStatusNotUsed(orderCouponDTO);
            if (couponResult.getCode() != 200) {
                throw new ServiceException("更新券状态失败：" + couponResult.getMsg());
            }

        } catch (Exception e) {
            log.error("处理久久券订单退款失败", e);
            throw new ServiceException("处理久久券订单退款失败：" + e.getMessage());
        }
    }

    /**
     * 调用退款接口
     */
    private String callRefundApi(TeboMallOrderDO batteryOrder) {
        try {
            // 这里需要调用张鑫的退款接口
            // 暂时返回一个模拟的退款单号
            String refundNo = "RF" + System.currentTimeMillis();

            // TODO: 实际调用退款接口
            // 可能需要调用微信支付退款接口或其他第三方支付退款接口

            log.info("调用退款接口成功，订单号：{}，退款单号：{}", batteryOrder.getOrderNo(), refundNo);
            return refundNo;

        } catch (Exception e) {
            log.error("调用退款接口失败，订单号：{}", batteryOrder.getOrderNo(), e);
            throw new ServiceException("调用退款接口失败：" + e.getMessage());
        }
    }

    /**
     * 记录退款记录
     */
    private void recordRefund(String batteryOrderNo, String giftPackOrderNo, Integer refundAmount,
                             String wechatRefundNo, TeboBatteryOrderRefundDTO refundDTO) {
        try {
            // 创建退款记录
            TeboBatteryOrderRefundDO refundRecord = new TeboBatteryOrderRefundDO();
            refundRecord.setId(SnowFlakeUtil.nextId());
            refundRecord.setBatteryOrderNo(batteryOrderNo);
            refundRecord.setGiftPackOrderNo(giftPackOrderNo);
            refundRecord.setRefundAmount(refundAmount);
            refundRecord.setRefundStatus(1); // 1:退款中
            refundRecord.setRefundReason(refundDTO.getRefundReason());
            refundRecord.setWechatRefundNo(wechatRefundNo);
            refundRecord.setOperator(refundDTO.getOperator());

            // 保存退款记录
            teboBatteryOrderRefundMapper.insert(refundRecord);

            log.info("退款记录保存成功，订单号：{}", batteryOrderNo);

        } catch (Exception e) {
            log.error("保存退款记录失败，订单号：{}", batteryOrderNo, e);
            throw new ServiceException("保存退款记录失败：" + e.getMessage());
        }
    }
}
