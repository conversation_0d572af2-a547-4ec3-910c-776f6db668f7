package com.tebo.mall.manager.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tebo.mall.entity.TeboRankActivitiesShopDO;
import com.tebo.mall.manager.TeboRankActivitiesShopManger;
import com.tebo.mall.mapper.TeboRankActivitiesShopMapper;
import com.tebo.mall.web.domain.dto.TeboRankActivitiesShopQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class TeboRankActivitiesShopMangerImpl extends ServiceImpl<TeboRankActivitiesShopMapper, TeboRankActivitiesShopDO> implements TeboRankActivitiesShopManger {

    @Autowired
    private TeboRankActivitiesShopMapper rankActivitiesShopMapper;


    @Override
    public List<TeboRankActivitiesShopDO> listActivitiesShop(TeboRankActivitiesShopQueryDTO queryDTO) {
        return rankActivitiesShopMapper.listActivitiesShop(queryDTO);
    }
}
