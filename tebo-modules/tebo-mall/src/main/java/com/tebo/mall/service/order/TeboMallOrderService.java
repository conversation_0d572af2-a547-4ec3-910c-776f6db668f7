package com.tebo.mall.service.order;

import com.tebo.mall.domain.TeboOrderGoodsCategoryGroupWebVO;
import com.tebo.mall.domain.TeboOrderGoodsCodeGroupWebVO;
import com.tebo.mall.web.domain.dto.order.inner.TeboTenantOrderDTO;
import com.tebo.mall.web.domain.dto.order.outer.TeboMallOrderAddOuterDTO;
import com.tebo.mall.web.domain.dto.order.outer.TeboMallOrderQueryInnerDTO;
import com.tebo.mall.web.domain.dto.order.outer.TeboMallSpecialOrderAddOuterDTO;
import com.tebo.mall.web.domain.vo.WriteOffOrderResultVO;
import com.tebo.mall.web.domain.vo.order.TeboMallOrderGoodsVO;
import com.tebo.mall.web.domain.vo.order.TeboMallOrderNumberVO;
import com.tebo.mall.web.domain.vo.order.TeboMallOrderVO;
import com.tebo.mall.web.domain.vo.order.TeboShopVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface TeboMallOrderService {
    /**
     * c端订单列表
     *
     * @param orderDTO
     * @return
     */
    List<TeboMallOrderVO> getAppletOrderList(TeboMallOrderQueryInnerDTO orderDTO);

    /**
     * pc端订单列表
     *
     * @param orderDTO
     * @return
     */
    List<TeboMallOrderVO> getOrderList(TeboMallOrderQueryInnerDTO orderDTO);


    /**
     * 导出商城订单
     *
     * @param orderDTO
     */
    void exportOrderList(HttpServletResponse response, TeboMallOrderQueryInnerDTO orderDTO);

    /**
     * 获取订单数量
     */
    TeboMallOrderNumberVO getOrderCount(TeboMallOrderQueryInnerDTO orderDTO);

    /**
     * 下单
     *
     * @param addDTO
     * @return
     */
    Map createOrder(TeboMallOrderAddOuterDTO addDTO);

    /**
     * 专供品下单
     */
    Map createSpecialOrder(TeboMallSpecialOrderAddOuterDTO addDTO);

    /**
     * 再买一单
     *
     * @param addDTO
     * @return
     */
    Long buyAnotherOrder(TeboMallOrderAddOuterDTO addDTO);

    /**
     * 订单详情
     *
     * @param orderId
     * @return
     */
    TeboMallOrderVO getDetailById(Long orderId);

    /**
     * 订单核销
     *
     * @param orderNo
     * @return
     */
    WriteOffOrderResultVO writeOffOrder(String orderNo);

    /**
     * 专供品订单核销
     */
    WriteOffOrderResultVO writeOffSpecialOrder(String orderNo);

    /**
     * 专供品订单核销-new
     * 1. 分玖玖券的电池款 13.9
     * 2. 给商分钱
     * 3. 更新券状态
     */
    WriteOffOrderResultVO writeOffSpecialOrder(Long shopId, String orderNo, String batteryNumber);

    /**
     * 将超过15分钟未支付的订单取消
     */
    void mallOrderPayTimeOut();

    /**
     * 门店详情
     *
     * @param shopId
     * @return
     */
    TeboShopVO getShopDetail(Long shopId);


    /**
     * 扫码看订单商品列表
     *
     * @param orderNo
     * @return
     */
    TeboMallOrderVO orderGoodsByNo(String orderNo);

    /**
     * 商品编码分组聚合
     *
     * @param queryDTO
     * @return
     */
    List<TeboOrderGoodsCodeGroupWebVO> getOrderGoodsCodeGroupLimit7(TeboMallOrderQueryInnerDTO queryDTO);

    /**
     * 类目分组
     * @param queryDTO
     * @return
     */
    List<TeboOrderGoodsCategoryGroupWebVO> getOrderGoodsCategoryGroupLimit10(TeboMallOrderQueryInnerDTO queryDTO);

    TeboOrderGoodsCodeGroupWebVO getOrderGoodsCodeGroupAll(TeboMallOrderQueryInnerDTO queryDTO);

    void getOrderGoodsCodeGroupExport(HttpServletResponse response, TeboMallOrderQueryInnerDTO queryDTO);

    void getOrderGoodsCategoryGroupExport(HttpServletResponse response, TeboMallOrderQueryInnerDTO queryDTO);

    List<TeboOrderGoodsCodeGroupWebVO> getOrderGoodsCodeGroup(TeboMallOrderQueryInnerDTO queryDTO);
    List<TeboOrderGoodsCategoryGroupWebVO> getOrderGoodsCategoryGroup(TeboMallOrderQueryInnerDTO queryDTO);


    /**
     * 掌心泰博专供品订单列表
     *
     * @param query
     * @return
     */
    List<TeboMallOrderGoodsVO> getAppletOrderGoodsList(TeboTenantOrderDTO query);

    /**
     * 订单详情
     * @param orderId
     * @return
     */
    TeboMallOrderGoodsVO getMallOrderInfo(Long orderId);

    /**
     * 更新订单已收回
     * @param orderId
     * @return
     */
    void updateMallOrderInfo(Long orderId);

    /**
     * 订单低佣金提取
     * @param orderId
     * @return
     */
    void updateMallOrderAmount(Long orderId);
}