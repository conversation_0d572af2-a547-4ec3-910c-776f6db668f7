package com.tebo.mall.disruptor.consumer;

import com.lmax.disruptor.EventHandler;
import com.tebo.common.core.utils.SpringUtils;
import com.tebo.common.util.constant.TeboDisMqConstant;
import com.tebo.mall.disruptor.domain.DisMsgData;
import com.tebo.mall.service.IRankActivitiesService;
import com.tebo.system.api.domain.dto.RemoteConsumerRecordDTO;
import com.tebo.system.api.remote.RemoteTeboConsumerService;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * <AUTHOR> ZhangFan
 * @date : 2024/1/12 10:05
 * @Desc : 消费者入口
 */
@Slf4j
public class DisEventHandler implements EventHandler<DisMsgData> {

    @Override
    public void onEvent(DisMsgData event, long sequence, boolean endOfBatch) {
        try {
//            log.info("DisEventHandler 消费者开始处理消息");
//            log.info("消费者消费的信息是：{}", event);
            if (event != null) {
                Map<String, Object> params = event.getData();
                Integer msgType = event.getMsgType();
                // 消费者行为记录
                if (msgType.equals(TeboDisMqConstant.CONSUMER_SERVICE_RECORD)) {
                    RemoteConsumerRecordDTO param = new RemoteConsumerRecordDTO();
                    param.setUnionId(params.get("unionId").toString());
                    param.setLastType(Integer.valueOf(params.get("lastType").toString()));
                    param.setShopId(Long.valueOf(params.get("shopId").toString()));
                    SpringUtils.getBean(RemoteTeboConsumerService.class).recordConsumerRecord(param);
                }

                //商城下单后处理
                if (msgType.equals(TeboDisMqConstant.MALL_WRITE_OFF)) {
                    long orderId = (long) params.get("orderId");
                    SpringUtils.getBean(IRankActivitiesService.class).recordRankActivityByOrder(orderId);
                }
            }
        } catch (Exception e) {
            log.error("DisEventHandler 消费者处理消息失败 event {}", event);
        }
//        log.info("DisEventHandler 消费者处理消息结束");
    }
}
