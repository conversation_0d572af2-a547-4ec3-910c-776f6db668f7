package com.tebo.mall.web.domain.dto.order;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 退款回调DTO
 */
@Data
public class RefundCallbackDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    /**
     * 退款单号
     */
    @NotBlank(message = "退款单号不能为空")
    private String refundNo;

    /**
     * 回调状态 1:成功 2:失败
     */
    @NotNull(message = "回调状态不能为空")
    private Integer callbackStatus;

    /**
     * 回调时间
     */
    private String callbackTime;

    /**
     * 回调消息
     */
    private String callbackMessage;
}
