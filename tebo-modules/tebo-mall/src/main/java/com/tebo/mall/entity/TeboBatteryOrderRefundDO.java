package com.tebo.mall.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 电池订单退款记录表
 * </p>
 */
@Getter
@Setter
@TableName("tebo_battery_order_refund")
public class TeboBatteryOrderRefundDO extends Model<TeboBatteryOrderRefundDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 电池订单号
     */
    @TableField("battery_order_no")
    private String batteryOrderNo;

    /**
     * 久久券订单号
     */
    @TableField("gift_pack_order_no")
    private String giftPackOrderNo;

    /**
     * 退款金额(分)
     */
    @TableField("refund_amount")
    private Integer refundAmount;

    /**
     * 退款状态 1:退款中 2:退款成功 3:退款失败
     */
    @TableField("refund_status")
    private Integer refundStatus;

    /**
     * 退款原因
     */
    @TableField("refund_reason")
    private String refundReason;

    /**
     * 微信退款单号
     */
    @TableField("wechat_refund_no")
    private String wechatRefundNo;

    /**
     * 操作人
     */
    @TableField("operator")
    private String operator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
