package com.tebo.mall.service.order.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.security.util.MaintainerOnlineUserUtil;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.common.util.time.DateUtil;
import com.tebo.mall.api.domain.view.TeboServiceInsurancePolicyOrderVO;
import com.tebo.mall.entity.TeboInsurancePolicyOrderDO;
import com.tebo.mall.entity.TeboMallOrderDO;
import com.tebo.mall.enums.InsuranceMallOrderStatusEnum;
import com.tebo.mall.enums.MallOrderStatusEnum;
import com.tebo.mall.manager.order.TeboInsurancePolicyOrderManager;
import com.tebo.mall.manager.order.TeboMallOrderGoodsManager;
import com.tebo.mall.manager.order.TeboMallOrderManager;
import com.tebo.mall.mapper.TeboMallOrderMapper;
import com.tebo.mall.redisson.queue.RedisDelayQueueEnum;
import com.tebo.mall.redisson.queue.RedisDelayQueueUtil;
import com.tebo.mall.service.order.TeboInsurancePolicyService;
import com.tebo.mall.util.InsurancePolicyUtil;
import com.tebo.mall.web.domain.dto.TeboInsurancePolicyDTO;
import com.tebo.mall.web.domain.dto.TeboInsurancePolicyQueryDTO;
import com.tebo.mall.web.domain.dto.order.outer.TeboMallOrderAddOuterDTO;
import com.tebo.mall.web.domain.vo.order.TeboInsurancePolicyOrderVO;
import com.tebo.mall.web.domain.vo.order.TeboMallOrderVO;
import com.tebo.mall.web.domain.vo.order.TeboOrderGoodsVO;
import com.tebo.rescue.api.RemoteBatteryService;
import com.tebo.rescue.api.RemoteQueueOrderService;
import com.tebo.rescue.api.domain.dto.ProductCheckDTO;
import com.tebo.rescue.api.domain.dto.ServiceOrderDTO;
import com.tebo.rescue.api.domain.view.ProductCheckVO;
import com.tebo.system.api.RemoteAccountService;
import com.tebo.system.api.RemoteFileService;
import com.tebo.system.api.RemoteShopService;
import com.tebo.system.api.RemoteWxPayService;
import com.tebo.system.api.model.TeboAccountInfoVO;
import com.tebo.system.api.model.TeboShop;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.time.temporal.ChronoUnit;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class TeboInsurancePolicyServiceImpl implements TeboInsurancePolicyService {
   @Resource
   private RemoteBatteryService remoteBatteryService;
    @Resource
    private TeboMallOrderManager teboMallOrderManager;
    @Resource
    private RemoteShopService shopService;
    @Resource
    private RemoteAccountService remoteAccountService;
    @Resource
    private TeboMallOrderMapper orderMapper;
    @Resource
    private TeboInsurancePolicyOrderManager insurancePolicyOrderManager;
    @Resource
    private TeboMallOrderGoodsManager teboMallOrderGoodsManager;
    @Resource
    private RedisDelayQueueUtil redisDelayQueueUtil;
    @Resource
    private InsurancePolicyUtil insurancePolicyUtil;
    @Resource
    private RemoteQueueOrderService remoteQueueOrderService;
    @Autowired
    private RemoteWxPayService remoteWxPayService;

    /**
     * 保险金额
     */
    @Value("${insurance.amount}")
    private String amount;

    @Override
    public Map createInsurancePolicy(TeboMallOrderAddOuterDTO addDTO) {
        TeboInsurancePolicyOrderDO insurancePolicyOrderDO = insurancePolicyOrderManager.selectByParentOrderId(addDTO.getParentOrderId());
        if (ObjectUtil.isNotEmpty(insurancePolicyOrderDO) && insurancePolicyOrderDO.getOrderStatus() == 3){
           throw new GlobalException("保单已存在") ;
        }
        Map map = new HashMap();
        TeboInsurancePolicyOrderDO teboInsurancePolicyOrderDO = new TeboInsurancePolicyOrderDO();
        teboInsurancePolicyOrderDO.setId(SnowFlakeUtil.nextId());
        teboInsurancePolicyOrderDO.setParentOrderId(addDTO.getParentOrderId());
        teboInsurancePolicyOrderDO.setOrderStatus(1);
        teboInsurancePolicyOrderDO.setOrderAmount(MoneyUtil.yuanToFen(amount));
        teboInsurancePolicyOrderDO.setBatteryImageUrl(addDTO.getBatteryImageUrl());
        teboInsurancePolicyOrderDO.setRealName(addDTO.getRealName());
        teboInsurancePolicyOrderDO.setIdCardNo(addDTO.getIdCardNo());
        teboInsurancePolicyOrderDO.setBatteryCodeImageUrl(addDTO.getBatteryCodeImageUrl());
        if (ObjectUtil.isNotEmpty(addDTO.getSource()) && addDTO.getSource() == 2){
            R<ServiceOrderDTO> serviceOrderResult = remoteQueueOrderService.getServiceOrderById(addDTO.getParentOrderId());
            teboInsurancePolicyOrderDO.setOrderNo(serviceOrderResult.getData().getOrderNo()+"-1");
            teboInsurancePolicyOrderDO.setUnionid(serviceOrderResult.getData().getUnionid());
            teboInsurancePolicyOrderDO.setSource(2);
            teboInsurancePolicyOrderDO.setProductCode(addDTO.getProductCode());
            String data = remoteWxPayService.generateOrderCode(teboInsurancePolicyOrderDO.getId(),2).getData();
            if (StringUtils.isEmpty(data)) {
                throw new ServiceException("生成支付二维码失败，请联系管理员");
            }
            map.put("payQrCode",data);
        }else {
            TeboMallOrderVO teboMallOrderVO = teboMallOrderManager.getDetailById(addDTO.getParentOrderId());
            teboInsurancePolicyOrderDO.setOrderNo(teboMallOrderVO.getOrderNo()+"-1");
            teboInsurancePolicyOrderDO.setUnionid(teboMallOrderVO.getUnionid());
            teboInsurancePolicyOrderDO.setSource(1);
        }
        map.put("orderId",teboInsurancePolicyOrderDO.getId());
        insurancePolicyOrderManager.insert(teboInsurancePolicyOrderDO);
        Map<String, String> param = new HashMap<>();
        param.put("orderId", teboInsurancePolicyOrderDO.getId().toString());
        param.put("parentOrderId", addDTO.getParentOrderId().toString());
        param.put("remark", "保险单支付超时，自动取消订单");
        // 添加订单支付超时，自动取消订单延迟队列。
        redisDelayQueueUtil.addDelayQueue(param, 15, TimeUnit.MINUTES, RedisDelayQueueEnum.TEBO_MALL_INSURANCE_ORDER_PAYMENT_TIMEOUT.getCode());
        return map;
    }

    @Override
    public TeboMallOrderVO createInsurancePolicyOrder(TeboInsurancePolicyDTO teboInsurancePolicyDTO) {
        ProductCheckDTO productCheckDTO = new ProductCheckDTO();
        productCheckDTO.setProductCode(teboInsurancePolicyDTO.getProductCode());
        R<ProductCheckVO> productCheckVOR = remoteBatteryService.checkProduct(productCheckDTO);
        ProductCheckVO productCheckVO = productCheckVOR.getData();
        if (ObjectUtil.isEmpty(productCheckVO)){
           throw new ServiceException(productCheckVOR.getMsg());
        }
        /**
         * 系统校验电池三包日期距离当前时间不允许超过两个月,超过2个月的无法识别,并做出提示“该电池日期已超出可购买保险范围,请选择2个月内电池上传
         */
        Date prodDate = DateUtil.transferString2Date(productCheckVO.getProdDate());
        LocalDate prodLocalDate = DateUtil.dateToLocalDateTime(prodDate).toLocalDate();
        LocalDate currentDate = LocalDate.now();
        if (prodLocalDate.isBefore(currentDate)) {
            throw new ServiceException("该电池日期已超出可购买保险范围,请选择2个月内电池上传");
        }else {
            long monthsBetween = ChronoUnit.MONTHS.between(prodLocalDate,currentDate);
            if (monthsBetween >= 2){
              throw new ServiceException("该电池日期已超出可购买保险范围,请选择2个月内电池上传");
            }
        }

        TeboMallOrderVO teboMallOrderVO = teboMallOrderManager.getDetailById(teboInsurancePolicyDTO.getOrderId());
        R<TeboShop> teboShopR = shopService.getShopInfo(teboMallOrderVO.getShopId());
        if (teboShopR.getCode() != 200 ||teboShopR.getData() == null){
            throw new GlobalException("门店不存在");
        }
        /**
         * 保险单
         */
        TeboInsurancePolicyOrderDO insurancePolicyOrderDO = insurancePolicyOrderManager.selectByParentOrderId(teboInsurancePolicyDTO.getOrderId());
        TeboShop teboShop = teboShopR.getData();
        /**
         * 电池价格
         */
        teboInsurancePolicyDTO.setBatteryPrices(teboMallOrderVO.getOrderAmount());
        teboInsurancePolicyDTO.setCustomerName(insurancePolicyOrderDO.getRealName());
        teboInsurancePolicyDTO.setCustomerPhone(teboMallOrderVO.getCustomerPhone());
        teboInsurancePolicyDTO.setSalesName(teboShop.getShopName());
        teboInsurancePolicyDTO.setSalesID(teboShop.getShopCode());
        String areaName = teboShop.getAreaName();
        String[] areaNameArray = areaName.split("-");
        teboInsurancePolicyDTO.setSalesProvince(areaNameArray[0]);
        teboInsurancePolicyDTO.setSalesCity(areaNameArray[1]);
        teboInsurancePolicyDTO.setSalesCounty(areaNameArray[2]);
        teboInsurancePolicyDTO.setBatterySaleDate(teboMallOrderVO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        teboInsurancePolicyDTO.setProductionDate(productCheckVO.getProdDate());
        teboInsurancePolicyDTO.setBatteryCode(teboInsurancePolicyDTO.getProductCode());
        teboInsurancePolicyDTO.setServiceStartDate(productCheckVO.getProdDate());
        teboInsurancePolicyDTO.setBatteryType(productCheckVO.getStyle());
        String wuLiao = productCheckVO.getWuliao();
        String[] wuLiaoArray = wuLiao.split("/");
        teboInsurancePolicyDTO.setBatterySeries(wuLiaoArray[1]);
        String style = productCheckVO.getStyle();
        if (style.indexOf("(")>0){
            style= style.substring(0,style.indexOf("("));
        }
        String[] styleArray = style.split("V");
        /**
         * 电压
         */
        Double voltage = Double.parseDouble(styleArray[0]);
        /**
         * 电流
         */
        Double current = Double.parseDouble(styleArray[1].replace("AH",""));
        /**
         * 功率
         */
        Double power = voltage*(current.intValue());

        /**
         * 功率
         */
        teboInsurancePolicyDTO.setBatteryPower(power.intValue()+"W");
        /**
         * 容量
         */
        teboInsurancePolicyDTO.setBatteryCapacity(styleArray[1]);
        teboInsurancePolicyDTO.setWarrantyStartDate(productCheckVO.getProdDate());
        teboInsurancePolicyDTO.setOrderNo("TBCX"+insurancePolicyOrderDO.getOrderNo());
        teboInsurancePolicyDTO.setCertCode(insurancePolicyOrderDO.getIdCardNo());
        Map<String,String> resultMap = insurancePolicyUtil.createInsurancePolicyOrder(teboInsurancePolicyDTO);
        String resultCode = resultMap.get("resultCode");
        if (!"0000".equals(resultCode)){
            throw new ServiceException(resultMap.get("resultMsg"));
        }
        /**
         * 将订单状态设置为已核销
         */
        if (CollectionUtil.isNotEmpty(resultMap) && StringUtils.isNotEmpty(resultMap.get("serviceOrderId"))){
            TeboMallOrderDO update = new TeboMallOrderDO();
            update.setId(teboInsurancePolicyDTO.getOrderId());
            Long userId = MaintainerOnlineUserUtil.getUserId();
            R<TeboAccountInfoVO> accountR = remoteAccountService.getAccountInfoById(userId);
            TeboAccountInfoVO accountInfoVO = accountR.getData();
            if (Objects.nonNull(accountInfoVO)) {
                update.setVerificationName(accountInfoVO.getAccountName());
                update.setVerificationAccount(accountInfoVO.getUserName());
                update.setVerificationAccountId(accountInfoVO.getId());
            }
            update.setOrderStatus(MallOrderStatusEnum.FINISH.getCode());
            update.setVerificationTime(LocalDateTime.now());
            orderMapper.updateById(update);
            /**
             * 更新保单状态
             */
            if (ObjectUtil.isEmpty(insurancePolicyOrderDO)){
                throw new ServiceException("保险订单不存在");
            }
            TeboInsurancePolicyOrderDO teboInsurancePolicyOrderDO = new TeboInsurancePolicyOrderDO();
            teboInsurancePolicyOrderDO.setParentOrderId(teboInsurancePolicyDTO.getOrderId());
            teboInsurancePolicyOrderDO.setId(insurancePolicyOrderDO.getId());
            teboInsurancePolicyOrderDO.setOrderStatus(InsuranceMallOrderStatusEnum.EFFECTIVE.getCode());
            teboInsurancePolicyOrderDO.setBatteryCode(teboInsurancePolicyDTO.getBatteryCode());
            teboInsurancePolicyOrderDO.setBatteryCodeImageUrl(teboInsurancePolicyDTO.getBatteryCodeImageUrl());
            teboInsurancePolicyOrderDO.setBatteryImageUrl(teboInsurancePolicyDTO.getBatteryImageUrl());
            teboInsurancePolicyOrderDO.setServiceOrderId(resultMap.get("serviceOrderId"));
            teboInsurancePolicyOrderDO.setServicePdfUrl(resultMap.get("servicePdfUrl"));
            teboInsurancePolicyOrderDO.setServicePdfImageUrl(resultMap.get("servicePdfImageUrl"));
            teboInsurancePolicyOrderDO.setServiceStartTime(resultMap.get("serviceStartTime"));
            teboInsurancePolicyOrderDO.setServiceEndTime(resultMap.get("serviceEndTime"));
            teboInsurancePolicyOrderDO.setUpdateTime(LocalDateTimeUtil.now());
            insurancePolicyOrderManager.updateById(teboInsurancePolicyOrderDO);
            return getDetailById(insurancePolicyOrderDO.getId());
        }
        return getDetailById(insurancePolicyOrderDO.getId());
    }

    @Override
    public String createServiceOrderInsurancePolicyOrder(TeboInsurancePolicyDTO teboInsurancePolicyDTO) {
        ProductCheckDTO productCheckDTO = new ProductCheckDTO();
        productCheckDTO.setProductCode(teboInsurancePolicyDTO.getProductCode());
        R<ProductCheckVO> productCheckVOR = remoteBatteryService.checkProduct(productCheckDTO);
        ProductCheckVO productCheckVO = productCheckVOR.getData();
        if (ObjectUtil.isEmpty(productCheckVO)){
            throw new ServiceException(productCheckVOR.getMsg());
        }
        /**
         * 系统校验电池三包日期距离当前时间不允许超过两个月,超过2个月的无法识别,并做出提示“该电池日期已超出可购买保险范围,请选择2个月内电池上传
         */
        Date prodDate = DateUtil.transferString2Date(productCheckVO.getProdDate());
        LocalDate prodLocalDate = DateUtil.dateToLocalDateTime(prodDate).toLocalDate();
        LocalDate currentDate = LocalDate.now();
        if (prodLocalDate.isBefore(currentDate)) {
            throw new ServiceException("该电池日期已超出可购买保险范围,请选择2个月内电池上传");
        }else {
            long monthsBetween = ChronoUnit.MONTHS.between(prodLocalDate,currentDate);
            if (monthsBetween >= 2){
                throw new ServiceException("该电池日期已超出可购买保险范围,请选择2个月内电池上传");
            }
        }
        R<ServiceOrderDTO> serviceOrderDTOResult = remoteQueueOrderService.getServiceOrderById(teboInsurancePolicyDTO.getOrderId());
        ServiceOrderDTO serviceOrderDTO = serviceOrderDTOResult.getData();
        R<TeboShop> teboShopR = shopService.getShopInfo(serviceOrderDTO.getShopId());
        if (teboShopR.getCode() != 200 ||teboShopR.getData() == null){
            throw new GlobalException("门店不存在");
        }
        TeboShop teboShop = teboShopR.getData();
        /**
         * 电池价格
         */
        teboInsurancePolicyDTO.setBatteryPrices(serviceOrderDTO.getOrderAmountStr());
        teboInsurancePolicyDTO.setCustomerName(serviceOrderDTO.getNickName());
        teboInsurancePolicyDTO.setCustomerPhone(serviceOrderDTO.getPhoneNumber());
        teboInsurancePolicyDTO.setSalesName(teboShop.getShopName());
        teboInsurancePolicyDTO.setSalesID(teboShop.getShopCode());
        String areaName = teboShop.getAreaName();
        String[] areaNameArray = areaName.split("-");
        teboInsurancePolicyDTO.setSalesProvince(areaNameArray[0]);
        teboInsurancePolicyDTO.setSalesCity(areaNameArray[1]);
        teboInsurancePolicyDTO.setSalesCounty(areaNameArray[2]);
        teboInsurancePolicyDTO.setBatterySaleDate(serviceOrderDTO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        teboInsurancePolicyDTO.setProductionDate(productCheckVO.getProdDate());
        teboInsurancePolicyDTO.setBatteryCode(teboInsurancePolicyDTO.getProductCode());
        teboInsurancePolicyDTO.setServiceStartDate(productCheckVO.getProdDate());
        teboInsurancePolicyDTO.setBatteryType(productCheckVO.getStyle());
        String wuLiao = productCheckVO.getWuliao();
        String[] wuLiaoArray = wuLiao.split("/");
        teboInsurancePolicyDTO.setBatterySeries(wuLiaoArray[1]);
        String[] styleArray = productCheckVO.getStyle().split("V");
        /**
         * 电压
         */
        Double voltage = Double.parseDouble(styleArray[0]);
        /**
         * 电流
         */
        Double current = Double.parseDouble(styleArray[1].replace("AH",""));
        /**
         * 功率
         */
        Double power = voltage*(current.intValue());

        /**
         * 功率
         */
        teboInsurancePolicyDTO.setBatteryPower(power.intValue()+"W");
        /**
         * 容量
         */
        teboInsurancePolicyDTO.setBatteryCapacity(styleArray[1]);
        teboInsurancePolicyDTO.setWarrantyStartDate(productCheckVO.getProdDate());
        Map<String,String> resultMap = insurancePolicyUtil.createInsurancePolicyOrder(teboInsurancePolicyDTO);
        String resultCode = resultMap.get("resultCode");
        if (!"0000".equals(resultCode)){
            throw new ServiceException(resultMap.get("resultMsg"));
        }
        /**
         * 将订单状态设置为已核销
         */
        TeboInsurancePolicyOrderDO insurancePolicyOrderDO = insurancePolicyOrderManager.selectByParentOrderId(teboInsurancePolicyDTO.getOrderId());
        if (CollectionUtil.isNotEmpty(resultMap) && StringUtils.isNotEmpty(resultMap.get("serviceOrderId"))){
            TeboMallOrderDO update = new TeboMallOrderDO();
            update.setId(teboInsurancePolicyDTO.getOrderId());
            Long userId = MaintainerOnlineUserUtil.getUserId();
            R<TeboAccountInfoVO> accountR = remoteAccountService.getAccountInfoById(userId);
            TeboAccountInfoVO accountInfoVO = accountR.getData();
            if (Objects.nonNull(accountInfoVO)) {
                update.setVerificationName(accountInfoVO.getAccountName());
                update.setVerificationAccount(accountInfoVO.getUserName());
                update.setVerificationAccountId(accountInfoVO.getId());
            }
            update.setOrderStatus(MallOrderStatusEnum.FINISH.getCode());
            update.setVerificationTime(LocalDateTime.now());
            orderMapper.updateById(update);
            /**
             * 更新保单状态
             */
            if (ObjectUtil.isEmpty(insurancePolicyOrderDO)){
                throw new ServiceException("保险订单不存在");
            }
            TeboInsurancePolicyOrderDO teboInsurancePolicyOrderDO = new TeboInsurancePolicyOrderDO();
            teboInsurancePolicyOrderDO.setParentOrderId(teboInsurancePolicyDTO.getOrderId());
            teboInsurancePolicyOrderDO.setId(insurancePolicyOrderDO.getId());
            teboInsurancePolicyOrderDO.setOrderStatus(InsuranceMallOrderStatusEnum.EFFECTIVE.getCode());
            teboInsurancePolicyOrderDO.setBatteryCode(teboInsurancePolicyDTO.getBatteryCode());
            teboInsurancePolicyOrderDO.setBatteryCodeImageUrl(teboInsurancePolicyDTO.getBatteryCodeImageUrl());
            teboInsurancePolicyOrderDO.setBatteryImageUrl(teboInsurancePolicyDTO.getBatteryImageUrl());
            teboInsurancePolicyOrderDO.setServiceOrderId(resultMap.get("serviceOrderId"));
            teboInsurancePolicyOrderDO.setServicePdfUrl(resultMap.get("servicePdfUrl"));
            teboInsurancePolicyOrderDO.setServicePdfImageUrl(resultMap.get("servicePdfImageUrl"));
            teboInsurancePolicyOrderDO.setServiceStartTime(resultMap.get("serviceStartTime"));
            teboInsurancePolicyOrderDO.setServiceEndTime(resultMap.get("serviceEndTime"));
            teboInsurancePolicyOrderDO.setUpdateTime(LocalDateTimeUtil.now());
            insurancePolicyOrderManager.updateById(teboInsurancePolicyOrderDO);
            return resultMap.get("servicePdfImageUrl");
        }
        return resultMap.get("servicePdfImageUrl");

    }

    @Override
    public List<TeboInsurancePolicyOrderVO> getInsurancePolicyOrderList(TeboInsurancePolicyQueryDTO queryDTO) {
       return insurancePolicyOrderManager.getInsurancePolicyOrderList(queryDTO);
    }

    @Override
    public TeboMallOrderVO getDetailById(Long orderId) {
        TeboInsurancePolicyOrderVO insurancePolicyOrderVO = new TeboInsurancePolicyOrderVO();
        TeboInsurancePolicyOrderDO teboInsurancePolicyOrderDO = insurancePolicyOrderManager.selectById(orderId);
        if (ObjectUtil.isNotEmpty(teboInsurancePolicyOrderDO)){
            BeanConvert.copy(teboInsurancePolicyOrderDO,insurancePolicyOrderVO);
        }
        if (teboInsurancePolicyOrderDO == null){
            return null;
        }
        TeboMallOrderVO teboMallOrderVO = teboMallOrderManager.getDetailById(teboInsurancePolicyOrderDO.getParentOrderId());
        Long shopId = teboMallOrderVO.getShopId();
        R<TeboShop> shopResult =  shopService.getShopInfo(shopId);
        if (shopResult.getCode() != 200 ||shopResult.getData() == null){
            throw new GlobalException("门店不存在");
        }
        teboMallOrderVO.setPhoneNumber(shopResult.getData().getPhoneNumber());
        List<TeboOrderGoodsVO> goodsVOList = teboMallOrderGoodsManager.getOrderGoodsByOrderId(teboMallOrderVO.getId());
        teboMallOrderVO.setInsurancePolicyOrderVO(insurancePolicyOrderVO);
        teboMallOrderVO.setGoodsList(goodsVOList);
        return teboMallOrderVO;
    }

    @Override
    public TeboServiceInsurancePolicyOrderVO getServiceInsurancePolicyOrder(Long parentOrderId) {
        TeboServiceInsurancePolicyOrderVO insurancePolicyOrderVO = new TeboServiceInsurancePolicyOrderVO();
        TeboInsurancePolicyOrderDO teboInsurancePolicyOrderDO = insurancePolicyOrderManager.selectByParentOrderId(parentOrderId);
        if (ObjectUtil.isNotEmpty(teboInsurancePolicyOrderDO)){
            BeanConvert.copy(teboInsurancePolicyOrderDO,insurancePolicyOrderVO);
        }
        return insurancePolicyOrderVO;
    }
}