package com.tebo.mall.web.domain.vo.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.tebo.mall.web.domain.vo.TeboOrderCommentVO;
import com.tebo.mall.web.domain.vo.order.TeboOrderGoodsVO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 商城单导出
 */

@ColumnWidth(20)
@Data
public class MallOrderListExcelVO {



@ExcelIgnore
    private Long id;

    /**
     * 订单号
     */
    @ExcelProperty("订单单号")
    private String orderNo;

    @ExcelProperty("下单人姓名")
    private String customerName;

    @ExcelProperty("下单人电话")
    private String customerPhone;

    @ExcelProperty("商品名称")
    private String goodsName;

    @ExcelProperty("购买方式")
    private String priceTypeName;

    @ExcelIgnore
    private Integer priceType;

    @ExcelProperty("订单金额")
    private String orderAmount;

    @ExcelProperty("优惠金额")
    private String parValue;

    /**
     * 服务类型 1上门 2 到店
     */
    @ExcelProperty("配送方式")
    private String serviceTypeName;

    @ExcelIgnore
    private Integer serviceType;


    /**
     * 订单状态 1:待支付 2:待提货,3 已完成  4 已评价 10:已取消
     */
    @ExcelProperty("订单状态")
    private String orderStatusName;

    @ExcelIgnore
    private Integer orderStatus;



    /**
     * 门店名称
     */
    @ExcelProperty("销售门店")
    private String shopName;


    @ExcelProperty("门店经营区域")
    private String areaName;

    @ExcelProperty("销售门店合伙人")
    private String tenantName;

    @ExcelIgnore
    private Integer channel;

    @ExcelProperty("渠道")
    private String channelStr;

    /**
     * 核销人员名字
     */
    @ExcelProperty("核销师傅")
    private String verificationName;

    /**
     * 核销电池码
     */
    @ExcelProperty("核销电池码")
    private String batteryCode;


    /**
     * 预约时间
     */
    @ExcelProperty("预约时段")
    private String appointmentServiceTime;



    @ExcelProperty("下单时间")
    private LocalDateTime createTime;


    /**
     * 核销时间
     */
    @ExcelProperty("核销时间")
    private LocalDateTime verificationTime;


    /**
     * 支付时间
     */
    @ExcelProperty("付款时间")
    private LocalDateTime payTime;


    /**
     * 取消时间
     */
    @ExcelProperty("取消时间")
    private LocalDateTime cancelTime;


    @ExcelProperty("完成时间")
    private LocalDateTime completeTime;


    /**
     * 商品信息
     */
    @ExcelIgnore
    private List<TeboOrderGoodsVO> goodsList;

}
