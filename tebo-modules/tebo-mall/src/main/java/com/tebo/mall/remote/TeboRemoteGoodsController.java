package com.tebo.mall.remote;

import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.mall.api.domain.view.TeboGoodsVO;
import com.tebo.mall.service.goods.TeBoGoodsService;
import com.tebo.mall.service.goods.TeBoPlatGoodsPartnerService;
import com.tebo.mall.service.goods.TeBoPlatGoodsService;
import com.tebo.mall.web.domain.dto.goods.TeboGoodsQueryDTO;
import com.tebo.mall.web.domain.dto.goods.TeboPlatGoodsQueryDTO;
import com.tebo.mall.web.domain.dto.goods.TeboPlatGoodsShopQueryDTO;
import com.tebo.mall.web.domain.vo.TeboPlatGoodsGroupVO;
import com.tebo.mall.web.domain.vo.TeboPlatGoodsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/open/goods")
public class TeboRemoteGoodsController extends BaseController {
    @Resource
    private TeBoGoodsService teboGoodsService;
    @Resource
    private TeBoPlatGoodsService teBoPlatGoodsService;
    @Resource
    private TeBoPlatGoodsPartnerService platGoodsPartnerService;


    /**
     * 根据门店id获取是否有商品
     */
    @PostMapping("/countGoods")
    private R<Integer> countGoods(@RequestBody TeboGoodsQueryDTO TeboGoodsQueryDTO) {
        return R.ok(teboGoodsService.countGoods(TeboGoodsQueryDTO));
    }

    /**
     * 商品列表
     */
    @PostMapping("/fullyQuery")
    public R<List<TeboGoodsVO>> fullyQuery(@RequestBody TeboGoodsQueryDTO queryDTO) {
        List<TeboGoodsVO> goodsList = teboGoodsService.listGoodsForResue(queryDTO);
        return R.ok(goodsList);
    }

    /**
     * 商品列表 单表
     */
    @PostMapping("/listGoodsSingle")
    public R<List<TeboGoodsVO>> listGoodsSingle(@RequestBody TeboGoodsQueryDTO queryDTO) {
        List<TeboGoodsVO> goodsList = teboGoodsService.listGoodsSingle(queryDTO);
        return R.ok(goodsList);
    }

    /**
     * 商品列表
     */
    @PostMapping("/countPlatGoodsByShopId")
    public R<Integer> countPlatGoodsByShopId(@RequestParam("shopId") Long shopId) {
        return R.ok(teboGoodsService.countPlatGoodsByShopId(shopId));
    }

    /**
     * 商品列表
     */
    @PostMapping("/getPlatGoodsByShop")
    public R<List<TeboGoodsVO>> getPlatGoodsByShop(@RequestBody TeboPlatGoodsShopQueryDTO platGoodsShopQueryDTO) {
        return R.ok(teboGoodsService.getPlatGoodsByShop(platGoodsShopQueryDTO));
    }


    /**
     * 平台商品列表
     */
    @PostMapping("/listPlatGoods")
    public R<List<TeboPlatGoodsVO>> listPlatGoods(@RequestBody TeboPlatGoodsQueryDTO queryDTO) {
        return R.ok(teBoPlatGoodsService.listGoods(queryDTO));
    }

    /**
     * 授权合伙人商品数量
     */
    @PostMapping("/partner/getGoodsCount")
    public R<Integer> getPartnerGoodsCount(@RequestParam("tenantId") Long tenantId) {
        return R.ok(platGoodsPartnerService.getGoodsCount(tenantId));
    }

    /**
     * 获取门店推荐商品
     */
    @PostMapping("/nearbyShopRecommendGoodsList")
    public R<List<TeboGoodsVO>> nearbyShopRecommendGoodsList(@RequestBody TeboGoodsQueryDTO teboGoodsQueryDTO) {
        if (ObjectUtils.isEmpty(teboGoodsQueryDTO.getShopId()) && CollectionUtils.isEmpty(teboGoodsQueryDTO.getShopIdList())) {
            throw new ServiceException("门店id不能为空");
        }
        return R.ok(teboGoodsService.nearbyShopRecommendGoodsList(teboGoodsQueryDTO));
    }


    /**
     * 合伙人商品 聚合
     */
    @PostMapping("/partner/platGoodsPartnerGroup")
    public R<List<TeboPlatGoodsGroupVO>> platGoodsPartnerGroup(@RequestBody TeboPlatGoodsQueryDTO queryDTO) {
        handleTime(queryDTO);
        List<TeboPlatGoodsGroupVO> goodsGroupVOS = platGoodsPartnerService.platGoodsPartnerGroup(queryDTO);
        return R.ok(goodsGroupVOS);
    }

    private static void handleTime(TeboPlatGoodsQueryDTO queryDTO) {
        if (Objects.nonNull(queryDTO.getCreateTimeStart())) {
            queryDTO.setCreateTimeStartSecond(queryDTO.getCreateTimeStart().atStartOfDay());
        }
        if (Objects.nonNull(queryDTO.getCreateTimeEnd())) {
            queryDTO.setCreateTimeEndSecond(queryDTO.getCreateTimeEnd().plusDays(1).atStartOfDay());
        }
    }

}
