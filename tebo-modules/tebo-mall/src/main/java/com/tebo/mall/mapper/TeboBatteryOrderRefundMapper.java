package com.tebo.mall.mapper;

import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.mall.entity.TeboBatteryOrderRefundDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 电池订单退款记录表 Mapper 接口
 * </p>
 */
@Mapper
public interface TeboBatteryOrderRefundMapper extends TeboBaseMapper<TeboBatteryOrderRefundDO> {

    /**
     * 根据电池订单号查询退款记录
     * @param batteryOrderNo
     * @return
     */
    TeboBatteryOrderRefundDO selectByBatteryOrderNo(@Param("batteryOrderNo") String batteryOrderNo);

    /**
     * 查询长时间未回调的退款记录
     * @param minutes 分钟数
     * @return
     */
    List<TeboBatteryOrderRefundDO> selectPendingRefunds(@Param("minutes") Integer minutes);
}
