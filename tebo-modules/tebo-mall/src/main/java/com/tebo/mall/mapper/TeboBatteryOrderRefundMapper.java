package com.tebo.mall.mapper;

import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.mall.entity.TeboBatteryOrderRefundDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 电池订单退款记录表 Mapper 接口
 * </p>
 */
@Mapper
public interface TeboBatteryOrderRefundMapper extends TeboBaseMapper<TeboBatteryOrderRefundDO> {

    /**
     * 根据电池订单号查询退款记录
     * @param batteryOrderNo
     * @return
     */
    TeboBatteryOrderRefundDO selectByBatteryOrderNo(@Param("batteryOrderNo") String batteryOrderNo);
}
