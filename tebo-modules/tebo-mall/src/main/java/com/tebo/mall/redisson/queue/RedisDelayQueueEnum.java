package com.tebo.mall.redisson.queue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024/3/9 14:11
 * @Desc : 延迟队列业务枚举
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum RedisDelayQueueEnum {
    TEBO_MALL_ORDER_PAYMENT_TIMEOUT("TEBO_MALL_ORDER_PAYMENT_TIMEOUT","订单支付超时，自动取消订单", "teboMallOrderPayTimeout"),
    TEBO_SUPPLY_GOODS_SYNC_TASK("TEBO_SUPPLY_GOODS_SYNC_TASK","授权专供品后，同步到合伙人", "teboSupplyGoodsSyncTask"),
    TEBO_MALL_ORDER_FEE_TIMEOUT("TEBO_MALL_ORDER_FEE_TIMEOUT","订单支付费用计算", "teboMallOrderFeeTimeout"),
    TEBO_MALL_ORDER_MERGE_TIMEOUT("TEBO_MALL_ORDER_MERGE_TIMEOUT","直营订单合并支付后久久券处理", "teboMallOrderMergeTimeout"),
    TEBO_MALL_SYNCHRONIZE_PLAT_GOODS("TEBO_MALL_SYNCHRONIZE_PLAT_GOODS","同步平台商品信息到门店", "teboSynchronizePlatGoods"),
    TEBO_MALL_SYNCHRONIZE_PLAT_GOODS_AUTHORIZATION_TASK("TEBO_MALL_SYNCHRONIZE_PLAT_GOODS_AUTHORIZATION_TASK","同步平台商品信息价格变动到门店", "teboSynchronizePlatGoodsAuthorizationTask"),
    TEBO_MALL_INSURANCE_ORDER_PAYMENT_TIMEOUT("TEBO_MALL_INSURANCE_ORDER_PAYMENT_TIMEOUT","保险订单支付超时，自动取消订单", "teboInsuranceOrderPayTimeOutTask"),
    ;
    /**
     * 延迟队列 Redis Key
     */
    private String code;

    /**
     * 中文描述
     */
    private String name;

    /**
     * 延迟队列具体业务实现的 Bean
     * 可通过 Spring 的上下文获取
     */
    private String beanId;

}
