package com.tebo.mall.mapper;

import com.tebo.mall.api.domain.view.TeboSpecialSupplyDistrictGoodsVO;
import com.tebo.mall.entity.TeboSpecialSupplyDistrictGoodsDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.mall.web.domain.dto.goods.special.TeboSpecialSupplyDistrictGoodsQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 专供品授权区域商品表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-25
 */
@Mapper
public interface TeboSpecialSupplyDistrictGoodsMapper extends TeboBaseMapper<TeboSpecialSupplyDistrictGoodsDO> {

    void batchInsert(List<TeboSpecialSupplyDistrictGoodsDO> list);
    TeboSpecialSupplyDistrictGoodsDO selectById(Long id);
    List<TeboSpecialSupplyDistrictGoodsDO> list(TeboSpecialSupplyDistrictGoodsQueryDTO queryDTO);
    int goodsCount(TeboSpecialSupplyDistrictGoodsQueryDTO queryDTO);

    Long getShopIdByUnionId(@Param("unionId") String unionId);

    int delGoodsRelate(@Param("goodsNo")String goodsNo);


    List<TeboSpecialSupplyDistrictGoodsVO> listNew(TeboSpecialSupplyDistrictGoodsQueryDTO queryDTO);
}
