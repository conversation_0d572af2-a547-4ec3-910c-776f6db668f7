package com.tebo.mall.web.domain.vo.order;

import com.tebo.common.util.number.MoneyUtil;
import com.tebo.mall.web.domain.vo.TeboOrderCommentVO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class TeboMallOrderGoodsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 门店安装费
     */
    private Integer shopFee;

    private Long id;

    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 订单状态 1:待支付 2:待提货,3 已完成  4 已评价 10:已取消
     */
    private Integer orderStatus;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 门店名称
     */
    private String shopName;
    /**
     * 合伙人id
     */
    private Long tenantId;

    /**
     * 合伙人名字
     */
    private String tenantName;
    /**
     * 订单金额，以分为单位
     */
    private Integer orderAmount;

    /**
     * 优惠券面值
     */
    private Integer parValue;

    private String parValueStr;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

//    /**
//     * 用户姓名
//     */
//    private String customerName;
//
    /**
     * 用户手机号
     */
    private String customerPhone;

    /**
     * 核销时间
     */
    private LocalDateTime verificationTime;

    /**
     * 门店安装费状态 1未到账2已未到账
     */
    private Integer shopFeeStatus;
//
//    /**
//     * 门店老板电话
//     */
//    private String phoneNumber;
//
//    /**
//     * 门店地址
//     */
//    private String shopAddress;


//
//
//    /**
//     * 优惠券编码
//     */
//    private String couponCode;
//
//
    /**
     * 下单时间
     */
    private LocalDateTime createTime;


    /**
     * 核销人员名字
     */
    private String verificationName;
//
//    /**
//     * 订单商品数量
//     */
//    private Integer totalGoodsNumber;

    /**
     * 商品信息
     */
    private List<TeboOrderGoodsDetailVO> goodsList;

    public String getOrderAmount() {
        if(orderAmount == null) {
            return null;
        }
        return MoneyUtil.fenToYuan(orderAmount);
    }
    public String getShopFee() {
        if(shopFee == null) {
            return null;
        }
        return MoneyUtil.fenToYuan(shopFee);
    }
}
