package com.tebo.mall.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 品牌表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Getter
@Setter
@TableName("tebo_brand")
public class TeboBrandDO extends Model<TeboBrandDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *合伙人id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 品牌编号
     */
    @TableField("brand_no")
    private String brandNo;

    /**
     * 品牌名称
     */
    @TableField("brand_name")
    private String brandName;

    /**
     * 首字母
     */
    @TableField("first_char")
    private String firstChar;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 品牌logo
     */
    @TableField("brand_logo")
    private String brandLogo;

    /**
     * 品牌专区大图
     */
    @TableField("brand_detail")
    private String brandDetail;

    /**
     * 品牌故事
     */
    @TableField("brand_story")
    private String brandStory;

    /**
     * 是否显示
     */
    @TableField("is_show")
    private Integer isShow;

    /**
     * 是否是品牌制造商 0:否 1:是
     */
    @TableField("is_manufacturer")
    private Integer manufacturer;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField(value = "del_flag",fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建者
     */
    @TableField(value = "create_by",fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by",fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
