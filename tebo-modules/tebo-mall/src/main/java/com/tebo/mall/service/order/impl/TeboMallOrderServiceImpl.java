package com.tebo.mall.service.order.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tebo.common.core.constant.GiftPackUrlConstants;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.core.utils.excel.ExcelUtil;
import com.tebo.common.security.util.MaintainerOnlineUserUtil;
import com.tebo.common.util.DataUtils;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.DistanceUtil;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.mall.api.RemoteIntegralOrderService;
import com.tebo.mall.api.domain.dto.TeboSpecialGoodsVerificationDTO;
import com.tebo.mall.api.domain.dto.TeboUserIntegralDTO;
import com.tebo.mall.api.domain.enums.RemoteConsumerRecordEnum;
import com.tebo.mall.api.domain.view.TeboAttributeRelationVO;
import com.tebo.mall.api.domain.view.TeboGoodsPicVO;
import com.tebo.mall.api.domain.view.TeboGoodsVO;
import com.tebo.mall.api.enums.MallOrderChannelEnum;
import com.tebo.mall.domain.*;
import com.tebo.mall.entity.*;
import com.tebo.mall.enums.*;
import com.tebo.mall.generator.TeboNumberGenerator;
import com.tebo.mall.manager.TeboAddressManger;
import com.tebo.mall.manager.TeboGiftManger;
import com.tebo.mall.manager.TeboGoodsBrandManager;
import com.tebo.mall.manager.TeboOrderCommentManger;
import com.tebo.mall.manager.goods.TeboGoodsManager;

import com.tebo.mall.manager.goods.TeboGoodsPicManager;
import com.tebo.mall.manager.goods.TeboPlatGoodsPicManager;
import com.tebo.mall.manager.order.TeboInsurancePolicyOrderManager;
import com.tebo.mall.manager.order.TeboMallOrderGoodsManager;
import com.tebo.mall.manager.order.TeboMallOrderManager;
import com.tebo.mall.manager.order.TeboOrderGiftGoodsManager;
import com.tebo.mall.mapper.*;
import com.tebo.mall.mapper.category.TeboGoodsCategoryMapper;
import com.tebo.mall.mapper.special.TeboSpecialSupplyGoodsMapper;
import com.tebo.mall.redisson.queue.RedisDelayQueueEnum;
import com.tebo.mall.redisson.queue.RedisDelayQueueUtil;
import com.tebo.mall.service.category.TeboGoodsCategoryService;
import com.tebo.mall.service.goods.TeboGoodsAttributeRelationService;
import com.tebo.mall.service.mq.DisMqService;
import com.tebo.mall.service.order.TeboMallOrderService;
import com.tebo.mall.service.pay.TeboMallOrderPayService;
import com.tebo.mall.service.rewardsPoint.RewardsPointService;
import com.tebo.mall.util.RewardsPointUtil;
import com.tebo.mall.web.domain.dto.TeboInsurancePolicyQueryDTO;
import com.tebo.mall.web.domain.dto.category.TeboCategoryQueryDTO;
import com.tebo.mall.web.domain.dto.TeboGiftQueryDTO;
import com.tebo.mall.web.domain.dto.goods.TeboGoodsAttributeDTO;
import com.tebo.mall.web.domain.dto.goods.TeboGoodsQueryDTO;
import com.tebo.mall.web.domain.dto.goods.special.TeboSpecialSupplyDistrictGoodsQueryDTO;
import com.tebo.mall.web.domain.dto.order.inner.*;
import com.tebo.mall.web.domain.dto.order.outer.TeboMallOrderAddOuterDTO;
import com.tebo.mall.web.domain.dto.order.outer.TeboMallOrderQueryInnerDTO;
import com.tebo.mall.web.domain.dto.order.outer.TeboMallSpecialOrderAddOuterDTO;
import com.tebo.mall.web.domain.dto.order.outer.TeboOrderGoodsAddOuterDTO;
import com.tebo.mall.web.domain.vo.*;
import com.tebo.mall.web.domain.vo.order.*;
import com.tebo.rescue.api.RemoteCouponService;
import com.tebo.rescue.api.RemoteQueueOrderService;
import com.tebo.rescue.api.domain.dto.OrderCouponDTO;
import com.tebo.rescue.api.domain.dto.TeboCouponCustomerDTO;
import com.tebo.mall.web.domain.vo.TeboGoodsAttributeVO;
import com.tebo.mall.web.domain.vo.TeboGoodsCategoryVO;
import com.tebo.mall.web.domain.vo.TeboOrderCommentVO;
import com.tebo.mall.web.domain.vo.WriteOffOrderResultVO;
import com.tebo.mall.web.domain.vo.excel.MallOrderListExcelVO;
import com.tebo.mall.web.domain.vo.order.TeboMallOrderNumberVO;
import com.tebo.mall.web.domain.vo.order.TeboMallOrderVO;
import com.tebo.mall.web.domain.vo.order.TeboOrderGoodsVO;
import com.tebo.rescue.api.domain.dto.TeboCouponMallOrderDTO;
import com.tebo.system.api.RemoteAccountService;
import com.tebo.system.api.RemoteCustomerService;
import com.tebo.system.api.RemoteShopService;
import com.tebo.system.api.RemoteWalletService;
import com.tebo.system.api.domain.dto.RemoteConsumerRecordDTO;
import com.tebo.system.api.domain.dto.TeboShopQueryDTO;
import com.tebo.system.api.domain.dto.wallet.TeboSpecialGoodsDTO;
import com.tebo.system.api.domain.dto.wallet.TeboWalletPlanDTO;
import com.tebo.system.api.domain.dto.wallet.TeboWalletPlanDetailDTO;
import com.tebo.system.api.domain.view.TeboShopListVO;
import com.tebo.system.api.model.TeboConsumer;
import com.tebo.system.api.model.TeboShop;
import lombok.extern.slf4j.Slf4j;
import com.tebo.system.api.model.TeboAccountInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TeboMallOrderServiceImpl implements TeboMallOrderService {
    @Resource
    private TeboMallOrderManager teboMallOrderManager;
    @Resource
    private TeboMallOrderGoodsManager orderGoodsManager;
    @Resource
    private TeboOrderGiftGoodsManager orderGiftGoodsManager;
    @Resource
    private TeboGoodsManager teboGoodsManager;
    @Resource
    private TeboSpecialSupplyDistrictGoodsMapper districtGoodsMapper;
    @Resource
    private RemoteCustomerService customerService;
    @Resource
    private RemoteShopService shopService;
    @Resource
    private TeboGoodsAttributeRelationService attributeRelationService;
    @Resource
    private TeboOrderCommentManger orderCommentManger;
    @Resource
    private RedisDelayQueueUtil redisDelayQueueUtil;
    @Resource
    private TeboAddressManger teboAddressManger;

    @Autowired
    private TeboMallOrderPayService mallOrderPayService;

    @Autowired
    private RemoteAccountService remoteAccountService;
    @Resource
    private RemoteCouponService remoteCouponService;
    @Resource
    private TeboGiftManger teboGiftManger;

    @Autowired
    private RemoteIntegralOrderService remoteIntegralOrderService;

    @Value("${partner.secondCategoryId}")
    private String secondCategoryId;

    @Resource
    private TeboGoodsCategoryService goodsCategoryService;

    @Autowired
    private DisMqService disMqService;

    @Resource
    private TeboInsurancePolicyOrderManager insurancePolicyOrderManager;

    @Resource
    private TeboPlatGoodsPicManager teboPlatGoodsPicManager;

    @Resource
    private TeboGoodsPicManager goodsPicManager;

    @Resource
    private RewardsPointService rewardsPointService;

    @Resource
    private RemoteWalletService remoteWalletService;

    @Resource
    private RemoteShopService remoteShopService;

    @Resource
    private TeboGoodsBrandMapper teboGoodsBrandMapper;

    @Resource
    private TeboGoodsCategoryMapper teboGoodsCategoryMapper;
    @Resource
    private TeboGoodsOrderSplitMapper teboGoodsOrderSplitMapper;
    @Resource
    private TeboGoodsOrderCouponMapper teboGoodsOrderCouponMapper;
    @Resource
    private TeboTenantGoodsNoPriceMapper  teboTenantGoodsNoPriceMapper;
    @Autowired
    private RewardsPointUtil rewardsPointUtil;
    @Resource
    private TeboMallOrderGoodsManager mallOrderGoodsManager;
    @Resource
    private RemoteQueueOrderService remoteQueueOrderService;

    @Override
    public List<TeboMallOrderVO> getAppletOrderList(TeboMallOrderQueryInnerDTO orderDTO) {
        List<TeboMallOrderVO> list = teboMallOrderManager.getOrderList(orderDTO);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        List<Long> orderIdList = list.stream().map(item -> item.getId()).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(orderIdList)) {
            return null;
        }
        TeboInsurancePolicyQueryDTO teboInsurancePolicyQueryDTO = new TeboInsurancePolicyQueryDTO();
        teboInsurancePolicyQueryDTO.setOrderIdList(orderIdList);
        List<TeboInsurancePolicyOrderVO> insuranceOrderList = insurancePolicyOrderManager.getInsurancePolicyOrderList(teboInsurancePolicyQueryDTO);
        Map<Long, TeboInsurancePolicyOrderVO> insurancePolicyOrderVOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(insuranceOrderList)) {
            insurancePolicyOrderVOMap = DataUtils.listToMap(insuranceOrderList, TeboInsurancePolicyOrderVO::getParentOrderId);
        }
        List<Long> shopIdList = list.stream().map(item -> item.getShopId()).collect(Collectors.toList());
        Map<Long, TeboShopListVO> shopMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(shopIdList)) {
            TeboShopQueryDTO teboShopQueryDTO = new TeboShopQueryDTO();
            teboShopQueryDTO.setIdList(shopIdList);
            R<List<TeboShopListVO>> shopResult = shopService.fullyQuery(teboShopQueryDTO);
            List<TeboShopListVO> shopList = shopResult.getData();
            if (CollectionUtil.isNotEmpty(shopList)) {
                shopMap = DataUtils.listToMap(shopList, TeboShopListVO::getId);
            }
        }
        TeboMallOrderQueryDTO queryDTO = new TeboMallOrderQueryDTO();
        queryDTO.setOrderIdList(orderIdList);
        List<TeboOrderGoodsVO> goodsList = orderGoodsManager.getOrderGoodsList(queryDTO);
        Map<Long, List<TeboOrderGiftGoodsVO>> giftGoodsMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(goodsList)) {
            List<TeboOrderGiftGoodsVO> orderGiftGoodList = orderGiftGoodsManager.getOrderGiftGoodsList(orderIdList);
            if (CollectionUtil.isNotEmpty(orderGiftGoodList)) {
                giftGoodsMap = DataUtils.listToGroup(orderGiftGoodList, TeboOrderGiftGoodsVO::getOrderId);
            }
        }
        Map<Long, List<TeboOrderGiftGoodsVO>> finalGiftGoodsMap = giftGoodsMap;
        if (CollectionUtil.isNotEmpty(goodsList)) {
            goodsList.forEach(item -> {
                if (ObjectUtil.isNotEmpty(item.getGoodsAttribute())) {
                    item.setGoodsAttributeList(JSONUtil.toList(item.getGoodsAttribute(), TeboGoodsAttributeVO.class));
                }
            });
        }
        Map<Long, List<TeboOrderGoodsVO>> map = DataUtils.listToGroup(goodsList, TeboOrderGoodsVO::getOrderId);
        if (ObjectUtil.isEmpty(map)) {
            return null;
        }
        Map<Long, TeboShopListVO> finalShopMap = shopMap;
        Map<Long, TeboInsurancePolicyOrderVO> finalInsurancePolicyOrderVOMap = insurancePolicyOrderVOMap;
        list.forEach(item -> {
            item.setGoodsList(map.get(item.getId()));
            if (CollectionUtil.isNotEmpty(finalInsurancePolicyOrderVOMap)) {
                TeboInsurancePolicyOrderVO teboInsurancePolicyOrderVO = finalInsurancePolicyOrderVOMap.get(item.getId());
                if (teboInsurancePolicyOrderVO != null && (teboInsurancePolicyOrderVO.getOrderStatus() == 2 || teboInsurancePolicyOrderVO.getOrderStatus() == 3)) {
                    item.setInsurancePolicyOrderVO(teboInsurancePolicyOrderVO);
                }
            }
            TeboShopListVO shopListVO = finalShopMap.get(item.getShopId());
            if (ObjectUtil.isNotEmpty(shopListVO)) {
                item.setPhoneNumber(shopListVO.getPhoneNumber());
            }
            if (CollectionUtil.isNotEmpty(finalGiftGoodsMap)) {
                List<TeboOrderGiftGoodsVO> orderGiftGoodsVOS = finalGiftGoodsMap.get(item.getId());
                if (CollectionUtil.isNotEmpty(orderGiftGoodsVOS)) {
                    Map<String, List<TeboOrderGiftGoodsVO>> goodsMap = DataUtils.listToGroup(orderGiftGoodsVOS, TeboOrderGiftGoodsVO::getGoodsNo);
                    List<TeboOrderGoodsVO> goodsVoList = item.getGoodsList();
                    goodsVoList.forEach(goodsVO -> {
                        List<TeboOrderGiftGoodsVO> giftList = goodsMap.get(goodsVO.getGoodsNo());
                        if (CollectionUtil.isNotEmpty(giftList)) {
                            giftList.forEach(gift -> {
                                gift.setGoodsName(gift.getGiftName());
                                gift.setMainPic(gift.getPicUrl());
                                gift.setGoodsNumber(1);
                                gift.setTradeInPrice(gift.getPrice());
                            });
                        }
                        goodsVO.setGiftList(giftList);
                    });

                }
            }
        });
        return list;
    }

    @Override
    public List<TeboMallOrderVO> getOrderList(TeboMallOrderQueryInnerDTO orderDTO) {
        List<TeboMallOrderVO> list = teboMallOrderManager.getOrderList(orderDTO);
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        Map<Long, TeboShopListVO> goodsMap = new HashMap<>();
        List<Long> shopIdList = list.stream().map(item -> item.getShopId()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(shopIdList)) {
            TeboShopQueryDTO queryDTO = new TeboShopQueryDTO();
            queryDTO.setIdList(shopIdList);
            R<List<TeboShopListVO>> shopResult = shopService.fullyQuery(queryDTO);
            if (shopResult.getCode() != 200) {
                throw new ServiceException(shopResult.getMsg());
            }
            List<TeboShopListVO> result = shopResult.getData();
            if (CollectionUtil.isNotEmpty(result)) {
                goodsMap = DataUtils.listToMap(result, TeboShopListVO::getId);
            }
        }
        Map<Long, TeboShopListVO> finalGoodsMap = goodsMap;
        list.forEach(item -> {
            TeboShopListVO shop = finalGoodsMap.get(item.getShopId());
            if (ObjectUtil.isNotEmpty(shop)) {
                // item.setShopName(shop.getShopName());
                item.setShopAddress(shop.getAreaName() + shop.getAddress());
            }

        });
        List<Long> orderIds = list.stream().map(TeboMallOrderVO::getId).collect(Collectors.toList());
        TeboMallOrderQueryDTO orderQueryDTO = new TeboMallOrderQueryDTO();
        orderQueryDTO.setOrderIdList(orderIds);
        List<TeboOrderGoodsVO> orderGoodsList = mallOrderGoodsManager.getOrderGoodsList(orderQueryDTO);
        Map<Long,List<TeboOrderGoodsVO>> goodsListMap = orderGoodsList.stream().collect(Collectors.groupingBy(TeboOrderGoodsVO::getOrderId));
        list.forEach(item -> {
            List<TeboOrderGoodsVO> goodsList = goodsListMap.get(item.getId());
            item.setGoodsList(goodsList);
        });
        return list;
    }

    @Override
    public void exportOrderList(HttpServletResponse response, TeboMallOrderQueryInnerDTO orderDTO) {
        List<TeboMallOrderVO> orderList = getOrderList(orderDTO);

        List<MallOrderListExcelVO> listExcelVOS = BeanConvert.copyList(orderList, MallOrderListExcelVO::new);
        for (MallOrderListExcelVO listExcelVO : listExcelVOS) {
            Integer orderStatus = listExcelVO.getOrderStatus();
            if (Objects.nonNull(orderStatus)) {
                MallOrderStatusEnum orderStatusEnum = MallOrderStatusEnum.getMallOrderStatus(orderStatus);
                if (Objects.nonNull(orderStatusEnum)) {
                    listExcelVO.setOrderStatusName(orderStatusEnum.getMsg());
                }
            }
            Integer serviceType = listExcelVO.getServiceType();
            if (Objects.nonNull(serviceType)) {
                String serviceTypeEnum = TeboMallOrderServiceTypeEnum.getServiceTypeEnum(serviceType);
                listExcelVO.setServiceTypeName(serviceTypeEnum);
            }
            if (Objects.nonNull(listExcelVO.getChannel())) {
                listExcelVO.setChannelStr(MallOrderChannelEnum.getMallOrderStatus(listExcelVO.getChannel()).getMsg());
            }
            if (CollectionUtil.isNotEmpty(listExcelVO.getGoodsList())) {
                listExcelVO.setGoodsName(listExcelVO.getGoodsList().get(0).getGoodsName());
                if (listExcelVO.getGoodsList().get(0).getPriceType() == 1) {
                    listExcelVO.setPriceTypeName("以旧换新");
                }
                if (listExcelVO.getGoodsList().get(0).getPriceType() == 2) {
                    listExcelVO.setPriceTypeName("直接购买");
                }
            }
            listExcelVO.setCompleteTime(listExcelVO.getVerificationTime());
        }
        ExcelUtil.exportExcelToResponse(response, listExcelVOS, MallOrderListExcelVO.class, "商城订单");
    }

    @Override
    public TeboMallOrderNumberVO getOrderCount(TeboMallOrderQueryInnerDTO orderDTO) {
        List<TeboMallOrderVO> list = teboMallOrderManager.getOrderList(orderDTO);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        TeboMallOrderNumberVO numberVO = new TeboMallOrderNumberVO();
        numberVO.setToBePaid(list.stream().filter(item -> item.getOrderStatus() == 1).count());
        numberVO.setToBePickedUp(list.stream().filter(item -> item.getOrderStatus() == 2).count());
        return numberVO;
    }

    @Override
    @Transactional
    public Map createOrder(TeboMallOrderAddOuterDTO outerAddDTO) {
        TeboMallOrderAddDTO innerAddDto = new TeboMallOrderAddDTO();
        BeanConvert.copy(outerAddDTO, innerAddDto);
        innerAddDto.setId(SnowFlakeUtil.nextId());
        innerAddDto.setOrderStatus(MallOrderStatusEnum.TO_BE_PAID.getCode());
        Map<Long, TeboGoodsVO> goodsMap = new HashMap<>();
        /**
         * 根据unionid查询用户信息
         */
        R<TeboConsumer> teboConsumerResult = customerService.selectByUnionId(outerAddDTO.getUnionid());
        if (teboConsumerResult.getCode() != 200) {
            throw new GlobalException(teboConsumerResult.getMsg());
        }
        if (ObjectUtil.isNotEmpty(teboConsumerResult.getData())) {
            TeboConsumer consumer = teboConsumerResult.getData();
            innerAddDto.setCustomerName(consumer.getNickName());
            innerAddDto.setCustomerPhone(consumer.getPhoneNumber());
        }
        /**
         * 根据shopId查询门店信息
         */
        R<TeboShop> shopResult = shopService.getShopInfo(outerAddDTO.getShopId());
        if (shopResult.getCode() != 200 || ObjectUtil.isEmpty(shopResult)) {
            throw new GlobalException("店铺信息不存在");
        }
        TeboShop teboShop = shopResult.getData();
        if (outerAddDTO.getServiceType() == 2) {
            TeboAddressDO teboAddressDO = teboAddressManger.getAddressById(outerAddDTO.getAddressId());
            if (ObjectUtil.isNotEmpty(teboAddressDO)) {
                innerAddDto.setAddress(teboAddressDO.getRegion() + teboAddressDO.getDetailedAddress());
            }
            innerAddDto.setHomeServiceFee(teboShop.getHomeServiceFee());
            /**
             * 校验门店距离用户的距离，如果超过6公里
             */
            Double distance = DistanceUtil.getDistance(teboShop.getLongitude(), teboShop.getLatitude(), teboAddressDO.getLongitude(), teboAddressDO.getLatitude());
            if (distance > 6) {
                throw new GlobalException("距离超过6公里，不提供服务");
            }
        }
        innerAddDto.setShopName(teboShop.getShopName());
        innerAddDto.setTenantId(teboShop.getTenantId());
        innerAddDto.setTenantName(teboShop.getTenantName());
        innerAddDto.setShopAddress(teboShop.getAddress());
        List<TeboOrderGoodsAddOuterDTO> goodsList = outerAddDTO.getGoodsList();
        /**
         * 商品信息
         */
        List<Long> goodsIdList = goodsList.stream().map(goods -> goods.getGoodsId()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(goodsIdList)) {
            TeboGoodsQueryDTO teboGoodsQueryDTO = new TeboGoodsQueryDTO();
            teboGoodsQueryDTO.setGoodsIdList(goodsIdList);
            List<TeboGoodsVO> teboGoodsList = teboGoodsManager.listGoodsIncludeDeleted(teboGoodsQueryDTO);
            if (CollectionUtil.isNotEmpty(teboGoodsList)) {
                goodsMap = DataUtils.listToMap(teboGoodsList, TeboGoodsVO::getId);
            }
            Integer channel = buildChannel(teboGoodsList);
            if (ObjectUtil.isNotEmpty(channel)) {
                innerAddDto.setChannel(channel);
            }
        }
        //商品总数
        int totalGoodsNumber = goodsList.stream().mapToInt(item -> item.getGoodsNumber()).sum();
        innerAddDto.setTotalGoodsNumber(totalGoodsNumber);
        List<TeboOrderGoodsAddDTO> innerOrderGoodsList = new ArrayList<>();
        Map<Long, TeboGoodsVO> finalGoodsMap = goodsMap;
        /**
         * 商品属性信息
         */
        TeboGoodsAttributeDTO teboGoodsAttributeDTO = new TeboGoodsAttributeDTO();
        teboGoodsAttributeDTO.setGoodsIdList(goodsIdList);
        List<TeboAttributeRelationVO> attributeList = attributeRelationService.listGoodsAttribute(teboGoodsAttributeDTO);
        Map<Long, List<TeboAttributeRelationVO>> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(attributeList)) {
            map = DataUtils.listToGroup(attributeList, TeboAttributeRelationVO::getGoodsId);
        }
        /**
         * 拼接订单商品信息
         */
        Map<Long, List<TeboAttributeRelationVO>> finalAttrobuteMap = map;
        /**
         * 所有的赠品id
         */
        List<TeboOrderGiftGoodsDO> orderGiftGoodsList = new ArrayList<>();
        List<Long> allGiftIdList = new ArrayList<>();
        Map<Long, TeboGiftDO> teboGiftDOMap = new HashMap<>();
        goodsList.forEach(item -> {
            if (CollectionUtil.isNotEmpty(item.getGiftIdList())) {
                allGiftIdList.addAll(item.getGiftIdList());
            }
        });
        if (CollectionUtil.isNotEmpty(allGiftIdList)) {
            TeboGiftQueryDTO teboGiftQueryDTO = new TeboGiftQueryDTO();
            teboGiftQueryDTO.setIds(allGiftIdList);
            List<TeboGiftDO> list = teboGiftManger.giftList(teboGiftQueryDTO);
            if (CollectionUtil.isNotEmpty(list)) {
                teboGiftDOMap = DataUtils.listToMap(list, TeboGiftDO::getId);
            }
        }
        Map<Long, TeboGiftDO> finalTeboGiftDOMap = teboGiftDOMap;
        goodsList.forEach(item -> {
            TeboOrderGoodsAddDTO goodsAddInnerDTO = new TeboOrderGoodsAddDTO();
            TeboGoodsVO teboGoodsVO = finalGoodsMap.get(item.getGoodsId());
            if (teboGoodsVO == null || teboGoodsVO.getStatus() == 2 || teboGoodsVO.getDelFlag() == 1) {
                throw new GlobalException(teboGoodsVO.getGoodsName() + " 已下架");
            }
            goodsAddInnerDTO.setOrderId(innerAddDto.getId());
            BeanConvert.copy(item, goodsAddInnerDTO);
            if (ObjectUtil.isNotEmpty(teboGoodsVO)) {
                BeanConvert.copy(teboGoodsVO, goodsAddInnerDTO);
            }
            goodsAddInnerDTO.setCreateTime(null);
            if (ObjectUtil.isNotEmpty(teboGoodsVO.getShopCommission())) {
                goodsAddInnerDTO.setShopCommission(MoneyUtil.yuanToFen(teboGoodsVO.getShopCommission()));
            }
            if (ObjectUtil.isNotEmpty(teboGoodsVO.getPartnerCommission())) {
                goodsAddInnerDTO.setPartnerCommission(MoneyUtil.yuanToFen(teboGoodsVO.getPartnerCommission()));
            }
            /**
             * 商品积分
             */
            BigDecimal integrationCoefficient = teboShop.getIntegrationCoefficient();
            if (ObjectUtil.isNotEmpty(integrationCoefficient)) {
                goodsAddInnerDTO.setIntegral(0);
                if (ObjectUtil.isNotEmpty(teboGoodsVO.getBasicIntegral())) {
                    BigDecimal multiply = new BigDecimal(teboGoodsVO.getBasicIntegral()).multiply(integrationCoefficient).multiply(new BigDecimal(item.getGoodsNumber()));
                    goodsAddInnerDTO.setIntegral(multiply.intValue());
                }

            }
            goodsAddInnerDTO.setCreateTime(null);
            List<TeboAttributeRelationVO> list = finalAttrobuteMap.get(item.getGoodsId());
            if (CollectionUtil.isNotEmpty(list)) {
                goodsAddInnerDTO.setGoodsAttribute(JSONObject.toJSONString(BeanConvert.copyList(list, TeboGoodsAttributeVO::new)));
            }
            if (ObjectUtil.isNotEmpty(teboGoodsVO.getBusinessCategory())) {
                goodsAddInnerDTO.setBusinessCategory(TeboGoodsCategoryEnum.getGoodsCategoryEnum(teboGoodsVO.getBusinessCategory()));
            }
            /**
             * 商品价格
             */
            if (item.getPriceType() == 1) {
                goodsAddInnerDTO.setPrice(MoneyUtil.yuanToFen(teboGoodsVO.getTradeInPrice()));
            } else if (item.getPriceType() == 2) {
                goodsAddInnerDTO.setPrice(MoneyUtil.yuanToFen(teboGoodsVO.getSalePrice()));
            }

            if (!MoneyUtil.yuanToFen(item.getPrice()).equals(goodsAddInnerDTO.getPrice())) {
                throw new GlobalException(teboGoodsVO.getGoodsName() + " 价格已变动，请重新加购");
            }
            goodsAddInnerDTO.setId(SnowFlakeUtil.nextId());
            List<Long> giftIdList = item.getGiftIdList();
            /**
             * 赠品
             */
            if (CollectionUtil.isNotEmpty(giftIdList)) {
                giftIdList.forEach(gift -> {
                    TeboOrderGiftGoodsDO giftOrderGoods = new TeboOrderGiftGoodsDO();
                    if (CollectionUtil.isNotEmpty(finalTeboGiftDOMap) && ObjectUtil.isNotEmpty(finalTeboGiftDOMap.get(gift))) {
                        TeboGiftDO teboGiftDO = finalTeboGiftDOMap.get(gift);
                        BeanConvert.copy(teboGiftDO, giftOrderGoods);
                        giftOrderGoods.setGiftId(teboGiftDO.getId());
                        giftOrderGoods.setOrderId(innerAddDto.getId());
                        giftOrderGoods.setGoodsNo(teboGoodsVO.getGoodsNo());
                        giftOrderGoods.setId(SnowFlakeUtil.nextId());
                        giftOrderGoods.setCreateTime(null);
                        orderGiftGoodsList.add(giftOrderGoods);
                    }
                });
            }
            innerOrderGoodsList.add(goodsAddInnerDTO);
        });
        //订单积分
        Integer orderIntegral = innerOrderGoodsList.stream().mapToInt(item -> item.getIntegral()).sum();
        innerAddDto.setIntegral(orderIntegral);
        /**
         * 订单优惠券
         */
        if (StringUtils.isNotEmpty(outerAddDTO.getCouponCode())) {
            R<TeboCouponCustomerDTO> couponResult = remoteCouponService.getDetailByCouponCode(outerAddDTO.getCouponCode());
            if (couponResult.getCode() != 200) {
                throw new GlobalException("获取优惠券接口异常");
            }
            TeboCouponCustomerDTO couponCustomerDTO = couponResult.getData();
            /**
             * 优惠券金额
             */
            if (ObjectUtil.isNotEmpty(couponCustomerDTO)) {
                innerAddDto.setParValue(MoneyUtil.yuanToFen(couponCustomerDTO.getParValue()));
                innerAddDto.setCouponCode(outerAddDTO.getCouponCode());
            }
        }
        /**
         * 最终的订单金额
         */
        Integer homeServiceFee = innerAddDto.getHomeServiceFee() == null ? 0 : innerAddDto.getHomeServiceFee();
        Integer parValue = innerAddDto.getParValue() == null ? 0 : innerAddDto.getParValue();
        //订单金额
        Integer orderAmount = innerOrderGoodsList.stream().mapToInt(item -> item.getGoodsNumber() * item.getPrice()).sum();
        //最终支付金额
        Integer finalOrderAmount = orderAmount + homeServiceFee - parValue;
        if (finalOrderAmount <= 0) {
            innerAddDto.setOrderAmount("0");
            innerAddDto.setOrderStatus(MallOrderStatusEnum.TO_BO_PICKER_UP.getCode());
            /**
             * 更新优惠券为已占用
             */
            if (ObjectUtil.isNotEmpty(innerAddDto.getCouponCode())) {
                OrderCouponDTO orderCouponDTO = new OrderCouponDTO();
                orderCouponDTO.setShopId(innerAddDto.getShopId());
                orderCouponDTO.setShopName(innerAddDto.getShopName());
                orderCouponDTO.setCouponCode(innerAddDto.getCouponCode());
                orderCouponDTO.setOrderNo(innerAddDto.getOrderNo());
                remoteCouponService.updateCouponStatus(orderCouponDTO);
            }
        } else {
            innerAddDto.setOrderAmount(finalOrderAmount.toString());
        }
        /**
         * 订单编号
         */
        Long goodsCategory = innerOrderGoodsList.get(0).getSecondCategoryId();
        List<String> secondCategoryList = Arrays.asList(secondCategoryId.split(","));
        if (secondCategoryList.contains(goodsCategory.toString())) {
            innerAddDto.setOrderNo(TeboNumberGenerator.generateMallOrderNo(2));
        } else {
            innerAddDto.setOrderNo(TeboNumberGenerator.generateMallOrderNo(1));
        }
        //创建订单
        teboMallOrderManager.createOrder(innerAddDto);
        //创建订单商品
        if (CollectionUtil.isNotEmpty(innerOrderGoodsList)) {
            orderGoodsManager.createOrderGoods(innerOrderGoodsList);
        }
        //创建订单赠品
        if (CollectionUtil.isNotEmpty(orderGiftGoodsList)) {
            orderGiftGoodsManager.batchInsert(orderGiftGoodsList);
        }
        RemoteConsumerRecordDTO recordDTO = new RemoteConsumerRecordDTO();
        recordDTO.setLastType(RemoteConsumerRecordEnum.MALL.getType());
        recordDTO.setShopId(outerAddDTO.getShopId());
        recordDTO.setUnionId(outerAddDTO.getUnionid());
        disMqService.recordConsumerRecord(recordDTO);
        Map<String, String> param = new HashMap<>();
        param.put("orderId", innerAddDto.getId().toString());
        param.put("remark", "商城订单支付超时，自动取消订单");
        // 添加订单支付超时，自动取消订单延迟队列。为了测试效果，延迟10秒钟
        redisDelayQueueUtil.addDelayQueue(param, 15, TimeUnit.MINUTES, RedisDelayQueueEnum.TEBO_MALL_ORDER_PAYMENT_TIMEOUT.getCode());
        Map<String, Object> result = new HashMap<>();
        result.put("orderId", innerAddDto.getId());
        result.put("needPay", finalOrderAmount > 0 ? 1 : 0);
        return result;
    }
    @Override
    public Map createSpecialOrder(TeboMallSpecialOrderAddOuterDTO outerAddDTO){
        TeboMallOrderAddDTO innerAddDto = new TeboMallOrderAddDTO();
        BeanConvert.copy(outerAddDTO, innerAddDto);
        innerAddDto.setId(SnowFlakeUtil.nextId());
        innerAddDto.setOrderStatus(MallOrderStatusEnum.TO_BE_PAID.getCode());
        Map<Long, TeboSpecialSupplyDistrictGoodsDO> goodsMap = new HashMap<>();
        /**
         * 根据unionid查询用户信息
         */
        R<TeboConsumer> teboConsumerResult = customerService.selectByUnionId(outerAddDTO.getUnionid());
        if (teboConsumerResult.getCode() != 200) {
            throw new GlobalException(teboConsumerResult.getMsg());
        }
        if (ObjectUtil.isNotEmpty(teboConsumerResult.getData())) {
            TeboConsumer consumer = teboConsumerResult.getData();
            innerAddDto.setCustomerName(consumer.getNickName());
            innerAddDto.setCustomerPhone(consumer.getPhoneNumber());
        }
        /**
         * 根据shopId查询门店信息
         */
        R<TeboShop> shopResult = shopService.getShopInfo(outerAddDTO.getShopId());
        if (shopResult.getCode() != 200 || ObjectUtil.isEmpty(shopResult)) {
            throw new GlobalException("店铺信息不存在");
        }
        TeboShop teboShop = shopResult.getData();
        if (ObjectUtils.nullSafeEquals(teboShop.getVip(), 0)) {
            throw new GlobalException("当前门店为非超服店，无法购买");
        }
        innerAddDto.setShopName(teboShop.getShopName());
        innerAddDto.setTenantId(teboShop.getTenantId());
        innerAddDto.setTenantName(teboShop.getTenantName());
        innerAddDto.setShopAddress(teboShop.getAddress());
        innerAddDto.setAreaName(teboShop.getAreaName());
        /**
         * 商品信息
         */
        TeboSpecialSupplyDistrictGoodsDO districtGoodsDO = districtGoodsMapper.selectById(outerAddDTO.getGoodsId());
        int count = 1;
        while (count <= 5 && ObjectUtil.isEmpty(districtGoodsDO)) {
            districtGoodsDO = districtGoodsMapper.selectById(outerAddDTO.getGoodsId());
            count++;
            if (ObjectUtil.isNotEmpty(districtGoodsDO)){
                break;
            }
        }
        // 判断专供品是否配置服务费
        String goodsNo = districtGoodsDO.getGoodsNo();
        Long tenantId = teboShop.getTenantId();
        LambdaQueryWrapper<TeboTenantGoodsNoPriceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeboTenantGoodsNoPriceDO::getTenantId, tenantId)
                .eq(TeboTenantGoodsNoPriceDO::getGoodsNo, goodsNo);
        Long goodsCount = teboTenantGoodsNoPriceMapper.selectCount(queryWrapper);
        if (goodsCount == 0) {
            throw new GlobalException("玖玖电池未维护服务费，暂不支持此交易！");
        }
        //商品总数
        int totalGoodsNumber = 1;
        innerAddDto.setTotalGoodsNumber(totalGoodsNumber);
        List<TeboOrderGoodsAddDTO> innerOrderGoodsList = new ArrayList<>();
            TeboOrderGoodsAddDTO goodsAddInnerDTO = new TeboOrderGoodsAddDTO();
            goodsAddInnerDTO.setOrderId(innerAddDto.getId());
            goodsAddInnerDTO.setGoodsId(outerAddDTO.getGoodsId());
            if (ObjectUtil.isNotEmpty(districtGoodsDO)) {
                BeanConvert.copy(districtGoodsDO, goodsAddInnerDTO);
            }
            goodsAddInnerDTO.setCreateTime(null);
            goodsAddInnerDTO.setGoodsNumber(1);
            TeboGoodsBrandDO teboGoodsBrandDO = teboGoodsBrandMapper.selectById(districtGoodsDO.getBrandId());
            goodsAddInnerDTO.setBrandName(teboGoodsBrandDO.getBrandName());
            TeboGoodsCategoryDO teboGoodsCategoryDO = teboGoodsCategoryMapper.selectById(districtGoodsDO.getCategoryId());
            goodsAddInnerDTO.setCategoryName(teboGoodsCategoryDO.getCategoryName());
            TeboGoodsCategoryDO secondCategoryDO = teboGoodsCategoryMapper.selectById(districtGoodsDO.getSecondCategoryId());
           goodsAddInnerDTO.setSecondCategoryName(secondCategoryDO.getCategoryName());
            if (ObjectUtil.isNotEmpty(districtGoodsDO.getBusinessCategory())) {
                goodsAddInnerDTO.setBusinessCategory(TeboGoodsCategoryEnum.getGoodsCategoryEnum(districtGoodsDO.getBusinessCategory()));
            }
        /**
         * 商品价格
         */
        if (outerAddDTO.getPriceType() == 1) {
            goodsAddInnerDTO.setPrice(districtGoodsDO.getTradeInPrice());
        } else if (outerAddDTO.getPriceType() == 2) {
            goodsAddInnerDTO.setPrice(districtGoodsDO.getSalePrice());
        }
        goodsAddInnerDTO.setId(SnowFlakeUtil.nextId());
        goodsAddInnerDTO.setPriceType(outerAddDTO.getPriceType());
        innerOrderGoodsList.add(goodsAddInnerDTO);
        //订单金额
        Integer orderAmount = goodsAddInnerDTO.getPrice();
        Integer parAmount = 0;
        /**
         * 最终的订单金额
         */
        if (CollectionUtil.isNotEmpty(outerAddDTO.getCouponIdList())) {
            for (String couponCode:outerAddDTO.getCouponIdList()){
                R<TeboCouponCustomerDTO> couponResult = remoteCouponService.getDetailByCouponCode(couponCode);
                if (couponResult.getCode() == 200) {
                    TeboGoodsOrderCouponDO couponDO = new TeboGoodsOrderCouponDO();
                    // 包含两种券，金额剪掉指定金额
                    if (ObjectUtils.nullSafeEquals(couponResult.getData().getParValue(), "30.00")) {
                        parAmount += 3000;
                        couponDO.setId(SnowFlakeUtil.nextId());
                        couponDO.setOrderId(innerAddDto.getId());
                        couponDO.setUniqueCode(couponCode);
                        couponDO.setOrderNo(couponResult.getData().getOrderNo());
                        couponDO.setAmount(3000);
                        teboGoodsOrderCouponMapper.insert(couponDO);
                        // 更新优惠券为已占用
                        OrderCouponDTO orderCouponDTO = new OrderCouponDTO();
                        orderCouponDTO.setShopId(innerAddDto.getShopId());
                        orderCouponDTO.setShopName(innerAddDto.getShopName());
                        orderCouponDTO.setCouponCode(couponCode);
                        orderCouponDTO.setOrderNo(innerAddDto.getOrderNo());
                        orderCouponDTO.setUpdateTime(LocalDateTime.now());
                        orderCouponDTO.setOccStatus(2);
                        orderCouponDTO.setOccNumber(innerAddDto.getId().toString());
                        remoteCouponService.updateCouponOccStatus(orderCouponDTO);
                    }
                    if (ObjectUtils.nullSafeEquals(couponResult.getData().getParValue(), "20.00")) {
                        parAmount += 2000;
                        couponDO.setId(SnowFlakeUtil.nextId());
                        couponDO.setOrderId(innerAddDto.getId());
                        couponDO.setUniqueCode(couponCode);
                        couponDO.setOrderNo(couponResult.getData().getOrderNo());
                        couponDO.setAmount(2000);
                        teboGoodsOrderCouponMapper.insert(couponDO);
                        // 更新优惠券为已占用
                        OrderCouponDTO orderCouponDTO = new OrderCouponDTO();
                        orderCouponDTO.setShopId(innerAddDto.getShopId());
                        orderCouponDTO.setShopName(innerAddDto.getShopName());
                        orderCouponDTO.setCouponCode(couponCode);
                        orderCouponDTO.setOrderNo(innerAddDto.getOrderNo());
                        orderCouponDTO.setOccStatus(2);
                        orderCouponDTO.setOccNumber(innerAddDto.getId().toString());
                        remoteCouponService.updateCouponOccStatus(orderCouponDTO);
                    }
                }
            }
        }
        orderAmount -= parAmount;
        innerAddDto.setOrderAmount(orderAmount.toString());
        innerAddDto.setParValue(parAmount);
        /**
         * 订单编号
         */
        innerAddDto.setOrderNo(innerAddDto.getId().toString());
        innerAddDto.setOrderType(12);
        innerAddDto.setServiceType(1);
        innerAddDto.setTotalGoodsNumber(1);
        //创建订单
        teboMallOrderManager.createOrder(innerAddDto);
        //创建订单商品
        if (CollectionUtil.isNotEmpty(innerOrderGoodsList)) {
            orderGoodsManager.createOrderGoods(innerOrderGoodsList);
        }
        Map<String, String> param = new HashMap<>();
        param.put("orderId", innerAddDto.getId().toString());
        param.put("remark", "商城订单支付超时，自动取消订单");
        // 添加订单支付超时，自动取消订单延迟队列。为了测试效果，延迟10秒钟
        redisDelayQueueUtil.addDelayQueue(param, 5, TimeUnit.MINUTES, RedisDelayQueueEnum.TEBO_MALL_ORDER_PAYMENT_TIMEOUT.getCode());
        Map<String, Object> result = new HashMap<>();
        result.put("orderId", innerAddDto.getId());
        result.put("needPay", 1);
        return result;
    }

    @Override
    public Long buyAnotherOrder(TeboMallOrderAddOuterDTO addDTO) {
        TeboMallOrderVO teboMallOrderVO = getDetailById(addDTO.getOrderId());
        if (ObjectUtil.isEmpty(teboMallOrderVO)) {
            throw new GlobalException("订单不存在");
        }
        TeboMallOrderAddDTO innerAddDto = new TeboMallOrderAddDTO();
        innerAddDto.setUnionid(addDTO.getUnionid());
        innerAddDto.setShopId(teboMallOrderVO.getShopId());
        innerAddDto.setServiceType(teboMallOrderVO.getServiceType());
        innerAddDto.setId(SnowFlakeUtil.nextId());
        /**
         * 根据unionid查询用户信息
         */
        R<TeboConsumer> teboConsumerResult = customerService.selectByUnionId(addDTO.getUnionid());
        if (teboConsumerResult.getCode() != 200) {
            throw new GlobalException(teboConsumerResult.getMsg());
        }
        if (ObjectUtil.isNotEmpty(teboConsumerResult.getData())) {
            TeboConsumer consumer = teboConsumerResult.getData();
            innerAddDto.setCustomerName(consumer.getNickName());
            innerAddDto.setCustomerPhone(consumer.getPhoneNumber());
        }
        /**
         * 根据shopId查询门店信息
         */
        R<TeboShop> shopResult = new R<>();
        if (teboMallOrderVO.getOrderType() == 1) {
            shopResult = shopService.getShopInfo(teboMallOrderVO.getShopId());
            if (shopResult.getCode() != 200 || ObjectUtil.isEmpty(shopResult)) {
                throw new GlobalException("店铺信息不存在");
            }
        }
        if (teboMallOrderVO.getServiceType() == 2) {
            innerAddDto.setHomeServiceFee(shopResult.getData().getHomeServiceFee());
        }
        TeboShop teboShop = shopResult.getData();
        innerAddDto.setShopName(teboShop.getShopName());
        innerAddDto.setTenantId(teboShop.getTenantId());
        innerAddDto.setTenantName(teboShop.getTenantName());
        innerAddDto.setShopAddress(teboShop.getAddress());
        innerAddDto.setOrderStatus(MallOrderStatusEnum.TO_BE_PAID.getCode());
        innerAddDto.setAddress(teboMallOrderVO.getAddress());
        innerAddDto.setShopAddress(teboShop.getAddress());
        Map<Long, TeboGoodsVO> goodsMap = new HashMap<>();
        List<TeboOrderGoodsVO> goodsList = teboMallOrderVO.getGoodsList();
        /**
         * 赠品
         */
        List<String> goodsNoList = goodsList.stream().map(item -> item.getGoodsNo()).collect(Collectors.toList());
        Map<String, List<TeboGiftVO>> giftMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(teboMallOrderVO.getTenantId())) {
            giftMap = teboGiftManger.getGiftListByGoodsNo(goodsNoList, teboMallOrderVO.getTenantId());
        }
        /**
         * 商品信息
         */
        List<Long> goodsIdList = goodsList.stream().map(goods -> goods.getGoodsId()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(goodsIdList)) {
            TeboGoodsQueryDTO teboGoodsQueryDTO = new TeboGoodsQueryDTO();
            teboGoodsQueryDTO.setGoodsIdList(goodsIdList);
            List<TeboGoodsVO> teboGoodsList = new ArrayList<>();
            if (teboMallOrderVO.getOrderType() == 1) {
                teboGoodsList = teboGoodsManager.listGoodsIncludeDeleted(teboGoodsQueryDTO);
            }
            Integer channel = buildChannel(teboGoodsList);
            if (ObjectUtil.isNotEmpty(channel)) {
                innerAddDto.setChannel(channel);
            }
            if (CollectionUtil.isNotEmpty(teboGoodsList)) {
                goodsMap = DataUtils.listToMap(teboGoodsList, TeboGoodsVO::getId);
            }
        }
        //商品总数
        int totalGoodsNumber = goodsList.stream().mapToInt(item -> item.getGoodsNumber()).sum();
        innerAddDto.setTotalGoodsNumber(totalGoodsNumber);
        List<TeboOrderGoodsAddDTO> innerOrderGoodsList = new ArrayList<>();
        Map<Long, TeboGoodsVO> finalGoodsMap = goodsMap;
        Map<String, List<TeboGiftVO>> finalGiftMap = giftMap;
        List<TeboOrderGiftGoodsDO> orderGiftGoodsList = new ArrayList<>();
        goodsList.forEach(item -> {
            TeboOrderGoodsAddDTO goodsAddInnerDTO = new TeboOrderGoodsAddDTO();
            TeboGoodsVO teboGoodsVO = finalGoodsMap.get(item.getGoodsId());
            if (teboGoodsVO == null || teboGoodsVO.getStatus() == 2 || teboGoodsVO.getDelFlag() == 1) {
                throw new GlobalException(teboGoodsVO.getGoodsName() + " 已下架");
            }
            BeanConvert.copy(item, goodsAddInnerDTO);
            goodsAddInnerDTO.setOrderId(innerAddDto.getId());
            if (ObjectUtil.isNotEmpty(teboGoodsVO)) {
                BeanConvert.copy(teboGoodsVO, goodsAddInnerDTO);
            }
            if (ObjectUtil.isNotEmpty(teboGoodsVO.getBusinessCategory())) {
                goodsAddInnerDTO.setBusinessCategory(TeboGoodsCategoryEnum.getGoodsCategoryEnum(teboGoodsVO.getBusinessCategory()));
            }
            if (item.getPriceType() == 1) {
                goodsAddInnerDTO.setPrice(MoneyUtil.yuanToFen(teboGoodsVO.getTradeInPrice()));
            } else if (item.getPriceType() == 2) {
                goodsAddInnerDTO.setPrice(MoneyUtil.yuanToFen(teboGoodsVO.getSalePrice()));
            }
            goodsAddInnerDTO.setId(SnowFlakeUtil.nextId());
            goodsAddInnerDTO.setCreateTime(null);
            innerOrderGoodsList.add(goodsAddInnerDTO);
            /**
             * 赠品
             */
            if (CollectionUtil.isNotEmpty(finalGiftMap) && ObjectUtil.isNotEmpty(finalGiftMap.get(item.getGoodsNo()))) {
                List<TeboGiftVO> list = finalGiftMap.get(item.getGoodsNo());
                if (CollectionUtil.isNotEmpty(list)) {
                    list.forEach(gift -> {
                        TeboOrderGiftGoodsDO giftOrderGoods = new TeboOrderGiftGoodsDO();
                        BeanConvert.copy(gift, giftOrderGoods);
                        giftOrderGoods.setGiftId(gift.getId());
                        giftOrderGoods.setOrderId(innerAddDto.getId());
                        giftOrderGoods.setGoodsNo(teboGoodsVO.getGoodsNo());
                        giftOrderGoods.setId(SnowFlakeUtil.nextId());
                        giftOrderGoods.setCreateTime(null);
                        orderGiftGoodsList.add(giftOrderGoods);
                    });
                }
            }
        });
        //订单金额
        Integer orderAmount = innerOrderGoodsList.stream().mapToInt(item -> item.getGoodsNumber() * item.getPrice()).sum();
        Integer homeServiceFee = innerAddDto.getHomeServiceFee() == null ? 0 : innerAddDto.getHomeServiceFee();
        Integer finalOrderAmount = orderAmount + homeServiceFee;
        innerAddDto.setOrderAmount(finalOrderAmount.toString());
        //订单积分
        Integer orderIntegral = goodsList.stream().mapToInt(item -> item.getIntegral()).sum();
        innerAddDto.setIntegral(orderIntegral);
        Long goodsCategory = innerOrderGoodsList.get(0).getSecondCategoryId();
        List<String> secondCategoryList = Arrays.asList(secondCategoryId.split(","));
        if (secondCategoryList.contains(goodsCategory.toString())) {
            innerAddDto.setOrderNo(TeboNumberGenerator.generateMallOrderNo(2));
        } else {
            innerAddDto.setOrderNo(TeboNumberGenerator.generateMallOrderNo(1));
        }
        //创建订单
        teboMallOrderManager.createOrder(innerAddDto);
        //创建订单商品
        orderGoodsManager.createOrderGoods(innerOrderGoodsList);
        //创建订单赠品
        if (CollectionUtil.isNotEmpty(orderGiftGoodsList)) {
            orderGiftGoodsManager.batchInsert(orderGiftGoodsList);
        }
        Map<String, String> param = new HashMap<>();
        param.put("orderId", innerAddDto.getId().toString());
        param.put("remark", "商城订单支付超时，自动取消订单");
        // 添加订单支付超时，自动取消订单延迟队列。为了测试效果，延迟10秒钟
        redisDelayQueueUtil.addDelayQueue(param, 15, TimeUnit.MINUTES, RedisDelayQueueEnum.TEBO_MALL_ORDER_PAYMENT_TIMEOUT.getCode());
        return innerAddDto.getId();
    }

    @Override
    public TeboMallOrderVO getDetailById(Long orderId) {
        TeboMallOrderVO teboMallOrderVO = teboMallOrderManager.getDetailById(orderId);
        if (ObjectUtil.isEmpty(teboMallOrderVO)) {
            throw new ServiceException("订单不存在");
        }
        try {
            List<TeboOrderGoodsVO> goodsList = orderGoodsManager.getOrderGoodsByOrderId(orderId);

            Map<String, List<TeboOrderGiftGoodsVO>> giftGoodsMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(goodsList)) {
                List<Long> orderIdList = Arrays.asList(orderId);
                /**
                 * 赠品
                 */
                List<TeboOrderGiftGoodsVO> list = orderGiftGoodsManager.getOrderGiftGoodsList(orderIdList);
                if (CollectionUtil.isNotEmpty(list)) {
                    giftGoodsMap = DataUtils.listToGroup(list, TeboOrderGiftGoodsVO::getGoodsNo);
                }
            }
            Map<String, List<TeboOrderGiftGoodsVO>> finalGiftGoodsMap = giftGoodsMap;
            goodsList.forEach(item -> {
                if (ObjectUtil.isNotEmpty(item.getGoodsAttribute())) {
                    List<TeboGoodsAttributeVO> attributeVOList = JSONUtil.toList(item.getGoodsAttribute(), TeboGoodsAttributeVO.class);
                    item.setGoodsAttributeList(attributeVOList);
                }
                item.setTotalPrice(item.getGoodsNumber() * MoneyUtil.yuanToFen(item.getPrice()));
                if (CollectionUtil.isNotEmpty(finalGiftGoodsMap)) {
                    item.setGiftList(finalGiftGoodsMap.get(item.getGoodsNo()));
                }
                TeboGoodsVO teboGoodsVO = teboGoodsManager.selectById(item.getGoodsId());
                if (ObjectUtil.isNotEmpty(teboGoodsVO)) {
                    Integer productType = teboGoodsVO.getProductType();
                    if (productType == 1) {
                        List<TeboGoodsPicVO> goodsPicList = goodsPicManager.getGoodsPicList(teboGoodsVO.getId());
                        if (CollectionUtil.isNotEmpty(goodsPicList)) {
                            List<String> picList = goodsPicList.stream().map(pic -> pic.getGoodsPic()).collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(picList)) {
                                item.setGoodsPicList(picList);
                            }
                        }
                    } else if (productType == 2) {
                        List<TeboPlatGoodsPicVO> platGoodsPicList = teboPlatGoodsPicManager.getGoodsPicListByGoodsNo(teboGoodsVO.getGoodsNo());
                        if (CollectionUtil.isNotEmpty(platGoodsPicList)) {
                            List<String> goodsPicList = platGoodsPicList.stream().map(pic -> pic.getGoodsPic()).collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(goodsPicList)) {
                                item.setGoodsPicList(goodsPicList);
                            }
                        }
                    }
                }
            });
            teboMallOrderVO.setGoodsList(goodsList);
        } catch (Exception e) {
            log.error("获取商品属性报错", e);
        }
        TeboOrderCommentVO commentVO = orderCommentManger.getCommentsByOrderId(orderId);
        if (ObjectUtil.isEmpty(commentVO)) {
            teboMallOrderVO.setComment(new TeboOrderCommentVO());
        } else {
            teboMallOrderVO.setComment(commentVO);
        }
        teboMallOrderVO.setInsurancePolicyOrderVO(new TeboInsurancePolicyOrderVO());
        if (teboMallOrderVO.getChannel() != null && teboMallOrderVO.getChannel() == 4) {
            TeboInsurancePolicyOrderDO insurancePolicyOrderDO = insurancePolicyOrderManager.selectByParentOrderId(teboMallOrderVO.getId());
            if (ObjectUtil.isNotEmpty(insurancePolicyOrderDO) && (insurancePolicyOrderDO.getOrderStatus() == InsuranceMallOrderStatusEnum.EFFECTIVE.getCode() || insurancePolicyOrderDO.getOrderStatus() == InsuranceMallOrderStatusEnum.PENDING_EFFECTIVENESS.getCode())) {
                TeboInsurancePolicyOrderVO insurancePolicyOrderVO = new TeboInsurancePolicyOrderVO();
                BeanConvert.copy(insurancePolicyOrderDO, insurancePolicyOrderVO);
                if (ObjectUtil.isNotEmpty(insurancePolicyOrderDO.getOrderAmount())) {
                    insurancePolicyOrderVO.setOrderAmount(insurancePolicyOrderDO.getOrderAmount());
                }
                teboMallOrderVO.setInsurancePolicyOrderVO(insurancePolicyOrderVO);
            }
        }
        return teboMallOrderVO;
    }

    @Transactional
    @Override
    public WriteOffOrderResultVO writeOffOrder(String orderNo) {
        if (orderNo.startsWith("TBCXA")){
            throw new ServiceException("专供品订单请到专供品接口核销");
        }
        WriteOffOrderResultVO resultVO = new WriteOffOrderResultVO();
        TeboMallOrderDO orderDO = teboMallOrderManager.getDetailByNo(orderNo);
        List<TeboOrderGoodsVO> goods = orderGoodsManager.getOrderGoodsByOrderId(orderDO.getId());
        Long shopId = orderDO.getShopId();
        TeboShop shop = shopService.getShopInfo(shopId).getData();
        if (Objects.isNull(shop)) {
            throw new ServiceException("未找到门店");
        }
        //积分系数
        BigDecimal integrationCoefficient = shop.getIntegrationCoefficient();
        checkAccountAndOrder(orderDO);
        BeanConvert.copy(orderDO, resultVO);
        resultVO.setOrderTime(orderDO.getCreateTime());
        teboMallOrderManager.orderVerification(orderNo, null);
        if (CollectionUtil.isNotEmpty(goods)) {
            resultVO.setGoodsNameList(goods.stream().map(TeboOrderGoodsVO::getGoodsName).collect(Collectors.toList()));
            resultVO.setGoodsNumber(goods.size());

            for (TeboOrderGoodsVO orderGoodsVO : goods) {
                String goodsNo = orderGoodsVO.getGoodsNo();
                TeboGoodsQueryDTO goodsQueryDTO = new TeboGoodsQueryDTO();
                goodsQueryDTO.setGoodsNo(goodsNo);
                goodsQueryDTO.setTenantId(orderDO.getTenantId());
                goodsQueryDTO.setShopId(orderDO.getShopId());
                List<TeboGoodsVO> goodsVOS = teboGoodsManager.listGoods(goodsQueryDTO);
                if (CollectionUtil.isNotEmpty(goodsVOS)) {
                    TeboGoodsVO teboGoodsVO = goodsVOS.get(0);
                    //积分
                    Integer basicIntegral = teboGoodsVO.getBasicIntegral();
                    if (Objects.nonNull(basicIntegral) && basicIntegral > 0) {
                        if (Objects.isNull(integrationCoefficient)) {
                            throw new ServiceException("门店积分系数为空");
                        }
                        BigDecimal multiply = new BigDecimal(basicIntegral).multiply(integrationCoefficient).multiply(new BigDecimal(orderGoodsVO.getGoodsNumber()));
                        TeboUserIntegralDTO userIntegralDTO = new TeboUserIntegralDTO();
                        userIntegralDTO.setOrderId(orderDO.getId());
                        userIntegralDTO.setIntegral(multiply.intValue());
                        userIntegralDTO.setType(1);
                        userIntegralDTO.setUserAccount(shopId);
                        remoteIntegralOrderService.addIntegral(userIntegralDTO);
                    }
                }
            }
        }
        return resultVO;

    }

    @Transactional
    @Override
    public WriteOffOrderResultVO writeOffSpecialOrder(String orderNo) {
        if (!orderNo.startsWith("TBCXA")){
            throw new ServiceException("非专供品订单请到商城订单核销");
        }
        WriteOffOrderResultVO resultVO = new WriteOffOrderResultVO();
        TeboMallOrderDO orderDO = teboMallOrderManager.getDetailByNo(orderNo);
        List<TeboOrderGoodsVO> goods = orderGoodsManager.getOrderGoodsByOrderId(orderDO.getId());
        Long shopId = orderDO.getShopId();
        TeboShop shop = shopService.getShopInfo(shopId).getData();
        if (Objects.isNull(shop)) {
            throw new ServiceException("未找到门店");
        }
        checkAccountAndOrder(orderDO);
        BeanConvert.copy(orderDO, resultVO);
        resultVO.setOrderTime(orderDO.getCreateTime());
        teboMallOrderManager.orderVerification(orderNo, null);

        // 新的分佣逻辑, 给商分钱
        TeboWalletPlanDTO teboWalletPlanDTO = new TeboWalletPlanDTO();
        LambdaQueryWrapper<TeboGoodsOrderSplitDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeboGoodsOrderSplitDO::getOrderId, orderDO.getId());
        queryWrapper.eq(TeboGoodsOrderSplitDO::getType, 1);
        TeboGoodsOrderSplitDO orderSplitDO = teboGoodsOrderSplitMapper.selectOne(queryWrapper);
        if (!ObjectUtils.isEmpty(orderSplitDO)) {
            TeboWalletPlanDetailDTO detail = new TeboWalletPlanDetailDTO();
            detail.setWalletId(orderSplitDO.getWalletId());
            detail.setAmount(orderSplitDO.getAmount());
            detail.setFlowType(21);
            detail.setDesc("订单编号:" + orderDO.getOrderNo() + "");
            teboWalletPlanDTO.setOrderNo(orderDO.getOrderNo());
            teboWalletPlanDTO.setDetails(Collections.singletonList(detail));
            remoteWalletService.splitPlan(teboWalletPlanDTO);
            remoteWalletService.startSplitPlan(teboWalletPlanDTO);
            // 更新分账状态
            orderSplitDO.setStatus(2);
            teboGoodsOrderSplitMapper.updateById(orderSplitDO);
            // 调用柯峰接口解冻 60
            LambdaQueryWrapper<TeboGoodsOrderCouponDO> couponWrapper = new LambdaQueryWrapper<>();
            couponWrapper.eq(TeboGoodsOrderCouponDO::getOrderId, orderDO.getId());
            couponWrapper.eq(TeboGoodsOrderCouponDO::getAmount, 3000);
            TeboGoodsOrderCouponDO coupon = teboGoodsOrderCouponMapper.selectOne(couponWrapper);
            if (!ObjectUtils.isEmpty(coupon)) {
                try {
                    rewardsPointUtil.thawSaleCardAmountOrder(orderDO.getOrderNo() + "-" + coupon.getOrderNo());
                } catch (Exception e) {
                    log.info("thawSaleCardAmountOrder error");
                }
            }else {
                try {
                    rewardsPointUtil.thawSaleCardAmountOrder(orderDO.getOrderNo()+ "-" );
                } catch (Exception e) {
                    log.info("thawSaleCardAmountOrder error e");
                }

            }

        }
        // 25.4.21 新增给门店冻结安装费逻辑
        teboWalletPlanDTO = new TeboWalletPlanDTO();
        LambdaQueryWrapper<TeboGoodsOrderSplitDO> shopWrapper = new LambdaQueryWrapper<>();
        shopWrapper.eq(TeboGoodsOrderSplitDO::getOrderId, orderDO.getId());
        shopWrapper.eq(TeboGoodsOrderSplitDO::getType, 2);
        TeboGoodsOrderSplitDO shopSplitDO = teboGoodsOrderSplitMapper.selectOne(shopWrapper);
        if (!ObjectUtils.isEmpty(shopSplitDO)) {
            // 增加冻结流水
            TeboWalletPlanDetailDTO detail = new TeboWalletPlanDetailDTO();
            detail.setWalletId(shopSplitDO.getWalletId());
            detail.setAmount(shopSplitDO.getAmount());
            detail.setFlowType(21);
            detail.setDesc("订单编号:" + orderDO.getOrderNo() + "-安装费");
            teboWalletPlanDTO.setOrderNo(orderDO.getOrderNo());
            teboWalletPlanDTO.setDetails(Collections.singletonList(detail));
            remoteWalletService.splitPlan(teboWalletPlanDTO);
        }
        if (CollectionUtil.isNotEmpty(goods)) {
            resultVO.setGoodsNameList(goods.stream().map(TeboOrderGoodsVO::getGoodsName).collect(Collectors.toList()));
            resultVO.setGoodsNumber(goods.size());
        }
        return resultVO;
    }

    @Override
    public WriteOffOrderResultVO writeOffSpecialOrder(Long shopId, String orderNo, String batteryNumber) {
        if (!orderNo.startsWith("TBCXA")){
            throw new ServiceException("非直营商品订单请到商城订单核销");
        }
        if (ObjectUtil.isEmpty(batteryNumber)) {
            throw new ServiceException("电池码不可为空！");
        }
        if (ObjectUtil.isEmpty(orderNo)) {
            throw new ServiceException("订单编号不可为空！");
        }
        // TODO 校验电池码是否是玖玖电池
        rewardsPointUtil.checkBatteryCode(batteryNumber);
        // 校验是否重复核销
        R<Boolean> batteryCheck = remoteCouponService.checkBatteryCode(batteryNumber);
        if (batteryCheck.getData()) {
            throw new ServiceException("该电池已核销！请更换一组电池");
        }
        WriteOffOrderResultVO resultVO = new WriteOffOrderResultVO();
        TeboMallOrderDO orderDO = teboMallOrderManager.getDetailByNo(orderNo);
        List<TeboOrderGoodsVO> goods = orderGoodsManager.getOrderGoodsByOrderId(orderDO.getId());
        if (!shopId.equals(orderDO.getShopId())) {
            throw new ServiceException("师傅和订单不在一个门店下");
        }
//        checkAccountAndOrder(orderDO);
        BeanConvert.copy(orderDO, resultVO);
        resultVO.setOrderTime(orderDO.getCreateTime());
        teboMallOrderManager.orderVerification(orderNo, batteryNumber);

        // 新的分佣逻辑, 给商分钱
        TeboWalletPlanDTO teboWalletPlanDTO = new TeboWalletPlanDTO();
        LambdaQueryWrapper<TeboGoodsOrderSplitDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeboGoodsOrderSplitDO::getOrderId, orderDO.getId());
        queryWrapper.eq(TeboGoodsOrderSplitDO::getType, 1);
        TeboGoodsOrderSplitDO orderSplitDO = teboGoodsOrderSplitMapper.selectOne(queryWrapper);
        if (!ObjectUtils.isEmpty(orderSplitDO)) {
            TeboWalletPlanDetailDTO detail = new TeboWalletPlanDetailDTO();
            detail.setWalletId(orderSplitDO.getWalletId());
            detail.setAmount(orderSplitDO.getAmount());
            detail.setFlowType(21);
            detail.setDesc("订单编号:" + orderDO.getOrderNo() + "");
            teboWalletPlanDTO.setOrderNo(orderDO.getOrderNo());
            teboWalletPlanDTO.setDetails(Collections.singletonList(detail));
            remoteWalletService.splitPlan(teboWalletPlanDTO);
            remoteWalletService.startSplitPlan(teboWalletPlanDTO);
            // 更新分账状态
            orderSplitDO.setStatus(2);
            teboGoodsOrderSplitMapper.updateById(orderSplitDO);
            // 调用柯峰接口解冻 60
            LambdaQueryWrapper<TeboGoodsOrderCouponDO> couponWrapper = new LambdaQueryWrapper<>();
            couponWrapper.eq(TeboGoodsOrderCouponDO::getOrderId, orderDO.getId());
            couponWrapper.eq(TeboGoodsOrderCouponDO::getAmount, 3000);
            TeboGoodsOrderCouponDO coupon = teboGoodsOrderCouponMapper.selectOne(couponWrapper);
            if (!ObjectUtils.isEmpty(coupon)) {
                try {
                    rewardsPointUtil.thawSaleCardAmountOrder(orderDO.getOrderNo() + "-" + coupon.getOrderNo());
                } catch (Exception e) {
                    log.info("thawSaleCardAmountOrder error");
                }
            }else {
                try {
                    rewardsPointUtil.thawSaleCardAmountOrder(orderDO.getOrderNo()+ "-" );
                } catch (Exception e) {
                    log.info("thawSaleCardAmountOrder error e");
                }

            }
        }
        // 25.4.21 新增给门店冻结安装费逻辑
        teboWalletPlanDTO = new TeboWalletPlanDTO();
        LambdaQueryWrapper<TeboGoodsOrderSplitDO> shopWrapper = new LambdaQueryWrapper<>();
        shopWrapper.eq(TeboGoodsOrderSplitDO::getOrderId, orderDO.getId());
        shopWrapper.eq(TeboGoodsOrderSplitDO::getType, 2);
        TeboGoodsOrderSplitDO shopSplitDO = teboGoodsOrderSplitMapper.selectOne(shopWrapper);
        if (!ObjectUtils.isEmpty(shopSplitDO)) {
            // 增加冻结流水
            TeboWalletPlanDetailDTO detail = new TeboWalletPlanDetailDTO();
            detail.setWalletId(shopSplitDO.getWalletId());
            detail.setAmount(shopSplitDO.getAmount());
            detail.setFlowType(21);
            detail.setDesc("订单编号:" + orderDO.getOrderNo() + "-安装费");
            teboWalletPlanDTO.setOrderNo(orderDO.getOrderNo());
            teboWalletPlanDTO.setDetails(Collections.singletonList(detail));
            remoteWalletService.splitPlan(teboWalletPlanDTO);
        }
        // 2025.07.16 增加
        // 如果券是已占用，则需要更新券状态，发放13.9，解冻60等
        splitPlan(orderDO, batteryNumber);
        if (CollectionUtil.isNotEmpty(goods)) {
            resultVO.setGoodsNameList(goods.stream().map(TeboOrderGoodsVO::getGoodsName).collect(Collectors.toList()));
            resultVO.setGoodsNumber(goods.size());
        }
        // 更新券为已使用
        LambdaQueryWrapper<TeboGoodsOrderCouponDO> couponWrapper = new LambdaQueryWrapper<>();
        couponWrapper.eq(TeboGoodsOrderCouponDO::getOrderId, orderDO.getId());
        List<TeboGoodsOrderCouponDO> coupon = teboGoodsOrderCouponMapper.selectList(couponWrapper);
        if (!CollectionUtils.isEmpty(coupon)) {
            OrderCouponDTO orderCouponDTO = new OrderCouponDTO();
            coupon.forEach(item ->{
                orderCouponDTO.setCouponCode(item.getUniqueCode());
                remoteCouponService.updateCouponListStatus(orderCouponDTO);
            });
        }
        return resultVO;
    }

    /**
     * 分佣 13.9
     * @param orderDO
     */
    private void splitPlan(TeboMallOrderDO orderDO, String batteryNumber) {
        log.info("splitPlan orderDO {}", orderDO);
        // 专供品订单，更新优惠券为未使用
        LambdaQueryWrapper<TeboGoodsOrderCouponDO> couponWrapper = new LambdaQueryWrapper<>();
        couponWrapper.eq(TeboGoodsOrderCouponDO::getOrderId, orderDO.getId());
        couponWrapper.eq(TeboGoodsOrderCouponDO::getAmount, 3000);
        List<TeboGoodsOrderCouponDO> couponDOList = teboGoodsOrderCouponMapper.selectList(couponWrapper);
        // 卡券不为空，则有13.9分佣
        if (!ObjectUtils.isEmpty(couponDOList)) {
            log.info("splitPlan couponDOList {}", couponDOList);
            // 如果券是已占用，则需要更新券状态，发放13.9，解冻60等
            TeboGoodsOrderCouponDO couponDO = couponDOList.get(0);
            log.info("splitPlan couponDO {}", couponDO);
            R<TeboCouponCustomerDTO> customerDTOResult = remoteCouponService.getDetailByCouponCode(couponDO.getUniqueCode());
            // 如果是已使用状态，说明是升级前的订单，已经发放过了
            if (customerDTOResult.getData().getStatus() == 2) {
                return;
            }
            // 如果券未被占用，则异常数据，也不再次发放
            if (customerDTOResult.getData().getOccStatus() == 1) {
                return;
            }
            // 所属门店钱包
            LambdaQueryWrapper<TeboGoodsOrderSplitDO> splitWrapper = new LambdaQueryWrapper<>();
            splitWrapper.eq(TeboGoodsOrderSplitDO::getOrderId, orderDO.getId());
            splitWrapper.eq(TeboGoodsOrderSplitDO::getType, 2);
            TeboGoodsOrderSplitDO splitDO = teboGoodsOrderSplitMapper.selectOne(splitWrapper);
            if (ObjectUtils.isEmpty(splitDO)) {
                return;
            }
            TeboWalletPlanDetailDTO detail = new TeboWalletPlanDetailDTO();
            detail.setWalletId(splitDO.getWalletId());
            detail.setAmount(1390);
            detail.setFlowType(14);
            detail.setDesc(orderDO.getCustomerPhone()+ "-玖玖电池款");
            TeboWalletPlanDTO teboWalletPlanDTO = new TeboWalletPlanDTO();
            teboWalletPlanDTO.setOrderNo(couponDOList.get(0).getOrderNo());
            teboWalletPlanDTO.setDetails(Collections.singletonList(detail));
            remoteWalletService.splitPlan(teboWalletPlanDTO);
            remoteWalletService.startSplitPlan(teboWalletPlanDTO);
            // 调用柯峰接口
            TeboSpecialGoodsVerificationDTO dto = new TeboSpecialGoodsVerificationDTO();
            R<TeboShop> shopRes = shopService.getShopInfo(orderDO.getShopId());
            if (shopRes.getCode() == 200 && ObjectUtil.isNotEmpty(shopRes.getData())) {
                dto.setCloudShopId(shopRes.getData().getCloudShopId());
                dto.setCouponCode(couponDOList.get(0).getUniqueCode());
                dto.setOrderCode(orderDO.getOrderNo());
                dto.setAccountId(orderDO.getUnionid());
                dto.setPhoneNumber(orderDO.getCustomerPhone());
                rewardsPointUtil.placeOrderVerified(dto);
                TeboCouponMallOrderDTO orderDTO = new TeboCouponMallOrderDTO();
                orderDTO.setCouponCode(couponDOList.get(0).getUniqueCode());
                orderDTO.setShopId(orderDO.getShopId());
                orderDTO.setShopName(orderDO.getShopName());
                orderDTO.setShopPhone(shopRes.getData().getPhoneNumber());
                orderDTO.setBatteryType("玖玖电池");
                orderDTO.setBatteryModel("玖玖");
                orderDTO.setBatteryNum(1);
                orderDTO.setBatteryCode(batteryNumber);
                remoteQueueOrderService.signRecordNew(orderDTO);
            }

        }
    }

    @Override
    public void mallOrderPayTimeOut() {
        /**
         * 查询超时15分钟的订单
         */
        LocalDateTime compareTime = LocalDateTime.now().minusMinutes(15);
        List<TeboMallOrderVO> list = teboMallOrderManager.getPayTimeOutOrder(compareTime);
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<Long> orderIdList = list.stream().map(item -> item.getId()).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(orderIdList)) {
            return;
        }
        TeboOrderGoodsUpdateDTO updateDTO = new TeboOrderGoodsUpdateDTO();
        updateDTO.setIdList(orderIdList);
        updateDTO.setOrderStatus(MallOrderStatusEnum.CANCEL.getCode());
        updateDTO.setCancelType(MallOrderCancelTypeEnum.TIME_OUT_CANCEL.getCode());
        updateDTO.setCancelTime(LocalDateTime.now());
        teboMallOrderManager.updateMallOrderStatus(updateDTO);
    }

    /**
     * 门店详情
     *
     * @param shopId
     * @return
     */
    @Override
    public TeboShopVO getShopDetail(Long shopId) {
        TeboShopVO shopVO = new TeboShopVO();
        R<TeboShop> shopInfo = shopService.getShopInfo(shopId);
        if (shopInfo == null || shopInfo.getCode() != 200 || shopInfo.getData() == null) {
            throw new GlobalException("获取门店信息异常");
        }
        BeanConvert.copy(shopInfo.getData(), shopVO);
        TeboGoodsQueryDTO teboGoodsQueryDTO = new TeboGoodsQueryDTO();
        teboGoodsQueryDTO.setShopId(shopId);
        teboGoodsQueryDTO.setStatus(1);
        /**
         * 商品售卖类型
         */
        List<Integer> salesList = new ArrayList();
        salesList.add(TeboMallSalesTypeEnum.ALL.getCode());
        salesList.add(TeboMallSalesTypeEnum.MALL.getCode());
        teboGoodsQueryDTO.setSalesTypeList(salesList);
        /**
         * 获取所有导航栏展示的二级目录
         */
        if (teboGoodsQueryDTO.getSecondCategoryId() == null) {
            //可展示的一级目录
            List<TeboGoodsCategoryVO> list = goodsCategoryService.allCategoryList();
            if (CollectionUtil.isEmpty(list)) {
                return null;
            }
            List<Long> catagoryIdList = list.stream().map(item -> item.getId()).collect(Collectors.toList());
            TeboCategoryQueryDTO teboCategoryQueryDTO = new TeboCategoryQueryDTO();
            teboCategoryQueryDTO.setCatagoryIdList(catagoryIdList);
            //可展示的二级目录
            List<TeboGoodsCategoryVO> secondCategoryList = goodsCategoryService.userCategoryList(teboCategoryQueryDTO);
            if (CollectionUtil.isEmpty(secondCategoryList)) {
                List<Long> categoryIdList = secondCategoryList.stream().map(item -> item.getId()).collect(Collectors.toList());
                teboGoodsQueryDTO.setSecondCategoryIdList(categoryIdList);
            }
        }
        List<TeboGoodsVO> goodsList = teboGoodsManager.recommendList(teboGoodsQueryDTO);
        List<String> secondCategoryList = Arrays.asList(secondCategoryId.split(","));
        goodsList.forEach(item -> {
            item.setReceiver(1);
            if (secondCategoryList.contains(item.getSecondCategoryId().toString())) {
                item.setReceiver(2);
            }
        });
        shopVO.setGoodsList(goodsList);

        return shopVO;
    }

    @Override
    public TeboMallOrderVO orderGoodsByNo(String orderNo) {
        Assert.notNull(orderNo, "单号不能为空");
        TeboMallOrderDO detailByNo = teboMallOrderManager.getDetailByNo(orderNo);
        if (Objects.isNull(detailByNo)) {
            throw new ServiceException("未匹配到订单");
        }
        checkAccountAndOrder(detailByNo);
        List<TeboOrderGoodsVO> goods = orderGoodsManager.getOrderGoodsByOrderId(detailByNo.getId());
        String customerName = detailByNo.getCustomerName();
        String customerPhone = detailByNo.getCustomerPhone();
        TeboMallOrderVO mallOrderVO = new TeboMallOrderVO();
        mallOrderVO.setGoodsList(goods);
        mallOrderVO.setCustomerName(customerName);
        mallOrderVO.setCustomerPhone(customerPhone);
        mallOrderVO.setOrderNo(detailByNo.getOrderNo());
        mallOrderVO.setCreateTime(detailByNo.getCreateTime());
        mallOrderVO.setChannel(detailByNo.getChannel());
        mallOrderVO.setOrderType(detailByNo.getOrderType());
        return mallOrderVO;


    }

    @Override
    public List<TeboOrderGoodsCodeGroupWebVO> getOrderGoodsCodeGroupLimit7(TeboMallOrderQueryInnerDTO queryDTO) {
        List<TeboOrderGoodsCodeGroupWebVO> teboOrderGoodsCodeGroupWebVOS = this.getOrderGoodsCodeGroup(queryDTO);
        if (CollectionUtil.isNotEmpty(teboOrderGoodsCodeGroupWebVOS) && teboOrderGoodsCodeGroupWebVOS.size() > 6) {
            teboOrderGoodsCodeGroupWebVOS = teboOrderGoodsCodeGroupWebVOS.subList(0, 6);
        }
        return teboOrderGoodsCodeGroupWebVOS;
    }

    @Override
    public List<TeboOrderGoodsCategoryGroupWebVO> getOrderGoodsCategoryGroupLimit10(TeboMallOrderQueryInnerDTO queryDTO) {

        List<TeboOrderGoodsCategoryGroupWebVO> orderGoodsCategoryGroup = this.getOrderGoodsCategoryGroup(queryDTO);
        if (CollectionUtil.isNotEmpty(orderGoodsCategoryGroup) && orderGoodsCategoryGroup.size() > 10) {
            orderGoodsCategoryGroup = orderGoodsCategoryGroup.subList(0, 10);
        }
        return orderGoodsCategoryGroup;
    }

    @Override
    public TeboOrderGoodsCodeGroupWebVO getOrderGoodsCodeGroupAll(TeboMallOrderQueryInnerDTO queryDTO) {
        List<TeboOrderGoodsCodeGroup> orderGoodsCodeGroup = teboMallOrderManager.getOrderGoodsCodeGroup(queryDTO);
        if (CollectionUtil.isEmpty(orderGoodsCodeGroup)) {
            return new TeboOrderGoodsCodeGroupWebVO();
        }
        TeboOrderGoodsCodeGroupWebVO all = new TeboOrderGoodsCodeGroupWebVO();
        long allCount = orderGoodsCodeGroup.stream().mapToLong(TeboOrderGoodsCodeGroup::getAllCount).reduce(0, Long::sum);
        Integer allAmount = orderGoodsCodeGroup.stream().mapToInt(TeboOrderGoodsCodeGroup::getAllAmount).reduce(0, Integer::sum);
        all.setAllCount(allCount);
        all.setAllAmount(String.valueOf(allAmount));
        return all;
    }

    @Override
    public void getOrderGoodsCodeGroupExport(HttpServletResponse response, TeboMallOrderQueryInnerDTO queryDTO) {
        List<TeboOrderGoodsCodeGroup> orderGoodsCodeGroup = teboMallOrderManager.getOrderGoodsCodeGroup(queryDTO);
        List<TeboOrderGoodsCodeGroupExcelVO> teboOrderGoodsCodeGroupExcelVos = new ArrayList<>();
        for (TeboOrderGoodsCodeGroup teboOrderGoodsCodeGroup : orderGoodsCodeGroup) {
            TeboOrderGoodsCodeGroupExcelVO excel = new TeboOrderGoodsCodeGroupExcelVO();
            BeanConvert.copy(teboOrderGoodsCodeGroup, excel);
            if (Objects.nonNull(teboOrderGoodsCodeGroup.getAllAmount())) {
                excel.setAllAmountStr(MoneyUtil.fenToYuan(teboOrderGoodsCodeGroup.getAllAmount()));
            }
            teboOrderGoodsCodeGroupExcelVos.add(excel);
        }


        // 将数据写入流中
        try {
            EasyExcel.write(response.getOutputStream(), TeboOrderGoodsCodeGroupExcelVO.class).sheet("产品汇总").doWrite(teboOrderGoodsCodeGroupExcelVos);
        } catch (IOException e) {
            log.error("导出文件异常", e);
            throw new ServiceException("导出文件异常,请联系管理员");
        }
    }

    @Override
    public void getOrderGoodsCategoryGroupExport(HttpServletResponse response, TeboMallOrderQueryInnerDTO queryDTO) {

        List<TeboOrderGoodsCategoryGroup> orderGoodsCategoryGroup = teboMallOrderManager.getOrderGoodsCategoryGroup(queryDTO);
        List<TeboOrderGoodsCategoryGroupExcelVO> teboOrderGoodsCodeGroupExcelVos = new ArrayList<>();
        for (TeboOrderGoodsCategoryGroup teboOrderGoodsCategoryGroup : orderGoodsCategoryGroup) {
            TeboOrderGoodsCategoryGroupExcelVO excel = new TeboOrderGoodsCategoryGroupExcelVO();
            BeanConvert.copy(teboOrderGoodsCategoryGroup, excel);
            if (Objects.nonNull(teboOrderGoodsCategoryGroup.getAllAmount())) {
                excel.setAllAmountStr(MoneyUtil.fenToYuan(teboOrderGoodsCategoryGroup.getAllAmount()));
            }
            teboOrderGoodsCodeGroupExcelVos.add(excel);
        }


        // 将数据写入流中
        try {
            EasyExcel.write(response.getOutputStream(), TeboOrderGoodsCategoryGroupExcelVO.class).sheet("分类汇总").doWrite(teboOrderGoodsCodeGroupExcelVos);
        } catch (IOException e) {
            log.error("导出文件异常", e);
            throw new ServiceException("导出文件异常,请联系管理员");
        }
    }

    @Override
    public List<TeboOrderGoodsCodeGroupWebVO> getOrderGoodsCodeGroup(TeboMallOrderQueryInnerDTO queryDTO) {
        List<TeboOrderGoodsCodeGroup> orderGoodsCodeGroup = teboMallOrderManager.getOrderGoodsCodeGroup(queryDTO);
        List<TeboOrderGoodsCodeGroupWebVO> teboOrderGoodsCodeGroupWebVOS = new ArrayList<>();
        for (TeboOrderGoodsCodeGroup teboOrderGoodsCodeGroup : orderGoodsCodeGroup) {
            TeboOrderGoodsCodeGroupWebVO webVO = new TeboOrderGoodsCodeGroupWebVO();
            BeanConvert.copy(teboOrderGoodsCodeGroup, webVO);
            if (Objects.nonNull(teboOrderGoodsCodeGroup.getAllAmount())) {
                webVO.setAllAmount(String.valueOf(teboOrderGoodsCodeGroup.getAllAmount()));
            }
            teboOrderGoodsCodeGroupWebVOS.add(webVO);
        }

        return teboOrderGoodsCodeGroupWebVOS;

    }

    @Override
    public List<TeboOrderGoodsCategoryGroupWebVO> getOrderGoodsCategoryGroup(TeboMallOrderQueryInnerDTO queryDTO) {
        List<TeboOrderGoodsCategoryGroup> orderGoodsCategoryGroup = teboMallOrderManager.getOrderGoodsCategoryGroup(queryDTO);
        List<TeboOrderGoodsCategoryGroupWebVO> teboOrderGoodsCodeGroupWebVOS = new ArrayList<>();
        for (TeboOrderGoodsCategoryGroup teboOrderGoodsCategoryGroup : orderGoodsCategoryGroup) {
            TeboOrderGoodsCategoryGroupWebVO webVO = new TeboOrderGoodsCategoryGroupWebVO();
            BeanConvert.copy(teboOrderGoodsCategoryGroup, webVO);
            if (Objects.nonNull(teboOrderGoodsCategoryGroup.getAllAmount())) {
                webVO.setAllAmount(String.valueOf(teboOrderGoodsCategoryGroup.getAllAmount()));
            }
            teboOrderGoodsCodeGroupWebVOS.add(webVO);
        }

        return teboOrderGoodsCodeGroupWebVOS;
    }

    @Override
    public List<TeboMallOrderGoodsVO> getAppletOrderGoodsList(TeboTenantOrderDTO query) {
        List<TeboMallOrderGoodsVO> res = teboMallOrderManager.getOrderList(query);
        if (CollectionUtil.isEmpty(res)) {
            return null;
        }
        List<Long> orderIdList = res.stream().map(item -> item.getId()).collect(Collectors.toList());
        TeboMallOrderQueryDTO queryDTO = new TeboMallOrderQueryDTO();
        queryDTO.setOrderIdList(orderIdList);
        List<TeboOrderGoodsVO> goodList = orderGoodsManager.getOrderGoodsList(queryDTO);
        if (CollectionUtil.isNotEmpty(goodList)) {
            List<TeboOrderGoodsDetailVO> goodsList = BeanConvert.copyList(goodList, TeboOrderGoodsDetailVO::new);
            Map<Long, List<TeboOrderGoodsDetailVO>> map = DataUtils.listToGroup(goodsList, TeboOrderGoodsDetailVO::getOrderId);
            res.forEach(item -> {
                item.setGoodsList(map.get(item.getId()));
            });
        }
        return res;
    }

    @Override
    public TeboMallOrderGoodsVO getMallOrderInfo(Long orderId) {
        TeboMallOrderVO teboMallOrderVO = teboMallOrderManager.getDetailById(orderId);
        if (Objects.isNull(teboMallOrderVO)) {
            return null;
        }
        TeboMallOrderQueryDTO queryDTO = new TeboMallOrderQueryDTO();
        queryDTO.setOrderIdList(Collections.singletonList(teboMallOrderVO.getId()));
        List<TeboOrderGoodsVO> goodList = orderGoodsManager.getOrderGoodsList(queryDTO);
        TeboMallOrderGoodsVO teboMallOrderGoodsVO = new TeboMallOrderGoodsVO();
        BeanConvert.copy(teboMallOrderVO, teboMallOrderGoodsVO);
        teboMallOrderGoodsVO.setParValueStr(teboMallOrderVO.getParValue());
        teboMallOrderGoodsVO.setGoodsList(BeanConvert.copyList(goodList, TeboOrderGoodsDetailVO::new));
        LambdaQueryWrapper<TeboGoodsOrderSplitDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeboGoodsOrderSplitDO::getOrderId, orderId);
        queryWrapper.eq(TeboGoodsOrderSplitDO::getType, 2);
        TeboGoodsOrderSplitDO splitDO = teboGoodsOrderSplitMapper.selectOne(queryWrapper);
        if (!ObjectUtil.isEmpty(splitDO)) {
            teboMallOrderGoodsVO.setShopFee(splitDO.getAmount());
            teboMallOrderGoodsVO.setShopFeeStatus(splitDO.getStatus());
        }
        teboMallOrderGoodsVO.setOrderAmount(MoneyUtil.yuanToFen(teboMallOrderVO.getOrderAmount()));
        return teboMallOrderGoodsVO;
    }

    @Override
    public void updateMallOrderInfo(Long orderId) {
        TeboMallOrderVO teboMallOrderVO = teboMallOrderManager.getDetailById(orderId);
        if (Objects.isNull(teboMallOrderVO)) {
            return;
        }
        TeboOrderGoodsUpdateDTO updateDTO = new TeboOrderGoodsUpdateDTO();
        updateDTO.setOrderStatus(4);
        updateDTO.setId(orderId);
//        updateDTO.setPayTime(LocalDateTime.now());
        teboMallOrderManager.updateMallOrderStatus(updateDTO);
        // 打款给门店
        LambdaQueryWrapper<TeboGoodsOrderSplitDO> splitWrapper = new LambdaQueryWrapper<>();
        splitWrapper.eq(TeboGoodsOrderSplitDO::getOrderId, teboMallOrderVO.getId());
        splitWrapper.eq(TeboGoodsOrderSplitDO::getType, 2);
        splitWrapper.eq(TeboGoodsOrderSplitDO::getStatus, 1);
        TeboGoodsOrderSplitDO splitDO = teboGoodsOrderSplitMapper.selectOne(splitWrapper);
        if (ObjectUtils.isEmpty(splitDO)) {
            return;
        }
        TeboWalletPlanDetailDTO detail = new TeboWalletPlanDetailDTO();
        detail.setWalletId(splitDO.getWalletId());
        detail.setAmount(splitDO.getAmount());
        detail.setFlowType(21);
        detail.setDesc("订单编号:" + teboMallOrderVO.getOrderNo() + "-安装费");
        TeboWalletPlanDTO teboWalletPlanDTO = new TeboWalletPlanDTO();
        teboWalletPlanDTO.setOrderNo(teboMallOrderVO.getOrderNo());
        teboWalletPlanDTO.setDetails(Collections.singletonList(detail));
        // 25.4.21 新增给门店冻结安装费逻辑(直接解冻)
//        remoteWalletService.splitPlan(teboWalletPlanDTO);
        remoteWalletService.startSplitPlan(teboWalletPlanDTO);
        // 更新分账状态
        splitDO.setStatus(2);
        teboGoodsOrderSplitMapper.updateById(splitDO);
    }

    @Override
    public void updateMallOrderAmount(Long orderId) {
        log.info("updateMallOrderAmount orderId {}", orderId);
        TeboMallOrderVO teboMallOrderVO = teboMallOrderManager.getDetailById(orderId);
        if (Objects.isNull(teboMallOrderVO)) {
            return;
        }
        // 打款低佣金给门店
        LambdaQueryWrapper<TeboGoodsOrderSplitDO> splitWrapper = new LambdaQueryWrapper<>();
        splitWrapper.eq(TeboGoodsOrderSplitDO::getOrderId, teboMallOrderVO.getId());
        splitWrapper.eq(TeboGoodsOrderSplitDO::getType, 3);
        splitWrapper.eq(TeboGoodsOrderSplitDO::getStatus, 1);
        TeboGoodsOrderSplitDO splitDO = teboGoodsOrderSplitMapper.selectOne(splitWrapper);
        if (ObjectUtils.isEmpty(splitDO) || splitDO.getAmount() <= 0) {
            return;
        }
        log.info("updateMallOrderAmount splitDO {}", splitDO);
        TeboWalletPlanDetailDTO detail = new TeboWalletPlanDetailDTO();
        detail.setWalletId(splitDO.getWalletId());
        detail.setAmount(splitDO.getAmount());
        detail.setFlowType(22);
        detail.setDesc("订单编号:" + teboMallOrderVO.getOrderNo());
        TeboWalletPlanDTO teboWalletPlanDTO = new TeboWalletPlanDTO();
        teboWalletPlanDTO.setOrderNo(teboMallOrderVO.getOrderNo());
        teboWalletPlanDTO.setDetails(Collections.singletonList(detail));
        remoteWalletService.splitPlan(teboWalletPlanDTO);
        remoteWalletService.startSplitPlan(teboWalletPlanDTO);
        // 更新分账状态
        splitDO.setStatus(2);
        teboGoodsOrderSplitMapper.updateById(splitDO);
    }

    /**
     * 校验师傅和订单门店是否匹配
     *
     * @param orderDO
     */
    private void checkAccountAndOrder(TeboMallOrderDO orderDO) {
        Long userId = MaintainerOnlineUserUtil.getUserId();
        R<TeboAccountInfoVO> accountR = remoteAccountService.getAccountInfoById(userId);
        TeboAccountInfoVO accountInfoVO = accountR.getData();
        if (Objects.isNull(accountInfoVO)) {
            throw new ServiceException("未匹配到师傅信息");
        }
        Long accountShopId = accountInfoVO.getShopId();
        Long accountTenantId = accountInfoVO.getTenantId();

        Long orderShopId = orderDO.getShopId();
        Long orderTenantId = orderDO.getTenantId();

        //核销人员的校验 支付到哪里  1 门店 2 合伙人
        Integer receiverType = mallOrderPayService.getReceiverTypeByOrderId(orderDO.getId());
        if (1 == receiverType) {  //师傅必须是这个门店的
            if (!accountShopId.equals(orderShopId)) {
                throw new ServiceException("师傅和订单不在一个门店下");
            }

        } else if (2 == receiverType) {
            //师傅必须是这个合伙人的
            if (!accountTenantId.equals(orderTenantId)) {
                throw new ServiceException("师傅和订单不在一个合伙人下");
            }
        }
    }

    Integer buildChannel(List<TeboGoodsVO> teboGoodsList) {
        for (TeboGoodsVO teboGoodsVO : teboGoodsList) {
            if (1810956072198864896L == teboGoodsVO.getSecondCategoryId()) {
                return MallOrderChannelEnum.WARCRAFT.getCode();
            }
        }
        return null;
    }
}

