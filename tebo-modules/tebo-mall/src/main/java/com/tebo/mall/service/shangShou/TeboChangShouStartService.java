package com.tebo.mall.service.shangShou;

import com.tebo.mall.web.domain.changShou.TeboChangShouStarActivityDTO;
import com.tebo.mall.web.domain.changShou.TeboChangShouStarActivityQueryDTO;
import com.tebo.mall.web.domain.vo.changShou.TeboChangShouStartActivityVO;
import org.springframework.jmx.export.naming.IdentityNamingStrategy;

import java.util.List;

public interface TeboChangShouStartService {

    /**
     * 参加活动
     */
    void addActivity(TeboChangShouStarActivityDTO teboChangShouStarActivityDTO);

    List<TeboChangShouStartActivityVO> getCompetitionList(TeboChangShouStarActivityQueryDTO queryDTO);
    /**
     * 中奖记录
     */
    List<TeboChangShouStartActivityVO> getCompetitionLotteryList(TeboChangShouStarActivityQueryDTO queryDTO);

    Integer getMaxPhase();


    List<TeboChangShouStartActivityVO> getCompetitionLotteryPhase();

    TeboChangShouStartActivityVO selectById(Long id);

    void checkChangShowStartParam(TeboChangShouStarActivityDTO teboChangShouStarActivityDTO);

    /**
     * 抽奖
     */
    void drawPrize();

    void synchronizeAuditStatus();

    /**
     * 兜底给上个月排行
     */
    void rank();
}