package com.tebo.mall.web.domain.dto.order.inner;

import com.baomidou.mybatisplus.annotation.TableField;
import com.tebo.common.core.web.page.PageDomain;
import com.tebo.mall.web.domain.dto.order.outer.TeboOrderGoodsAddOuterDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TeboMallOrderAddDTO extends PageDomain implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 订单类型 1普通订单 2合作店订单 4:保险单 12:专供品订单
     */
    private Integer orderType;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * unionId
     */
    private String unionid;

    /**
     * 用户名称
     */
    private String customerName;

    /**
     * 用户电话
     */
    private String customerPhone;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 合伙人id
     */
    private Long tenantId;

    /**
     * 合伙人名字
     */
     private String tenantName;
    /**
     * 服务类型 1上门 2 到店
     */
    private Integer serviceType;

    /**
     * 用户地址
     */
    private String address;

    /**
     * 门店地址
     */

    private String shopAddress;

    /**
     * 服务时间
     */
    private String appointmentServiceTime;

    /**
     * 订单金额
     */
    private String orderAmount;
    /**
     * 订单商品数量
     */
    private Integer totalGoodsNumber;

    /**
     * 商品信息
     */
     private List<TeboOrderGoodsAddOuterDTO> goodsList;

    /**
     * 上门服务费
     */
    private Integer homeServiceFee;

    /**
     * 优惠券金额
     */
    private Integer parValue;

    /**
     * 优惠券code
     */
    private String couponCode;

    /**
     * 积分
     */
    private Integer integral;
    /**
     * 渠道 1:普通订单 2:魔兽订单 3:驴充充 4:延保
     */
    private Integer channel;

    /**
     * 区域名称
     */
    private String areaName;

}