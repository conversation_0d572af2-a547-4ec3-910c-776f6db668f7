package com.tebo.mall.manager.order;

import com.tebo.mall.domain.TeboOrderGoodsCategoryGroup;
import com.tebo.mall.domain.TeboOrderGoodsCodeGroup;
import com.tebo.mall.entity.TeboMallOrderDO;
import com.tebo.mall.web.domain.dto.order.inner.TeboMallOrderAddDTO;
import com.tebo.mall.web.domain.dto.order.inner.TeboMallOrderQueryDTO;
import com.tebo.mall.web.domain.dto.order.inner.TeboOrderGoodsUpdateDTO;
import com.tebo.mall.web.domain.dto.order.inner.TeboTenantOrderDTO;
import com.tebo.mall.web.domain.dto.order.outer.TeboMallOrderQueryInnerDTO;
import com.tebo.mall.web.domain.vo.WriteOffOrderResultVO;
import com.tebo.mall.web.domain.vo.order.TeboMallOrderGoodsVO;
import com.tebo.mall.web.domain.vo.order.TeboMallOrderVO;

import java.time.LocalDateTime;
import java.util.List;

public interface TeboMallOrderManager {

    /**
     * 订单列表
     * @param orderDTO
     * @return
     */
    List<TeboMallOrderVO> getOrderList(TeboMallOrderQueryInnerDTO orderDTO);

    /**
     * 创建订单
     * @param addDTO
     * @return
     */
    int createOrder(TeboMallOrderAddDTO addDTO);

    /**
     * 订单核销
     * @return
     */
    Integer orderVerification(String orderNo, String batteryCode);

    /**
     * 查询订单详情
     * @param orderId
     * @return
     */
    TeboMallOrderVO getDetailById(Long orderId);

    /**
     * 根据订单编号查询商品
     * @param orderNo
     * @return
     */
    TeboMallOrderDO getDetailByNo(String orderNo);

    /**
     * 更新订单状态
     * @param
     */
    void updateMallOrderStatus(TeboOrderGoodsUpdateDTO updateDTO);

    /**
     * 订单超时取消
     *
     */
    List<TeboMallOrderVO> getPayTimeOutOrder(LocalDateTime localDateTime);

    /**
     * 查询延保订单
     */
    List<TeboMallOrderVO> getExtendedWarrantyOrderOrder(LocalDateTime localDateTime);


    /**
     * 商品编码分组聚合
     * @param queryDTO
     * @return
     */
    List<TeboOrderGoodsCodeGroup> getOrderGoodsCodeGroup(TeboMallOrderQueryInnerDTO queryDTO);

    /**
     * 商品类目分组
     * @param queryDTO
     * @return
     */
    List<TeboOrderGoodsCategoryGroup> getOrderGoodsCategoryGroup(TeboMallOrderQueryInnerDTO queryDTO);


    /**
     * 专供品订单列表
     * @param orderDTO
     * @return
     */
    List<TeboMallOrderGoodsVO> getOrderList(TeboTenantOrderDTO orderDTO);

}