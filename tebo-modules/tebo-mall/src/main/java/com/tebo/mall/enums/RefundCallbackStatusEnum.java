package com.tebo.mall.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款回调状态枚举
 */
@Getter
@AllArgsConstructor
public enum RefundCallbackStatusEnum {

    NOT_CALLBACK(0, "未回调"),
    CALLBACK_SUCCESS(1, "回调成功"),
    CALLBACK_FAILED(2, "回调失败");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static RefundCallbackStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RefundCallbackStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
