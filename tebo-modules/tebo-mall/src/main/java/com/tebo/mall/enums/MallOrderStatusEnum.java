package com.tebo.mall.enums;

import java.util.Arrays;
import java.util.List;

/**
 * 订单状态
 */
public enum MallOrderStatusEnum {
    TO_BE_PAID(1, "待支付"),
    TO_BO_PICKER_UP(2, "待提货"),
    FINISH(3, "已完成"),
    REVIEWED(4, "已评价"),
    CANCEL(10, "已取消"),
    REFUNDED(11, "已退款");
    private Integer code;
    private String msg;

    MallOrderStatusEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static MallOrderStatusEnum getMallOrderStatus(Integer code) {
        for (MallOrderStatusEnum orderStatusEnum : MallOrderStatusEnum.values()) {
            if (orderStatusEnum.getCode().equals(code)) {
                return orderStatusEnum;
            }
        }
        return null;
    }

    /**
     * 获取已支付的状态
     * @return
     */
    public static List<Integer> getPayedStatusList() {
        return Arrays.asList(TO_BO_PICKER_UP.getCode(), FINISH.getCode(), REVIEWED.getCode());
    }

    /**
     * 获取可退款的状态
     * @return
     */
    public static List<Integer> getRefundableStatusList() {
        return Arrays.asList(TO_BO_PICKER_UP.getCode());
    }
}
