package com.tebo.mall.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 商城订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Data
@TableName("tebo_mall_order")
public class TeboMallOrderDO extends Model<TeboMallOrderDO> {

    private static final long serialVersionUID = 1L;

    @TableField("id")
    private Long id;

    @TableField("unionid")
    private String unionid;

    /**
     * 驴充充消费者id
     */
    @TableField("consumer_id")
    private String consumerId;


    /**
     * 用户姓名
     */
    @TableField("customer_name")
    private String customerName;

    /**
     * 用户手机号
     */
    @TableField("customer_phone")
    private String customerPhone;

    /**
     * 门店id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 门店名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 合伙人id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 合伙人名字
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 订单状态 1:待支付 2:待提货,3待带回  4 已完成 10:已取消
     */
    @TableField("order_status")
    private Integer orderStatus;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 上门服务费
     */
    @TableField("home_service_fee")
    private Integer homeServiceFee;

    /**
     * 用户地址
     */
    @TableField("address")
    private String address;

    /**
     * 门店地址
     */
    @TableField("shop_address")
    private String shopAddress;

    /**
     * 订单金额，以分为单位
     */
    @TableField("order_amount")
    private Integer orderAmount;

    /**
     * 优惠券面值
     */
    @TableField("par_value")
    private Integer parValue;

    /**
     * 优惠券编码
     */
    @TableField("coupon_code")
    private String couponCode;
    /**
     * 订单商品数量
     */
    @TableField("total_goods_number")
    private Integer totalGoodsNumber;

    /**
     * 服务类型 1上门 2 到店
     */
    @TableField("service_type")
    private Integer serviceType;

    /**
     * 订单类型 1普通订单 11 驴充充订单  12:专供品
     */
    @TableField("order_type")
    private Integer orderType;
    /**
     * 渠道 1:普通订单 2:魔兽订单 3:驴充充 4:延保
     */
    @TableField("channel")
    private Integer channel;
    /**
     * 支付时间
     */
    @TableField("pay_time")
    private LocalDateTime payTime;

    /**
     * 取消方式 1 手动取消 2 超时取消
     */
    @TableField("cancel_type")
    private Integer cancelType;

    /**
     * 取消时间
     */
    @TableField("cancel_time")
    private LocalDateTime cancelTime;

    /**
     * 预约时间
     */
    @TableField("appointment_service_time")
    private String appointmentServiceTime;

    /**
     * 核销人员名字
     */
    @TableField("verification_name")
    private String verificationName;

    /**
     * 核销时间
     */
    @TableField("verification_time")
    private LocalDateTime verificationTime;

    /**
     * 核销人员账号
     */
    @TableField("verification_account")
    private String verificationAccount;
    /**
     * 核销人员账号
     */
    @TableField("verification_account_id")
    private Long verificationAccountId;

    /**
     * 积分
     */
    @TableField("integral")
    private Integer integral;
    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time",fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除状态 1是删除
     */
    @TableField("del_flag")
    private Boolean delFlag;

    /**
     * 核销电池码
     */
    @TableField("battery_code")
    private String batteryCode;

    /**
     * 订单区域
     */
    @TableField("area_name")
    private String areaName;

    /**
     * 退款回调是否成功 0:未回调 1:回调成功 2:回调失败
     */
    @TableField("refund_callback_status")
    private Integer refundCallbackStatus;

    @Override
    public Serializable pkVal() {
        return null;
    }

}
