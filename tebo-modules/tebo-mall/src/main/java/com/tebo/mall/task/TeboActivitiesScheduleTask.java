package com.tebo.mall.task;


import cn.hutool.core.collection.CollectionUtil;
import com.tebo.common.redis.service.RedisService;
import com.tebo.mall.api.util.DrawLotteryUtil;
import com.tebo.mall.entity.TeboLotteryActivitiesDO;
import com.tebo.mall.entity.TeboLotteryPrizeDO;
import com.tebo.mall.manager.TeboLotteryPrizeManger;
import com.tebo.mall.mapper.TeboLotteryActivitiesMapper;
import com.tebo.mall.service.IGiftService;
import com.tebo.mall.service.ILotteryActivitiesService;
import com.tebo.mall.service.IRankActivitiesService;
import com.tebo.mall.util.DistributedLock;
import com.tebo.mall.util.TeboMallCommonCacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
public class TeboActivitiesScheduleTask {

    @Autowired
    private ILotteryActivitiesService lotteryActivitiesService;

    @Autowired
    private IRankActivitiesService rankActivitiesService;

    @Autowired
    private IGiftService giftService;

    @Resource
    private TeboLotteryActivitiesMapper lotteryActivitiesMapper;

    @Autowired
    private TeboLotteryPrizeManger lotteryPrizeManger;

    @Resource
    private RedisService redisService;

    @Resource
    private DistributedLock distributedLock;

    /**
     * 根据有效期变更活动状态
     * 每天零点 把开始时间是今天的，状态不是进行中的状态变更为进行中
     * 把结束时间的日期是今天上一天的，状态变更为已结束
     */
    @Scheduled(cron = "0 0 0 * * *")
    public void changeActivitiesStatusByDate() {
        if (!distributedLock.tryLock(TeboMallCommonCacheConstant.getScheduledTaskPrefix("TeboActivitiesScheduleTask:changeActivitiesStatusByDate"), 10, 30)) {
            return;
        }
//        rankActivitiesService.changeActivitiesStatusByDate();
        lotteryActivitiesService.changeActivitiesStatusByDate();
//        giftService.changeActivitiesStatusByDate();;
    }

    /**
     * 每个月8号的时候将奖品库存写进去redis
     */
    @Scheduled(cron = "0 30 15 8 * ?")
  //  @Scheduled(cron = "0 0/1 * * * ? ")
    public void updatePrizeInventory() {
        TeboLotteryActivitiesDO lotteryActivitiesDO = lotteryActivitiesMapper.getRecentActivities();
        List<TeboLotteryPrizeDO> lotteryPrizeDOS = lotteryPrizeManger.getByActivitiesId(lotteryActivitiesDO.getId());
        if (CollectionUtil.isNotEmpty(lotteryPrizeDOS)){
            lotteryPrizeDOS.forEach(item ->{
                redisService.setCacheObject(DrawLotteryUtil.prizeInventory + item.getId(), item.getPrizeNum().intValue(),  7L, TimeUnit.DAYS);
            });
        }

    }
}
