package com.tebo.mall.service.category.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;

import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.CodeGenerator;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.mall.entity.TeboGoodsAttributeDO;
import com.tebo.mall.entity.TeboGoodsCategoryDO;
import com.tebo.mall.manager.TeboGoodsAttributeManger;
import com.tebo.mall.manager.category.TeboCategoryManager;
import com.tebo.mall.manager.goods.TeboGoodsManager;
import com.tebo.mall.service.category.TeboGoodsCategoryService;
import com.tebo.mall.service.goods.TeBoGoodsService;
import com.tebo.mall.web.domain.dto.TeboAttributeQueryDTO;
import com.tebo.mall.web.domain.dto.category.TeboGoodsCategoryDTO;
import com.tebo.mall.web.domain.dto.category.TeboCategoryQueryDTO;
import com.tebo.mall.web.domain.dto.goods.TeboGoodsQueryDTO;
import com.tebo.mall.web.domain.vo.TeboGoodsCategoryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author：zhengmk
 * @Date 2023/12/19 13:28
 */
@Service
@Slf4j
public class CategoryServiceImpl implements TeboGoodsCategoryService  {
    @Resource
    private TeboCategoryManager teboCategoryManager;
    @Resource
    private TeboGoodsManager teboGoodsManager;
    @Resource
    private TeboGoodsAttributeManger attributeManger;

    @Override
    public List<TeboGoodsCategoryVO> listCategory(TeboCategoryQueryDTO queryDTO) {
        return teboCategoryManager.list(queryDTO);
    }

    /*
    @Override
    public List<TeboGoodsCategoryVO> appletSelectableCategoryList(TeboCategoryQueryDTO teboCategoryQueryDTO) {
        teboCategoryQueryDTO.setCategoryLevel(2);
        //获得所有可展示的二级目录
        List<TeboGoodsCategoryVO> list = teboCategoryManager.getNavShowCategoryList(teboCategoryQueryDTO);

       return list;
    }

     */

    @Override
    public List<TeboGoodsCategoryVO> userCategoryList(TeboCategoryQueryDTO teboCategoryQueryDTO) {
        teboCategoryQueryDTO.setCategoryLevel(2);
        teboCategoryQueryDTO.setNavShow(1);
        //获得所有可展示的二级目录
        List<TeboGoodsCategoryVO> result = teboCategoryManager.getNavShowCategoryList(teboCategoryQueryDTO);
        return result;
    }

    @Override
    public List<TeboGoodsCategoryVO> allCategoryList() {
        TeboGoodsCategoryDTO categoryQueryDTO = new TeboGoodsCategoryDTO();
        categoryQueryDTO.setCategoryLevel(1);
        categoryQueryDTO.setNavShow(1);
        return  teboCategoryManager.allCategoryList(categoryQueryDTO);
    }

    @Override
    public List<TeboGoodsCategoryVO> selectableCategoryList(Long parentId) {
        return teboCategoryManager.selectableCategoryList(parentId);
    }

    @Override
    public TeboGoodsCategoryVO getCategoryById(Long id) {
        return teboCategoryManager.selectById(id);
    }
    @Transactional
    @Override
    public Boolean addCategory(TeboGoodsCategoryDTO teboCategoryDTO) {
        TeboGoodsCategoryDO teboCategoryDO = new TeboGoodsCategoryDO();
        BeanConvert.copy(teboCategoryDTO, teboCategoryDO);
        teboCategoryDO.setId(SnowFlakeUtil.nextId());
        if (StringUtils.isEmpty(teboCategoryDTO.getCategoryNo())){
            teboCategoryDO.setCategoryNo(CodeGenerator.generateCategoryCode());
        }
        if (ObjectUtil.isNotEmpty(teboCategoryDTO.getParentCategoryId())){
            teboCategoryDO.setCategoryLevel(2);
        }else {
            teboCategoryDO.setCategoryLevel(1);
        }
        TeboGoodsCategoryVO count = teboCategoryManager.countByCategoryName(teboCategoryDTO);
        if (count != null) {
            throw new GlobalException("分类名称已存在");
        }
        return teboCategoryManager.insert(teboCategoryDO) > 0;
    }

    @Transactional
    @Override
    public Boolean updateCategory(TeboGoodsCategoryDTO teboCategoryDTO) {
        if(teboCategoryDTO == null || teboCategoryDTO.getId() == null) {
            throw new GlobalException("参数不能为空");
        }
        TeboGoodsCategoryVO count = teboCategoryManager.countByCategoryName(teboCategoryDTO);
        if (count != null && !count.getId().equals(teboCategoryDTO.getId() )) {
            throw new GlobalException("分类名称已存在");
        }
        TeboGoodsCategoryVO teboGoodsCategoryVO = teboCategoryManager.selectById(teboCategoryDTO.getId());
        if (ObjectUtil.isEmpty(teboGoodsCategoryVO)){
            return false;
        }
        if (teboGoodsCategoryVO.getParentCategoryId() == null){
            teboGoodsCategoryVO.setCategoryLevel(1);
            teboGoodsCategoryVO.setParentCategoryId(0L);
        }else {
            teboGoodsCategoryVO.setCategoryLevel(2);
        }
        TeboGoodsCategoryDO teboGoodsCategoryDO = new TeboGoodsCategoryDO();
        BeanConvert.copy(teboCategoryDTO,teboGoodsCategoryDO);
        return teboCategoryManager.updateById(teboGoodsCategoryDO) > 0;
    }

    @Transactional
    @Override
    public Boolean deleteCategory(Long id) {
        TeboGoodsCategoryVO teboGoodsCategoryVO = teboCategoryManager.selectById(id);
        if (ObjectUtil.isEmpty(teboGoodsCategoryVO)){
            return false;
        }
        if (teboGoodsCategoryVO.getCategoryLevel() == 1){
            TeboCategoryQueryDTO goodsQueryDTO = new TeboCategoryQueryDTO();
            goodsQueryDTO.setParentCategoryId(id);
            List<TeboGoodsCategoryVO> list = teboCategoryManager.list(goodsQueryDTO);
            if (CollectionUtil.isNotEmpty(list)) {
                throw new GlobalException("该分类下有下级分类，不能删除");
            }
            TeboAttributeQueryDTO teboAttributeQueryDTO = new TeboAttributeQueryDTO();
            teboAttributeQueryDTO.setCategoryId(id);
            List<TeboGoodsAttributeDO> attributeList = attributeManger.list(teboAttributeQueryDTO);
            if (CollectionUtil.isNotEmpty(attributeList)){
                throw new GlobalException("该分类下已绑定属性，不能删除");
            }

        }else {
            TeboGoodsQueryDTO goodsQueryDTO = new TeboGoodsQueryDTO();
            goodsQueryDTO.setSecondCategoryId(id);
            int count = teboGoodsManager.countGoods(goodsQueryDTO);
            if (count > 0) {
                throw new ServiceException("该分类下有商品，不能删除");
            }
        }
        return teboCategoryManager.batchDelete(Collections.singletonList(id)) > 0;
    }
    @Override
    public List<TeboGoodsCategoryVO> builTree(TeboCategoryQueryDTO teboCategoryQueryDTO) {
        List<TeboGoodsCategoryVO> result = new ArrayList<>();
        List<TeboGoodsCategoryVO> list = selectableCategoryList(0L);
        if (teboCategoryQueryDTO.getCategoryLevel() == 1){
            return list;
        }
        if (teboCategoryQueryDTO.getCategoryLevel() == 2){
            for (TeboGoodsCategoryVO categoryVO : list) {
                buildChildTree(categoryVO);
                if (teboCategoryQueryDTO.getFilterateCaterogy() != null && teboCategoryQueryDTO.getFilterateCaterogy() == 1){
                  if(CollectionUtil.isEmpty(categoryVO.getChildren())) {
                      continue;
                  }
                  result.add(categoryVO);
                }else {
                  result.add(categoryVO);
                }
            }

        }
        return result;
    }

    @Override
    public List<TeboGoodsCategoryVO> recommendUserCategoryList() {
        TeboCategoryQueryDTO queryDTO = new TeboCategoryQueryDTO();
        //可展示的一级目录
        List<TeboGoodsCategoryVO> list =  allCategoryList();
        if (CollectionUtil.isEmpty(list)){
            return null;
        }
        List<Long> catagoryIdList = list.stream().map(item -> item.getId()).collect(Collectors.toList());
        queryDTO.setCatagoryIdList(catagoryIdList);
        queryDTO.setHomePageRecommend(1);
        queryDTO.setCategoryLevel(2);
        queryDTO.setNavShow(1);
        return teboCategoryManager.recommendUserCategoryList(queryDTO);
    }

    //递归，建立子树形结构
    private TeboGoodsCategoryVO buildChildTree(TeboGoodsCategoryVO node) {
        List<TeboGoodsCategoryVO> childList = new ArrayList<>();
        TeboCategoryQueryDTO teboCategoryQueryDTO = new TeboCategoryQueryDTO();
        teboCategoryQueryDTO.setNavShow(node.getNavShow());
        List<TeboGoodsCategoryVO> secondList = listCategory(teboCategoryQueryDTO);
        secondList = secondList.stream().filter(item -> item.getParentCategoryId()!=0).collect(Collectors.toList());
        for (TeboGoodsCategoryVO teboGoodsCategoryVO : secondList) {
            if (teboGoodsCategoryVO.getParentCategoryId().equals(node.getId())) {
                childList.add(buildChildTree(teboGoodsCategoryVO));
            }
        }
        if (CollectionUtil.isNotEmpty(childList)){
            node.setChildren(childList);
        }
        return node;
    }
}
