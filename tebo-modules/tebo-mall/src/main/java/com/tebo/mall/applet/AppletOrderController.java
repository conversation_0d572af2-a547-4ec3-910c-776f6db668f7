package com.tebo.mall.applet;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.domain.AjaxResult;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.common.security.util.MaintainerOnlineUserUtil;
import com.tebo.common.util.applet.AppletUtil;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.mall.api.domain.dto.TeboSourceDTO;
import com.tebo.mall.api.enums.MallOrderChannelEnum;
import com.tebo.mall.enums.MallOrderStatusEnum;
import com.tebo.mall.service.order.TeboBatteryOrderService;
import com.tebo.mall.service.order.TeboMallOrderService;
import com.tebo.mall.web.domain.dto.order.inner.TeboMallOrderQueryDTO;
import com.tebo.mall.web.domain.dto.order.inner.TeboTenantOrderDTO;
import com.tebo.mall.web.domain.dto.order.outer.TeboMallOrderAddOuterDTO;
import com.tebo.mall.web.domain.dto.order.outer.TeboMallOrderQueryInnerDTO;
import com.tebo.mall.web.domain.dto.order.outer.TeboMallSpecialOrderAddOuterDTO;
import com.tebo.mall.web.domain.dto.order.outer.TeboOrderGoodsAddOuterDTO;
import com.tebo.mall.web.domain.vo.WriteOffOrderResultVO;
import com.tebo.mall.web.domain.vo.order.TeboMallOrderGoodsVO;
import com.tebo.mall.web.domain.vo.order.TeboMallOrderNumberVO;
import com.tebo.mall.web.domain.vo.order.TeboMallOrderVO;
import com.tebo.mall.web.domain.vo.order.TeboShopVO;
import com.tebo.system.api.RemoteAccountService;
import com.tebo.system.api.model.TeboAccountInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 订单
 */
@RestController
@Slf4j
@RequestMapping("/applet/order")
public class AppletOrderController  extends BaseController {
    @Resource
    private TeboMallOrderService teboMallOrderService;
    @Resource
    private RemoteAccountService remoteAccountService;
    @Resource
    private TeboBatteryOrderService teboBatteryOrderService;

    /**
     * 用户下单
     */
    @PostMapping("/createOrder")
    public R createOrder(@RequestBody TeboMallOrderAddOuterDTO addDTO, HttpServletRequest request) {
        log.info("createOrder param:{}", JSONObject.toJSONString(addDTO));
        if (ObjectUtil.isEmpty(addDTO) || ObjectUtil.isEmpty(addDTO.getShopId()) ){
            throw new GlobalException("门店信息不能为空");
        }
        if (ObjectUtil.isEmpty(addDTO.getGoodsList())){
            throw new GlobalException("商品信息不能为空");
        }
        List<TeboOrderGoodsAddOuterDTO> goodsList = addDTO.getGoodsList();
        goodsList.forEach(item ->{
            if (ObjectUtil.isEmpty(item.getPriceType())){
                throw new GlobalException("价格类型不能为空");
            }
            if (ObjectUtil.isEmpty(item.getGoodsNumber())){
                throw new GlobalException("商品数量不能为空");
            }
            if (ObjectUtil.isEmpty(item.getPrice())){
                throw new GlobalException("商品价格不能为空");
            }
        });
        if (addDTO.getServiceType() == 2 && ObjectUtil.isEmpty(addDTO.getAddressId())){
            throw new GlobalException("用户地址不能为空");
        }
        String unionid = AppletUtil.getUnionIdByRequest(request);
        addDTO.setUnionid(unionid);
        addDTO.setChannel(MallOrderChannelEnum.NORMAL.getCode());
        return R.ok(teboMallOrderService.createOrder(addDTO));
    }
    /**
     * 专供品订单
     */
    @PostMapping("/createSpecialOrder")
    public R createSpecialOrder(@RequestBody TeboMallSpecialOrderAddOuterDTO addDTO, HttpServletRequest request) {
        log.info("createSpecialOrder param:{}", JSONObject.toJSONString(addDTO));
        if (ObjectUtil.isEmpty(addDTO) || ObjectUtil.isEmpty(addDTO.getShopId()) ){
            throw new GlobalException("门店信息不能为空");
        }
        if (ObjectUtil.isEmpty(addDTO.getGoodsId())){
            throw new GlobalException("商品信息不能为空");
        }
        if (ObjectUtil.isEmpty(addDTO.getPriceType())){
            throw new GlobalException("价格类型不能为空");
        }
        String unionid = AppletUtil.getUnionIdByRequest(request);
        addDTO.setUnionid(unionid);
        addDTO.setChannel(MallOrderChannelEnum.SPECIAL_GOODS.getCode());
//        return R.ok(teboMallOrderService.createSpecialOrder(addDTO));
        return R.ok(teboBatteryOrderService.createOrderWithCoupon(addDTO));
    }
    /**
     * 再买一单
     */
    @PostMapping("/buyAnotherOrder")
    public R buyAnotherOrder(@RequestBody TeboMallOrderAddOuterDTO addDTO, HttpServletRequest request) {
        log.info("buyAnotherOrder param:{}", JSONObject.toJSONString(addDTO));
        if (ObjectUtil.isEmpty(addDTO) || ObjectUtil.isEmpty(addDTO.getOrderId()) ){
            throw new GlobalException("订单id不能为空");
        }
        String unionid = AppletUtil.getUnionIdByRequest(request);
        addDTO.setUnionid(unionid);
        addDTO.setChannel(MallOrderChannelEnum.NORMAL.getCode());
        return R.ok(teboMallOrderService.buyAnotherOrder(addDTO));
    }

    /**
     * 订单列表
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody TeboMallOrderQueryDTO orderDTO, HttpServletRequest request) {
        if(orderDTO == null || orderDTO.getPageNum() == null || orderDTO.getPageSize() == null) {
            throw new GlobalException("参数错误");
        }
        String unionid = AppletUtil.getUnionIdByRequest(request);
        orderDTO.setUnionid(unionid);
        Page page = PageHelper.startPage(orderDTO.getPageNum(), orderDTO.getPageSize());
        TeboMallOrderQueryInnerDTO innerDTO = new TeboMallOrderQueryInnerDTO();
        BeanConvert.copy(orderDTO,innerDTO);
        innerDTO.setOrderStatus(null);
        List<Integer> orderStatusList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(orderDTO.getOrderStatus())){
            if (orderDTO.getOrderStatus() == 3){
                orderStatusList.add(MallOrderStatusEnum.FINISH.getCode());
                orderStatusList.add(MallOrderStatusEnum.REVIEWED.getCode());
                innerDTO.setOrderStatusList(orderStatusList);
            }else {
                orderStatusList.add(orderDTO.getOrderStatus());
                innerDTO.setOrderStatusList(orderStatusList);
            }
        }
        List<TeboMallOrderVO> list = teboMallOrderService.getAppletOrderList(innerDTO);
        return getDataTable(list,page);
    }

    /**
     * 订单数量
     */
    @PostMapping("/getOrderCount")
    public R<TeboMallOrderNumberVO> getOrderCount(@RequestBody TeboSourceDTO teboSourceDTO, HttpServletRequest request) {
        if(teboSourceDTO == null || teboSourceDTO.getSource() == null) {
            throw new GlobalException("参数错误");
        }
        TeboMallOrderQueryInnerDTO innerDTO = new TeboMallOrderQueryInnerDTO();
        if (teboSourceDTO.getSource() == 1){
            String unionId = AppletUtil.getUnionIdByRequest(request);
            innerDTO.setUnionid(unionId);
        }else {
            Long accountId = MaintainerOnlineUserUtil.getUserId();
            TeboAccountInfoVO accountInfoVO = remoteAccountService.getAccountInfoById(accountId).getData();
            if(accountInfoVO == null) {
                throw new GlobalException("当前师傅账号不存在");
            }
            innerDTO.setShopId(accountInfoVO.getShopId());
        }
        return R.ok(teboMallOrderService.getOrderCount(innerDTO));
    }

    /**
     * 订单核销
     */
    @PostMapping("/writeOffOrder")
    public R<WriteOffOrderResultVO> writeOffOrder(@RequestBody TeboMallOrderQueryDTO orderDTO) {
        Assert.notNull(orderDTO.getOrderNo(),"订单编码不能为空");
        return R.ok(teboMallOrderService.writeOffOrder(orderDTO.getOrderNo()));
    }

    /**
     * 专供品订单核销
     */
    @PostMapping("/writeOffSpecialOrder")
    public R<WriteOffOrderResultVO> writeOffSpecialOrder(@RequestBody TeboMallOrderQueryDTO orderDTO) {
        Assert.notNull(orderDTO.getOrderNo(),"订单编码不能为空");
        Assert.notNull(orderDTO.getBatteryNumber(),"电池码不能为空");
        return R.ok(teboMallOrderService.writeOffSpecialOrder(orderDTO.getShopId(), orderDTO.getOrderNo(),orderDTO.getBatteryNumber()));
    }

    /**
     * 专供品订单核销-新
     */
    @PostMapping("/v2/writeOffSpecialOrder")
    public R<WriteOffOrderResultVO> writeOffSpecialOrderV2(@RequestBody TeboMallOrderQueryDTO orderDTO) {
//        Assert.notNull(orderDTO.getOrderNo(),"订单编码不能为空");
//        Assert.notNull(orderDTO.getBatteryNumber(),"电池码不能为空");
        return R.ok(teboMallOrderService.writeOffSpecialOrder(orderDTO.getShopId(), orderDTO.getOrderNo(),orderDTO.getBatteryNumber()));
    }

    /**
     * 师傅查看订单商品
     * @param orderDTO
     * @return
     */
    @PostMapping("/orderGoodsByNo")
    public R<TeboMallOrderVO > orderGoodsByNo(@RequestBody TeboMallOrderQueryDTO orderDTO) {
        Assert.notNull(orderDTO.getOrderNo(),"订单编码不能为空");
        return R.ok(teboMallOrderService.orderGoodsByNo(orderDTO.getOrderNo()));
    }

    /**
     * 门店详情
     */
    @GetMapping("/getShopDetail/{shopId}")
    public R<TeboShopVO> getShopDetail(@PathVariable("shopId") Long shopId){
        if(ObjectUtil.isEmpty(shopId)) {
            throw new GlobalException("门店id不能为空");
        }
        return R.ok(teboMallOrderService.getShopDetail(shopId));
   }

    /**
     * 师傅端订单列表
     */
    @PostMapping("/maintenance/list")
    public TableDataInfo maintenanceOrderList(@RequestBody TeboMallOrderQueryDTO orderDTO, HttpServletRequest request) {
        if(orderDTO == null || orderDTO.getPageNum() == null || orderDTO.getPageSize() == null) {
            throw new GlobalException("参数错误");
        }
        Long accountId = MaintainerOnlineUserUtil.getUserId();
        TeboAccountInfoVO accountInfoVO = remoteAccountService.getAccountInfoById(accountId).getData();
        if(accountInfoVO == null) {
            throw new GlobalException("当前师傅账号不存在");
        }
        orderDTO.setShopId(accountInfoVO.getShopId());
        Page page = PageHelper.startPage(orderDTO.getPageNum(), orderDTO.getPageSize());
        TeboMallOrderQueryInnerDTO innerDTO = new TeboMallOrderQueryInnerDTO();
        BeanConvert.copy(orderDTO,innerDTO);
        innerDTO.setOrderStatus(null);
        List<Integer> orderStatusList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(orderDTO.getOrderStatus())){
            if (orderDTO.getOrderStatus() == 3){
                orderStatusList.add(MallOrderStatusEnum.FINISH.getCode());
                orderStatusList.add(MallOrderStatusEnum.REVIEWED.getCode());
                innerDTO.setOrderStatusList(orderStatusList);
            }else {
                orderStatusList.add(orderDTO.getOrderStatus());
                innerDTO.setOrderStatusList(orderStatusList);
            }
        }
        List<TeboMallOrderVO> list = teboMallOrderService.getAppletOrderList(innerDTO);
        return getDataTable(list,page);
    }

    /**
     * 订单详情
     */
    @GetMapping("/detailById/{orderId}")
    public R<TeboMallOrderVO> detailById(@PathVariable("orderId") Long orderId) {
        if (ObjectUtil.isEmpty(orderId)) {
            throw new GlobalException("订单id不能为空");
        }
        return R.ok(teboMallOrderService.getDetailById(orderId));
    }


    /**
     * 掌心泰博专供品订单列表
     */
    @PostMapping("/tenant/list")
    public TableDataInfo tenantOrderList(@RequestBody TeboTenantOrderDTO query) {
        if(query == null || query.getPageNum() == null || query.getPageSize() == null) {
            throw new GlobalException("参数错误");
        }
        if (ObjectUtil.isEmpty(query.getTenantId())) {
            throw new GlobalException("合伙人不能为空");
        }
        if (ObjectUtil.isEmpty(query.getOrderStatus())) {
            throw new GlobalException("状态不能为空");
        }
        Page page = PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<TeboMallOrderGoodsVO> list = teboMallOrderService.getAppletOrderGoodsList(query);
        return getDataTable(list,page);
    }

    /**
     * 订单详情
     */
    @GetMapping("/tenant/info")
    public AjaxResult tenantOrderList(@RequestParam Long orderId) {
        return success(teboMallOrderService.getMallOrderInfo(orderId));
    }

    /**
     * 订单详情
     */
    @GetMapping("/tenant/tenantOrderChange")
    public AjaxResult tenantOrderChange(@RequestParam Long orderId) {
        teboMallOrderService.updateMallOrderInfo(orderId);
        return success();
    }

    /**
     * 低佣金提取现金
     */
    @GetMapping("/tenant/updateMallOrderAmount")
    public AjaxResult updateMallOrderAmount(@RequestParam Long orderId) {
        teboMallOrderService.updateMallOrderAmount(orderId);
        return success();
    }

}
