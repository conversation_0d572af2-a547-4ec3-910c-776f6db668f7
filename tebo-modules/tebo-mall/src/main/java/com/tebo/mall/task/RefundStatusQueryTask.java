package com.tebo.mall.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tebo.mall.entity.TeboBatteryOrderRefundDO;
import com.tebo.mall.enums.RefundStatusEnum;
import com.tebo.mall.mapper.TeboBatteryOrderRefundMapper;
import com.tebo.mall.service.order.TeboBatteryOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 退款状态查询定时任务
 */
@Slf4j
@Component
public class RefundStatusQueryTask {

    @Resource
    private TeboBatteryOrderRefundMapper refundMapper;
    
    @Resource
    private TeboBatteryOrderService batteryOrderService;

    /**
     * 每5分钟执行一次，查询长时间未回调的退款订单
     */
    @Scheduled(fixedRate = 5 * 60 * 1000)
    public void queryPendingRefunds() {
        log.info("开始执行退款状态查询定时任务");
        
        try {
            // 查询退款中且创建时间超过10分钟的记录
            LocalDateTime tenMinutesAgo = LocalDateTime.now().minusMinutes(10);
            
            QueryWrapper<TeboBatteryOrderRefundDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(TeboBatteryOrderRefundDO::getRefundStatus, 1) // 退款中
                    .lt(TeboBatteryOrderRefundDO::getCreateTime, tenMinutesAgo);
            
            List<TeboBatteryOrderRefundDO> pendingRefunds = refundMapper.selectList(queryWrapper);
            
            if (pendingRefunds.isEmpty()) {
                log.info("没有需要查询的退款记录");
                return;
            }
            
            log.info("找到{}条需要查询状态的退款记录", pendingRefunds.size());
            
            for (TeboBatteryOrderRefundDO refund : pendingRefunds) {
                try {
                    log.info("查询退款状态，订单号：{}，退款单号：{}", 
                            refund.getBatteryOrderNo(), refund.getWechatRefundNo());
                    
                    batteryOrderService.queryRefundStatus(refund.getBatteryOrderNo());
                    
                    // 避免频繁调用，每次查询间隔1秒
                    Thread.sleep(1000);
                    
                } catch (Exception e) {
                    log.error("查询退款状态失败，订单号：{}", refund.getBatteryOrderNo(), e);
                }
            }
            
            log.info("退款状态查询定时任务执行完成");
            
        } catch (Exception e) {
            log.error("退款状态查询定时任务执行异常", e);
        }
    }

    /**
     * 每天凌晨2点执行，清理超过30天的退款记录
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupOldRefunds() {
        log.info("开始执行退款记录清理任务");
        
        try {
            LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
            
            QueryWrapper<TeboBatteryOrderRefundDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .in(TeboBatteryOrderRefundDO::getRefundStatus, 2, 3) // 已完成或失败
                    .lt(TeboBatteryOrderRefundDO::getCreateTime, thirtyDaysAgo);
            
            int deletedCount = refundMapper.delete(queryWrapper);
            
            log.info("退款记录清理完成，删除{}条记录", deletedCount);
            
        } catch (Exception e) {
            log.error("退款记录清理任务执行异常", e);
        }
    }
}
