package com.tebo.mall.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款状态枚举
 */
@Getter
@AllArgsConstructor
public enum RefundStatusEnum {

    REFUNDING(1, "退款中"),
    REFUND_SUCCESS(2, "退款成功"),
    REFUND_FAILED(3, "退款失败");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static RefundStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RefundStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
