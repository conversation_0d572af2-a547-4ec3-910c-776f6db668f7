package com.tebo.mall.web.domain.vo.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.mall.web.domain.vo.TeboOrderCommentVO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class TeboMallOrderVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String unionid;

    /**
     * 用户姓名
     */
    private String customerName;

    /**
     * 用户手机号
     */
    private String customerPhone;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 门店老板电话
     */
    private String phoneNumber;

    /**
     * 门店地址
     */
    private String shopAddress;

    /**
     * 合伙人id
     */
    private Long tenantId;

    /**
     * 合伙人名字
     */
    private String tenantName;

    /**
     * 订单状态 1:待支付 2:待提货,3 已完成  4 已评价 10:已取消
     */
    private Integer orderStatus;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 用户地址
     */
    private String address;

    /**
     * 订单金额，以分为单位
     */
    private Integer orderAmount;

    /**
     * 上门服务费
     */
    private Integer homeServiceFee;

    /**
     * 优惠券面值
     */
    private Integer parValue;

    /**
     * 优惠券编码
     */
    private String couponCode;

    /**
     * 服务类型 1上门 2 到店
     */
    private Integer serviceType;

    /**
     * 订单类型 1普通订单
     */
    private Integer orderType;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 取消方式 1 手动取消 2 超时取消
     */
    private Integer cancelType;

    /**
     * 下单时间
     */
    private LocalDateTime createTime;

    /**
     * 取消时间
     */
    private LocalDateTime cancelTime;

    /**
     * 预约时间
     */
    private String appointmentServiceTime;

    /**
     * 核销人员名字
     */
    private String verificationName;

    /**
     * 核销时间
     */
    private LocalDateTime verificationTime;

    /**
     * 核销人员账号
     */
    private String verificationAccount;

    /**
     * 订单评价
     */
    private TeboOrderCommentVO comment;

    /**
     * 订单商品数量
     */
    private Integer totalGoodsNumber;

    /**
     * 积分
     */
    private Integer integral;

    /**
     * 商品信息
     */
    private List<TeboOrderGoodsVO> goodsList;

    /**
     * 保险信息
     */
    private TeboInsurancePolicyOrderVO insurancePolicyOrderVO;

    /**
     * 渠道 1:普通订单 2:魔兽订单 3:驴充充 4:延保
     */
    private Integer channel;


    /**
     * 核销电池码
     */
    private String batteryCode;

    /**
     * 区域名称
     */
    private String areaName;
    /**
     * 第三方用户id
     */
    private String consumerId;

    public String getOrderAmount() {
        if(orderAmount == null) {
            return null;
        }
        return MoneyUtil.fenToYuan(orderAmount);
    }

    public String getHomeServiceFee() {
        if(homeServiceFee == null) {
            return null;
        }
        return MoneyUtil.fenToYuan(homeServiceFee);
    }

    public String getParValue() {
        if(parValue == null) {
            return null;
        }
        return MoneyUtil.fenToYuan(parValue);
    }
}
