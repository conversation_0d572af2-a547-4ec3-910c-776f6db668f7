package com.tebo.mall.service.goods;

import com.tebo.mall.entity.TeboPlatGoodsPartnerDO;
import com.tebo.mall.web.domain.dto.goods.TeboGoodsShopDTO;
import com.tebo.mall.web.domain.dto.goods.TeboPlatGoodsPartnerDTO;
import com.tebo.mall.web.domain.dto.goods.TeboPlatGoodsQueryDTO;
import com.tebo.mall.web.domain.vo.TeboPartnerGoodsVO;
import com.tebo.mall.web.domain.vo.TeboPlatGoodsGroupVO;
import com.tebo.mall.web.domain.vo.TeboPartnerGoodsVO;
import com.tebo.mall.web.domain.vo.TeboPlatGoodsVO;
import com.tebo.mall.web.domain.vo.TeboGoodsShopVO;
import com.tebo.system.api.domain.view.TeboPartnerVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 平台商品
 */
public interface TeBoPlatGoodsPartnerService {
    /**
     * 商品列表
     */
    public List<TeboPlatGoodsVO> listGoods(TeboPlatGoodsQueryDTO queryDTO);

    /**
     * 平台商品 合伙人分组
     * @param queryDTO
     * @return
     */
     List<TeboPlatGoodsGroupVO> platGoodsPartnerGroup(TeboPlatGoodsQueryDTO queryDTO);

    /**
     * 平台将商品授权给合伙人
     * @param goodsDTO
     * @return
     */
    int authorizedPartnerGoods(TeboPlatGoodsPartnerDTO goodsDTO);

    /**
     * 合伙人将商品授权给门店
     * @param goodsDTO
     * @return
     */
    Boolean authorizedShopGoods(TeboPlatGoodsPartnerDTO goodsDTO);
    /**
     *
     * 商品详情
     */
    TeboPlatGoodsVO detailGoods(Long id);

    /**
     * 更新商品信息
     * @param goodsDTO
     * @return
     */
    public Boolean updateGoods(TeboPlatGoodsPartnerDTO goodsDTO);

    List<TeboPlatGoodsPartnerDO> getPlatGoodsByUpdateTime(LocalDateTime compareTime);

    TeboPlatGoodsVO selectGoods(TeboGoodsShopDTO goodsShopDTO);

    /**
     * 合伙人授权商品数量
     */
   Integer getGoodsCount(Long tenantId);

    /**
     * 被授权商品的合伙人列表
     * @param teboPlatGoodsQueryDTO
     * @return
     */
   List<TeboPartnerGoodsVO> partnerList(TeboPlatGoodsQueryDTO teboPlatGoodsQueryDTO);

    /**
     * 被授权商品的门店列表
     */
    List<TeboGoodsShopVO> shopList(TeboPlatGoodsQueryDTO queryDTO);
}
