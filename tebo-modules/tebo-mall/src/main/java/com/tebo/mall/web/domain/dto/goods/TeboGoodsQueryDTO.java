package com.tebo.mall.web.domain.dto.goods;

import com.tebo.common.core.web.page.PageDomain;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author：zhengmk
 * @Date 2023/12/20 9:24
 */
@Data
public class TeboGoodsQueryDTO extends PageDomain implements Serializable {
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 商品所属类型 1:自营品 2:平台品
     */
    private Integer productType;
    /**
     * 商品编号
     */
    private String goodsNo;

    /**
     * 一级类目id
     */
    private Long categoryId;

    /**
     * 二级类目id
     */

    private Long secondCategoryId;

    /**
     * 品牌
     */
    private Long brandId;

    /**
     * 经营类别
     */
    private Integer businessCategory;

    /**
     * 是否上架
     */

    private Integer status;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 门店id列表
     */

    private List<Long> shopIdList;

    /**
     * 商品id列表
     */
    private List<Long> goodsIdList;

    // 商品货号列表
    private List<String> goodsNoList;

    /**
     * 合伙人id
     */

    private Long tenantId;

    /**
     * 销售类型
     */
    private List<Integer> salesTypeList;

    /**
     * 经度
     */
    private String lgt;

    /**
     * 纬度
     */
    private String lnt;

    /**
     * 二级目录id
     */
    private List<Long> secondCategoryIdList;

    /**
     * 是否删除 0 未删除 1 删除
     */
    private Integer delFlag;

    /**
     * 推荐商品 1:不推荐 2 推荐
     */
    private Integer recommend;

    /**
     * 置顶 1:不置顶 2:置顶
     */
    private Integer top;

    /**
     * 不包括
     */
    private List<Long> notIncludeList;

    /**
     * 是否展示泰博麒麟和泰博金刚等电池 默认不显示 1显示
     */
    private Integer show;

    /**
     * 商品审核状态
     */
    private Integer reviewStatus;
}
