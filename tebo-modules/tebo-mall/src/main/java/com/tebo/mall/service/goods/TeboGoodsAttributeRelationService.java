package com.tebo.mall.service.goods;

import com.tebo.mall.api.domain.view.TeboAttributeRelationVO;
import com.tebo.mall.web.domain.dto.goods.TeboGoodsAttributeDTO;
import com.tebo.mall.web.domain.dto.goods.TeboGoodsShopDTO;

import java.util.List;

public interface TeboGoodsAttributeRelationService {
    /**
     * 新建商品属性
     */
    public void addGoodsAttribute(List<Long> goodsIdList,List<TeboGoodsAttributeDTO> attributeDTOList);

    /**
     * 更新商品属性
     */
    public void updateGoodsAttribute(List<Long> goodsIdList,TeboGoodsShopDTO teboGoodsShopDTO);

    /**
     * 获取商品属性列表以及属性值
     */
    public List<TeboAttributeRelationVO> listGoodsAttribute(TeboGoodsAttributeDTO teboGoodsAttributeDTO);

}
