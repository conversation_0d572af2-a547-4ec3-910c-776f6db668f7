package com.tebo.mall.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户积分流水表
 */
@Getter
@Setter
@TableName("tebo_user_integral_record")
public class TeboUserIntegralRecordDO extends Model<TeboUserIntegralRecordDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableField("id")
    private Long id;

    /**
     * 用户id
     */
    @TableField("user_account")
    private Long userAccount;

    /**
     * 订单id
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 积分值
     */
    @TableField("integral")
    private Integer integral;

    /**
     * 积分类型 1增加 2减少
     */
    @TableField("type")
    private Integer type;

    /**
     * 原因
     */
    @TableField("reason")
    private String reason;
    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Override
    public Serializable pkVal() {
        return null;
    }

}
