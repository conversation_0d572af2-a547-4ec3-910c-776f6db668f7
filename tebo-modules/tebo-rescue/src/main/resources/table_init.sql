CREATE TABLE `tebo_queue_order`
(
    `id`                bigint      NOT NULL AUTO_INCREMENT COMMENT '唯一主键',
    `unionid`           varchar(64) NOT NULL COMMENT 'unionid',
    `shop_id`           bigint      NOT NULL COMMENT '门店id',
    `tenant_id`         bigint      NOT NULL COMMENT '合伙人id',
    `order_no`          varchar(64) NOT NULL COMMENT '订单号',
    `account_id`        bigint        DEFAULT NULL COMMENT '维修师傅id',
    `order_type`        tinyint(1) NOT NULL COMMENT '订单类型0:维修 1:安装 2:保养 3:洗车',
    `order_status`      tinyint(1) NOT NULL COMMENT '订单状态 0:进行中 1:已叫号 2:待付款 3:已完成 4:已取消',
    `pickup_type`       tinyint(1) NOT NULL DEFAULT 0 COMMENT '取件方式 0:到店取号 1:预约取号',
    `handle_result`     varchar(1024) DEFAULT NULL COMMENT '处理结果',
    `queue_number`      INT         NOT NULL COMMENT '排队号',
    `queue_number_str`  varchar(64) NOT NULL COMMENT '排队号字符串',
    `total_comment`     TINYINT(1) COMMENT '总体评价0-5',
    `service_comment`   TINYINT(1) COMMENT '服务评价0-5',
    `quantity_comment`  TINYINT(1) COMMENT '质量0-5',
    `comment_finished`  TINYINT(1) DEFAULT 0 COMMENT '是否评价完成0-未完成 1-已完成',
    `remark`            VARCHAR(255)  DEFAULT NULL COMMENT '备注',
    `order_amount`      INT           DEFAULT NULL COMMENT '订单金额,单位分',
    `actual_pay_amount` INT           DEFAULT NULL COMMENT '实际支付金额,单位分',
    `call_time`         datetime      DEFAULT NULL COMMENT '叫号时间',
    `pay_time`          datetime      DEFAULT NULL COMMENT '支付时间',
    `comment_time`      datetime      DEFAULT NULL COMMENT '评价时间',
    `del_flag`          tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    `create_by`         varchar(64)   DEFAULT '' COMMENT '创建者',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `update_by`         varchar(64)   DEFAULT '' COMMENT '更新者',
    `update_time`       datetime      DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='C端服务订单表';


CREATE TABLE `tebo_service_order`
(
    `id`                bigint      NOT NULL AUTO_INCREMENT COMMENT '唯一主键',
    `account_id`        bigint      NOT NULL COMMENT '维修师傅id',
    `shop_id`           bigint      NOT NULL COMMENT '门店id',
    `tenant_id`         bigint      NOT NULL COMMENT '合伙人id',
    `order_no`          varchar(64) NOT NULL COMMENT '订单号',
    `order_type`        tinyint(1) NOT NULL COMMENT '订单类型0:维修 1:安装 2:保养 3:洗车',
    `service_type`      tinyint(1) NOT NULL COMMENT '服务类型0:维修-更换轮胎，1维修-更换电池，2维修-更换刹车片，3维修-车体维修，4维修-更换机油，5维修-更换充电器，6维修-修补轮胎，7安装-整车安装，8保养-更换机油，9保养-更换轮胎，10保养-更换电池，11保养-更换刹车片，12-更换充电器，13洗车-洗车',
    `order_status`      tinyint(1) NOT NULL COMMENT '订单状态 0:维修中 1:已完成 2:已取消',
    `queue_order_id`    bigint      NOT NULL COMMENT 'C端叫号单id',
    `station_name`      varchar(64) NOT NULL DEFAULT '' COMMENT '工位名称',
    `handle_result`     varchar(1024)        DEFAULT NULL COMMENT '处理结果',
    `remark`            VARCHAR(255)         DEFAULT NULL COMMENT '备注',
    `base_amount`       INT                  DEFAULT NULL COMMENT '服务基础金额,单位分',
    `order_amount`      INT                  DEFAULT NULL COMMENT '订单金额,单位分',
    `actual_pay_amount` INT                  DEFAULT NULL COMMENT '实际支付金额,单位分',
    `pay_time`          datetime             DEFAULT NULL COMMENT '支付时间',
    `del_flag`          tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    `create_by`         varchar(64)          DEFAULT '' COMMENT '创建者',
    `create_time`       datetime             DEFAULT NULL COMMENT '创建时间',
    `update_by`         varchar(64)          DEFAULT '' COMMENT '更新者',
    `update_time`       datetime             DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='师傅端服务订单表';


CREATE TABLE `tebo_service_order_goods`
(
    `id`               bigint      NOT NULL AUTO_INCREMENT COMMENT '唯一主键',
    `account_id`       bigint      NOT NULL COMMENT '维修师傅id',
    `shop_id`          bigint      NOT NULL COMMENT '门店id',
    `service_order_id` varchar(64) NOT NULL COMMENT '订单号',
    `type`             tinyint(1) NOT NULL COMMENT '是否手动添加 0:否 1:是',
    `goods_id`         bigint COMMENT '商品id，当是选择配件时，有id',
    `goods_name`       varchar(64) NOT NULL DEFAULT '' COMMENT '商品名称',
    `brand_name`       varchar(64) NOT NULL DEFAULT '' COMMENT '品牌名称',
    `sku`              varchar(64) NOT NULL DEFAULT '' COMMENT '商品sku',
    `goods_price`      INT                  DEFAULT NULL COMMENT '商品单价,单位分',
    `goods_num`        INT                  DEFAULT NULL COMMENT '商品数量',
    `goods_amount`     INT                  DEFAULT NULL COMMENT '商品总金额,单位分',
    `color`            varchar(64) NOT NULL DEFAULT '' COMMENT '商品颜色',
    `remark`           VARCHAR(255)         DEFAULT NULL COMMENT '备注',
    `del_flag`         tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    `create_by`        varchar(64)          DEFAULT '' COMMENT '创建者',
    `create_time`      datetime             DEFAULT NULL COMMENT '创建时间',
    `update_by`        varchar(64)          DEFAULT '' COMMENT '更新者',
    `update_time`      datetime             DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='师傅端服务订单商品表';


CREATE TABLE `tebo_service_order_coupon`
(
    `id`               bigint      NOT NULL AUTO_INCREMENT COMMENT '唯一主键',
    `service_order_id` varchar(64) NOT NULL COMMENT '订单号',
    `pack_id`          bigint      DEFAULT NULL COMMENT '礼包id',
    `coupon_id`        bigint      NOT NULL COMMENT '优惠券id',
    `unique_code`      varchar(64) NOT NULL COMMENT '卡券编码',
    `coupon_name`      varchar(64) NOT NULL COMMENT '优惠券名称',
    `coupon_price`     INT         DEFAULT NULL COMMENT '优惠券金额,单位分',
    `del_flag`         tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    `create_by`        varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time`      datetime    DEFAULT NULL COMMENT '创建时间',
    `update_by`        varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time`      datetime    DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='师傅端服务订单优惠券表';



CREATE TABLE `tebo_gift_pack_order`
(
    `id`           bigint      NOT NULL AUTO_INCREMENT COMMENT '唯一主键',
    `order_no`     varchar(64) NOT NULL COMMENT '订单号',
    `pack_id`      bigint      DEFAULT NULL COMMENT '礼包id',
    `union_id`     varchar(64) NOT NULL COMMENT '用户unionid',
    `order_status` tinyint(1) NOT NULL COMMENT '订单状态 0:待支付 1:已支付,10:已取消',
    `order_amount` INT         DEFAULT NULL COMMENT '订单金额,单位分',
    `pay_time`     datetime    DEFAULT NULL COMMENT '支付时间',
    `del_flag`     tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    `create_by`    varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time`  datetime    DEFAULT NULL COMMENT '创建时间',
    `update_by`    varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time`  datetime    DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='礼包订单表';

