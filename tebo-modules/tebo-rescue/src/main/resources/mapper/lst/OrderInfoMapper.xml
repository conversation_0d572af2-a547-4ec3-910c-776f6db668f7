<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tebo.rescue.lst.mapper.OrderInfoMapper">
    <resultMap id="BaseResultMap" type="com.tebo.rescue.lst.domain.entity.OrderInfoDo">
        <id column="id" property="id"/>
        <result column="tebo_partner_code" property="teboPartnerCode"/>
        <result column="tebo_partner_name" property="teboPartnerName"/>
        <result column="tebo_partner_phone" property="teboPartnerPhone"/>
        <result column="shop_code" property="shopCode"/>
        <result column="order_no" property="orderNo"/>
        <result column="out_order_no" property="outOrderNo"/>
        <result column="order_source" property="orderSource"/>
        <result column="order_status" property="orderStatus"/>
        <result column="order_price" property="orderPrice"/>
        <result column="order_duration" property="orderDuration"/>
        <result column="order_type" property="orderType"/>
        <result column="service_type" property="serviceType"/>
        <result column="pay_status" property="payStatus"/>
        <result column="appointment_date" property="appointmentDate"/>
        <result column="appointment_time" property="appointmentTime"/>
        <result column="is_time_out" property="isTimeOut"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="store_name" property="storeName"/>
        <result column="store_tel" property="storeTel"/>
        <result column="store_full_address" property="storeFullAddress"/>
        <result column="real_phone" property="realPhone"/>
        <result column="tby_flag" property="tbyFlag"/>
    </resultMap>
    <sql id="selectAllOrderInfo">
        SELECT id,
               tebo_partner_code,
               tebo_partner_name,
               tebo_partner_phone,
               shop_code,
               order_no,
               out_order_no,
               order_source,
               order_status,
               order_price,
               order_duration,
               order_type,
               service_type,
               pay_status,
               appointment_date,
               appointment_time,
               is_time_out,
               delete_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               store_name,
               store_tel,
               store_full_address,
               real_phone,
               tby_flag
        FROM order_info
    </sql>

    <resultMap id="OrderInfoLocusVoMap" type="com.tebo.rescue.lst.domain.vo.OrderInfoLocusVo">
        <id column="locus_id" property="id"/>
        <result column="locus_order_no" property="orderNo"/>
        <result column="locus_info" property="locusInfo"/>
        <result column="locus_create_time" property="createTime"/>
    </resultMap>

    <resultMap id="OrderVoMap" type="com.tebo.rescue.lst.domain.vo.OrderVo">
        <!-- OrderInfoDo fields -->
        <id column="id" property="id"/>
        <result column="tebo_partner_code" property="teboPartnerCode"/>
        <result column="tebo_partner_name" property="teboPartnerName"/>
        <result column="tebo_partner_phone" property="teboPartnerPhone"/>
        <result column="shop_code" property="shopCode"/>
        <result column="shop_name" property="shopName"/>
        <result column="order_no" property="orderNo"/>
        <result column="out_order_no" property="outOrderNo"/>
        <result column="order_source" property="orderSource"/>
        <result column="order_status" property="orderStatus"/>
        <result column="order_price" property="orderPrice"/>
        <result column="order_duration" property="orderDuration"/>
        <result column="order_type" property="orderType"/>
        <result column="service_type" property="serviceType"/>
        <result column="pay_status" property="payStatus"/>
        <result column="appointment_date" property="appointmentDate"/>
        <result column="appointment_time" property="appointmentTime"/>
        <result column="is_time_out" property="isTimeOut"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="store_name" property="storeName"/>
        <result column="store_tel" property="storeTel"/>
        <result column="store_full_address" property="storeFullAddress"/>
        <result column="real_phone" property="realPhone"/>
        <result column="tby_flag" property="tbyFlag"/>
        <!-- OrderInfoDetailDo fields -->
        <result column="goods_name" property="goodsName"/>
        <result column="goods_mode" property="goodsMode"/>
        <result column="parent_name" property="parentName"/>
        <result column="qty" property="qty"/>
        <result column="receive_address" property="receiveAddress"/>
        <result column="receive_name" property="receiveName"/>
        <result column="receive_tel" property="receiveTel"/>
        <result column="remark_user" property="remarkUser"/>
        <result column="remark_platform" property="remarkPlatform"/>
        <result column="remark_hang" property="remarkHang"/>
        <result column="reason_hang" property="reasonHang"/>
        <result column="pic_order" property="picOrder"/>
        <result column="pic_finish" property="picFinish"/>
        <result column="pic_comment" property="picComment"/>
        <result column="service_code" property="serviceCode"/>
        <result column="extra_fee" property="extraFee"/>
        <result column="ref_settle_price" property="refSettlePrice"/>
        <result column="car_name" property="carName"/>
        <!-- OrderInfoLocusVo as a list -->
        <collection property="locusList" ofType="com.tebo.rescue.lst.domain.vo.OrderInfoLocusVo"
                    resultMap="OrderInfoLocusVoMap"/>
    </resultMap>

    <!-- 订单sql -->
    <sql id="baseListSql">
        SELECT oi.id,
               oi.tebo_partner_code,
               oi.tebo_partner_name,
               oi.tebo_partner_phone,
               oi.shop_code,
               si.shop_name,
               oi.order_no,
               oi.out_order_no,
               oi.order_source,
               oi.order_status,
               oi.order_price,
               oi.order_type,
               oi.real_phone,
               oi.service_type,
               oi.pay_status,
               oi.create_by,
               oi.create_time,
               oi.update_by,
               oi.update_time,
               oi.real_phone,
               oid.goods_name,
               oid.goods_mode,
               oid.qty,
               oid.receive_address,
               oid.receive_name,
               oid.receive_tel,
               oid.extra_fee,
               oid.ref_settle_price,
               oid.car_name
    </sql>
    <select id="selectOrderVoList" resultMap="OrderVoMap">
        <include refid="baseListSql"></include>
        FROM (
        select * from order_info
        <where>
            delete_flag = '0'
            <if test="teboPartnerCode != null and teboPartnerCode != ''">
                AND tebo_partner_code = #{teboPartnerCode}
            </if>
            <if test="shopCode != null and shopCode != ''">
                AND shop_code = #{shopCode}
            </if>
            <if test="orderNo != null  and orderNo != ''">and order_no like concat('%', #{orderNo}, '%')</if>
            <if test="outOrderNo != null  and outOrderNo != ''">and out_order_no like concat('%', #{outOrderNo},
                '%')
            </if>
            <if test="orderSource != null  and orderSource != ''">and order_source = #{orderSource}</if>
            <if test="orderStatus != null ">
                and order_status = #{orderStatus}
            </if>
            <if test="orderType != null ">and order_type = #{orderType}</if>
            <if test="isTimeOut != null and isTimeOut != ''">and is_time_out = #{isTimeOut}</if>
            <if test="serviceType != null ">and service_type = #{serviceType}</if>
            <if test="beginTime != null and beginTime != ''">
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''">
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
        </where>
        ) oi
        LEFT JOIN order_info_detail oid ON oi.order_no = oid.order_no
        left join shop_info si on oi.shop_code = si.shop_code
        <where>
            <if test="goodsName != null and goodsName != ''">
                AND oid.goods_name like concat('%', #{goodsName}, '%')
            </if>
            <if test="goodsMode != null and goodsMode != ''">
                AND oid.goods_mode = #{goodsMode}
            </if>
            <if test="receiveName != null  and receiveName != ''">
                and oid.receive_name like concat('%', #{receiveName},'%')
            </if>
            <if test="receiveTel != null  and receiveTel != ''">
                and oid.receive_tel = #{receiveTel}
            </if>
            <if test="shopName != null  and shopName != ''">and si.shop_name like concat('%', #{shopName}, '%')</if>
        </where>
        ORDER BY oi.id DESC
    </select>
    <select id="selectStoreOrderVoList" resultMap="OrderVoMap">
        <include refid="baseListSql"></include>
        FROM (
        select * from order_info
        <where>
            delete_flag = '0'
            AND shop_code = #{shopCode}
            <if test="teboPartnerCode != null and teboPartnerCode != ''">
                AND tebo_partner_code = #{teboPartnerCode}
            </if>
            <if test="orderNo != null  and orderNo != ''">and order_no like concat('%', #{orderNo}, '%')</if>
            <if test="outOrderNo != null  and outOrderNo != ''">and out_order_no like concat('%', #{outOrderNo},
                '%')
            </if>
            <if test="orderSource != null  and orderSource != ''">and order_source = #{orderSource}</if>
            <if test="orderStatus != null ">
                <choose>
                    <!-- 门店全部 -->
                    <when test="orderStatus == 1001">
                        and order_status in (1,4,5,8)
                    </when>
                    <otherwise>
                        and order_status = #{orderStatus}
                    </otherwise>
                </choose>
            </if>
            <if test="orderType != null ">and order_type = #{orderType}</if>
            <if test="isTimeOut != null and isTimeOut != ''">and is_time_out = #{isTimeOut}</if>
            <if test="serviceType != null ">and service_type = #{serviceType}</if>
            <if test="beginTime != null and beginTime != ''">
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''">
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
        </where>
        ) oi
        LEFT JOIN order_info_detail oid ON oi.order_no = oid.order_no
        left join shop_info si on oi.shop_code = si.shop_code
        <where>
            <if test="goodsName != null and goodsName != ''">
                AND oid.goods_name like concat('%', #{goodsName}, '%')
            </if>
            <if test="goodsMode != null and goodsMode != ''">
                AND oid.goods_mode = #{goodsMode}
            </if>
            <if test="receiveName != null  and receiveName != ''">
                and oid.receive_name like concat('%', #{receiveName},'%')
            </if>
            <if test="receiveTel != null  and receiveTel != ''">
                and oid.receive_tel = #{receiveTel}
            </if>
            <if test="shopName != null  and shopName != ''">and si.shop_name like concat('%', #{shopName}, '%')</if>
        </where>
        ORDER BY oi.id DESC
    </select>

    <select id="selectPartnerOrderVoList" resultMap="OrderVoMap">
        <include refid="baseListSql"></include>
        FROM (
        select * from order_info
        <where>
            delete_flag = '0'
            AND tebo_partner_code = #{teboPartnerCode}
            <if test="shopCode != null and shopCode != ''">
                AND shop_code = #{shopCode}
            </if>
            <if test="orderNo != null  and orderNo != ''">and order_no like concat('%', #{orderNo}, '%')</if>
            <if test="outOrderNo != null  and outOrderNo != ''">and out_order_no like concat('%', #{outOrderNo},
                '%')
            </if>
            <if test="orderSource != null  and orderSource != ''">and order_source = #{orderSource}</if>
            <if test="orderStatus != null ">
                <choose>
                    <!-- 合伙人全部 -->
                    <when test="orderStatus == 1002">
                        and order_status in (1,4,5,8,9)
                    </when>
                    <otherwise>
                        and order_status = #{orderStatus}
                    </otherwise>
                </choose>
            </if>
            <if test="orderType != null ">and order_type = #{orderType}</if>
            <if test="isTimeOut != null and isTimeOut != ''">and is_time_out = #{isTimeOut}</if>
            <if test="serviceType != null ">and service_type = #{serviceType}</if>
            <if test="beginTime != null and beginTime != ''">
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''">
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
        </where>
        ) oi
        LEFT JOIN order_info_detail oid ON oi.order_no = oid.order_no
        left join shop_info si on oi.shop_code = si.shop_code
        <where>
            <if test="goodsName != null and goodsName != ''">
                AND oid.goods_name like concat('%', #{goodsName}, '%')
            </if>
            <if test="goodsMode != null and goodsMode != ''">
                AND oid.goods_mode = #{goodsMode}
            </if>
            <if test="receiveName != null  and receiveName != ''">
                and oid.receive_name like concat('%', #{receiveName},'%')
            </if>
            <if test="receiveTel != null  and receiveTel != ''">
                and oid.receive_tel = #{receiveTel}
            </if>
            <if test="shopName != null  and shopName != ''">and si.shop_name like concat('%', #{shopName}, '%')</if>
        </where>
        ORDER BY oi.id DESC
    </select>

    <select id="selectAdminOrderVoList" resultMap="OrderVoMap">
        <include refid="baseListSql"></include>
        FROM (
        select * from order_info
        <where>
            delete_flag = '0'
            <choose>
                <when test="teboPartnerCode != null and teboPartnerCode != ''">
                    AND tebo_partner_code = #{teboPartnerCode}
                </when>
                <otherwise>
                    AND (tebo_partner_code != '' AND tebo_partner_code IS NOT NULL)
                </otherwise>
            </choose>
            <if test="shopCode != null and shopCode != ''">
                AND shop_code = #{shopCode}
            </if>
            <if test="orderNo != null  and orderNo != ''">and order_no like concat('%', #{orderNo}, '%')</if>
            <if test="outOrderNo != null  and outOrderNo != ''">and out_order_no like concat('%', #{outOrderNo},
                '%')
            </if>
            <if test="orderSource != null  and orderSource != ''">and order_source = #{orderSource}</if>
            <if test="orderStatus != null ">
                <choose>
                    <!-- 合伙人全部 -->
                    <when test="orderStatus == 1002">
                        and order_status in (1,4,5,8,9)
                    </when>
                    <otherwise>
                        and order_status = #{orderStatus}
                    </otherwise>
                </choose>
            </if>
            <if test="orderType != null ">and order_type = #{orderType}</if>
            <if test="isTimeOut != null and isTimeOut != ''">and is_time_out = #{isTimeOut}</if>
            <if test="serviceType != null ">and service_type = #{serviceType}</if>
            <if test="beginTime != null and beginTime != ''">
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''">
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
        </where>
        ) oi
        LEFT JOIN order_info_detail oid ON oi.order_no = oid.order_no
        left join shop_info si on oi.shop_code = si.shop_code
        <where>
            <if test="goodsName != null and goodsName != ''">
                AND oid.goods_name like concat('%', #{goodsName}, '%')
            </if>
            <if test="goodsMode != null and goodsMode != ''">
                AND oid.goods_mode = #{goodsMode}
            </if>
            <if test="receiveName != null  and receiveName != ''">
                and oid.receive_name like concat('%', #{receiveName},'%')
            </if>
            <if test="receiveTel != null  and receiveTel != ''">
                and oid.receive_tel = #{receiveTel}
            </if>
            <if test="shopName != null  and shopName != ''">and si.shop_name like concat('%', #{shopName}, '%')</if>
        </where>
        ORDER BY oi.id DESC
    </select>


    <select id="countOrderVoList" resultType="long">
        select count(1) from order_info
        <where>
            delete_flag = '0'
            <if test="shopCode != null and shopCode != ''">
                AND shop_code = #{shopCode}
            </if>
            <if test="orderNo != null  and orderNo != ''">and order_no like concat('%', #{orderNo}, '%')</if>
            <if test="outOrderNo != null  and outOrderNo != ''">and out_order_no like concat('%', #{outOrderNo},
                '%')
            </if>
            <if test="orderSource != null  and orderSource != ''">and order_source = #{orderSource}</if>
            <if test="orderStatus != null ">
                <choose>
                    <!-- 小程序全部 -->
                    <when test="orderStatus == 1001">
                        and order_status in (1,4,5,8)
                    </when>
                    <otherwise>
                        and order_status = #{orderStatus}
                    </otherwise>
                </choose>
            </if>
            <if test="orderType != null ">and order_type = #{orderType}</if>
            <if test="serviceType != null ">and service_type = #{serviceType}</if>
            <if test="isTimeOut != null and isTimeOut != ''">and is_time_out = #{isTimeOut}</if>
            <if test="beginTime != null and beginTime != ''">
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''">
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
        </where>
    </select>

    <select id="selectOrderVo" resultMap="OrderVoMap" parameterType="long">
        SELECT oi.*,
               oid.goods_name,
               oid.goods_mode,
               oid.parent_name,
               oid.qty,
               oid.receive_address,
               oid.receive_name,
               oid.receive_tel,
               oid.remark_user,
               oid.remark_platform,
               oid.remark_hang,
               oid.reason_hang,
               oid.pic_order,
               oid.pic_finish,
               oid.pic_comment,
               oid.service_code,
               oid.extra_fee,
               oid.ref_settle_price,
               oid.car_name,
               oio.id          as locus_id,
               oio.order_no    as locus_order_no,
               oio.locus_info,
               oio.create_time as locus_create_time,
               si.shop_name,
               si.keeper_tel as storeTel,
               si.receive_address as storeFullAddress,
               si.keeper_name as storeName
        FROM order_info oi
                 LEFT JOIN order_info_detail oid ON oi.order_no = oid.order_no
                 LEFT JOIN order_info_locus oio ON oi.order_no = oio.order_no
                 left join shop_info si on oi.shop_code = si.shop_code
        WHERE oi.id = #{orderId}
          and oi.delete_flag = '0'
    </select>

    <update id="updateOrder">
        UPDATE order_info
        <set>
            <if test="shopCode != null">
                shop_code = #{shopCode},
            </if>
            <if test="teboPartnerCode != null">
                tebo_partner_code = #{teboPartnerCode},
            </if>
            <if test="teboPartnerName != null">
                tebo_partner_name = #{teboPartnerName},
            </if>
            <if test="teboPartnerPhone != null">
                tebo_partner_phone = #{teboPartnerPhone},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus},
            </if>
            <if test="orderDuration != null">
                order_duration = #{orderDuration},
            </if>
            update_time = NOW(),
            update_by = #{updateBy}
        </set>
        WHERE id = #{id}
    </update>

</mapper>
