<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tebo.rescue.lst.mapper.OrderSettleMapper">
    <resultMap id="BaseResultMap" type="com.tebo.rescue.lst.domain.entity.OrderSettleDo">
        <id column="id" property="id"/>
        <result column="out_order_no" property="outOrderNo"/>
        <result column="settle_amount" property="settleAmount"/>
        <result column="pay_account" property="payAccount"/>
        <result column="payer" property="payer"/>
        <result column="settle_date" property="settleDate"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
    </resultMap>
    <sql id="selectAllOrderSettle">
        SELECT id,
               out_order_no,
               settle_amount,
               pay_account,
               payer,
               settle_date,
               create_by,
               create_time
        FROM order_settle
    </sql>

    <!-- 定义你的 SQL 语句 -->
</mapper>
