<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.lst.mapper.ShopInfoMapper">


    <resultMap id="ShopInfoResultMap" type="com.tebo.rescue.lst.domain.entity.ShopInfoDo">
        <id column="id" property="id"/>
        <result column="wx_open_id" property="wxOpenId"/>
        <result column="shop_code" property="shopCode"/>
        <result column="shop_name" property="shopName"/>
        <result column="keeper_name" property="keeperName"/>
        <result column="keeper_tel" property="keeperTel"/>
        <result column="is_sign" property="isSign"/>
        <result column="apply_status" property="applyStatus"/>
        <result column="enable_status" property="enableStatus"/>
        <result column="social_credit_code" property="socialCreditCode"/>
        <result column="business_license" property="businessLicense"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="area" property="area"/>
        <result column="receive_address" property="receiveAddress"/>
        <result column="receive_type" property="receiveType"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="shop_level" property="shopLevel"/>
        <result column="parent_code" property="parentCode"/>
        <result column="id_card_front" property="idCardFront"/>
        <result column="id_crd" property="idCrd"/>
        <result column="shop_head_photo" property="shopHeadPhoto"/>
        <result column="contacts_name" property="contactsName"/>
        <result column="contacts_tel" property="contactsTel"/>
        <result column="id_card_front_fan" property="idCardFrontFan"/>
        <result column="audit_information" property="auditInformation"/>
        <result column="gys_code" property="gysCode"/>
        <result column="outside_code" property="outsideCode"/>
        <result column="outside_source" property="outsideSource"/>
    </resultMap>

    <resultMap id="ExtendedShopInfoResultMap" extends="ShopInfoResultMap" type="com.tebo.rescue.lst.domain.vo.ShopVo">
        <result column="alipay_account" property="alipayAccount"/>
        <result column="alipay_name" property="alipayName"/>
        <result column="supplier_name" property="supplierName"/>
    </resultMap>


    <insert id="addShopInfo" parameterType="com.tebo.rescue.lst.domain.entity.ShopInfoDo">
        INSERT INTO shop_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="wxOpenId != null">wx_open_id,</if>
            <if test="shopCode != null">shop_code,</if>
            <if test="shopName != null">shop_name,</if>
            <if test="keeperName != null">keeper_name,</if>
            <if test="keeperTel != null">keeper_tel,</if>
            <if test="isSign != null">is_sign,</if>
            <if test="applyStatus != null">apply_status,</if>
            <if test="enableStatus != null">enable_status,</if>
            <if test="socialCreditCode != null">social_credit_code,</if>
            <if test="businessLicense != null">business_license,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="area != null">area,</if>
            <if test="receiveAddress != null">receive_address,</if>
            <if test="receiveType != null">receive_type,</if>
            <if test="supplierCode != null">supplier_code,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="shopLevel != null">shop_level,</if>
            <if test="parentCode != null">parent_code,</if>
            <if test="idCardFront != null">id_card_front,</if>
            <if test="idCrd != null">id_crd,</if>
            <if test="shopHeadPhoto != null">shop_head_photo,</if>
            <if test="contactsName != null">contacts_name,</if>
            <if test="contactsTel != null">contacts_tel,</if>
            <if test="idCardFrontFan != null">id_card_front_fan,</if>
            <if test="auditInformation != null">audit_information,</if>
            <if test="gysCode != null">gys_code,</if>
            <if test="outsideCode != null">outside_code,</if>
            <if test="outsideSource != null">outside_source,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="wxOpenId != null">#{wxOpenId},</if>
            <if test="shopCode != null">#{shopCode},</if>
            <if test="shopName != null">#{shopName},</if>
            <if test="keeperName != null">#{keeperName},</if>
            <if test="keeperTel != null">#{keeperTel},</if>
            <if test="isSign != null">#{isSign},</if>
            <if test="applyStatus != null">#{applyStatus},</if>
            <if test="enableStatus != null">#{enableStatus},</if>
            <if test="socialCreditCode != null">#{socialCreditCode},</if>
            <if test="businessLicense != null">#{businessLicense},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="area != null">#{area},</if>
            <if test="receiveAddress != null">#{receiveAddress},</if>
            <if test="receiveType != null">#{receiveType},</if>
            <if test="supplierCode != null">#{supplierCode},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="shopLevel != null">#{shopLevel},</if>
            <if test="parentCode != null">#{parentCode},</if>
            <if test="idCardFront != null">#{idCardFront},</if>
            <if test="idCrd != null">#{idCrd},</if>
            <if test="shopHeadPhoto != null">#{shopHeadPhoto},</if>
            <if test="contactsName != null">#{contactsName},</if>
            <if test="contactsTel != null">#{contactsTel},</if>
            <if test="idCardFrontFan != null">#{idCardFrontFan},</if>
            <if test="auditInformation != null">#{auditInformation},</if>
            <if test="gysCode != null">#{gysCode},</if>
            <if test="outsideCode != null">#{outsideCode},</if>
            <if test="outsideSource != null">#{outsideSource},</if>
        </trim>
    </insert>

    <!--    查询门店信息-->
    <select id="selectShopVo" parameterType="String" resultMap="ExtendedShopInfoResultMap">
        SELECT si.id,
               si.shop_code,
               si.shop_name,
               si.keeper_name,
               si.keeper_tel,
               si.is_sign,
               si.apply_status,
               si.enable_status,
               si.social_credit_code,
               si.business_license,
               si.province,
               si.city,
               si.area,
               si.receive_address,
               si.receive_type,
               si.supplier_code,
               si.shop_level,
               si.parent_code,
               si.id_card_front,
               si.id_crd,
               si.shop_head_photo,
               si.contacts_name,
               si.contacts_tel,
               si.id_card_front_fan,
               si.audit_information,
               si.gys_code,
               si.outside_code,
               si.outside_source,
               sr.alipay_account,
               sr.alipay_name,
               si.delete_flag,
               si.create_by,
               si.create_time,
               si.update_by,
               si.update_time
        FROM shop_info si
                 LEFT JOIN shop_receive sr ON si.shop_code = sr.shop_code
        WHERE si.delete_flag = '0'
          and si.outside_code = #{outsideCode}
    </select>

    <!-- 查询列表操作 -->
    <select id="selectShopList" parameterType="com.tebo.rescue.lst.domain.entity.ShopInfoDo"
            resultMap="ExtendedShopInfoResultMap">
        SELECT si.id,
               si.shop_code,
               si.shop_name,
               si.keeper_name,
               si.keeper_tel,
               si.is_sign,
               si.apply_status,
               si.enable_status,
               si.social_credit_code,
               si.business_license,
               sp.name AS province,
               sc.name AS city,
               sa.name AS area,
               si.receive_address,
               si.receive_type,
               si.supplier_code,
               si.shop_level,
               si.parent_code,
               si.id_card_front,
               si.id_crd,
               si.shop_head_photo,
               si.contacts_name,
               si.contacts_tel,
               si.id_card_front_fan,
               si.audit_information,
               si.gys_code,
               si.outside_code,
               si.outside_source,
               sr.alipay_account,
               sr.alipay_name,
               si.delete_flag,
               si.create_by,
               si.create_time,
               si.update_by,
               si.update_time,
               gys.supplier_name
        FROM shop_info si
                 LEFT JOIN shop_receive sr ON si.shop_code = sr.shop_code
                 LEFT JOIN sys_region sp ON si.province = sp.id
                 LEFT JOIN sys_region sc ON si.city = sc.id
                 LEFT JOIN sys_region sa ON si.area = sa.id
                 Left join supplier_info gys on si.supplier_code = gys.supplier_code
    </select>


<select id="selectShopListByCodes" parameterType="com.tebo.rescue.lst.domain.entity.ShopInfoDo"
            resultMap="ExtendedShopInfoResultMap">
        SELECT si.id,
               si.shop_code,
               si.shop_name,
               si.keeper_name,
               si.keeper_tel,
               si.is_sign,
               si.apply_status,
               si.enable_status,
               si.social_credit_code,
               si.business_license,
               sp.name AS province,
               sc.name AS city,
               sa.name AS area,
               si.receive_address,
               si.receive_type,
               si.supplier_code,
               si.shop_level,
               si.parent_code,
               si.id_card_front,
               si.id_crd,
               si.shop_head_photo,
               si.contacts_name,
               si.contacts_tel,
               si.id_card_front_fan,
               si.audit_information,
               si.gys_code,
               si.outside_code,
               si.outside_source,
               sr.alipay_account,
               sr.alipay_name,
               si.delete_flag,
               si.create_by,
               si.create_time,
               si.update_by,
               si.update_time
        FROM shop_info si
                 LEFT JOIN shop_receive sr ON si.shop_code = sr.shop_code
                 LEFT JOIN sys_region sp ON si.province = sp.id
                 LEFT JOIN sys_region sc ON si.city = sc.id
                 LEFT JOIN sys_region sa ON si.area = sa.id
    <where>

        and si.shop_code in
        <foreach collection="shopCodes" item="shopCode" open="(" separator="," close=")">
            #{shopCode}
        </foreach>
    </where>

</select>

    <select id="queryShopSettleRefPrice" resultType="decimal">
        SELECT ref_settle_price
        FROM `shop_ref_price`
        where shop_code = #{shopCode}
          and sku_code = #{goodsMode}
          and delete_flag = 0
    </select>

</mapper>