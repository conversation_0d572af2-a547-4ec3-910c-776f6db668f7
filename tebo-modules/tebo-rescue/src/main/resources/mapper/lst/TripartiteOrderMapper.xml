<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tebo.rescue.lst.mapper.TripartiteOrderMapper">

    <resultMap id="TripartiteOrderResultMap" type="com.tebo.rescue.lst.domain.entity.TripartiteOrderDo">
        <id column="id" property="id"/>
        <result column="tripartite_no" property="tripartiteNo"/>
        <result column="order_code" property="orderCode"/>
        <result column="sku_code" property="skuCode"/>
        <result column="sku_name" property="skuName"/>
        <result column="qty" property="qty"/>
        <result column="receive_name" property="receiveName"/>
        <result column="receive_number" property="receiveNumber"/>
        <result column="receive_address" property="receiveAddress"/>
        <result column="order_remark" property="orderRemark"/>
        <result column="seller_remark" property="sellerRemark"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="order_status" property="orderStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="updated" property="updated"/>
        <result column="platform" property="platform"/>
        <result column="shop_type" property="shopType"/>
        <result column="channel" property="channel"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="need_sync" property="needSync"/>
        <result column="order_detail_amount" property="orderDetailAmount"/>
        <result column="store_name" property="storeName"/>
        <result column="store_tel" property="storeTel"/>
        <result column="store_full_address" property="storeFullAddress"/>
        <result column="install_id" property="installId"/>
        <result column="store_id" property="storeId"/>
        <result column="servicer_tel" property="servicerTel"/>
        <result column="ver_code_list" property="verCodeList"/>
        <result column="service_item_fee" property="serviceItemFee"/>
    </resultMap>

    <select id="selectByOrderCode" resultMap="TripartiteOrderResultMap" parameterType="String">
        SELECT id,
               tripartite_no,
               order_code,
               sku_code,
               sku_name,
               qty,
               receive_name,
               receive_number,
               receive_address,
               order_remark,
               seller_remark,
               order_amount,
               order_status,
               create_time,
               create_by,
               update_time,
               update_by,
               updated,
               platform,
               shop_type,
               channel,
               delete_flag,
               need_sync,
               order_detail_amount,
               store_name,
               store_tel,
               store_full_address,
               install_id,
               store_id,
               servicer_tel,
               ver_code_list,
               service_item_fee
        FROM tripartite_order
        WHERE order_code = #{orderCode}
          and delete_flag = 0
    </select>


</mapper>
