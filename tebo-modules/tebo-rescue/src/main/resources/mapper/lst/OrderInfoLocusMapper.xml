<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tebo.rescue.lst.mapper.OrderInfoLocusMapper">
    <resultMap id="BaseResultMap" type="com.tebo.rescue.lst.domain.entity.OrderInfoLocusDo">
        <id column="id" property="id"/>
        <result column="order_no" property="orderNo"/>
        <result column="locus_info" property="locusInfo"/>
        <result column="create_time" property="createTime"/>
    </resultMap>
    <sql id="selectAllOrderInfoLocus">
        SELECT id,
               order_no,
               locus_info,
               create_time
        FROM order_info_locus
    </sql>
    <!-- 定义你的 SQL 语句 -->
    <insert id="insertLocus" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO order_info_locus (order_no, locus_info, create_time)
        VALUES (#{orderNo}, #{locusInfo}, #{createTime})
    </insert>
</mapper>
