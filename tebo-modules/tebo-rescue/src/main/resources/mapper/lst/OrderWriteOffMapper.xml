<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tebo.rescue.lst.mapper.OrderWriteOffMapper">

    <resultMap id="OrderWriteOffResultMap" type="com.tebo.rescue.lst.domain.entity.OrderWriteOffDo">
        <id column="id" property="id"/>
        <result column="addressee" property="addressee"/>
        <result column="delivery_address" property="deliveryAddress"/>
        <result column="out_order_no" property="outOrderNo"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_from" property="orderFrom"/>
        <result column="order_date" property="orderDate"/>
        <result column="shop_info" property="shopInfo"/>
        <result column="customer_car_info" property="customerCarInfo"/>
        <result column="service_provider_info" property="serviceProviderInfo"/>
        <result column="order_settlement_amount" property="orderSettlementAmount"/>
        <result column="platform_remarks" property="platformRemarks"/>
        <result column="customer_remarks" property="customerRemarks"/>
        <result column="img_url" property="imgUrl"/>
        <result column="temp_file_paths" property="tempFilePaths"/>
        <result column="write_off" property="writeOff"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="create_user" property="createUser"/>
        <result column="create_date" property="createDate"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_date" property="updateDate"/>
        <result column="store_id" property="storeId"/>
        <result column="receive_tel" property="receiveTel"/>
    </resultMap>


    <insert id="insertWriteOffLog" parameterType="com.tebo.rescue.lst.domain.entity.OrderWriteOffDo">
        INSERT INTO order_write_off
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="addressee != null">addressee,</if>
            <if test="deliveryAddress != null">delivery_address,</if>
            <if test="outOrderNo != null">out_order_no,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="orderFrom != null">order_from,</if>
            <if test="orderDate != null">order_date,</if>
            <if test="shopInfo != null">shop_info,</if>
            <if test="customerCarInfo != null">customer_car_info,</if>
            <if test="serviceProviderInfo != null">service_provider_info,</if>
            <if test="orderSettlementAmount != null">order_settlement_amount,</if>
            <if test="platformRemarks != null">platform_remarks,</if>
            <if test="customerRemarks != null">customer_remarks,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="tempFilePaths != null">temp_file_paths,</if>
            <if test="writeOff != null">write_off,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="createUser != null">create_user,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="storeId != null">store_id,</if>
            <if test="receiveTel != null">receive_tel,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="addressee != null">#{addressee},</if>
            <if test="deliveryAddress != null">#{deliveryAddress},</if>
            <if test="outOrderNo != null">#{outOrderNo},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="orderFrom != null">#{orderFrom},</if>
            <if test="orderDate != null">#{orderDate},</if>
            <if test="shopInfo != null">#{shopInfo},</if>
            <if test="customerCarInfo != null">#{customerCarInfo},</if>
            <if test="serviceProviderInfo != null">#{serviceProviderInfo},</if>
            <if test="orderSettlementAmount != null">#{orderSettlementAmount},</if>
            <if test="platformRemarks != null">#{platformRemarks},</if>
            <if test="customerRemarks != null">#{customerRemarks},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="tempFilePaths != null">#{tempFilePaths},</if>
            <if test="writeOff != null">#{writeOff},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="storeId != null">#{storeId},</if>
            <if test="receiveTel != null">#{receiveTel},</if>
        </trim>
    </insert>


</mapper>
