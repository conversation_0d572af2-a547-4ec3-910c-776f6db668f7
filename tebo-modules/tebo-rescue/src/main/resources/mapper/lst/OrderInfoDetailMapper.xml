<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tebo.rescue.lst.mapper.OrderInfoDetailMapper">
    <resultMap id="BaseResultMap" type="com.tebo.rescue.lst.domain.entity.OrderInfoDetailDo">
        <id column="id" property="id"/>
        <result column="order_no" property="orderNo"/>
        <result column="goods_name" property="goodsName"/>
        <result column="goods_mode" property="goodsMode"/>
        <result column="parent_name" property="parentName"/>
        <result column="qty" property="qty"/>
        <result column="receive_address" property="receiveAddress"/>
        <result column="receive_name" property="receiveName"/>
        <result column="receive_tel" property="receiveTel"/>
        <result column="remark_user" property="remarkUser"/>
        <result column="remark_platform" property="remarkPlatform"/>
        <result column="remark_hang" property="remarkHang"/>
        <result column="reason_hang" property="reasonHang"/>
        <result column="pic_order" property="picOrder"/>
        <result column="pic_finish" property="picFinish"/>
        <result column="pic_comment" property="picComment"/>
        <result column="service_code" property="serviceCode"/>
        <result column="extra_fee" property="extraFee"/>
        <result column="ref_settle_price" property="refSettlePrice"/>
        <result column="car_name" property="carName"/>
    </resultMap>
    <sql id="selectAllOrderInfoDetail">
        SELECT id,
               order_no,
               goods_name,
               goods_mode,
               parent_name,
               qty,
               receive_address,
               receive_name,
               receive_tel,
               remark_user,
               remark_platform,
               remark_hang,
               reason_hang,
               pic_order,
               pic_finish,
               pic_comment,
               service_code,
               extra_fee,
               ref_settle_price,
               car_name
        FROM order_info_detail
    </sql>


    <update id="updateShopInfoDetail" parameterType="com.tebo.rescue.lst.domain.entity.OrderInfoDetailDo">
        UPDATE order_info_detail
        <set>
            <if test="refSettlePrice != null">
                ref_settle_price = #{refSettlePrice},
            </if>
            <if test="picFinish != null">
                pic_finish = #{picFinish},
            </if>
            <if test="reasonHang != null">
                reason_hang = #{reasonHang},
            </if>
            <if test="remarkHang != null">
                remark_hang = #{remarkHang},
            </if>
        </set>
        WHERE order_no = #{orderNo}
    </update>
</mapper>
