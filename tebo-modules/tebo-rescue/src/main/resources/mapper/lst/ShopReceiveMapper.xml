<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.lst.mapper.ShopReceiveMapper">
	<resultMap id="ShopReceiveResultMap" type="com.tebo.rescue.lst.domain.entity.ShopReceiveDo">
		<id column="id" property="id" />
		<result column="shop_code" property="shopCode" />
		<result column="alipay_account" property="alipayAccount" />
		<result column="alipay_name" property="alipayName" />
		<result column="wxpay_account" property="wxpayAccount" />
		<result column="wxpay_name" property="wxpayName" />
		<result column="bank_name" property="bankName" />
		<result column="bank_mobile" property="bankMobile" />
		<result column="bank_account" property="bankAccount" />
		<result column="bank_account_name" property="bankAccountName" />
		<result column="bank_code" property="bankCode" />
		<result column="delete_flag" property="deleteFlag" />
		<result column="create_by" property="createBy" />
		<result column="create_time" property="createTime" />
		<result column="update_by" property="updateBy" />
		<result column="update_time" property="updateTime" />
	</resultMap>


	<insert id="insertShopReceive" parameterType="com.tebo.rescue.lst.domain.entity.ShopReceiveDo">
        INSERT INTO shop_receive
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopCode != null">shop_code,</if>
            <if test="alipayAccount != null">alipay_account,</if>
            <if test="alipayName != null">alipay_name,</if>
            <if test="wxpayAccount != null">wxpay_account,</if>
            <if test="wxpayName != null">wxpay_name,</if>
            <if test="bankName != null">bank_name,</if>
            <if test="bankMobile != null">bank_mobile,</if>
            <if test="bankAccount != null">bank_account,</if>
            <if test="bankAccountName != null">bank_account_name,</if>
            <if test="bankCode != null">bank_code,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="shopCode != null">#{shopCode},</if>
            <if test="alipayAccount != null">#{alipayAccount},</if>
            <if test="alipayName != null">#{alipayName},</if>
            <if test="wxpayAccount != null">#{wxpayAccount},</if>
            <if test="wxpayName != null">#{wxpayName},</if>
            <if test="bankName != null">#{bankName},</if>
            <if test="bankMobile != null">#{bankMobile},</if>
            <if test="bankAccount != null">#{bankAccount},</if>
            <if test="bankAccountName != null">#{bankAccountName},</if>
            <if test="bankCode != null">#{bankCode},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>
</mapper>	