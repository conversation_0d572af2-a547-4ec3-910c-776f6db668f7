<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboCouponSignRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboCouponSignRecordDO">
        <id column="id" property="id" />
        <result column="coupon_code" property="couponCode" />
        <result column="shop_name" property="shopName" />
        <result column="shop_phone" property="shopPhone" />
        <result column="write_off_time" property="writeOffTime" />
        <result column="battery_type" property="batteryType" />
        <result column="battery_model" property="batteryModel" />
        <result column="battery_code" property="batteryCode" />
        <result column="battery_num" property="batteryNum" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, coupon_code, shop_name, shop_phone, write_off_time, battery_type, battery_model, battery_code, battery_num, create_time
    </sql>

    <select id="signRecordList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_coupon_sign_record
        where shop_phone = #{phoneNumber} order by write_off_time desc  limit 20
    </select>
</mapper>
