<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboServiceOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboServiceOrderDO">
        <id column="id" property="id" />
        <result column="account_id" property="accountId" />
        <result column="account_name" property="accountName" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="shop_type" property="shopType" />
        <result column="tenant_id" property="tenantId" />
        <result column="order_no" property="orderNo" />
        <result column="order_type" property="orderType" />
        <result column="service_type" property="serviceType" />
        <result column="order_status" property="orderStatus" />
        <result column="queue_order_id" property="queueOrderId" />
        <result column="queue_order_type" property="queueOrderType" />
        <result column="nick_name" property="nickName" />
        <result column="phone_number" property="phoneNumber" />
        <result column="station_name" property="stationName" />
        <result column="handle_result" property="handleResult" />
        <result column="remark" property="remark" />
        <result column="base_amount" property="baseAmount" />
        <result column="order_amount" property="orderAmount" />
        <result column="actual_pay_amount" property="actualPayAmount" />
        <result column="is_agent_pay" property="isAgentPay" />
        <result column="pay_time" property="payTime" />
        <result column="pay_by" property="payBy" />
        <result column="pay_type" property="payType" />
        <result column="del_flag" property="delFlag" />
        <result column="is_quick_order" property="isQuickOrder" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="cancel_time" property="cancelTime"/>
        <result column="cancel_by" property="cancelBy" />
        <result column="queue_business_type" property="queueBusinessType" />
        <result column="discount_amount" property="discountAmount" />
        <result column="unionid" property="unionid" />
        <result column="depart_time" property="departTime" />
        <result column="arrive_time" property="arriveTime" />
        <result column="open_order_time" property="openOrderTime" />
        <result column="finish_time" property="finishTime" />
        <result column="cancel_reason" property="cancelReason" />
        <result column="change_new" property="changeNew" />
        <result column="category_name" property="categoryName" />

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, account_id, account_name, shop_id, shop_name,shop_type, tenant_id, order_no, order_type,service_type, order_status, queue_order_id, queue_order_type, nick_name, phone_number, station_name,handle_result, remark,base_amount, order_amount, discount_amount, actual_pay_amount, is_agent_pay, pay_time, pay_by, pay_type, is_quick_order ,del_flag, create_by, create_time, update_by, update_time, cancel_time,
          cancel_by,queue_business_type,maintain_process,unionid,depart_time,arrive_time,open_order_time,finish_time,cancel_reason,change_new,category_name
    </sql>
    <select id="list" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from tebo_service_order
        where del_flag = 0
        <if test="accountId!= null ">
            and account_id = #{accountId}
        </if>

        <if test="accountIds != null and accountIds.size() > 0" >
            and account_id in
            <foreach collection="accountIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="shopId!= null ">
            and shop_id = #{shopId}
        </if>
        <if test="orderStatus!= null ">
            and order_status = #{orderStatus}
        </if>
        <if test="isQuickOrder!= null ">
            and is_quick_order = #{isQuickOrder}
        </if>
        <if test="orderStatusList != null and orderStatusList.size() > 0">
            and order_status in
            <foreach collection="orderStatusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queueOrderIdList != null and queueOrderIdList.size() > 0">
            and queue_order_id in
            <foreach collection="queueOrderIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queueOrderId!= null ">
            and queue_order_id = #{queueOrderId}
        </if>

        <if test="orderType != null" >
            and order_type = #{orderType}
        </if>
        <if test="orderTypeList != null and orderTypeList.size() > 0" >
            and order_type in
            <foreach collection="orderTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queueBusinessTypeList != null and queueBusinessTypeList.size() > 0">
            and queue_business_type in
            <foreach collection="queueBusinessTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queueOrderType != null">
              and queue_order_type = #{queueOrderType}
          </if>
        <if test="unionid != null">
            and unionid = #{unionid}
        </if>

        <if test="tenantId != null" >
            and tenant_id = #{tenantId}
        </if>

        <if test="createTimeStartSecond!= null">
            and create_time >= #{createTimeStartSecond ,jdbcType=TIMESTAMP}
        </if>
        <if test="createTimeEndSecond!= null">
            and #{createTimeEndSecond ,jdbcType=TIMESTAMP} > create_time
        </if>

        <if test="finishTimeStartSecond!= null">
            and finish_time >= #{finishTimeStartSecond ,jdbcType=TIMESTAMP}
        </if>
        <if test="finishTimeEndSecond!= null">
            and #{finishTimeEndSecond ,jdbcType=TIMESTAMP} > finish_time
        </if>
        <if test="shopName != null and shopName != ''">
            and shop_name = #{shopName}
        </if>
        <if test="shopNameLike != null and shopNameLike != ''">
            and shop_name like concat('%', #{shopNameLike}, '%')
        </if>
        <if test="shopType != null" >
            and shop_type = #{shopType}
        </if>
        <if test="accountName != null and accountName != ''">
            and account_name = #{accountName}
        </if>
        <if test="accountNameLike != null and accountNameLike != ''">
            and account_name like concat('%', #{accountNameLike}, '%')
        </if>
        <if test="phoneNumber != null and phoneNumber != ''">
            and phone_number = #{phoneNumber}
        </if>
        <if test="phoneNumberLike != null and phoneNumberLike != ''">
            and phone_number like concat('%', #{phoneNumberLike}, '%')
        </if>
        <if test="orderNo != null and orderNo != ''">
            and order_no = #{orderNo}
        </if>
        <if test="orderNoLike != null and orderNoLike != ''">
            and order_no  like concat('%', #{orderNoLike}, '%')
        </if>

        <if test="queueBusinessType != null ">
            and queue_business_type = #{queueBusinessType}
        </if>


        <if test="orderByColumn == null">
            order by create_time desc
        </if>
        <if test="orderByColumn != null and orderByColumn!=''">
            order by #{orderByColumn}
        </if>
    </select>
    <select id="selectByQueueOrderId" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from tebo_service_order
        where
            del_flag = 0
            and queue_order_id = #{queueOrderId}


    </select>
    <select id="selectByOrderNo" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from tebo_service_order
        where
            del_flag = 0
            and order_no = #{orderNo}
    </select>


    <select id="count" resultType="java.lang.Integer">
        select count(1) from tebo_service_order
        where del_flag = 0
        <if test="accountId!= null ">
            and account_id = #{accountId}
        </if>
        <if test="shopId!= null ">
            and shop_id = #{shopId}
        </if>
        <if test="orderStatus!= null ">
            and order_status = #{orderStatus}
        </if>
        <if test="orderStatusList != null and orderStatusList.size() > 0" >
            and order_status in
            <foreach collection="orderStatusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queueOrderIdList != null and queueOrderIdList.size() > 0" >
            and queue_order_id in
            <foreach collection="queueOrderIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="orderType != null" >
            and order_type = #{orderType}
        </if>
        <if test="orderTypeList != null and orderTypeList.size() > 0" >
            and order_type in
            <foreach collection="orderTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queueBusinessTypeList != null and queueBusinessTypeList.size() > 0">
            and queue_business_type in
            <foreach collection="queueBusinessTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queueOrderType != null">
            and queue_order_type = #{queueOrderType}
        </if>
        <if test="unionid != null">
            and unionid = #{unionid}
        </if>

        <if test="tenantId != null" >
            and tenant_id = #{tenantId}
        </if>
        <if test="orderByColumn == null">
            order by create_time desc
        </if>
        <if test="orderByColumn != null and orderByColumn!=''">
            order by #{orderByColumn}
        </if>
    </select>
    <select id="getLstOrderList" resultType="com.tebo.rescue.remote.domain.LstOrderDetailVO">
        SELECT
            id, order_no as orderNo, merchant_store_name as shopName, order_type as orderType, 1 as serviceType, order_status as orderStatus, order_source as orderSource,
            remark_platform as remarkPlatform, hanging_remarks as remarkUser, create_time as createTime, settlement_time as settleDate
        FROM
            tebo_lst.order_info oi
        WHERE
            oi.tebo_shop_id IN (
                SELECT
                    id
                FROM
                    tebo_system_prod.tebo_shop ts
                WHERE
                    ts.shop_code IN
                    (SELECT outside_code FROM bttn_lst.shop_info si
                                         WHERE si.gys_code = #{gysCode}
                                         AND si.outside_code IS NOT NULL)
            )
        order by create_time desc
    </select>
    <select id="selectLstOrderDetailInfo" resultType="com.tebo.rescue.remote.domain.LstOrderDetailInfoVO">
        SELECT
            oi.id,os.product_name as productName, 1 as goodsNumber, oi.merchant_store_name as shopName, ts.shop_code as shopCode, oi.order_status as orderStatus, oi.customer_address as customerAddress, oi.customer_name as customerName, oi.customer_phone as customerPhone,oi.create_time as createTime, oi.settlement_time as settleDate, os.ref_settle_price as orderPrice, oi.hanging_remarks as hangingRemarks, '' as reasonHang
        FROM
            tebo_lst.order_info oi
                LEFT JOIN tebo_lst.order_skus os
                          on oi.id = os.order_id
                LEFT JOIN tebo_system_prod.tebo_shop ts
                          on  oi.tebo_shop_id = ts.id
        where oi.id = #{orderId}
    </select>
</mapper>
