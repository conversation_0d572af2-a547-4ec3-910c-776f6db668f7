<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboCustomerPromoteRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboCustomerPromoteRecordDO">
        <result column="id" property="id" />
        <result column="reference_phone" property="referencePhone" />
        <result column="reference_wallet_id" property="referenceWalletId" />
        <result column="type" property="type" />
        <result column="union_id" property="unionId" />
        <result column="promoted_phone" property="promotedPhone" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, reference_phone, reference_wallet_id, `type`, union_id, promoted_phone, create_time, update_time
    </sql>

    <select id="selectByUnionId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_customer_promote_record
        where union_id = #{unionId} limit 1
    </select>
</mapper>
