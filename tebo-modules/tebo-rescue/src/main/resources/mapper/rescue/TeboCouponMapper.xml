<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboCouponMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboCouponDO">
        <id column="id" property="id" />
        <id column="single" property="single" />
        <id column="in_pack" property="inPack" />
        <result column="coupon_code" property="couponCode" />
        <result column="coupon_name" property="couponName" />
        <result column="vest" property="vest" />
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="type" property="type" />
        <result column="area" property="area" />
        <result column="area_code" property="areaCode" />
        <result column="area_name" property="areaName" />
        <result column="publish_num" property="publishNum" />
        <result column="use_num" property="useNum" />
        <result column="residue_num" property="residueNum" />
        <result column="par_value" property="parValue" />
        <result column="par_receive" property="parReceive" />
        <result column="receive_type" property="receiveType" />
        <result column="use_type" property="useType" />
        <result column="use_reduce" property="useReduce" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="status" property="status" />
        <result column="good_limit" property="goodLimit" />
        <result column="type_id" property="typeId" />
        <result column="use_shop" property="useShop" />
        <result column="desc" property="desc" />
        <result column="main_image" property="mainImage" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, main_image, single, in_pack, coupon_code, coupon_name, vest, tenant_id, tenant_name, `type`, area, area_code,area_name, publish_num, use_num, residue_num, par_value, par_receive, receive_type, use_type, use_reduce, start_time, end_time, `status`, good_limit, type_id, use_shop, `desc`, remark, create_by, create_time, update_by, update_time, del_flag
    </sql>
    <update id="batchUpdateCouponStatus">
        update tebo_coupon
        <set>
            <if test="status != null">
                `status` = #{status},
            </if>
        </set>
        where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="getCouponList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tebo_coupon
        where del_flag = 0
        <if test="couponName!= null and couponName!= ''">
            and coupon_name like concat('%', #{couponName}, '%')
        </if>
        <if test="type != null and type != ''">
            and `type` = #{type}
        </if>
        <if test="status != null and status != ''">
            and status = #{status}
        </if>
        <if test="inPack == 0">
            and in_pack = #{inPack}
        </if>
        <if test="single != null and single != ''">
            and single = #{single}
        </if>
        <if test="startTime != null">
            and start_time between #{startTime} and #{startTimeEnd}
        </if>
        <if test="endTime != null">
            and end_time between #{endTime} and #{endTimeEnd}
        </if>
        order by create_time desc
    </select>
    <select id="getCouponListByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tebo_coupon
        where del_flag = 0
        and id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>
