<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboReissueCycleOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboReissueCycleOrderDO">
        <result column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, create_time, update_time
    </sql>

    <select id="getReissueCouponOrderNo" resultType="String">
        SELECT i.coupon_order_no
        FROM `tebo_mall_prod`.tebo_cyclying_insurance_order i
        LEFT JOIN `tebo_rescue_prod`.tebo_gift_pack_order o ON i.coupon_order_no = o.order_no
        LEFT JOIN `tebo_rescue_prod`.tebo_group_purchase_order g ON  o.group_order_id = g.id
        LEFT JOIN tebo_pay.pay_delay_trans_confirm_log l ON l.order_no = i.coupon_order_no
        AND l.amount  = 3200
        WHERE i.order_status = 3 AND o.order_type = 1 AND l.update_time IS null
        and DATE(i.create_time) = CURDATE()
    </select>
</mapper>
