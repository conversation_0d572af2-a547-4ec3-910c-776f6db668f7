<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboOrderFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboOrderFileDO">
        <id column="id" property="id" />
        <result column="file_url" property="fileUrl" />
        <result column="order_id" property="orderId" />
        <result column="order_table" property="orderTable" />
        <result column="del_flag" property="delFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="cancel_time" property="cancelTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, file_url,  order_id, order_table, del_flag, create_by, create_time, cancel_time, update_by, update_time
    </sql>

    <insert id="insertBatch">
        insert into tebo_order_file ( id, file_url, order_id, order_table, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.fileUrl}, #{item.orderId}, #{item.orderTable}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

</mapper>
