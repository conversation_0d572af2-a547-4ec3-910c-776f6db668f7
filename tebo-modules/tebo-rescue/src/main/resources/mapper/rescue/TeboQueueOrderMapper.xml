<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboQueueOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboQueueOrderDO">
        <id column="id" property="id" />
        <result column="unionid" property="unionid" />
        <result column="nick_name" property="nickName" />
        <result column="phone_number" property="phoneNumber" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="tenant_id" property="tenantId" />
        <result column="order_no" property="orderNo" />
        <result column="account_id" property="accountId" />
        <result column="account_name" property="accountName" />
        <result column="station_name" property="stationName" />
        <result column="order_type" property="orderType" />
        <result column="order_status" property="orderStatus" />
        <result column="pickup_type" property="pickupType" />
        <result column="handle_result" property="handleResult" />
        <result column="queue_number" property="queueNumber" />
        <result column="queue_number_str" property="queueNumberStr" />
        <result column="total_comment" property="totalComment" />
        <result column="service_comment" property="serviceComment" />
        <result column="image_comment" property="imageComment" />
        <result column="competence_comment" property="competenceComment" />
        <result column="attitude_comment" property="attitudeComment" />
        <result column="comment_finished" property="commentFinished" />
        <result column="remark" property="remark" />
        <result column="order_amount" property="orderAmount" />
        <result column="call_time" property="callTime" />
        <result column="cancel_time" property="cancelTime" />
        <result column="pay_time" property="payTime" />
        <result column="comment_time" property="commentTime" />
        <result column="del_flag" property="delFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <resultMap id="orderStatusNumResultMap" type="com.tebo.rescue.applet.domain.view.QueueOrderStatusCountVO">
        <result column="order_status" property="orderStatus" />
        <result column="num" property="num" />
    </resultMap>

    <resultMap id="shopIdNumResultMap" type="com.tebo.rescue.applet.domain.view.ShopCustomerVO">
        <result column="shop_id" property="shopId" />
        <result column="num" property="num" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, unionid, nick_name, phone_number,shop_id, shop_name, tenant_id, order_no, account_id, account_name, station_name, order_type, order_status,pickup_type,handle_result, queue_number,queue_number_str, total_comment, service_comment, image_comment, competence_comment, attitude_comment,comment_finished, remark, order_amount, call_time, cancel_time, pay_time, comment_time, del_flag, create_by, create_time, update_by, update_time
    </sql>
    <update id="batchCancel">
        update tebo_queue_order set order_status = 4 where id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tebo_queue_order
        where del_flag = 0
        <if test="unionid!= null and unionid!= ''">
            and unionid = #{unionid}
        </if>
        <if test="shopId!=null">
            and shop_id = #{shopId}
        </if>
        <if test="tenantId != null">
            and tenant_id = #{tenantId}
        </if>
        <if test="orderStatus!= null">
            and order_status = #{orderStatus}
        </if>
        <if test="orderStatusList!= null and orderStatusList.size() > 0">
            and order_status in
            <foreach collection="orderStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="orderType!= null">
            and order_type = #{orderType}
        </if>
        <if test="accountId != null">
            and account_id = #{accountId}
        </if>
        <if test="accountIds != null and accountIds.size() > 0" >
            and account_id in
            <foreach collection="accountIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="serviceOrderType != null and serviceOrderType == 0">
            and order_type in (0,1,2)
        </if>
        <if test="serviceOrderType != null and serviceOrderType == 1">
            and order_type = 3
        </if>
        <if test="commentFinished != null">
            and comment_finished = #{commentFinished}
        </if>
        <if test="pickupType != null">
            and pickup_type = #{pickupType}
        </if>
        <if test="keyword != null and keyword != ''">
            and (shop_name like concat('%', #{keyword}, '%')
            or nick_name like concat('%', #{keyword}, '%')
            or phone_number like concat('%', #{keyword}, '%')
            or order_no like concat('%', #{keyword}, '%')
            or account_name like concat('%', #{keyword}, '%')
            )
        </if>
        <if test="createTimeStartSecond != null ">
            and create_time >= #{createTimeStartSecond,jdbcType=TIMESTAMP}
        </if>
        <if test="createTimeEndSecond != null ">
            and #{createTimeEndSecond ,jdbcType=TIMESTAMP} > create_time
        </if>
        <if test="orderByColumn == null ">
            order by create_time desc
        </if>
        <if test="orderByColumn != null and orderByColumn!=''">
            order by #{orderByColumn}
        </if>

    </select>
    <select id="selectRank" resultType="java.lang.Integer">
        select count(1) from tebo_queue_order
            where
        del_flag = 0
        AND shop_id = #{shopId}
        and queue_number &lt; #{queueNumber}
        and order_status in
            <foreach collection="orderStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        <if test="orderType != null and (orderType == 0 or orderType == 1 or orderType == 2)">
            and order_type in (0,1,2)
        </if>
        <if test="orderType!= null and orderType == 3">
            and order_type = 3
        </if>
    </select>
    <select id="selectNextQueueId" resultType="java.lang.Long">
        select id from tebo_queue_order
        where
        del_flag = 0
        <if test="orderType != null and (orderType == 0 or orderType == 1 or orderType == 2)">
            and order_type in (0,1,2)
        </if>
            <if test="orderType!= null and orderType == 3">
            and order_type = 3
        </if>
        and shop_id = #{shopId}
        and queue_number &gt; #{queueNumber}
        and order_status = 0
        order by queue_number asc limit 1
    </select>
    <select id="listUnCallingbeforeToday" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from tebo_queue_order
            where del_flag = 0
            and order_status in (0,1)
            and create_time &lt; #{maxTime}
    </select>
    <select id="orderStatusCount" resultMap="orderStatusNumResultMap">
        select order_status, count(1) as num from tebo_queue_order
            where del_flag = 0
            and unionid = #{unionid}
            <if test="orderStatus!= null">
                and order_status = #{orderStatus}
            </if>
            <if test="orderStatusList!= null and orderStatusList.size() > 0">
                and order_status in
                <foreach collection="orderStatusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="commentFinished != null" >
                and comment_finished = #{commentFinished}
            </if>
            group by order_status
    </select>
    <select id="getShopCustomerNum" resultMap="shopIdNumResultMap">
        select shop_id,count(distinct unionid) as num
        from tebo_queue_order
        where del_flag = 0
        <if test="shopIdList != null and shopIdList.size() > 0">
            and shop_id in
            <foreach collection="shopIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by shop_id
    </select>

</mapper>
