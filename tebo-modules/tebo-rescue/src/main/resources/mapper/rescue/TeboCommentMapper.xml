<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboCommentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboCommentDO">
        <id column="id" property="id" />
        <result column="commented_id" property="commentedId" />
        <result column="commented_name" property="commentedName" />
        <result column="order_id" property="orderId" />
        <result column="order_business_type" property="orderBusinessType" />
        <result column="total_comment" property="totalComment" />
        <result column="service_comment" property="serviceComment" />
        <result column="competence_comment" property="competenceComment" />
        <result column="attitude_comment" property="attitudeComment" />
        <result column="image_comment" property="imageComment" />
        <result column="quantity_comment" property="quantityComment" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, commented_id, commented_name, order_id, order_business_type, total_comment, service_comment, competence_comment, attitude_comment, image_comment, quantity_comment, remark, create_by, create_time, update_by, update_time, del_flag
    </sql>

    <select id="getByOrderId" resultMap="BaseResultMap">
        select  <include refid="Base_Column_List"/> from tebo_comment
        where del_flag = 0
        <if test="orderId != null">
            and order_id = #{orderId}
        </if>
    </select>

</mapper>
