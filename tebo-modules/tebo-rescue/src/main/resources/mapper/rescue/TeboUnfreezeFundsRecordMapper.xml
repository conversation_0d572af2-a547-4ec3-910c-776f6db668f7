<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboUnfreezeFundsRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboUnfreezeFundsRecordDO">
        <result column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="type" property="type" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no,type, create_time, update_time
    </sql>

    <select id="getByOrderNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_unfreeze_funds_record
        <where>
            <if test="orderNo!= null">
                and order_no = #{orderNo}
            </if>
            <if test="type!= null">
                and type = #{type}
            </if>
        </where>
    </select>
</mapper>
