<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboCouponSkuMappingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboCouponSkuMappingDO">
        <id column="id" property="id" />
        <result column="coupon_id" property="couponId" />
        <result column="sku_id" property="skuId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, coupon_id, sku_id
    </sql>

    <select id="getList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_coupon_sku_mapping
    </select>
</mapper>
