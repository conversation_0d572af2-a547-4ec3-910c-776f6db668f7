<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboRescueMsgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboRescueMsgDO">
        <result column="id" property="id" />
        <result column="type" property="type" />
        <result column="tenant_id" property="tenantId" />
        <result column="order_id" property="orderId" />
        <result column="reader" property="reader" />
        <result column="msg" property="msg" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, `type`, tenant_id,order_id, `reader`, msg, create_time, update_time
    </sql>

    <select id="getMsgList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_rescue_msg
        <where>
            <if test="tenantId!= null">
                 tenant_id = #{tenantId}
            </if>
            <if test="reader != null">
                and reader = #{reader}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>
