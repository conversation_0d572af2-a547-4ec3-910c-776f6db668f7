<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboGiftPackOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboGiftPackOrderDO">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="pack_id" property="packId" />
        <result column="union_id" property="unionId" />
        <result column="type" property="type" />
        <result column="order_type" property="orderType" />
        <result column="order_status" property="orderStatus" />
        <result column="district" property="district" />
        <result column="reference_phone" property="referencePhone" />
        <result column="order_amount" property="orderAmount" />
        <result column="pay_time" property="payTime" />
        <result column="del_flag" property="delFlag" />
        <result column="verification_status" property="verificationStatus" />
        <result column="verification_time" property="verificationTime" />
        <result column="verification_shop_id" property="verificationShopId" />
        <result column="verification_tenant_id" property="verificationTenantId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="refund_time" property="refundTime" />
        <result column="rice_verification_status" property="riceVerificationStatus" />
        <result column="cycle_verification_status" property="cycleVerificationStatus" />
        <result column="battery_verification_status" property="batteryVerificationStatus" />
        <result column="rice_verification_time" property="riceVerificationTime" />
        <result column="battery_verification_time" property="batteryVerificationTime" />
        <result column="cycle_verification_time" property="cycleVerificationTime" />
        <result column="group_customer_order_id" property="groupCustomerOrderId" />
        <result column="group_order_id" property="groupOrderId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, reference_phone,group_customer_order_id,group_order_id,type,order_no,order_type, pack_id, union_id, order_status, order_amount,district, pay_time, rice_verification_status,cycle_verification_status,battery_verification_status,
      del_flag, create_time, update_time,verification_time,verification_status,reference_wallet_id,verification_shop_id,verification_tenant_id,rice_verification_time,battery_verification_time,cycle_verification_time,refund_time
    </sql>
    <update id="cancelExpiredPackOrder">
        update tebo_gift_pack_order set order_status = 2
        where order_status = 0
        and create_time &lt; #{maxTime}
        and del_flag = 0
    </update>
    <select id="selectByOrderNo" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from tebo_gift_pack_order
        where order_no = #{orderNo}
        and del_flag = 0
    </select>
    <select id="count" resultType="java.lang.Integer">
        select count(1) from tebo_gift_pack_order
            where del_flag = 0
        <if test="orderStatus!= null">
            and order_status = #{orderStatus}
        </if>
        <if test="unionid != null">
            and union_id = #{unionid}
        </if>
        <if test="packId!= null">
            and pack_id = #{packId}
        </if>
    </select>

    <select id="getTeboGiftPackOrder" resultType="java.lang.Integer">
        select count(*) from tebo_gift_pack_order
        where del_flag = 0 and MONTH(create_time) = MONTH(CURRENT_DATE())
        AND YEAR(create_time) = YEAR(CURRENT_DATE())
        <if test="orderStatus!= null">
            and order_status = #{orderStatus}
        </if>
        <if test="unionid != null">
            and union_id = #{unionid}
        </if>
        <if test="packId!= null">
            and pack_id = #{packId}
        </if>
        <if test="orderType!= null and orderType != '' ">
            and order_type = #{orderType}
        </if>
    </select>


    <select id="selectByUnionId" resultType="com.tebo.rescue.entity.TeboGiftPackOrderDO">
        select
            a.*
        from tebo_gift_pack_order a
        left join tebo_gift_pack b on b.id = a.pack_id
        where a.union_id = #{unionId}
        and b.type = 3
        and a.order_status = 1
    </select>

    <select id="selectByUnionIdAndStatus" resultType="com.tebo.rescue.entity.TeboGiftPackOrderDO">
        select
            a.*
        from tebo_gift_pack_order a
                 left join tebo_gift_pack b on b.id = a.pack_id
        where a.union_id = #{unionId}
          and b.type = 3
          and a.order_status in(1, 3)
        order by a.create_time desc
    </select>

    <select id="selectByReferencePhone" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_gift_pack_order
        where order_status != 3  and  type in
        <foreach collection="typeList" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
          and
        reference_phone in
        <foreach collection="referencePhoneList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by create_time desc
    </select>

    <select id="getOldCouponSign" resultType="String">
        select distinct union_id from tebo_coupon_customer
        where status = 2
    </select>

    <select id="getNewCouponSign" resultType="String">
        select union_id from tebo_gift_pack_order
        where order_status = 1 and verification_status = 1
        and DATE(verification_time) = CURDATE()
        AND verification_time > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
        order by verification_time desc
    </select>
</mapper>
