<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboGroupPurchaseCustomerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboGroupPurchaseCustomerDO">
        <result column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="phone_number" property="phoneNumber" />
        <result column="status" property="status" />
        <result column="del_flag" property="delFlag" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id,status,phone_number, del_flag, create_time, update_time
    </sql>

    <select id="getOrder" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_group_purchase_customer
        <where>
            del_flag = 0
            <if test="orderId!= null">
                and order_id = #{orderId}
            </if>
            <if test="status!= null">
                and status = #{status}
            </if>
            <if test="phoneNumber!= null">
                and phone_number = #{phoneNumber}
            </if>
        </where>
        order by create_time asc
    </select>
</mapper>
