<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboShopRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboShopRelationDO">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="shop_code" property="shopCode" />
        <result column="shop_name" property="shopName" />
        <result column="phone_number" property="phoneNumber" />
        <result column="boss_name" property="bossName" />
        <result column="upper_name" property="upperName" />
        <result column="low_status" property="lowStatus" />
        <result column="senior_status" property="seniorStatus" />
        <result column="low_id" property="lowId" />
        <result column="senior_id" property="seniorId" />
        <result column="upper_code" property="upperCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="apply_time" property="applyTime" />
        <result column="cloud_shop_id" property="cloudShopId" />
        <result column="cloud_shop_phone" property="cloudShopPhone" />
        <result column="cloud_shop_name" property="cloudShopName" />
        <result column="cloud_shop_boss" property="cloudShopBoss" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, shop_code, shop_name, phone_number, boss_name, upper_name, upper_code, create_time, update_time, apply_time,
          low_status, senior_status, low_id, senior_id,cloud_shop_id, cloud_shop_phone, cloud_shop_name, cloud_shop_boss
    </sql>

    <insert id="insertBatch">
        INSERT INTO `tebo_group_purchase_customer` (`order_id`, `status`, `boss_name`, `phone_number`, `shop_name`, `upper_name`, `upper_code`, `cloud_shop_id`, `cloud_shop_phone`, `cloud_shop_name`, `cloud_shop_boss`)
        values
            <foreach collection="shopList" item="item" separator=",">
                (#{orderId},0,#{item.bossName}, #{item.cloudShopPhone},#{item.shopName},#{item.upperName},#{item.upperCode},#{item.cloudShopId},#{item.cloudShopPhone},#{item.cloudShopName},#{item.cloudShopBoss})
            </foreach>
    </insert>
    <select id="getList" resultType="com.tebo.rescue.applet.domain.view.TeboShopRelationVO">
        select
        <include refid="Base_Column_List"></include>
        from tebo_shop_relation
        where upper_code IN
            <foreach collection="upperCodeList" item="upperCode" open="(" separator="," close=")">
                #{upperCode}
            </foreach>
            <if test="searchText != '' and searchText != null ">
                and (cloud_shop_name like concat('%',#{searchText},'%') or cloud_shop_phone like concat('%',#{searchText},'%') or cloud_shop_boss like concat('%',#{searchText},'%'))
            </if>
    </select>
    <select id="orderInfo" resultType="com.tebo.rescue.applet.domain.view.TeboShopRelationDetailVO">
        SELECT
            (SELECT COUNT(id) FROM tebo_group_purchase_customer WHERE order_id = #{orderId} AND del_flag = 0) AS 'use',
            (SELECT COUNT(id) FROM tebo_gift_pack_order WHERE group_order_id = #{orderId}) AS 'active',
            (SELECT COUNT(1) FROM tebo_gift_pack_order WHERE group_order_id = #{orderId} AND verification_status = 1) AS 'apply'
    </select>
    <select id="selectDetailByOrderId" resultType="java.util.Map">
        select tgpc.phone_number, tgpo.create_time, tgpo.verification_status
        from tebo_gift_pack_order tgpo
                 LEFT JOIN tebo_group_purchase_customer tgpc
                           on tgpo.group_customer_order_id = tgpc.id
        where tgpo.group_order_id = #{orderId}
    </select>
    <select id="getListNew" resultType="com.tebo.rescue.applet.domain.view.TeboShopRelationVO">
        select
--         tsr.id, tsr.shop_id, tsr.shop_code, tsr.shop_name, tsr.phone_number, tsr.boss_name, tsr.upper_name, tsr.upper_code, tsr.create_time, tsr.update_time, tgpc.create_time as apply_time,
--         tsr.cloud_shop_id, tsr.cloud_shop_phone, tsr.cloud_shop_name, tsr.cloud_shop_boss
        tgpc.*
        FROM
        tebo_group_purchase_customer tgpc
--         LEFT JOIN tebo_shop_relation tsr
--         on tgpc.phone_number = tsr.cloud_shop_phone
<!--        and tsr.upper_code IN-->
<!--        <foreach collection="upperCodeList" item="upperCode" open="(" separator="," close=")">-->
<!--            #{upperCode}-->
<!--        </foreach>-->
        WHERE
        tgpc.order_id = #{orderId}
        and tgpc.del_flag = 0
        <if test="searchText != '' and searchText != null ">
            and (tgpc.cloud_shop_name like concat('%',#{searchText},'%') or tgpc.cloud_shop_phone like concat('%',#{searchText},'%') or tgpc.cloud_shop_boss like concat('%',#{searchText},'%'))
        </if>
        order by tgpc.cloud_shop_id, tgpc.create_time desc
    </select>

</mapper>
