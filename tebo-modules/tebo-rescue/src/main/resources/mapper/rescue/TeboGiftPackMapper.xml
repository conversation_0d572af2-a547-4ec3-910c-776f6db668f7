<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboGiftPackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboGiftPackDO">
        <id column="id" property="id" />
        <result column="pack_code" property="packCode" />
        <result column="pack_name" property="packName" />
        <result column="vest" property="vest" />
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="type" property="type" />
        <result column="month_num" property="monthNum" />
        <result column="area" property="area" />
        <result column="publish_num" property="publishNum" />
        <result column="use_num" property="useNum" />
        <result column="residue_num" property="residueNum" />
        <result column="par_value" property="parValue" />
        <result column="price" property="price" />
        <result column="pay_type" property="payType" />
        <result column="buy_limit" property="buyLimit" />
        <result column="buy_limit_num" property="buyLimitNum" />
        <result column="status" property="status" />
        <result column="main_image" property="mainImage" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, main_image, pack_code, pack_name, vest, tenant_id, tenant_name, `type`, month_num, area, publish_num, use_num, residue_num, par_value, price, pay_type, buy_limit, buy_limit_num, `status`, remark, create_by, create_time, update_by, update_time, del_flag
    </sql>
    <select id="getGiftPackList" resultType="com.tebo.rescue.entity.TeboGiftPackDO">
        select
        <include refid="Base_Column_List" />
        from tebo_gift_pack
        where del_flag = 0
        <if test="packName!= null and packName!= ''">
            and pack_name like concat('%', #{packName}, '%')
        </if>
        <if test="vest!= null and vest!= ''">
            and vest = #{vest}
        </if>
        <if test="type!= null and type!= ''">
            and type = #{type}
        </if>
        <if test="tenantId!= null and tenantId == '0'">
            and tenant_id = 0
        </if>
        <if test="tenantId!= null and tenantId != '0'">
            and tenant_id IN (0, #{tenantId})
        </if>
        <if test="status!= null and status!= ''">
            and status = #{status}
        </if>
        <if test="areaCode!= null and areaCode!= ''">
            and (area_code = #{areaCode} or area_code = '全国')
        </if>
        order by create_time desc
    </select>

</mapper>
