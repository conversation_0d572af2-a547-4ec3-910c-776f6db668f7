<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboCouponCustomerRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboCouponCustomerRecordDO">
        <id column="id" property="id" />
        <id column="unique_code" property="uniqueCode" />
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="coupon_id" property="couponId" />
        <result column="single" property="single" />
        <result column="coupon_name" property="couponName" />
        <result column="coupon_code" property="couponCode" />
        <result column="union_id" property="unionId" />
        <result column="customer_name" property="customerName" />
        <result column="phone" property="phone" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="status" property="status" />
        <result column="receive_time" property="receiveTime" />
        <result column="use_time" property="useTime" />
        <result column="order_code" property="orderCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, tenant_name, coupon_id, single, coupon_name, coupon_code, union_id, customer_name, phone, shop_id, shop_name, `status`, receive_time, use_time, order_code
    </sql>
    <update id="batchUpdateByUniqueCode">
        update tebo_coupon_customer_record
        <set>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="shopId != null">
                `shop_id` = #{shopId},
            </if>
            <if test="shopName != null">
                `shop_name` = #{shopName},
            </if>
            <if test="useTime != null">
                `use_time` = #{useTime},
            </if>
            <if test="orderCode != null">
                `order_code` = #{orderCode},
            </if>
        </set>
        where unique_code in
        <foreach collection="uniqueCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="batchExpireCouponRecord">
        update tebo_coupon_customer_record
        set status = 3
        where status = 2
        and coupon_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <delete id="batchDelCouponRecord">
        delete from tebo_coupon_customer_record
        where coupon_id = #{couponId}
        and status != 3
    </delete>
    <select id="getCouponCustomerList" resultType="com.tebo.rescue.entity.TeboCouponCustomerRecordDO">
        select
        <include refid="Base_Column_List"/>
        from tebo_coupon_customer_record
        <where>
            <if test="id != null">
                and coupon_id = #{id}
            </if>
            <if test="status == 99">
                and status != 10
            </if>
            <if test="status != 99">
                and status = #{status}
            </if>
        </where>

    </select>

</mapper>
