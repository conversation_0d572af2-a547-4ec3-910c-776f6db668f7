<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboGroupPurchaseOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboGroupPurchaseOrderDO">
        <result column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="goods_name" property="goodsName" />
        <result column="shop_id" property="shopId" />
        <result column="type" property="type" />
        <result column="unit_integral_price" property="unitIntegralPrice" />
        <result column="total_integral" property="totalIntegral" />
        <result column="purchaser_type" property="purchaserType" />
        <result column="purchaser_name" property="purchaserName" />
        <result column="unit_price" property="unitPrice" />
        <result column="purchase_quantity" property="purchaseQuantity" />
        <result column="full_delivery_quantity" property="fullDeliveryQuantity" />
        <result column="contact" property="contact" />
        <result column="phone_number" property="phoneNumber" />
        <result column="total_goods_number" property="totalGoodsNumber" />
        <result column="order_amount" property="orderAmount" />
        <result column="active_qr_code" property="activeQrCode" />
        <result column="order_status" property="orderStatus" />
        <result column="active_status" property="activeStatus" />
        <result column="del_flag" property="delFlag" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="pay_time" property="payTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no,active_qr_code,type,shop_id, unit_integral_price,total_integral,goods_name, purchaser_type, purchaser_name,unit_price,purchase_quantity, full_delivery_quantity,contact, phone_number, total_goods_number, order_amount, order_status, active_status,create_time, update_time,pay_time
    </sql>

    <select id="getGroupOrderList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_group_purchase_order
        <where>
            del_flag = 0
            <if test="orderNo!= null and orderNo !='' ">
                and order_no = #{orderNo}
            </if>
            <if test="shopId!= null and shopId !='' ">
                and shop_id = #{shopId}
            </if>
            <if test="purchaserType!= null">
                and purchaser_type = #{purchaserType}
            </if>
            <if test="purchaserName!= null and purchaserName != ''">
                and purchaser_name = #{purchaserName}
            </if>
            <if test="contact!= null and contact != '' ">
                and contact like concat('%', #{contact}, '%')
            </if>
            <if test="phoneNumber!= null and phoneNumber != ''">
                and phone_number like concat('%', #{phoneNumber}, '%')
            </if>
            <if test="orderStatus!= null">
                and order_status = #{orderStatus}
            </if>
            <if test="activeStatus!= null">
                and active_status = #{activeStatus}
            </if>
            <if test="createStartTimeSecond!= null">
                and create_time >= #{createStartTimeSecond}
            </if>
            <if test="createEndTimeSecond!= null">
                and #{createEndTimeSecond} > create_time
            </if>
            <if test="orderStatusList!= null">
                and order_status in
                <foreach collection="orderStatusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        </where>
        order by create_time desc
    </select>

    <select id="selectByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tebo_group_purchase_order
        where order_no = #{orderNo}
        and del_flag = 0
    </select>

    <select id="getPayTimeOutOrder" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tebo_group_purchase_order
        where del_flag = 0
        and order_status = 1
        and create_time &lt; #{time}
    </select>

    <select id="selectOrderForSettle" resultType="com.tebo.rescue.entity.TeboGroupPurchaseOrderDO">
        select *
        from tebo_group_purchase_order
        where settle_status = 1
          and type = #{type}
          and order_status in (2, 3)
          and settle_status = 1
    </select>
    <select id="selectCountByOrderId" resultType="java.lang.Integer">
        select count(1)
        from tebo_gift_pack_order
        where group_order_id = #{orderId}
          and del_flag = 0
    </select>

    <select id="selectOrderAmountByOrderNo" resultType="java.lang.Integer">
        select amount-fee_amount as orderAmount from tebo_pay.pay_trade_split
        where order_no = #{orderNo}
    </select>

    <insert id="insertConfigLog">
        insert into tebo_pay.pay_delay_trans_confirm_log (id, order_no, wallet_id, amount, remark, status, create_time, update_time)
        values (#{id}, #{orderNo}, #{walletId}, #{amount}, #{remark}, #{status}, now(), now())
    </insert>
</mapper>
