<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboCouponShopMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboCouponShopDO">
        <id column="id" property="id" />
        <result column="coupon_id" property="couponId" />
        <result column="shop_id" property="shopId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, coupon_id, shop_id
    </sql>
    <delete id="deleteByCouponId">
        delete
        from tebo_coupon_shop
        where coupon_id = #{couponId}
    </delete>
    <select id="selectByCouponId" resultType="com.tebo.rescue.entity.TeboCouponShopDO">
        select
        <include refid="Base_Column_List" />
        from tebo_coupon_shop
        where coupon_id = #{couponId}
    </select>

    <insert id="batchInsert" >
        insert into tebo_coupon_shop (id, coupon_id, shop_id)
        values
        <foreach collection="list" item="coupon" index="index" separator=",">
            (#{coupon.id}, #{coupon.couponId}, #{coupon.shopId})
        </foreach>
    </insert>

</mapper>
