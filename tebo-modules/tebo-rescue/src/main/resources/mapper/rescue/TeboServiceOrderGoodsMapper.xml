<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboServiceOrderGoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboServiceOrderGoodsDO">
        <id column="id" property="id" />
        <result column="account_id" property="accountId" />
        <result column="shop_id" property="shopId" />
        <result column="service_order_id" property="serviceOrderId" />
        <result column="type" property="type" />
        <result column="goods_id" property="goodsId" />
        <result column="goods_name" property="goodsName" />
        <result column="brand_name" property="brandName" />
        <result column="sku" property="sku" />
        <result column="goods_price" property="goodsPrice" />
        <result column="goods_num" property="goodsNum" />
        <result column="goods_amount" property="goodsAmount" />
        <result column="color" property="color" />
        <result column="remark" property="remark" />
        <result column="del_flag" property="delFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="trade_in_price" property="tradeInPrice" />
    </resultMap>

    <resultMap id="TeboOrderGoodsIdGroup" type="com.tebo.rescue.lst.domain.vo.TeboOrderGoodsIdGroup">
        <result column="goods_id" property="goodsId" />
        <result column="goods_name" property="goodsName" />
        <result column="allCount" property="allCount" />
        <result column="allAmount" property="allAmount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, account_id, shop_id, service_order_id, `type`, goods_id, goods_name, brand_name, sku, goods_price, goods_num,goods_amount, color, remark, del_flag, create_by, create_time, update_by, update_time,trade_in_price
    </sql>
    <insert id="batchInsert">
        insert into tebo_service_order_goods (
            id, account_id, shop_id, service_order_id, `type`, goods_id, goods_name, brand_name, sku, goods_price, goods_num, goods_amount,color, remark, del_flag, create_by, create_time, update_by, update_time,trade_in_price
        )
        values
            <foreach collection="list" item="item" separator=",">
                (
                    #{item.id},
                    #{item.accountId},
                    #{item.shopId},
                    #{item.serviceOrderId},
                    #{item.type},
                    #{item.goodsId},
                    #{item.goodsName},
                    #{item.brandName},
                    #{item.sku},
                    #{item.goodsPrice},
                    #{item.goodsNum},
                    #{item.goodsAmount},
                    #{item.color},
                    #{item.remark},
                    #{item.delFlag},
                    #{item.createBy},
                    #{item.createTime},
                    #{item.updateBy},
                    #{item.updateTime},
                    #{item.tradeInPrice}
                )
            </foreach>
    </insert>
    <select id="list" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            tebo_service_order_goods
        where
            del_flag = 0
        <if test="serviceOrderId!= null">
            and service_order_id = #{serviceOrderId}
        </if>

    </select>


    <select id="getOrderGoodsIdGroup" resultMap="TeboOrderGoodsIdGroup">

        SELECT goods_id ,goods_name,sum(goods_num) allCount,sum(goods_amount) allAmount from tebo_service_order_goods
        where service_order_id in
        <foreach collection="serviceOrderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        GROUP BY goods_id;

    </select>

</mapper>
