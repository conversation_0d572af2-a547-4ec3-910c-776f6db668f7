<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboPromoteRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboPromoteRecordDO">
        <result column="id" property="id" />
        <result column="reference_phone" property="referencePhone" />
        <result column="channel" property="channel" />
        <result column="partner_code" property="partnerCode" />
        <result column="reference_wallet_id" property="referenceWalletId" />
        <result column="promoted_phone" property="promotedPhone" />
        <result column="promoted_name" property="promotedName" />
        <result column="union_id" property="unionId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, reference_phone,partner_code,channel,reference_wallet_id, promoted_phone,promoted_name, union_id, create_time
    </sql>

    <select id="getPromoteRecord" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_promote_record
        <where>
            <if test="promotedPhone!= null and promotedPhone!= ''">
                and promoted_phone = #{promotedPhone}
            </if>
            <if test="unionId!= null and unionId!= ''">
                and unionId = #{unionId}
            </if>
            <if test="channel!= null and channel!= ''">
                and channel = #{channel}
            </if>
        </where>
        limit 1
    </select>

    <select id="selectByUnionId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_promote_record
        where union_id = #{unionId} limit 1
    </select>

    <select id="getPromotedPersonList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_promote_record
       <where>
           <if test="referencePhone!= null and referencePhone!= ''">
               and reference_phone = #{referencePhone}
           </if>
           <if test="gysCode!= null and gysCode!= ''">
               and partner_code = #{gysCode}
           </if>
           <if test="channel!= null and channel!= ''">
               and channel = #{channel}
           </if>
           <if test="promotedPhone!= null and promotedPhone!= ''">
               and promoted_phone = #{promotedPhone}
           </if>
       </where>
    </select>
</mapper>
