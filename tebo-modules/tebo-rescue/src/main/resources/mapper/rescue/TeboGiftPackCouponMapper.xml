<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboGiftPackCouponMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboGiftPackCouponDO">
        <id column="id" property="id" />
        <result column="coupon_code" property="couponId" />
        <result column="pack_code" property="packId" />
        <result column="num" property="num" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, coupon_id, pack_id,num
    </sql>
    <delete id="deleteByPackId">
        delete
        from tebo_gift_pack_coupon
        where pack_id = #{packId}
    </delete>
    <select id="selectByPackId" resultType="com.tebo.rescue.entity.TeboGiftPackCouponDO">
        select
        <include refid="Base_Column_List" />
        from tebo_gift_pack_coupon
        where pack_id = #{packId}
    </select>

    <select id="selectByCouponIds" resultType="com.tebo.rescue.entity.TeboGiftPackCouponDO">
        select
        <include refid="Base_Column_List" />
        from tebo_gift_pack_coupon
        where coupon_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>
