<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboServiceOrderCouponMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboServiceOrderCouponDO">
        <id column="id" property="id" />
        <result column="service_order_id" property="serviceOrderId" />
        <result column="pack_id" property="packId" />
        <result column="coupon_id" property="couponId" />
        <result column="coupon_code" property="couponCode" />
        <result column="unique_code" property="uniqueCode" />
        <result column="coupon_name" property="couponName" />
        <result column="coupon_price" property="couponPrice" />
        <result column="del_flag" property="delFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, service_order_id, pack_id, coupon_id,coupon_code, unique_code, coupon_name, coupon_price, del_flag, create_by, create_time, update_by, update_time
    </sql>
    <insert id="batchInsert">
        insert into tebo_service_order_coupon (id,service_order_id, pack_id, coupon_id,coupon_code, unique_code, coupon_name, coupon_price, del_flag, create_by, create_time, update_by, update_time)
        values
            <foreach collection="list" item="item" separator=",">
                (#{item.id}, #{item.serviceOrderId}, #{item.packId}, #{item.couponId}, #{item.couponCode}, #{item.uniqueCode}, #{item.couponName}, #{item.couponPrice}, #{item.delFlag}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
            </foreach>
    </insert>
    <select id="selectByServiceOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tebo_service_order_coupon
        where service_order_id = #{serviceOrderId} and del_flag = 0
    </select>

</mapper>
