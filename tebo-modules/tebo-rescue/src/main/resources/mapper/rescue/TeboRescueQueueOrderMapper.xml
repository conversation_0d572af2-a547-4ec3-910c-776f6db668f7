<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboRescueQueueOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboRescueQueueOrderDO">
        <id column="id" property="id" />
        <result column="unionid" property="unionid" />
        <result column="nick_name" property="nickName" />
        <result column="phone_number" property="phoneNumber" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="tenant_id" property="tenantId" />
        <result column="order_no" property="orderNo" />
        <result column="account_id" property="accountId" />
        <result column="account_name" property="accountName" />
        <result column="station_name" property="stationName" />
        <result column="order_type" property="orderType" />
        <result column="order_status" property="orderStatus" />
        <result column="handle_result" property="handleResult" />
        <result column="remark" property="remark" />
        <result column="order_amount" property="orderAmount" />
        <result column="actual_pay_amount" property="actualPayAmount" />
        <result column="initiation_time" property="initiationTime" />
        <result column="pre_door_time" property="preDoorTime" />
        <result column="pay_time" property="payTime" />
        <result column="fault_msg" property="faultMsg" />
        <result column="brand" property="brand" />
        <result column="request_location" property="requestLocation" />
        <result column="lon" property="lon" />
        <result column="lat" property="lat" />
        <result column="service_item" property="serviceItem" />
        <result column="initiation_Type" property="initiationType" />
        <result column="cancel_by" property="cancelBy" />
        <result column="distance" property="distance" />
        <result column="del_flag" property="delFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="cancel_time" property="cancelTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="cancel_reason" property="cancelReason" />
        <result column="receive_time" property="receiveTime" />
        <result column="receive_by" property="receiveBy" />
        <result column="account_phone" property="accountPhone" />
        <result column="shop_type" property="shopType" />
    </resultMap>

    <resultMap id="orderNumMap" type="com.tebo.rescue.applet.domain.view.MaintenanceQueueOrderNumberVO">
        <result column="order_type" property="orderType" />
        <result column="sum" property="sum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, unionid, nick_name, phone_number, shop_id, shop_name, tenant_id, order_no, account_id, account_name, station_name, order_type, order_status, handle_result, remark, order_amount, actual_pay_amount, initiation_time, pre_door_time, pay_time, fault_msg, brand, request_location, lon, lat, service_item, initiation_Type, cancel_by, distance, del_flag, create_by, create_time, cancel_time, update_by, update_time, cancel_reason, receive_time, receive_by, account_phone, shop_type
    </sql>


    <sql id="whereCondition" >
        where del_flag = 0
        <if test="orderStatus!= null">
            and order_status = #{orderStatus}
        </if>

        <if test="ids!= null and ids.size()>0">
            and id in
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="orderType!= null">
            and order_type = #{orderType}
        </if>
        <if test="orderTypeList!= null and orderTypeList.size()>0">
            and order_type in
            <foreach collection="orderTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="serviceItem!= null">
            and service_item = #{serviceItem}
        </if>
        <if test="preDoorTimeStart!= null">
            and pre_door_time >= #{preDoorTimeStart ,jdbcType=TIMESTAMP}
        </if>
        <if test="preDoorTimeEnd!= null">
            and #{preDoorTimeEnd ,jdbcType=TIMESTAMP} > pre_door_time
        </if>
        <if test="createTimeStartSecond!= null">
            and create_time >= #{createTimeStartSecond ,jdbcType=TIMESTAMP}
        </if>
        <if test="createTimeEndSecond!= null">
            and #{createTimeEndSecond ,jdbcType=TIMESTAMP} > create_time
        </if>

        <if test="initiationTimeStart!= null">
            and initiation_time >= #{initiationTimeStart ,jdbcType=TIMESTAMP}
        </if>
        <if test="initiationTimeEnd!= null">
            and #{initiationTimeEnd ,jdbcType=TIMESTAMP} > initiation_time
        </if>
        <if test="orderNo!= null and orderNo != '' ">
            and order_no = #{orderNo}
        </if>
        <if test="shopId!= null">
            and shop_id = #{shopId}
        </if>
        <if test="shopType!= null">
            and shop_type = #{shopType}
        </if>
        <if test="unionid!= null">
            and unionid = #{unionid}
        </if>
        <if test="tenantId!= null">
            and tenant_id = #{tenantId}
        </if>
        <if test="orderStatusList!= null and orderStatusList.size() >0 ">
            and order_status in
            <foreach collection="orderStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


    </sql>
<select id="listBySO" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include>
    from tebo_rescue_queue_order <include refid="whereCondition"/>

</select>

    <select id="listBySODesc" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
        from tebo_rescue_queue_order  <include refid="whereCondition"/>
        order by create_time desc
    </select>

    <select id="getMaintenanceServiceOrderCount" resultMap="orderNumMap">
        SELECT order_type,count(1) as sum FROM `tebo_rescue_queue_order`
        where order_status = 0 and shop_id = #{shopId}
        and order_type in (2,3)
        GROUP BY order_type
    </select>
</mapper>
