<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboReportMapper">


    <select id="selectPartnerReport" resultType="com.tebo.rescue.applet.domain.view.report.TeboReportPartnerVO">
        select sum(todayOrderNum) todayOrderNum, sum(t.totalOrderNum) totalOrderNum,sum(t.todayArriveOrderNum) todayArriveOrderNum, sum(t.todayRescueOrderNum) todayRescueOrderNum, sum(t.totalRescueOrderNum) totalRescueOrderNum
        from (
        SELECT count(1) todayOrderNum, 0 totalOrderNum, 0 todayArriveOrderNum, 0 todayRescueOrderNum, 0 totalRescueOrderNum  from tebo_service_order a
        where DATE(a.create_time) = CURDATE()
        and a.tenant_id = #{tenantId}
        and a.order_status in (1,4)
        union  all
        SELECT 0 todayOrderNum, count(1) totalOrderNum, 0 todayArriveOrderNum, 0 todayRescueOrderNum, 0 totalRescueOrderNum from tebo_service_order b
        where b.tenant_id = #{tenantId}
        and b.order_status in (1,4)
        union  all
        select 0 todayOrderNum, 0 totalOrderNum,count(1) todayArriveOrderNum, 0 todayRescueOrderNum, 0 totalRescueOrderNum  from tebo_queue_order c
        where DATE(c.create_time) = CURDATE()
        and c.tenant_id = #{tenantId}
        union  all
        select 0 todayOrderNum, 0 totalOrderNum,0 todayArriveOrderNum, count(1) todayRescueOrderNum, 0 totalRescueOrderNum   from tebo_rescue_queue_order d
        where DATE(d.create_time) = CURDATE()
        and d.order_type in(2,3)
        and d.tenant_id = #{tenantId}
        union  all
        select 0 todayOrderNum, 0 totalOrderNum,0 todayArriveOrderNum, 0 todayRescueOrderNum,count(1) totalRescueOrderNum from tebo_rescue_queue_order e
        where e.order_type in(2,3)
        and e.tenant_id = #{tenantId}

        ) as t;
    </select>
    <select id="selectPartnerBatteryReport"
            resultType="com.tebo.rescue.applet.domain.view.report.TeboReportPartnerVO">
        select sum(todayBatteryNum) todayBatteryNum, sum(t.totalBatteryNum) totalBatteryNum,sum(t.todayBatteryRevenue) todayBatteryRevenue, sum(t.totalBatteryRevenue) totalBatteryRevenue
        from (
        SELECT sum(goods_num) todayBatteryNum, 0 totalBatteryNum, 0 todayBatteryRevenue, 0 totalBatteryRevenue FROM `tebo_service_order_goods` a
        where a.brand_name = '天能'
        and DATE(a.create_time) = CURDATE()
        and a.service_order_id in (
        select id from tebo_service_order
        where order_status in (1,4)
        and DATE(create_time) = CURDATE()
        and tenant_id = #{tenantId}
        )

        union all
        SELECT 0 todayBatteryNum, sum(b.goods_num) totalBatteryNum, 0 todayBatteryRevenue, 0 totalBatteryRevenue FROM `tebo_service_order_goods` b
        where b.brand_name = '天能'
        and b.service_order_id in (
        select id from tebo_service_order
        where order_status in (1,4)
        and tenant_id = #{tenantId}
        )
        union all


        SELECT 0 todayBatteryNum, 0 totalBatteryNum, IFNULL(sum(c.goods_amount),0) todayBatteryRevenue, 0 totalBatteryRevenue FROM `tebo_service_order_goods` c
        where c.brand_name = '天能'
        and DATE(c.create_time) = CURDATE()
        and c.service_order_id in (
        select id from tebo_service_order
        where order_status in (1,4)
        and DATE(create_time) = CURDATE()
        and tenant_id = #{tenantId}
        )
        union all

        SELECT 0 todayBatteryNum, 0 totalBatteryNum, 0 todayBatteryRevenue, sum(d.goods_amount) totalBatteryRevenue FROM `tebo_service_order_goods` d
        where d.brand_name = '天能'
        and d.service_order_id in (
        select id from tebo_service_order
        where order_status in (1,4)
        and tenant_id = #{tenantId}
        )
        ) t


    </select>
    <select id="selectPartnerShopReport"
            resultType="com.tebo.rescue.applet.domain.view.report.TeboReportPartnerVO">
        select sum(todayOrderNum) todayOrderNum, sum(t.totalOrderNum) totalOrderNum,sum(t.todayArriveOrderNum) todayArriveOrderNum, sum(t.todayRescueOrderNum) todayRescueOrderNum, sum(t.totalRescueOrderNum) totalRescueOrderNum
        from (
        SELECT count(1) todayOrderNum, 0 totalOrderNum, 0 todayArriveOrderNum, 0 todayRescueOrderNum, 0 totalRescueOrderNum  from tebo_service_order a
        where DATE(a.create_time) = CURDATE()
        and a.shop_id = #{shopId}
        and a.order_status in (1,4)
        union  all
        SELECT 0 todayOrderNum, count(1) totalOrderNum, 0 todayArriveOrderNum, 0 todayRescueOrderNum, 0 totalRescueOrderNum from tebo_service_order b
        where b.shop_id = #{shopId}
        and b.order_status in (1,4)
        union  all
        select 0 todayOrderNum, 0 totalOrderNum,count(1) todayArriveOrderNum, 0 todayRescueOrderNum, 0 totalRescueOrderNum  from tebo_queue_order c
        where DATE(c.create_time) = CURDATE()
        and c.shop_id = #{shopId}
        union  all
        select 0 todayOrderNum, 0 totalOrderNum,0 todayArriveOrderNum, count(1) todayRescueOrderNum, 0 totalRescueOrderNum   from tebo_rescue_queue_order d
        where DATE(d.create_time) = CURDATE()
        and d.order_type in(2,3)
        and d.shop_id = #{shopId}
        union  all
        select 0 todayOrderNum, 0 totalOrderNum,0 todayArriveOrderNum, 0 todayRescueOrderNum,count(1) totalRescueOrderNum from tebo_rescue_queue_order e
        where e.order_type in(2,3)
        and e.shop_id = #{shopId}

        ) as t;

    </select>


    <select id="selectPartnerShopBatteryReport"
            resultType="com.tebo.rescue.applet.domain.view.report.TeboReportPartnerVO">

        select sum(todayBatteryNum) todayBatteryNum, sum(t.totalBatteryNum) totalBatteryNum,sum(t.todayBatteryRevenue) todayBatteryRevenue, sum(t.totalBatteryRevenue) totalBatteryRevenue
        from (
        SELECT sum(goods_num) todayBatteryNum, 0 totalBatteryNum, 0 todayBatteryRevenue, 0 totalBatteryRevenue FROM `tebo_service_order_goods` a
        where a.brand_name = '天能'
        and DATE(a.create_time) = CURDATE()
        and a.service_order_id in (
        select id from tebo_service_order
        where order_status in (1,4)
        and DATE(create_time) = CURDATE()
        and shop_id = #{shopId}
        )

        union all
        SELECT 0 todayBatteryNum, sum(b.goods_num) totalBatteryNum, 0 todayBatteryRevenue, 0 totalBatteryRevenue FROM `tebo_service_order_goods` b
        where b.brand_name = '天能'
        and b.service_order_id in (
        select id from tebo_service_order
        where order_status in (1,4)
        and shop_id = #{shopId}
        )
        union all


        SELECT 0 todayBatteryNum, 0 totalBatteryNum, IFNULL(sum(c.goods_amount),0) todayBatteryRevenue, 0 totalBatteryRevenue FROM `tebo_service_order_goods` c
        where c.brand_name = '天能'
        and DATE(c.create_time) = CURDATE()
        and c.service_order_id in (
        select id from tebo_service_order
        where order_status in (1,4)
        and DATE(create_time) = CURDATE()
        and shop_id = #{shopId}
        )
        union all

        SELECT 0 todayBatteryNum, 0 totalBatteryNum, 0 todayBatteryRevenue, sum(d.goods_amount) totalBatteryRevenue FROM `tebo_service_order_goods` d
        where d.brand_name = '天能'
        and d.service_order_id in (
        select id from tebo_service_order
        where order_status in (1,4)
        and shop_id = #{shopId}
        )
        ) t

    </select>
    <select id="getIndexRescueReport" resultType="com.tebo.rescue.api.domain.view.TeboIndexReportRescueVO">
        select sum(todayOrderNum) todayOrderNum, sum(t.todayPendingRescueOrderNum) todayPendingRescueOrderNum,sum(t.todayAcceptRescueOrderNum) todayAcceptRescueOrderNum, sum(t.todayTimeoutRescueOrderNum) todayTimeoutRescueOrderNum
        from (

        select count(1) as todayOrderNum, 0 todayPendingRescueOrderNum, 0 todayAcceptRescueOrderNum, 0 todayTimeoutRescueOrderNum
        from tebo_service_order
        where order_status = 1
        and DATE(create_time) = CURDATE()
        union all
        select 0 todayOrderNum, count(1) as todayPendingRescueOrderNum, 0 todayAcceptRescueOrderNum, 0 todayTimeoutRescueOrderNum
        from tebo_rescue_queue_order
        where order_type = 2
<!--        and DATE(create_time) = CURDATE()-->
        and order_status = 0
        and del_flag = 0
        union all
        select 0 todayOrderNum, 0 todayPendingRescueOrderNum, count(1) as todayAcceptRescueOrderNum,  0 todayTimeoutRescueOrderNum
        from tebo_rescue_queue_order
        where order_type = 2
<!--        and DATE(create_time) = CURDATE()-->
        and order_status in (1,2,3)
        and del_flag = 0
        union all
        select 0 todayOrderNum, 0 todayPendingRescueOrderNum, 0 as todayAcceptRescueOrderNum, count(1) as todayTimeoutRescueOrderNum
        from tebo_rescue_queue_order
        where order_type = 2
<!--        and DATE(create_time) = CURDATE()-->
        and order_status = 9
        and del_flag = 0
        )t
    </select>
    <select id="getIndexSubscribeReport" resultType="com.tebo.rescue.api.domain.view.TeboIndexReportRescueVO">
        select sum(todayPendingSubscribeOrderNum) todayPendingSubscribeOrderNum, sum(t.todayAcceptSubscribeOrderNum) todayAcceptSubscribeOrderNum,sum(t.todayInvalidOrderNum) todayInvalidOrderNum
        from (
        select count(1) as todayPendingSubscribeOrderNum, 0 todayAcceptSubscribeOrderNum, 0 todayInvalidOrderNum
        from tebo_rescue_queue_order
        where order_type = 3
<!--        and DATE(create_time) = CURDATE()-->
        and order_status = 0
        and del_flag = 0
        union all
        select 0 todayPendingSubscribeOrderNum, count(1)  todayAcceptSubscribeOrderNum, 0 todayInvalidOrderNum
        from tebo_rescue_queue_order
        where order_type = 3
<!--        and DATE(create_time) = CURDATE()-->
        and order_status in (1,2,3)
        and del_flag = 0
        union all
        select 0 todayPendingSubscribeOrderNum, 0 todayAcceptSubscribeOrderNum, count(1) todayInvalidOrderNum
        from tebo_rescue_queue_order
        where
        order_status = 8
        and del_flag = 0
        )t
    </select>
    <select id="getReferenceInfo" resultType="java.util.Map">
        SELECT tcpr.reference_phone as referencePhone, tcpr.type as type FROM tebo_rescue_prod.`tebo_customer_promote_record` tcpr
            LEFT JOIN tebo_system_prod.tebo_customer tc
                   on tcpr.union_id = tc.unionid
        where tcpr.promoted_phone = #{phoneNumber}
    </select>
    <select id="getPersonInfo" resultType="java.util.Map">
        SELECT tgpo.pay_time as payTime,tgpo.order_no as orderNo FROM tebo_rescue_prod.tebo_gift_pack_order tgpo
            left join tebo_system_prod.tebo_customer tc
                      on tgpo.union_id = tc.unionid
        where tc.phone_number = #{phoneNumber}
          and order_status = 1
    </select>
    <select id="getReferencePersonInfo" resultType="java.util.Map">
        -- 作为推广者，目前推广的人的订单
        SELECT tgpo.pay_time,tc.phone_number as phoneNumber,tgpo.order_no as orderNo FROM tebo_rescue_prod.tebo_gift_pack_order tgpo
             left join tebo_system_prod.tebo_customer tc
                       on tgpo.union_id = tc.unionid
        where tgpo.reference_phone =  #{phoneNumber}
          and tgpo.order_status = 1
    </select>

</mapper>
