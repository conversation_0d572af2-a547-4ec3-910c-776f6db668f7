<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboCouponGoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboCouponGoodsDO">
        <id column="id" property="id" />
        <result column="coupon_id" property="couponId" />
        <result column="coupon_id" property="couponId" />
        <result column="goods_no" property="goodsNo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, coupon_id, goods_id, goods_no
    </sql>
    <delete id="deleteByCouponId">
        delete from tebo_coupon_goods
        where coupon_id = #{couponId}
    </delete>
    <select id="selectByCouponId" resultType="com.tebo.rescue.entity.TeboCouponGoodsDO">
        select
        <include refid="Base_Column_List" />
        from tebo_coupon_goods
        where coupon_id = #{couponId}
    </select>
    <select id="selectByCouponIdList" resultType="com.tebo.rescue.entity.TeboCouponGoodsDO">
        select
        <include refid="Base_Column_List" />
        from tebo_coupon_goods
        where coupon_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="batchInsert" >
        insert into tebo_coupon_goods (id, coupon_id, goods_id, goods_no)
        values
        <foreach collection="list" item="coupon" index="index" separator=",">
            (#{coupon.id}, #{coupon.couponId}, #{coupon.goodsId}, #{coupon.goodsNo})
        </foreach>
    </insert>
</mapper>
