<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboCouponCustomerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboCouponCustomerDO">
        <id column="id" property="id" />
        <id column="unique_code" property="uniqueCode" />
        <id column="order_no" property="orderNo" />
        <result column="coupon_id" property="couponId" />
        <result column="source" property="source" />
        <result column="pack_id" property="packId" />
        <result column="union_id" property="unionId" />
        <result column="status" property="status" />
        <result column="occ_status" property="occStatus" />
        <result column="occ_number" property="occNumber" />
        <result column="receive_time" property="receiveTime" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="write_off_time" property="writeOffTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,order_no, unique_code, coupon_id, pack_id, union_id, `status`,occ_status,occ_number, receive_time, start_time, end_time,write_off_time,update_time
    </sql>

    <insert id="insertBatch">
        insert into tebo_coupon_customer (id,order_no, unique_code, coupon_id, pack_id, union_id, `status`, receive_time, start_time, end_time,update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.orderNo},#{item.uniqueCode}, #{item.couponId}, #{item.packId}, #{item.unionId}, #{item.status}, #{item.receiveTime}, #{item.startTime}, #{item.endTime},#{item.updateTime})
        </foreach>
    </insert>
    <update id="batchUpdateByUniqueCode">
        update tebo_coupon_customer
        set status = #{status},update_time = now()
        where unique_code in
        <foreach collection="list" item="unique" open="(" separator="," close=")">
            #{unique}
        </foreach>
    </update>
    <update id="batchExpireCouponStatus">
        update tebo_coupon_customer
        set status = 3
        where coupon_id = #{couponId}
        and status = 2
        and coupon_id in
        <foreach collection="list" item="unique" open="(" separator="," close=")">
            #{unique}
        </foreach>
    </update>
    <delete id="batchDelCouponStatus">
        delete from tebo_coupon_customer
        where coupon_id = #{couponId}
        and status != 2
    </delete>
    <select id="getList" resultType="com.tebo.rescue.entity.TeboCouponCustomerDO">
        select
        <include refid="Base_Column_List"/>
        from tebo_coupon_customer
        <where>
            <if test="unionId!= null">
                and union_id = #{unionId}
            </if>
            <if test="couponId!= null">
                and coupon_id = #{couponId}
            </if>
            <if test="orderNo!= null">
                and order_no = #{orderNo}
            </if>
            <if test="status!= null and status != 20">
                and status = #{status}
            </if>
            <if test="statusStr != null">
                and status in (3,10,11)
            </if>

            <if test="statusList != null and statusList.size() > 0" >
                and status in
                <foreach collection="statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>

            <if test="couponIdList != null and couponIdList.size() > 0" >
                and coupon_id in
                <foreach collection="couponIdList" item="couponId" open="(" separator="," close=")">
                    #{couponId}
                </foreach>
            </if>

            <if test="maxTime != null">
                and end_time &lt; #{maxTime}
            </if>
        </where>
        order by end_time asc
    </select>

    <select id="getBatteryList" resultType="com.tebo.rescue.entity.TeboCouponCustomerDO">
        select
        <include refid="Base_Column_List"/>
        from tebo_coupon_customer c
        <where>
            <if test="unionId!= null">
                and c.union_id = #{unionId}
            </if>
            <if test="couponId!= null">
                and c.coupon_id = #{couponId}
            </if>
            <if test="orderNo!= null">
                and c.order_no = #{orderNo}
            </if>
            <if test="status!= null and status == 1">
                and c.status = 1 and c.occ_status = 1
            </if>
            <if test="status!= null and status != 20 and  status != 40 and status != 1">
                and c.status = #{status}
            </if>
            <if test="statusStr != null and statusStr == 20">
                and c.status in (3,10,11)
            </if>
            <if test="statusStr != null and statusStr == 40">
                and c.status in (1,2,3,10,20)
            </if>
            <if test="statusList != null and statusList.size() > 0" >
                and c.status in
                <foreach collection="statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>

            <if test="couponIdList != null and couponIdList.size() > 0" >
                and c.coupon_id in
                <foreach collection="couponIdList" item="couponId" open="(" separator="," close=")">
                    #{couponId}
                </foreach>
            </if>

            <if test="maxTime != null">
                and c.end_time &lt; #{maxTime}
            </if>
        </where>
        <choose>
            <when test="status != null and status == 1">
                ORDER BY c.end_time asc,c.order_no desc
            </when>
            <when test="status != null and status == 2">
                ORDER BY c.update_time DESC,c.order_no desc
            </when>
            <when test="status != null and status == 40">
                ORDER BY c.update_time DESC,c.order_no desc
            </when>
            <when test="status != null and status == 10">
                ORDER BY c.update_time DESC,c.order_no desc
            </when>
        </choose>
    </select>

    <select id="getDetailByCouponCode" resultType="com.tebo.rescue.entity.TeboCouponCustomerDO">
        select
        <include refid="Base_Column_List"/>
        from tebo_coupon_customer
        where unique_code = #{couponCode}
    </select>
    <select id="getCouponListByUniqueList" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
            from tebo_coupon_customer
        where
            unique_code in
        <foreach collection="list" item="unique" open="(" separator="," close=")">
            #{unique}
        </foreach>
    </select>
    <select id="getExpireList" resultType="com.tebo.rescue.entity.TeboCouponCustomerDO">
        select
        <include refid="Base_Column_List"/>
        from tebo_coupon_customer
        where
            status not in(2,3) and end_time &lt; DATE_SUB(NOW(), INTERVAL 1 DAY)
    </select>

    <select id="getCustomerCouponByCouponId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tebo_coupon_customer
        where
        union_id = #{unionId} and coupon_id = #{couponId} order by receive_time desc limit 1
    </select>

    <select id="getCustomerCouponList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tebo_coupon_customer
        <where>
            <if test="unionId!= null">
                and union_id = #{unionId}
            </if>
            <if test="orderNo!= null">
                and order_no = #{orderNo}
            </if>
            <if test="couponId!= null">
                and coupon_id = #{couponId}
            </if>
            <if test="status!= null">
                and status = #{status}
            </if>
            <if test="statusList != null and statusList.size() > 0" >
                and status in
                <foreach collection="statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="couponIdList != null and couponIdList.size() > 0" >
                and coupon_id in
                <foreach collection="couponIdList" item="couponId" open="(" separator="," close=")">
                    #{couponId}
                </foreach>
            </if>
        </where>
        order by status asc, end_time asc
    </select>

    <select id="getUnUsedCustomerCouponList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tebo_coupon_customer
        <where>
             status = 1 and occ_status = 1
            <if test="unionId!= null">
                and union_id = #{unionId}
            </if>
            <if test="orderNo!= null">
                and order_no = #{orderNo}
            </if>
            <if test="couponId!= null">
                and coupon_id = #{couponId}
            </if>

            <if test="statusList != null and statusList.size() > 0" >
                and status in
                <foreach collection="statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="couponIdList != null and couponIdList.size() > 0" >
                and coupon_id in
                <foreach collection="couponIdList" item="couponId" open="(" separator="," close=")">
                    #{couponId}
                </foreach>
            </if>
        </where>
        order by status asc, end_time asc
    </select>

    <select id="getCustomerRiceCouponList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tebo_coupon_customer
        <where>
            <if test="unionId!= null">
                and union_id = #{unionId}
            </if>
            <if test="orderNo!= null">
                and order_no = #{orderNo}
            </if>
            <if test="couponId!= null">
                and coupon_id = #{couponId}
            </if>
            <if test="status!= null">
                and status = #{status}
            </if>
            <if test="statusList != null and statusList.size() > 0" >
                and status in
                <foreach collection="statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="couponIdList != null and couponIdList.size() > 0" >
                and coupon_id in
                <foreach collection="couponIdList" item="couponId" open="(" separator="," close=")">
                    #{couponId}
                </foreach>
            </if>
        </where>
        <choose>
            <when test="statusList != null and statusList.size() == 1 and statusList[0] == 1">
                ORDER BY end_time asc
            </when>
            <when test="statusList != null and statusList.size() == 1 and statusList[0] == 2">
                ORDER BY update_time DESC
            </when>
            <when test="statusList != null and statusList.size() >2 ">
                ORDER BY update_time DESC
            </when>
        </choose>
    </select>

    <select id="getCustomerCycleCoupon" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_coupon_customer
        where  union_id = #{unionId} and coupon_id = '1850046210543452160' and order_no = #{orderNo}
    </select>

    <select id="getCustomerCouponByOrderNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tebo_coupon_customer
        where order_no = #{orderNo}
    </select>

    <select id="getOrderNoByFlowId" resultType="String">
        select pts.order_no from tebo_pay.merchants_wallet_record mwr
        LEFT JOIN tebo_pay.pay_trade_split pts
        on mwr.biz_id = pts.id
        where mwr.id = #{id} and order_no is not null
        union
        select tgpo.order_no from tebo_pay.merchants_wallet_record mwr
        LEFT JOIN tebo_gift_pack_order tgpo on mwr.biz_id = tgpo.id
        where mwr.id = #{id} and order_no is not null
    </select>
    <select id="getCouponCustomerList" resultType="com.tebo.rescue.entity.TeboCouponCustomerDO">
        select
        <include refid="Base_Column_List"/>
        FROM
        (SELECT *, ROW_NUMBER() OVER (PARTITION BY coupon_id ORDER BY start_time desc, id) AS rn
            FROM tebo_coupon_customer
            WHERE coupon_id IN (1853320012069076992, 10001)
            and union_id = #{unionId}
            and occ_status = 1
            and receive_time &gt; '2024-11-14 23:59:59'
            and STATUS = 1  ) AS ranked
        WHERE rn = 1;
    </select>

    <update id="updateCouponStatusAndTime">
        update tebo_coupon_customer
        set status = #{status},write_off_time = #{writeOffTime},update_time = #{updateTime}
        where unique_code = #{couponCode}
    </update>


    <delete id="deleteBatteryCoupon" >
        delete from tebo_coupon_customer where order_no = #{orderNo} and coupon_id in ('10001','1853320012069076992')
    </delete>
</mapper>
