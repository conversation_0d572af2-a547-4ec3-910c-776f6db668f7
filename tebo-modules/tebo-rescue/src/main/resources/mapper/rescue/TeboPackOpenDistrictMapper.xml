<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tebo.rescue.mapper.TeboPackOpenDistrictMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tebo.rescue.entity.TeboPackOpenDistrictDO">
        <result column="id" property="id" />
        <result column="district" property="district" />
        <result column="del_flag" property="delFlag" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, province, city, district, del_flag, create_time
    </sql>

    <select id="getAllDistrictList" resultType="String">
        select distinct(district) from tebo_pack_open_district where del_flag = 0
    </select>

</mapper>
