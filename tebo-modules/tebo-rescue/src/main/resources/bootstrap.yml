# Tomcat
server:
  port: 9212

# Spring
spring:
  application:
    # 应用名称
    name: tebo-rescue
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: @nacos.server.address@
        namespace: @nacos.server.namespace@
        group: tebo
        username: nacos
        password: Tn!@#$1234
      config:
        # 配置中心地址
        server-addr: @nacos.server.address@
        namespace: @nacos.server.namespace@
        group: tebo
        username: nacos
        password: Tn!@#$1234
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
