package com.tebo.rescue.applet.controller;

import com.tebo.common.core.domain.R;
import com.tebo.rescue.applet.domain.dto.ServiceOrderPayDTO;
import com.tebo.rescue.service.pay.TeboWechatPayService;
import com.tebo.system.api.domain.view.WechatPrepayResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024/1/19 13:18
 * @Desc :
 */
@RestController
@Slf4j
@RequestMapping("/applet/wechat/pay")
public class TeboWechatPayController {

    @Resource
    private TeboWechatPayService teboWechatPayService;

    /**
     * 工单预支付
     * @param orderId
     * @return
     */
    @RequestMapping("/serviceOrderPrePay/{orderId}")
    public R<WechatPrepayResponse> serviceOrderPrePay(@PathVariable("orderId") Long orderId) {
        return teboWechatPayService.serviceOrderPrePay(orderId);
    }

    /**
     * 快捷开单工单预支付
     * @return
     */
    @RequestMapping("/quickBillServiceOrderPrePay")
    public R<WechatPrepayResponse> quickBillServiceOrderPrePay(@RequestParam("orderId") Long orderId,@RequestParam("openId") String openId) {
        return teboWechatPayService.quickBillServiceOrderPrePay(orderId,openId);
    }
    /**
     * 礼包预支付
     * @param packId
     * @return
     */
    @RequestMapping("/giftPackPrePay")
    public R giftPackPrePay(@RequestParam("packId") Long packId,@RequestParam("unionId") String unionId) throws InterruptedException {
        return teboWechatPayService.giftPackPrePay(packId, unionId);
    }

    /**
     * 团购订单预支付
     */
    @RequestMapping("/groupOrderPrePay")
    public R groupOrderPrePay(@RequestParam("orderId") Long orderId,@RequestParam("unionId") String unionId) throws InterruptedException {
        return teboWechatPayService.groupOrderPrePay(orderId, unionId);
    }

    /**
     * 团购订单预支付-泰博出行商家版
     */
    @RequestMapping("/groupOrderPrePayMerchant")
    public R groupOrderPrePayMerchant(@RequestParam("orderId") Long orderId,@RequestParam("unionId") String unionId,@RequestParam("openId") String openId) throws InterruptedException {
        log.info("groupOrderPrePayMerchant orderId:{},unionId:{},openId:{}",orderId,unionId,openId);
        return teboWechatPayService.groupOrderPrePayMerchant(orderId, unionId,openId);
    }

    /**
     * 预支付-维修师傅代付工单
     * @param payDTO
     * @return
     */
    @RequestMapping("/serviceOrderBehalfPrePay")
    public R<WechatPrepayResponse> serviceOrderBehalfPrePay(@RequestBody ServiceOrderPayDTO payDTO) {
        return teboWechatPayService.serviceOrderBehalfPrePay(payDTO);
    }

}
