package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;

import com.tebo.common.util.number.SnowFlakeUtil;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 礼包卡券关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Getter
@Setter
@TableName("tebo_gift_pack_coupon")
public class TeboGiftPackCouponDO extends Model<TeboGiftPackCouponDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 卡券编号
     */
    @TableField("coupon_id")
    private Long couponId;

    /**
     * 礼包编号
     */
    @TableField("pack_id")
    private Long packId;

    /**
     * 数量
     */
    @TableField("num")
    private Integer num;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

    public TeboGiftPackCouponDO(Long couponId, Long packId, Integer num) {
        this.couponId = couponId;
        this.packId = packId;
        this.num = num;
        this.id = SnowFlakeUtil.nextId();
    }
    public TeboGiftPackCouponDO() {

    }

}
