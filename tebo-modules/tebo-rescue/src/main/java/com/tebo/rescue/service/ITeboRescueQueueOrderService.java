package com.tebo.rescue.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.tebo.rescue.applet.domain.dto.*;
import com.tebo.rescue.applet.domain.view.excel.RescuePreOrderExportVO;
import com.tebo.rescue.applet.domain.view.CustomerQueueStatusVO;
import com.tebo.rescue.applet.domain.view.MaintenanceQueueOrderNumberVO;
import com.tebo.rescue.entity.TeboRescueQueueOrderDO;
import com.tebo.system.api.domain.view.WechatPrepayResponse;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 救援，预约上门服务单
 */
public interface ITeboRescueQueueOrderService extends IService<TeboRescueQueueOrderDO> {

    /**
     * 创建救援单
     *
     * @param rescueQueueOrderSaveDTO
     */
    RescueQueueOrderSaveDTO createRescueOrder(RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO);

    /**
     * 创建救援单(新)
     *
     * @param rescueQueueOrderSaveDTO
     */
    WechatPrepayResponse createRescueOrderNew(RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO);

    /**
     * 处理超时的排队单
     */
    void handleTimeOutRescueQueueOrder();


    /**
     * 失效预约上门单
     */
    void cancelPreDoorOrder();


    /**
     * 失效预约到店单
     */
    void cancelPreStoreOrder();

    /**
     * 当前消费者的各个排队单状态
     */
    CustomerQueueStatusVO customerQueueStatus();

    void cancelRescueOrder(RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO);

    /**
     * 创建上门服务单
     *
     * @param rescueQueueOrderSaveDTO
     */
    RescueQueueOrderSaveDTO createPreDoorOrder(RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO);


    /**
     * 创建预约到店服务单
     *
     * @param rescueQueueOrderSaveDTO
     */
    RescueQueueOrderSaveDTO createPreStoreOrder(RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO);


    /**
     * 服务单付款（救援单/预约上门单）
     *
     * @param id
     */
//    boolean rescuePay(Long id);


    /**
     * 支付单明细
     *
     * @param id
     * @return
     */
//    RescuePayDetailDTO rescuePayDetail(Long id);


    /**
     * 我的预约单列表
     *
     * @return
     */
    List<RescueQueueOrderItemDTO> getPreOrderListByUser(RescueQueueOrderQueryDTO queryDTO);

    /**
     * 师傅端-预约上门单待接单列表
     * <p>
     * 查这个门店的待接单救援单
     *
     * @return
     */
    List<RescueQueueOrderItemDTO> getUnReceivedPreDoorOrderList();

    /**
     * 我的救援单列表
     *
     * @return
     */
    List<RescueQueueOrderItemDTO> getRescueOrderListByUser();

    /**
     * 师傅端-门店的待接单救援单列表
     *
     * @return
     */
    List<RescueQueueOrderItemDTO> shopStoreRescueOrderList(RescueQueueOrderQueryDTO queryDTO);

    /**
     * 师傅端-门店的预约上门  单救援单列表
     *
     * @return
     */
    List<RescueQueueOrderItemDTO> shopPreDoorOrderList(RescueQueueOrderQueryDTO queryDTO);


    /**
     * 救援单、预约上门，接单
     * <p>
     * 救援单，预约上门单接单后直接生成工单
     *
     * @return
     */
    Boolean receivingOrder(RescueQueueOrderReceiveDTO rescueQueueOrderReceiveDTO);


    /**
     * 查询救援单列表
     *
     * @param queryDTO
     * @return
     */
    List<RescueQueueOrderItemDTO> rescueOrderList(RescueQueueOrderQueryDTO queryDTO);


    /**
     * 导出救援单
     * @param queryDTO
     */
    void rescueOrderExport(HttpServletResponse response, RescueQueueOrderQueryDTO queryDTO);


    /**
     * 根据条件查询
     * @param queryDTO
     * @return
     */
    List<RescueQueueOrderItemDTO> listByCondition(RescueQueueOrderQueryDTO queryDTO);

    /**
     * 查询预约单列表
     *
     * @param queryDTO
     * @return
     */
    List<RescueQueueOrderItemDTO> preOrderList(RescueQueueOrderQueryDTO queryDTO);

    void preDoorExport(HttpServletResponse response, RescueQueueOrderQueryDTO queryDTO);

    /**
     * 救援 预约排队单详情
     *
     * @param queryDTO
     * @return
     */
    RescueQueueOrderDetailDTO orderDetail(RescueQueueOrderQueryDTO queryDTO);


    /**
     * 分配门店
     */
    void shopAllocation( AllocationShopDTO allocationShopDTO);

    /**
     * 统计师傅端救援服务单和预约上门服务单数量
     */
    List<MaintenanceQueueOrderNumberVO> getMaintenanceServiceOrderCount();
}
