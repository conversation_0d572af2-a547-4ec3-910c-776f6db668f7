package com.tebo.rescue.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.util.DataUtils;
import com.tebo.common.util.applet.AppletUtil;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.rescue.api.domain.dto.TeboPromoteRecordDTO;
import com.tebo.rescue.api.domain.view.TeboPromoteOrderVO;
import com.tebo.rescue.api.domain.view.TeboPromoteRecordVO;
import com.tebo.rescue.applet.domain.dto.GiftPackOrderQueryDTO;
import com.tebo.rescue.entity.TeboGiftPackOrderDO;
import com.tebo.rescue.entity.TeboPromoteRecordDO;
import com.tebo.rescue.mapper.TeboGiftPackOrderMapper;
import com.tebo.rescue.mapper.TeboPromoteRecordMapper;
import com.tebo.rescue.service.TeboPromoteRecordService;
import com.tebo.rescue.web.domain.dto.promote.PromoteQueryDTO;
import com.tebo.system.api.RemoteCustomerService;
import com.tebo.system.api.RemotePartnerService;
import com.tebo.system.api.RemoteShopService;
import com.tebo.system.api.RemoteUserService;
import com.tebo.system.api.domain.SysUser;
import com.tebo.system.api.domain.dto.TeboCustomerQueryDTO;
import com.tebo.system.api.domain.dto.TeboCustomerUpdateDTO;
import com.tebo.system.api.domain.view.TeboPartnerInfoVO;
import com.tebo.system.api.model.TeboConsumer;
import com.tebo.system.api.model.TeboShop;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TeboPromoteRecordServiceImpl implements TeboPromoteRecordService {
    @Resource
    private RemoteCustomerService remoteCustomerService;

    @Resource
    private RemoteShopService remoteShopService;
    @Resource
    private RemotePartnerService remotePartnerService;

    @Resource
    private RemoteUserService remoteUserService;

    @Resource
    private TeboPromoteRecordMapper teboPromoteRecordMapper;
    @Resource
    private TeboGiftPackOrderMapper giftPackOrderMapper;

    @Resource
    private HttpServletRequest request;

    @Override
    public void activateBinding(TeboPromoteRecordDTO teboPromoteRecordDTO) {
        TeboPromoteRecordDO teboPromoteRecordDO = new TeboPromoteRecordDO();
        /**
         * 推广人手机号
         */
        teboPromoteRecordDO.setReferencePhone(teboPromoteRecordDTO.getReferencePhone());
        /**
         * 推广人门店id
         */
        teboPromoteRecordDO.setReferenceShopId(teboPromoteRecordDTO.getReferenceShopId());
        TeboPromoteRecordDO record = teboPromoteRecordMapper.selectByUnionId(teboPromoteRecordDTO.getUnionId());
        if (ObjectUtil.isNotEmpty(record)){
            throw new ServiceException("您已存在分享身份，无法再激活新身份");
        }
        R<TeboShop> shopR = remoteShopService.getShopInfo(teboPromoteRecordDTO.getReferenceShopId());
        R<TeboConsumer> consumerR = remoteCustomerService.selectByUnionId(teboPromoteRecordDTO.getUnionId());
        teboPromoteRecordDO.setId(SnowFlakeUtil.nextId());
        /**
         * 被推广者微信标识
         */
        teboPromoteRecordDO.setUnionId(teboPromoteRecordDTO.getUnionId());
        /**
         * 推广人钱包id b端钱包id
         */
        teboPromoteRecordDO.setReferenceWalletId(shopR.getData().getWalletId());
        /**
         * 被推广人手机号
         */
        teboPromoteRecordDO.setPromotedPhone(consumerR.getData().getPhoneNumber());
        teboPromoteRecordDO.setChannel(1);
        teboPromoteRecordMapper.insert(teboPromoteRecordDO);
        R<TeboConsumer> teboConsumerR = remoteCustomerService.selectByUnionId(teboPromoteRecordDTO.getUnionId());
        TeboConsumer consumer = teboConsumerR.getData();
        if (ObjectUtil.isEmpty(consumer)){
            throw new ServiceException("用户不存在");
        }
        TeboCustomerUpdateDTO teboCustomerDTO = new TeboCustomerUpdateDTO();
        BeanConvert.copy(consumer,teboCustomerDTO);
        teboCustomerDTO.setChannel(1);
        remoteCustomerService.updateCustomerInfo(teboCustomerDTO);
    }

    @Override
    public TeboPromoteRecordVO selectByUnionId(String unionId) {
        TeboPromoteRecordVO teboPromoteRecordVO = new TeboPromoteRecordVO();
        TeboPromoteRecordDO teboPromoteRecordDO = teboPromoteRecordMapper.selectByUnionId(unionId);
        if (ObjectUtil.isEmpty(teboPromoteRecordDO)){
            return null;
        }
        BeanConvert.copy(teboPromoteRecordDO,teboPromoteRecordVO);
        return teboPromoteRecordVO;
    }
    @Override
    public List<TeboPromoteRecordVO> getPromotedPersonList(PromoteQueryDTO promoteQueryDTO){
        List<TeboPromoteRecordDO> list = teboPromoteRecordMapper.getPromotedPersonList(promoteQueryDTO);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        List<TeboPromoteRecordVO> result = BeanConvert.copyList(list,TeboPromoteRecordVO::new);
        List<String> promotedPhoneList = result.stream().map(item ->item.getPromotedPhone()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(promotedPhoneList)){
            GiftPackOrderQueryDTO giftPackOrderQueryDTO = new GiftPackOrderQueryDTO();
            List<Integer> typeList = new ArrayList<>();
            if (promoteQueryDTO.getChannel() == 1){
                typeList.add(5);
            }else if (promoteQueryDTO.getChannel() == 2){
                typeList.add(6);
            }else {
                typeList.add(5);
                typeList.add(6);
            }
            giftPackOrderQueryDTO.setTypeList(typeList);
            giftPackOrderQueryDTO.setReferencePhoneList(promotedPhoneList);
            List<TeboGiftPackOrderDO> giftPackList = giftPackOrderMapper.selectByReferencePhone(giftPackOrderQueryDTO);
            if (!CollectionUtils.isEmpty(giftPackList)){
                result.stream().forEach(gift ->{
                  String promotedPhone = gift.getPromotedPhone();
                  List<TeboGiftPackOrderDO> orderList = giftPackList.stream().filter(item ->item.getReferencePhone().equals(promotedPhone)).collect(Collectors.toList());
                  if (!CollectionUtils.isEmpty(orderList)){
                      gift.setOrderCount(orderList.size());
                     Long writeOffCount = orderList.stream().filter(item ->item.getVerificationStatus() == 1).count();
                     gift.setOrderWriteOffCount(writeOffCount);
                  }
                });
            }
        }
        return result ;
    }

    @Override
    public TeboPromoteRecordVO getCarSellOrderList(PromoteQueryDTO promoteQueryDTO){
        List<String> promotedPhoneList = new ArrayList<>();
        if (StringUtils.isEmpty(promoteQueryDTO.getReferencePhone())){
            String unionId = AppletUtil.getUnionIdByRequest(request);
            R<TeboConsumer> teboConsumerR = remoteCustomerService.selectByUnionId(unionId);
            if (ObjectUtil.isEmpty(teboConsumerR) || ObjectUtil.isEmpty(teboConsumerR.getData())){
                throw new ServiceException("用户不存在");
            }
            promotedPhoneList.add(teboConsumerR.getData().getPhoneNumber());
        }else {
            promotedPhoneList.add(promoteQueryDTO.getReferencePhone());
        }
        GiftPackOrderQueryDTO giftPackOrderQueryDTO = new GiftPackOrderQueryDTO();
        List<Integer> typeList = new ArrayList<>();
        typeList.add(6);
        giftPackOrderQueryDTO.setTypeList(typeList);
        giftPackOrderQueryDTO.setReferencePhoneList(promotedPhoneList);
        List<TeboGiftPackOrderDO> giftPackList = giftPackOrderMapper.selectByReferencePhone(giftPackOrderQueryDTO);
        if (CollectionUtils.isEmpty(giftPackList)){
            return null;
        }
        List<TeboPromoteOrderVO> orderList = BeanConvert.copyList(giftPackList,TeboPromoteOrderVO::new);
        TeboCustomerQueryDTO queryDTO = new TeboCustomerQueryDTO();
        Map<String,TeboConsumer> teboCustomerMap = new HashMap<>();
        List<String> unionIdList = orderList.stream().map(item ->item.getUnionId()).collect(Collectors.toList());
        queryDTO.setUnionIdList(unionIdList);
        R<List<TeboConsumer>> customerRList = remoteCustomerService.selectByUnionIdList(queryDTO);
        if (ObjectUtil.isNotEmpty(customerRList) && !CollectionUtils.isEmpty(customerRList.getData())){
            List<TeboConsumer> customerList = customerRList.getData();
            teboCustomerMap = DataUtils.listToMap(customerList, TeboConsumer::getUnionid);
        }
        Map<String, TeboConsumer> finalTeboCustomerMap = teboCustomerMap;
        orderList.forEach(item ->{
            TeboConsumer consumer = finalTeboCustomerMap.get(item.getUnionId());
            if (ObjectUtil.isNotEmpty(consumer)){
                item.setPhoneNumber(consumer.getPhoneNumber());
            }
        });
        TeboPromoteRecordVO teboPromoteRecordVO = new TeboPromoteRecordVO();
        teboPromoteRecordVO.setOrderList(orderList);
        teboPromoteRecordVO.setOrderCount(orderList.size());
        Long writeOffCount = giftPackList.stream().filter(item ->item.getVerificationStatus() == 1).count();
        teboPromoteRecordVO.setOrderWriteOffCount(writeOffCount);
        teboPromoteRecordVO.setOrderList(orderList);
        return teboPromoteRecordVO ;
    }
    @Override
    public TeboPromoteRecordVO getByPromotedPhone(PromoteQueryDTO queryDTO){
        TeboPromoteRecordDTO param = new TeboPromoteRecordDTO();
        param.setPromotedPhone(queryDTO.getReferencePhone());
        param.setChannel(1);
        TeboPromoteRecordDO promoteRecordDO = teboPromoteRecordMapper.getPromoteRecord(param);
        if (ObjectUtil.isEmpty(promoteRecordDO)){
            return null;
        }
        TeboPromoteRecordVO  teboPromoteRecordVO = new TeboPromoteRecordVO();
        BeanConvert.copy(promoteRecordDO,teboPromoteRecordVO);
       return teboPromoteRecordVO;
    }

    @Override
    public void updatePromotedPersonName(PromoteQueryDTO promoteQueryDTO) {
        TeboPromoteRecordDO promoteRecordDO = teboPromoteRecordMapper.selectById(promoteQueryDTO.getId());
        if (ObjectUtil.isEmpty(promoteRecordDO)){
            throw new ServiceException("记录不存在");
        }
        promoteRecordDO.setPromotedName(promoteQueryDTO.getPromotedName());
        teboPromoteRecordMapper.updateById(promoteRecordDO);
    }
    @Override
    @Transactional
    public void activeCarSaleChannel(TeboPromoteRecordDTO teboPromoteRecordDTO){
        SysUser sysUser = new SysUser();
        sysUser.setUserName(teboPromoteRecordDTO.getPartnerCode());
        sysUser.setUserType(0);
        R<List<SysUser>> userR = remoteUserService.listUser(sysUser);
        if (ObjectUtil.isEmpty(userR) || CollectionUtils.isEmpty(userR.getData())){
          throw new ServiceException("抱歉，您的老板还不是合伙人，您无法激活该身份！");
        }
        List<SysUser> userList = userR.getData();
        SysUser user = userList.get(0);
        Long tenantId = user.getTenantId();
        if (ObjectUtil.isEmpty(tenantId)){
            throw new ServiceException("合伙人不存在");
        }
        R<TeboPartnerInfoVO> partnerInfoVOR = remotePartnerService.getById(tenantId);
        TeboPartnerInfoVO teboPartnerInfoVO = partnerInfoVOR.getData();
        if (ObjectUtil.isEmpty(teboPartnerInfoVO)){
            throw new ServiceException("合伙人不存在");
        }
        R<TeboShop> teboShopR = remoteShopService.selectByNewTenantId(teboPartnerInfoVO.getId());
        if (ObjectUtil.isEmpty(teboShopR) || ObjectUtil.isEmpty(teboShopR.getData()) || ObjectUtil.isEmpty(teboShopR.getData())){
            throw new ServiceException("尚未激活超服店");
        }
        PromoteQueryDTO promoteQueryDTO = new PromoteQueryDTO();
        TeboPromoteRecordDO record = teboPromoteRecordMapper.selectByUnionId(teboPromoteRecordDTO.getUnionId());
        if (ObjectUtil.isNotEmpty(record)){
            throw new ServiceException("您已存在分享身份，无法再激活新身份");
        }
        promoteQueryDTO.setReferencePhone(teboPartnerInfoVO.getLinkPhone());
        promoteQueryDTO.setChannel(2);
        List<TeboPromoteRecordDO> list = teboPromoteRecordMapper.getPromotedPersonList(promoteQueryDTO);
        if (!CollectionUtils.isEmpty(list) && list.size() >= 25 && teboPromoteRecordDTO.getSource() == 1){
            throw new ServiceException("抱歉，您的老板员工绑定人数已达上限25人");
        }
        TeboPromoteRecordDO teboPromoteRecordDO = new TeboPromoteRecordDO();
        /**
         * 推广人手机号
         */
        teboPromoteRecordDO.setReferencePhone(teboPartnerInfoVO.getLinkPhone());
        R<TeboConsumer> consumerR = remoteCustomerService.selectByUnionId(teboPromoteRecordDTO.getUnionId());
        teboPromoteRecordDO.setId(SnowFlakeUtil.nextId());

        /**
         * 被推广人手机号
         */
        if (StringUtils.isEmpty(teboPromoteRecordDTO.getPromotedPhone())){
            teboPromoteRecordDO.setPromotedPhone(consumerR.getData().getPhoneNumber());
            /**
             * 被推广者微信标识
             */
            teboPromoteRecordDO.setUnionId(teboPromoteRecordDTO.getUnionId());
        }else {
            teboPromoteRecordDO.setPromotedPhone(teboPromoteRecordDTO.getPromotedPhone());
            R<TeboConsumer> consumerR1 = remoteCustomerService.selectByPhoneNumber(teboPromoteRecordDTO.getPromotedPhone());
            if (ObjectUtil.isNotEmpty(consumerR1) && ObjectUtil.isNotEmpty(consumerR1.getData())){
                teboPromoteRecordDO.setUnionId(consumerR1.getData().getUnionid());
            }
        }
        teboPromoteRecordDO.setChannel(2);
        teboPromoteRecordDO.setReferenceWalletId(teboShopR.getData().getWalletId());
        teboPromoteRecordDO.setPromotedName(teboPromoteRecordDTO.getPromotedName());
        teboPromoteRecordDO.setPartnerCode(teboPromoteRecordDTO.getPartnerCode());
        teboPromoteRecordMapper.insert(teboPromoteRecordDO);
        R<TeboConsumer> teboConsumerR = remoteCustomerService.selectByUnionId(teboPromoteRecordDTO.getUnionId());
        TeboConsumer consumer = teboConsumerR.getData();
        if (ObjectUtil.isEmpty(consumer)){
            throw new ServiceException("用户不存在");
        }
        TeboCustomerUpdateDTO teboCustomerDTO = new TeboCustomerUpdateDTO();
        BeanConvert.copy(consumer,teboCustomerDTO);
        teboCustomerDTO.setChannel(2);
        remoteCustomerService.updateCustomerInfo(teboCustomerDTO);
    }
}