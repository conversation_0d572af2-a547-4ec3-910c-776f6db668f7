package com.tebo.rescue.service;

import com.tebo.rescue.applet.domain.dto.group.TeboGroupPurchaseOrderDTO;
import com.tebo.rescue.applet.domain.view.TeboGroupReceiveOrderVO;
import com.tebo.rescue.web.domain.dto.groupOrder.TeboGroupOrderDTO;
import com.tebo.rescue.web.domain.dto.groupOrder.TeboGroupOrderQueryDTO;
import com.tebo.rescue.web.domain.view.TeboGroupOrderVO;

import java.util.List;

public interface TeboGroupOrderService {
    /**
     * pc创建订单
     */
    Long createOrder(TeboGroupOrderDTO teboGroupOrderDTO);

    /**
     * 创建B端团购订单
     */
    Long createAppletOrder(TeboGroupOrderDTO teboGroupOrderDTO);

    /**
     * 查询订单
     */
   List<TeboGroupOrderVO> getGroupOrderList(TeboGroupOrderQueryDTO teboGroupOrderQueryDTO);

    /**
     * 删除订单
     */
   void delete(Long id);

    /**
     * 订单详情
     */
    TeboGroupOrderVO getTeboGroupOrderDetail(Long id);

    /**
     * pc端消费者领取卡券
     */
    String receive(TeboGroupPurchaseOrderDTO teboGroupPurchaseOrderDTO);

    /**
     * 泰博出行商家版-消费者领取卡券
     */
    TeboGroupReceiveOrderVO merchantReceive(TeboGroupPurchaseOrderDTO teboGroupPurchaseOrderDTO);
}