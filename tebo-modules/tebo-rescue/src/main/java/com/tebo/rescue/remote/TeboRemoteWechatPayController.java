package com.tebo.rescue.remote;

import com.alibaba.fastjson.JSONObject;
import com.tebo.common.core.domain.R;
import com.tebo.rescue.applet.domain.dto.GiftPackOrderAfterPayDTO;
import com.tebo.rescue.applet.domain.dto.ServiceOrderAfterPayDTO;
import com.tebo.rescue.service.pay.TeboWechatPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024/1/19 13:18
 * @Desc : 微信支付回调接口
 */
@RestController
@Slf4j
@RequestMapping("/open/wechat/pay")
public class TeboRemoteWechatPayController {

    @Resource
    private TeboWechatPayService teboWechatPayService;

    /**
     * 工单支付成功后回调
     * @param afterPayDTO
     * @return
     */
    @PostMapping("/payOrderAfterNotify")
    public R<Boolean> payOrderAfterNotify(@RequestBody ServiceOrderAfterPayDTO afterPayDTO) {
        return R.ok(teboWechatPayService.payOrderAfterNotify(afterPayDTO));
    }

    /**
     * 团购订单支付回调
     */
    @PostMapping("/groupPackOrderAfterNotify")
    public R<Boolean> groupPackOrderAfterNotify(@RequestBody GiftPackOrderAfterPayDTO afterPayDTO) {
        log.info("groupPackOrderAfterNotify param:{}", JSONObject.toJSONString(afterPayDTO));
        return R.ok(teboWechatPayService.groupPackOrderAfterNotify(afterPayDTO));
    }

    /**
     * 订单支付回调
     * @param afterPayDTO
     * @return
     */
    @PostMapping("/payGiftPackAfterNotify")
    public R<Boolean> payGiftPackAfterNotify(@RequestBody GiftPackOrderAfterPayDTO afterPayDTO) {
        log.info("payGiftPackAfterNotify param:{}", JSONObject.toJSONString(afterPayDTO));
        return R.ok(teboWechatPayService.payGiftPackAfterNotify(afterPayDTO));
    }

    /**
     * 工单支付成功后回调
     * @param afterPayDTO
     * @return
     */
    @PostMapping("/payRescueFeeAfterNotify")
    public R<Boolean> payRescueFeeAfterNotify(@RequestBody ServiceOrderAfterPayDTO afterPayDTO) {
        return R.ok(teboWechatPayService.payRescueFeeAfterNotify(afterPayDTO));
    }

}
