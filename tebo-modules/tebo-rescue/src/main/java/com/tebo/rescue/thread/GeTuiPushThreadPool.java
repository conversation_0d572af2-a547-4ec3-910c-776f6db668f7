package com.tebo.rescue.thread;

import com.alibaba.ttl.threadpool.TtlExecutors;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.concurrent.*;

public class GeTuiPushThreadPool {
    private static final ExecutorService threadPoolExecutor;

    static{
        ThreadFactory facedeQueryThreadFactory = new CustomizableThreadFactory("GeTuiPushThreadPool-");

        ThreadPoolExecutor naivePool = new ThreadPoolExecutor(2,
                4,
                1,
                TimeUnit.HOURS,
                new ArrayBlockingQueue<Runnable>(100),
                facedeQueryThreadFactory, new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolExecutor = TtlExecutors.getTtlExecutorService(naivePool);
    }

    public static <T> Future<T> submitRtnFuture(Callable<T>  callable){
        return threadPoolExecutor.submit(callable);
    }

    public static ExecutorService thisPool(){
        return threadPoolExecutor;
    }
}
