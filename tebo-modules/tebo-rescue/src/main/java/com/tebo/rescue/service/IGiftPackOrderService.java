package com.tebo.rescue.service;

import com.tebo.rescue.api.domain.dto.TeboGiftPackOrderDTO;
import com.tebo.rescue.api.domain.dto.TeboRemoteGiftOrderDTO;
import com.tebo.rescue.applet.domain.dto.GiftPackOrderDTO;
import com.tebo.rescue.web.domain.view.TeboGiftPackOrderVO;
import com.tebo.rescue.web.domain.view.TeboJiFenAccountVO;

import java.util.List;

/**
 * @Author：zhengmk
 * @Date 2023/12/24 9:24
 */
public interface IGiftPackOrderService {

    /**
     * 创建礼包订单
     */
    Long createGiftOrder(GiftPackOrderDTO packOrderDTO);

    /**
     * 创建合并订单
     */
    Long createMergeOrder(TeboRemoteGiftOrderDTO orderDTO);

    /**
     * 团购:礼包订单
     */
    Long createGroupGiftOrder(GiftPackOrderDTO packOrderDTO);

    /**
     * 用户是否购买订单列表
     * @param unionId
     * @return
     */
    Boolean getGiftPackOrderList(String unionId);

    /**
     * 是否购买过久久卡
     */
    TeboJiFenAccountVO isVip(String unionId);

    /**
     * 某个用户会员礼包
     * @param unionId
     * @return
     */
    List<TeboGiftPackOrderVO> getList(String unionId);


    /**
     * @param orderNo
     * @return
     */
    Boolean refundGiftPack(String orderNo, String unionId);

    /**
     * 更新订单核销状态
     */
    void updateOrderStatus(TeboGiftPackOrderDTO giftPackOrderDTO);

    /**
     * 更新久久券订单状态为已退款
     * @param giftPackOrderNo 久久券订单号
     * @return
     */
    Boolean updateOrderStatusToRefunded(String giftPackOrderNo);
}
