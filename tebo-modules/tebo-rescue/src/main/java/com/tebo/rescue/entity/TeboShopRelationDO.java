package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 泰博门店表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-29
 */
@Getter
@Setter
@TableName("tebo_shop_relation")
public class TeboShopRelationDO extends Model<TeboShopRelationDO> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 泰博门店id
     */
    @TableField("shop_id")
    private String shopId;

    /**
     * CRMS账号
     */
    @TableField("shop_code")
    private String shopCode;

    /**
     * 门店名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 手机号
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 老板名称
     */
    @TableField("boss_name")
    private String bossName;

    /**
     * 商名称
     */
    @TableField("upper_name")
    private String upperName;

    /**
     * 商CRMS账号
     */
    @TableField("upper_code")
    private String upperCode;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 授权时间
     */
    @TableField("apply_time")
    private LocalDateTime applyTime;

    /**
     * 钜惠版1未占用2已占用
     */
    @TableField("low_status")
    private Integer lowStatus;


    /**
     * 安心版1未占用2已占用
     */
    @TableField("senior_status")
    private Integer seniorStatus;

    /**
     * 钜惠版订单id
     */
    @TableField("low_id")
    private Long lowId;

    /**
     * 安心版订单id
     */
    @TableField("senior_id")
    private Long seniorId;

    /**
     * 云门店id
     */
    @TableField("cloud_shop_id")
    private String cloudShopId;

    /**
     * 云门店手机号
     */
    @TableField("cloud_shop_phone")
    private String cloudShopPhone;

    /**
     * 云门店名称
     */
    @TableField("cloud_shop_name")
    private String cloudShopName;

    /**
     * 云门店老板名称
     */
    @TableField("cloud_shop_boss")
    private String cloudShopBoss;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
