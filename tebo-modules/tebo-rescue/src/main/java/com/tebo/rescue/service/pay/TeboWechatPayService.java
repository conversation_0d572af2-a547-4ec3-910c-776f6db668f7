package com.tebo.rescue.service.pay;

import com.tebo.common.core.domain.R;
import com.tebo.mall.api.domain.dto.TeboGeneralPayNotifyDTO;
import com.tebo.rescue.applet.domain.dto.GiftPackOrderAfterPayDTO;
import com.tebo.rescue.applet.domain.dto.RescueQueueOrderSaveDTO;
import com.tebo.rescue.applet.domain.dto.ServiceOrderAfterPayDTO;
import com.tebo.rescue.applet.domain.dto.ServiceOrderPayDTO;
import com.tebo.system.api.domain.view.WechatPrepayResponse;

/**
 * <AUTHOR> Zhang<PERSON>an
 * @date : 2024/1/19 13:02
 * @Desc : 微信支付服务
 */
public interface TeboWechatPayService {

    /**
     * 工单预支付
     * @param orderId
     * @return
     */
    R<WechatPrepayResponse> serviceOrderPrePay(Long orderId);

    /**
     * 快捷开单
     * @param orderId
     * @return
     */
    R<WechatPrepayResponse> quickBillServiceOrderPrePay(Long orderId,String jsCode);

    /**
     * 工单支付成功后回调
     * @param
     * @return
     */
    Boolean payOrderAfterNotify(ServiceOrderAfterPayDTO afterPayDTO);


    /**
     * 礼包预支付
     * @param packId
     * @param unionId
     * @return
     */
    R<WechatPrepayResponse> giftPackPrePay(Long orderId, String unionId) throws InterruptedException;

    /**
     *
     */
    R<WechatPrepayResponse> groupOrderPrePay(Long orderId, String unionId) throws InterruptedException;

    R<WechatPrepayResponse> groupOrderPrePayMerchant(Long orderId, String unionId,String openId) throws InterruptedException;


    /**
     * 礼包支付成功后回调
     * @param
     * @return
     */
    Boolean payGiftPackAfterNotify(GiftPackOrderAfterPayDTO afterPayDTO);

    /**
     * 团购订单支付回调
     */
    Boolean groupPackOrderAfterNotify(GiftPackOrderAfterPayDTO afterPayDTO);

    /**
     * 预支付-维修师傅代付工单
     * @param payDTO
     * @return
     */
    R<WechatPrepayResponse> serviceOrderBehalfPrePay(ServiceOrderPayDTO payDTO);


    /**
     * 预支付-救援上门服务费
     * @param
     * @return
     */
    R<WechatPrepayResponse> rescueOrderBehalfPrePay(RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO);


    /**
     * 上门服务费支付成功后回调
     * @param
     * @return
     */
    Boolean payRescueFeeAfterNotify(ServiceOrderAfterPayDTO afterPayDTO);

    /**
     * 通用汇富支付回调接口
     */
    R<Boolean> generalHfPayNotify(TeboGeneralPayNotifyDTO afterPayDTO);
}
