package com.tebo.rescue.mapper;

import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.rescue.api.domain.dto.TeboPromoteRecordDTO;
import com.tebo.rescue.entity.TeboPromoteRecordDO;
import com.tebo.rescue.web.domain.dto.promote.PromoteQueryDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 泰博出行小程序推广记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Mapper
public interface TeboPromoteRecordMapper extends TeboBaseMapper<TeboPromoteRecordDO> {
    /**
     * 根据手机号和 类型查询
     */
    TeboPromoteRecordDO getPromoteRecord(TeboPromoteRecordDTO teboPromoteRecordDTO);

    TeboPromoteRecordDO selectByUnionId(String unionId);

    /**
     * 被推荐人列表
     */
    List<TeboPromoteRecordDO> getPromotedPersonList(PromoteQueryDTO promoteQueryDTO);
}
