package com.tebo.rescue.remote.domain;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class LstOrderDetailVO {

    /**
     * 订单id
     */
    private Long id;
    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 服务类型
     */
    private Integer serviceType;
    /**
     * 订单状态 0待分配 1待接单 2服务中 3已完结 4已核销 5已结算 6已提现 7挂起 8已取消 9服务中-待审核
     */
    private Integer orderStatus;
    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 平台备注
     */
    private String remarkPlatform;

    /**
     * 用户备注
     */
    private String remarkUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 结算时间
     */
    private LocalDateTime settleDate;

}
