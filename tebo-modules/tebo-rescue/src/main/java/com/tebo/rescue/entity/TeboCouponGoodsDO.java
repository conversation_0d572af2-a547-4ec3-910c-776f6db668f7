package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 卡券可用商品表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-22
 */
@Getter
@Setter
@TableName("tebo_coupon_goods")
public class TeboCouponGoodsDO extends Model<TeboCouponGoodsDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 卡券ID
     */
    @TableField("coupon_id")
    private Long couponId;

    /**
     * 商品ID
     */
    @TableField("goods_id")
    private Long goodsId;

    /**
     * 商品货号
     */
    @TableField("goods_no")
    private String goodsNo;


    @Override
    public Serializable pkVal() {
        return this.id;
    }


    public TeboCouponGoodsDO(Long couponId,Long goodsId,String goodsNo) {
        this.couponId = couponId;
        this.goodsNo = goodsNo;
        this.goodsId = goodsId;
    }
    public TeboCouponGoodsDO() {

    }
}
