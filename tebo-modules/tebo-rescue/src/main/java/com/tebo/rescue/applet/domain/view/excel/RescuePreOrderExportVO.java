package com.tebo.rescue.applet.domain.view.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.tebo.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 预约单导出
 */

@ColumnWidth(20)
@Data
public class RescuePreOrderExportVO {

    @ExcelIgnore
    private Long id;


    /**
     * 服务单号
     */
    @ExcelProperty("服务单号")
    private String orderNo;

    /**
     * 请求人姓名
     */
    @ExcelProperty("请求人姓名")
    private String nickName;

    /**
     * 请求人电话
     */
    @ExcelProperty("请求人电话")
    private String phoneNumber;

    /**
     * 预约类型
     */
    @ExcelProperty("预约类型")
    private String orderTypeName;
    @ExcelIgnore
    private Integer orderType;

    /**
     * 服务项目
     */
    @ExcelProperty("服务项目")
    private String serviceItemName;
    @ExcelIgnore
    private Integer serviceItem;

    /**
     * 预约上门时间
     */
    @ExcelProperty("预约时间")
    private LocalDateTime preDoorTime;


    /**
     * 服务单状态
     */
    @ExcelProperty("服务单状态")
    private String orderStatusName;
    @ExcelIgnore
    private Integer orderStatus;



    /**
     * 故障
     */
    @ExcelProperty("请求简述")
    private String faultMsg;

    /**
     * 请求位置
     */
    @ExcelProperty("请求位置")
    private String requestLocation;


    /**
     * 预约门店
     */
    @ExcelProperty("预约门店")
    private String shopName;



    /**
     * 门店类型 0:旗舰店 1:标准店 2:社区店
     */
    @ExcelProperty("门店类型")
    private String shopTypeName;


    // 创建时间
    @ExcelProperty("发起时间")
    private LocalDateTime createTime;


    @ExcelProperty("取消人员")
    private String cancelBy;

    /**
     * 取消时间
     */
    @ExcelProperty("取消时间")
    private LocalDateTime cancelTime;


}
