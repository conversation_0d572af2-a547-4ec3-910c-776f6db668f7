package com.tebo.rescue.applet.controller;

import com.tebo.common.core.domain.R;
import com.tebo.rescue.applet.domain.dto.CommentOrderDTO;
import com.tebo.rescue.service.ICommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/comment")
public class CommentController {

    @Autowired
    private ICommentService commentService;

    /**
     * 对工单进行评价
     * @return
     */
    @PostMapping("/serviceOrder")
    public R commentOrder(@RequestBody CommentOrderDTO commentOrderDTO) {
        commentService.commentServiceOrder(commentOrderDTO);
        return R.ok();
    }
}
