package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 泰博出行小程序推广记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Getter
@Setter
@TableName("tebo_promote_record")
public class TeboPromoteRecordDO extends Model<TeboPromoteRecordDO> {

    private static final long serialVersionUID = 1L;

    @TableField("id")
    private Long id;

    /**
     * 推广人手机号
     */
    @TableField("reference_phone")
    private String referencePhone;

    /**
     * 合伙人编码
     */
    @TableField("partner_code")
    private String partnerCode;

    /**
     * 渠道 1:异业 2：车销
     */
    @TableField("channel")
    private Integer channel;

    /**
     * 推广人门店id
     */
    @TableField("reference_shop_id")
    private Long referenceShopId;

    /**
     * 推广人钱包id
     */
    @TableField("reference_wallet_id")
    private Long referenceWalletId;

    /**
     * 被推广人手机号
     */
    @TableField("promoted_phone")
    private String promotedPhone;

    /**
     * 被推广人姓名
     */
    @TableField("promoted_name")
    private String promotedName;

    /**
     * 用户微信唯一标识
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT)
    private LocalDateTime updateTime;


    @Override
    public Serializable pkVal() {
        return null;
    }

}
