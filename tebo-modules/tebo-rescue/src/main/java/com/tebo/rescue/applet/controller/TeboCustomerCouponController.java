package com.tebo.rescue.applet.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.domain.AjaxResult;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.util.applet.AppletUtil;
import com.tebo.common.util.number.DistanceUtil;
import com.tebo.rescue.applet.domain.dto.coupon.TeboCouponAllStaffDTO;
import com.tebo.rescue.applet.domain.dto.coupon.TeboCouponCheckDTO;
import com.tebo.rescue.applet.domain.dto.coupon.TeboCouponMallCheckDTO;
import com.tebo.rescue.applet.domain.dto.coupon.TeboCouponShareDTO;
import com.tebo.rescue.applet.domain.view.TeboShopConsumerVO;
import com.tebo.rescue.entity.TeboCouponCustomerDO;
import com.tebo.rescue.enums.CouponIdEnum;
import com.tebo.rescue.service.TeboCouponCustomerService;
import com.tebo.rescue.service.TeboGiftPackService;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponCustomerQueryDTO;
import com.tebo.rescue.web.domain.view.TeboCouponCustomerVO;
import com.tebo.rescue.web.domain.view.TeboGiftPackOrderVO;
import com.tebo.rescue.web.domain.view.TeboGiftPackVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ZhangFan
 * @date : 2023/12/14 18:36
 * @Desc : C 端消费者卡券接口
 */
@Slf4j
@RestController
@RequestMapping("/customer/coupon")
public class TeboCustomerCouponController extends BaseController {

    @Resource
    private TeboCouponCustomerService couponCustomerService;
    @Resource
    private HttpServletRequest request;
    /**
     * 我的卡券列表
     * @return
     */
    @PostMapping("/getList")
    public TableDataInfo getList(@RequestBody TeboCouponCustomerQueryDTO query) {
        String unionId = AppletUtil.getUnionIdByRequest(request);
        query.setUnionId(unionId);
        Page page = PageHelper.startPage(query.getPageNum(), query.getPageSize());
        if (ObjectUtils.isNotEmpty(query.getStatus()) && query.getStatus() == 20) {
            query.setStatusStr(20);
        }
        return getDataTable(couponCustomerService.getList(query), page);
    }

    /**
     * 我的电池券
     * @param query
     * @return
     */
    @PostMapping("/getCouponList")
    public TableDataInfo getCouponList(@RequestBody TeboCouponCustomerQueryDTO query) {
        String unionId = AppletUtil.getUnionIdByRequest(request);
        query.setUnionId(unionId);
        Page page = PageHelper.startPage(query.getPageNum(), query.getPageSize());
        if (ObjectUtils.isNotEmpty(query.getStatus()) && query.getStatus() == 20) {
            query.setStatusStr(20);
        }
        if (ObjectUtils.isNotEmpty(query.getStatus()) && query.getStatus() == 40) {
            query.setStatusStr(40);
        }
        List<Long> couponIdList = new ArrayList<>();
        couponIdList.add(Long.parseLong(CouponIdEnum.JU_HUI_BATTERY.getCode()));
        couponIdList.add(Long.parseLong(CouponIdEnum.AN_XIN_BATTERY.getCode()));
        // 11.14 老电池券
        couponIdList.add(1839262014409539584L);
        // 3.26 专攻品 20元券
        couponIdList.add(10001L);
        query.setCouponIdList(couponIdList);
        return getDataTable(couponCustomerService.getBatteryList(query), page);
    }

    /**
     * 我的大米券
     */
    @PostMapping("/getRiceCouponList")
    public TableDataInfo getRiceCouponList(@RequestBody TeboCouponCustomerQueryDTO query) {
        String unionId = AppletUtil.getUnionIdByRequest(request);
        query.setUnionId(unionId);
        Page page = PageHelper.startPage(query.getPageNum(), query.getPageSize());
        return getDataTable(couponCustomerService.getRiceCouponList(query), page);
    }

    /**
     * 获取用户未消费卡券数量
     * todo 重写sql
     */
    @GetMapping("/getCustomerCouponNumber")
    public R getCustomerCouponNumber(){
        String unionId = AppletUtil.getUnionIdByRequest(request);
        return R.ok(couponCustomerService.getCustomerCouponNumber(unionId));
    }

    /**
     * 我的会员卡券
     * @return
     */
    @PostMapping("/getMemberList")
    public AjaxResult getMemberList(@RequestBody TeboCouponCustomerQueryDTO query) {
        String unionId = AppletUtil.getUnionIdByRequest(request);
        query.setUnionId(unionId);
        return success(couponCustomerService.getMemberList(query));
    }


    /**
     * 根据券号卡券详情
     * @param couponCode
     * @return
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo(@RequestParam("couponCode") String couponCode,@RequestParam("lgt") String lgt,@RequestParam("lnt") String lnt) {
        String couponCodeKey = "tebo_rescue:couponCode:" + couponCode;
//        if (redisService.hasKey(couponCodeKey)) {
//            TeboCouponCustomerVO customerVO = redisService.getCacheObject(couponCodeKey);
//            return success(customerVO);
//        }
        TeboCouponCustomerVO customerVO = couponCustomerService.getDetailByCouponCode(couponCode);
//        customerVO.getShopList().stream().forEach(shop -> {
//            shop.setDistance(DistanceUtil.getDistance(shop.getLatitude(), shop.getLongitude(), lgt, lnt));
//        });
        return success(customerVO);
        // 24.9.29 查详情去掉门店
//        if(StringUtils.isAnyBlank(lgt, lnt)) {
//            customerVO.setShopList(Collections.emptyList());
//            return success(customerVO);
//        }
//        List<TeboShopConsumerVO> shopList = customerVO.getShopList().stream().sorted(Comparator.comparing(TeboShopConsumerVO::getDistance)).collect(Collectors.toList());
//        customerVO.setShopList(shopList);
////        redisService.setCacheObject(couponCodeKey, customerVO, 1l, TimeUnit.DAYS);
//        return success(customerVO);
    }

    /**
     * 根据券号卡券详情
     *
     * @param couponCode
     * @return
     */
    @GetMapping("/getCouponShop")
    public AjaxResult getCouponShop(@RequestParam("couponCode") String couponCode, @RequestParam("lgt") String lgt, @RequestParam("lnt") String lnt) {
        List<TeboShopConsumerVO> shop = couponCustomerService.getCouponShop(couponCode, lgt, lnt);
        return success(shop);
    }
    /**
     * 卡券核销记录
     * @param couponCode
     * @return
     */
    @GetMapping("/getCouponRecord")
    public AjaxResult getCouponRecord(@RequestParam("couponCode") String couponCode) {
        return success(couponCustomerService.getCouponRecord(couponCode));
    }
    /**
     * 分享卡券
     * @param couponCode
     * @return
     */
    @GetMapping("/share")
    public AjaxResult share(@RequestParam("couponCode") String couponCode) {
        return success(couponCustomerService.shareCouponCode(couponCode));
    }

    /**
     * 取消分享卡券
     */
    @GetMapping("/cancelShare")
    public AjaxResult cancelShare(@RequestParam("couponCode") String couponCode) {
        return success(couponCustomerService.cancelShareCouponCode(couponCode));
    }

    /**
     * 领取卡券
     * @return
     */
    @PostMapping("/receive")
    public AjaxResult receive(@RequestBody TeboCouponShareDTO teboCouponShareDTO) {
        String unionId = AppletUtil.getUnionIdByRequest(request);
        teboCouponShareDTO.setUnionId(unionId);
        return success(couponCustomerService.receive(teboCouponShareDTO));
    }

    /**
     * 测试发放卡券
     * @param packId
     * @param unionId
     * @return
     */
    @GetMapping("/getGiftPack")
    public AjaxResult grantPackCoupon(@RequestParam("packId") Long packId, @RequestParam("unionId") String unionId,@RequestParam("orderNo") String orderNo) {
        return success(couponCustomerService.grantPackCoupon(packId, unionId,orderNo));
    }

    /**
     * 校验卡券是否符合核销规则
     * @param checkDTO
     * @return
     */
    @PostMapping("/checkCouponRule")
    public AjaxResult checkCouponRule(@RequestBody TeboCouponCheckDTO checkDTO) {
        return success(couponCustomerService.checkCouponRule(checkDTO));
    }

    /**
     * 全员发放卡券
     */
    @PostMapping("/grantAllStaffCoupon")
    public AjaxResult grantAllStaffCoupon(@RequestBody TeboCouponAllStaffDTO allStaffDTO) {
        couponCustomerService.grantAllStaffCoupon(allStaffDTO);
        return success();
    }
    /**
     * 用户的商城卡券（可用卡券/不可用卡券）
     * 电池卡券、卡券归属
     * @return
     */
    @PostMapping("/getMallCouponCustomerList")
    public AjaxResult getMallCouponCustomerList(@RequestBody TeboCouponMallCheckDTO checkDTO,HttpServletRequest request) {
//        String unionId = AppletUtil.getUnionIdByRequest(request);
//        checkDTO.setGoodsNo(checkDTO.getGoodsNoList().get(0));
       // String unionId = "ozRHL6VQXGp7aJbI3bQmh28fnBbA";
        return success(null);
//        return success(couponCustomerService.getMallCouponCustomerList(unionId, checkDTO));
    }

    /**
     * 我的骑行卡券
     */
    @PostMapping("/getCyclingInsuranceCouponList")
    public R getCyclingInsuranceCouponList(@RequestBody TeboCouponCustomerQueryDTO query) {
        String unionId = AppletUtil.getUnionIdByRequest(request);
        query.setUnionId(unionId);
        return R.ok(couponCustomerService.getCyclingInsuranceCouponList(query));
    }

    /**
     * 大米收货7天以后解冻资金
     */
    @GetMapping("/unfreezeRiceFunds/{orderNo}")
    public R<Boolean> unfreezeRiceFunds(@PathVariable("orderNo")String orderNo) {
        logger.info("unfreezeRiceFunds param :{}", orderNo);
        return R.ok(couponCustomerService.unfreezeRiceFunds(orderNo)) ;
    }

    @GetMapping("/getCustomerCouponByFlowId/{flowId}")
    public R<TeboGiftPackOrderVO> getCustomerCouponByFlowId(@PathVariable("flowId")Long flowId) {
        logger.info("getCustomerCouponByFlowId param :{}", flowId);
        return R.ok(couponCustomerService.getCustomerCouponByFlowId(flowId)) ;
    }

    /**
     * 专供品可用电池卡券
     * @return
     */
    @PostMapping("/getCouponCustomerList")
    public AjaxResult getCouponCustomerList(HttpServletRequest request) {
        String unionId = AppletUtil.getUnionIdByRequest(request);
////        checkDTO.setGoodsNo(checkDTO.getGoodsNoList().get(0));
//         String unionId = "ozRHL6VQXGp7aJbI3bQmh28fnBbA";
//        return success(null);
        return success(couponCustomerService.getCouponCustomerList(unionId));
    }
}
