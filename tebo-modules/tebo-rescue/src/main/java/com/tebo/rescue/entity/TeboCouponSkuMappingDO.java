package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 卡券可用商品表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Getter
@Setter
@TableName("tebo_coupon_sku_mapping")
public class TeboCouponSkuMappingDO extends Model<TeboCouponSkuMappingDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 卡券ID
     */
    @TableField("coupon_id")
    private Long couponId;

    /**
     * 商品id
     */
    @TableField("sku_id")
    private String skuId;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
