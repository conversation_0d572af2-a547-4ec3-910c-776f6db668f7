package com.tebo.rescue.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.CodeGenerator;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.rescue.applet.domain.view.TeboGiftPackConsumerNewVO;
import com.tebo.rescue.applet.domain.view.TeboGiftPackConsumerVO;
import com.tebo.rescue.entity.TeboGiftPackCouponDO;
import com.tebo.rescue.entity.TeboGiftPackDO;
import com.tebo.rescue.mapper.TeboGiftPackCouponMapper;
import com.tebo.rescue.mapper.TeboGiftPackMapper;
import com.tebo.rescue.service.TeboCouponService;
import com.tebo.rescue.service.TeboGiftPackService;
import com.tebo.rescue.web.domain.dto.pack.TeboGiftPackChangeStatusDTO;
import com.tebo.rescue.web.domain.dto.pack.TeboGiftPackCouponDTO;
import com.tebo.rescue.web.domain.dto.pack.TeboGiftPackCreateDTO;
import com.tebo.rescue.web.domain.dto.pack.TeboGiftPackQueryDTO;
import com.tebo.rescue.web.domain.dto.pack.TeboGiftPackUpdateDTO;
import com.tebo.rescue.web.domain.view.TeboGiftPackCouponVO;
import com.tebo.rescue.web.domain.view.TeboGiftPackVO;
import com.tebo.system.api.RemoteAreaService;
import com.tebo.system.api.domain.view.TeboAreaVO;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ZhangFan
 * @date : 2023/12/14 9:37
 * @Desc :
 */
@Slf4j
@Service
public class TeboGiftPackServiceImpl implements TeboGiftPackService {

    @Resource
    private TeboGiftPackMapper teboGiftPackMapper;
    @Resource
    private TeboGiftPackCouponMapper teboGiftPackCouponMapper;
    @Resource
    private TeboCouponService teboCouponService;
    @Resource
    private RedisService redisService;


    @Override
    @Transactional
    public Integer create(TeboGiftPackCreateDTO create) {
        if (checkInUse(create.getCoupons(), null)) {
            throw new RuntimeException("存在卡券已被使用");
        }
        TeboGiftPackDO giftPackDO = buildGiftPackDO(create);
        if (!Collections.isEmpty(create.getCoupons())) {
            create.getCoupons().forEach(coupon -> {
                // TODO 后面改为批量操作
                TeboGiftPackCouponDO couponVO = new TeboGiftPackCouponDO(coupon.getCouponId(), giftPackDO.getId(), coupon.getNum());
                teboGiftPackCouponMapper.insert(couponVO);
            });
        }
        // // 约定 000 为全国
        // if ("000".equals(create.getArea())) {
        //     giftPackDO.setAreaCode("000");
        //     giftPackDO.setAreaName("全国");
        // }else {
        //     String[] areaCodeArr = create.getArea().split(",");
        //     giftPackDO.setAreaCode(areaCodeArr[2]);
        //     TeboAreaVO areaVO = teboRemoteAreaService.selectAreaByCode(create.getArea()).getData();
        //     giftPackDO.setAreaName(areaVO.getProvince() + "-" + areaVO.getCity() + "-"  + areaVO.getDistrict());
        // }
        // 更新卡券为已占用
        teboCouponService.batchCouponInUse(create.getCoupons().stream().map(TeboGiftPackCouponDTO::getCouponId).collect(Collectors.toList()));
        return teboGiftPackMapper.insert(giftPackDO);
    }

    @Override
    public List<TeboGiftPackVO> getGiftPackList(TeboGiftPackQueryDTO queryDTO) {
        List<TeboGiftPackDO> giftPackDOList = teboGiftPackMapper.getGiftPackList(queryDTO);
        if (CollectionUtils.isEmpty(giftPackDOList)) {
            return java.util.Collections.emptyList();
        }
        List<TeboGiftPackVO> result = BeanConvert.copyList(giftPackDOList, TeboGiftPackVO::new);
        return result;
    }

    @Override
    public TeboGiftPackVO getInfo(Long id) {
        TeboGiftPackVO result = new TeboGiftPackVO();
        TeboGiftPackDO giftPackDO = teboGiftPackMapper.selectById(id);
        List<TeboGiftPackCouponDO> coupons = teboGiftPackCouponMapper.selectByPackId(id);
        if (!Collections.isEmpty(coupons)) {
            Map<Long, Integer> couponMap = coupons.stream().collect(Collectors.toMap(TeboGiftPackCouponDO::getCouponId, TeboGiftPackCouponDO::getNum, (value1, value2) -> value1));
            List<TeboGiftPackCouponVO> couponVOS = teboCouponService.getCouponListByIds(coupons.stream().map(TeboGiftPackCouponDO::getCouponId).collect(Collectors.toList()));
            couponVOS.forEach(couponVO -> couponVO.setNum(couponMap.get(couponVO.getId())));
            result.setCoupons(couponVOS);
        }
        return BeanConvert.copy(giftPackDO, result);
    }

    @Override
    public TeboGiftPackConsumerNewVO getDetailById(Long id) {
        String key = "TEBO_RESCUE:PACK:DETAIL:"+id;
        String packDetail = redisService.getCacheObject(key);
        TeboGiftPackConsumerNewVO result = new TeboGiftPackConsumerNewVO();
        if (StringUtils.isNotEmpty(packDetail)){
            TeboGiftPackDO teboGiftPackDO = JSONObject.parseObject(packDetail,TeboGiftPackDO.class);
            BeanConvert.copy(teboGiftPackDO,result);
            result.setParValue(MoneyUtil.fenToYuan(teboGiftPackDO.getParValue()));
            result.setPrice(MoneyUtil.fenToYuan(teboGiftPackDO.getPrice()));
            return result;
        }
        TeboGiftPackDO giftPackDO = teboGiftPackMapper.selectById(id);
        List<TeboGiftPackCouponDO> coupons = teboGiftPackCouponMapper.selectByPackId(id);
        if (!Collections.isEmpty(coupons)) {
            Map<Long, Integer> couponMap = coupons.stream().collect(Collectors.toMap(TeboGiftPackCouponDO::getCouponId, TeboGiftPackCouponDO::getNum, (value1, value2) -> value1));
            List<TeboGiftPackCouponVO> couponVOS = teboCouponService.getCouponListByIds(coupons.stream().map(TeboGiftPackCouponDO::getCouponId).collect(Collectors.toList()));
            couponVOS.forEach(couponVO -> couponVO.setNum(couponMap.get(couponVO.getId())));
            result.setCoupons(couponVOS);
        }
        BeanConvert.copy(giftPackDO, result);
        redisService.setCacheObject(key, JSONObject.toJSONString(giftPackDO), 24L, TimeUnit.HOURS);
        result.setParValue(MoneyUtil.fenToYuan(giftPackDO.getParValue()));
        result.setPrice(MoneyUtil.fenToYuan(giftPackDO.getPrice()));
        return result;
    }

    @Override
    @Transactional
    public Integer update(TeboGiftPackUpdateDTO update) {
        TeboGiftPackDO giftPackDO = teboGiftPackMapper.selectById(update.getId());
        if (ObjectUtils.isEmpty(giftPackDO)) {
            return 0;
        }
        if (checkInUse(update.getCoupons(), giftPackDO.getId())) {
            throw new RuntimeException("存在卡券已被使用");
        }
        BeanConvert.copy(update, giftPackDO);
        if (!Collections.isEmpty(update.getCoupons())) {
            // 解除卡券占用
            List<TeboGiftPackCouponDO> coupons = teboGiftPackCouponMapper.selectByPackId(update.getId());
            teboCouponService.batchCouponNotInUse(coupons.stream().map(TeboGiftPackCouponDO::getCouponId).collect(Collectors.toList()));
            // 删除卡券关联
            teboGiftPackCouponMapper.deleteByPackId(giftPackDO.getId());
            update.getCoupons().forEach(coupon -> {
                // TODO 后面改为批量操作
                TeboGiftPackCouponDO couponVO = new TeboGiftPackCouponDO(coupon.getCouponId(), giftPackDO.getId(), coupon.getNum());
                teboGiftPackCouponMapper.insert(couponVO);
            });
        }
        // if (!StringUtils.isEmpty(update.getArea())) {
        //     // 约定 000 为全国
        //     if ("000".equals(update.getArea())) {
        //         giftPackDO.setAreaCode("000");
        //         giftPackDO.setAreaName("全国");
        //     }else {
        //         String[]  areaCodeArr=update.getArea().split(",");
        //         giftPackDO.setAreaCode(areaCodeArr[2]);
        //         TeboAreaVO areaVO = teboRemoteAreaService.selectAreaByCode(update.getArea()).getData();
        //         giftPackDO.setAreaName(areaVO.getProvince() + "-" + areaVO.getCity() + "-"  + areaVO.getDistrict());
        //     }
        //
        // }
        if (!StringUtils.isEmpty(update.getParValueStr())) {
            giftPackDO.setParValue(MoneyUtil.yuanToFen(update.getParValueStr()));
        }
        if (!StringUtils.isEmpty(update.getPriceStr())) {
            giftPackDO.setPrice(MoneyUtil.yuanToFen(update.getPriceStr()));
        }
        // 当修改发行量时，需要将卡券剩余量计算出来
        if (update.getPublishNum()!= null) {
            giftPackDO.setPublishNum(update.getPublishNum());
            // 计算剩余量
            Integer residueNum = update.getPublishNum() - giftPackDO.getUseNum();
            giftPackDO.setResidueNum(residueNum);
        }
        // 更新卡券为已占用
        teboCouponService.batchCouponInUse(update.getCoupons().stream().map(TeboGiftPackCouponDTO::getCouponId).collect(Collectors.toList()));
        if (update.getVest() == 0) {
            giftPackDO.setTenantId(0L);
        }
        return teboGiftPackMapper.updateById(giftPackDO);
    }

    @Override
    public Integer updateStatus(TeboGiftPackChangeStatusDTO update) {
        TeboGiftPackDO giftPackDO = teboGiftPackMapper.selectById(update.getId());
        if (ObjectUtils.isEmpty(giftPackDO)) {
            return 0;
        }
        giftPackDO.setStatus(update.getStatus());
        return teboGiftPackMapper.updateById(giftPackDO);
    }

    @Override
    public Integer reduceGiftPack(Long id) {
        TeboGiftPackDO giftPackDO = teboGiftPackMapper.selectById(id);
        giftPackDO.setResidueNum(giftPackDO.getResidueNum() - 1);
        giftPackDO.setUseNum(giftPackDO.getUseNum() + 1);
        return teboGiftPackMapper.updateById(giftPackDO);
    }

    private TeboGiftPackDO buildGiftPackDO(TeboGiftPackCreateDTO create) {
        String packCode = CodeGenerator.generateLBCode();
        Long mainId = SnowFlakeUtil.nextId();
        TeboGiftPackDO giftPackDO = new TeboGiftPackDO();
        BeanConvert.copy(create, giftPackDO);
        giftPackDO.setId(mainId);
        // // 默认为平台创建
        if (Objects.isNull(create.getTenantId())) {
            giftPackDO.setTenantId(0L);
        }
        giftPackDO.setPackCode(packCode);
        giftPackDO.setResidueNum(create.getPublishNum());
        giftPackDO.setParValue(MoneyUtil.yuanToFen(create.getParValueStr()));
        giftPackDO.setPrice(MoneyUtil.yuanToFen(create.getPriceStr()));
        // 默认微信支付0
        giftPackDO.setPayType(0);
        giftPackDO.setStatus(0);
        return giftPackDO;
    }

    /**
     * 卡券是否被礼包占用
     *
     * @param coupons
     * @param packId
     * @return
     */
    private boolean checkInUse(List<TeboGiftPackCouponDTO> coupons, Long packId) {
        AtomicReference<Boolean> result = new AtomicReference<>(false);
        // 新增，只需看卡券占用状态
        if (Objects.isNull(packId)) {
            List<TeboGiftPackCouponVO> couponVOS = teboCouponService.getCouponListByIds(coupons.stream().map(TeboGiftPackCouponDTO::getCouponId).collect(Collectors.toList()));
            couponVOS.forEach(coupon -> {
                // 存在一个被占用则改礼包不可创建
                if (coupon.getInPack() == 1) {
                    result.set(true);
                }
            });
            return result.get();
        } else {
            // 修改，如果卡券被占用且占用id不等于礼包id则改礼包不可创建
            List<Long> couponIds = coupons.stream().map(TeboGiftPackCouponDTO::getCouponId).collect(Collectors.toList());
            List<TeboGiftPackCouponDO> giftPackCouponList = teboGiftPackCouponMapper.selectByCouponIds(couponIds);
            if (!Collections.isEmpty(giftPackCouponList)) {
                giftPackCouponList.forEach(packCouponDO -> {
                    // 存在一个被占用则改礼包不可创建
                    if (!packCouponDO.getPackId().equals(packId)) {
                        result.set(true);
                    }
                });
            }
            return result.get();
        }
    }
}
