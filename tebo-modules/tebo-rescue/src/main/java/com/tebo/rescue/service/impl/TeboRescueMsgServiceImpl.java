package com.tebo.rescue.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.rescue.entity.TeboRescueMsgDO;
import com.tebo.rescue.manager.TeboRescueMsgManager;
import com.tebo.rescue.service.TeboRescueMsgService;
import com.tebo.rescue.web.domain.msg.TeboRescueMsgQueryDTO;
import com.tebo.rescue.web.domain.view.TeboRescueMsgVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class TeboRescueMsgServiceImpl implements TeboRescueMsgService {

    @Resource
    private TeboRescueMsgManager teboRescueMsgManager;

    @Override
    public List<TeboRescueMsgVO> getNotReadMsgList(TeboRescueMsgQueryDTO queryDTO) {
        List<TeboRescueMsgDO> list = teboRescueMsgManager.getMsgList(queryDTO);
        if (CollectionUtil.isEmpty(list)){
           return null;
        }
        return BeanConvert.copyList(list,TeboRescueMsgVO::new);
    }

    @Override
    public void updateMsgStatus(Long id) {
        teboRescueMsgManager.updateMsgStatus(id);
    }
}