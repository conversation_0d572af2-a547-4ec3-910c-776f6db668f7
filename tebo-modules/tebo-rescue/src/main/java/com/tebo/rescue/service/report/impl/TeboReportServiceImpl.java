package com.tebo.rescue.service.report.impl;

import com.tebo.common.core.domain.R;
import com.tebo.common.redis.service.RedisService;
import com.tebo.mall.api.RemoteMallReportService;
import com.tebo.mall.api.domain.view.MallPartnerReportVO;
import com.tebo.mall.api.domain.view.TeboIndexReportMallVO;
import com.tebo.rescue.api.domain.view.TeboIndexReportRescueVO;
import com.tebo.rescue.applet.domain.view.report.TeboReportPartnerVO;
import com.tebo.rescue.mapper.TeboReportMapper;
import com.tebo.rescue.service.report.TeboReportService;
import com.tebo.rescue.web.domain.view.TeboIndexReportVO;
import com.tebo.system.api.RemoteSystemReportService;
import com.tebo.system.api.domain.view.TeboSystemIndexReportVO;
import com.tebo.system.api.model.SystemPartnerReportVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> ZhangFan
 * @date : 2024/6/3 10:58
 * @Desc :
 */
@Slf4j
@Service
public class TeboReportServiceImpl implements TeboReportService {

    @Resource
    private TeboReportMapper teboReportMapper;
    @Resource
    private RemoteSystemReportService remoteSystemReportService;
    @Resource
    private RemoteMallReportService remoteMallReportService;
    @Resource
    private RedisService redisService;

    @Override
    public TeboReportPartnerVO partnerTotalInfo(Long tenantId) {
        // String key = "partnerTotalInfo:" + tenantId;
        // if (redisService.hasKey(key)) {
        //     return (TeboReportPartnerVO) redisService.getCacheObject(key);
        // }
        TeboReportPartnerVO result = new TeboReportPartnerVO();
//        R<SystemPartnerReportVO> sysRes = remoteSystemReportService.getPartnerReport(tenantId);
//        SystemPartnerReportVO systemPartnerReportVO = sysRes.getData();
        result.setPartnerNum(1);
        result.setShopNum(0);
        result.setAccountNum(0);
        result.setCustomerNum(0);

//        TeboReportPartnerVO teboOrder = teboReportMapper.selectPartnerReport(tenantId);
        result.setTodayOrderNum(0);
        result.setTotalOrderNum(0);
        result.setTodayArriveOrderNum(0);
        result.setTodayRescueOrderNum(0);
        result.setTotalRescueOrderNum(0);

//        R<MallPartnerReportVO> mallRes = remoteMallReportService.getPartnerReport(tenantId);
//        MallPartnerReportVO mallPartnerReportVO = mallRes.getData();
        result.setTodayMallOrderNum(0);
        result.setTotalMallOrderNum(0);

//        TeboReportPartnerVO battery = teboReportMapper.selectPartnerBatteryReport(tenantId);
        result.setTodayBatteryNum(0);
        result.setTotalBatteryNum(0);


        result.setTodayBatteryRevenue(0L);
        result.setTotalBatteryRevenue(0L);

        result.setTodayRevenue(0L);
        result.setMonthRevenue(0L);
        result.setTotalRevenue(0L);

        result.setTotalProfit(0L);
        result.setTodayPreOrderNum("432");
        // redisService.setCacheObject(key, result, 5L, TimeUnit.MINUTES);
        return result;
    }

    @Override
    public TeboReportPartnerVO shopInfo(Long shopId) {
        // String key = "shopInfo:" + DateUtil.genDateStr() + ":" + shopId;
        // if (redisService.hasKey(key)) {
        //     return (TeboReportPartnerVO) redisService.getCacheObject(key);
        // }
        TeboReportPartnerVO result = new TeboReportPartnerVO();
//        R<SystemPartnerReportVO> sysRes = remoteSystemReportService.getPartnerShopReport(shopId);
//        SystemPartnerReportVO systemPartnerReportVO = sysRes.getData();
        result.setPartnerNum(1);
//        result.setShopNum(systemPartnerReportVO.getShopNum());
//        result.setAccountNum(systemPartnerReportVO.getAccountNum());
//        result.setCustomerNum(systemPartnerReportVO.getCustomerNum());

        result.setShopNum(0);
        result.setAccountNum(0);
        result.setCustomerNum(0);

//        TeboReportPartnerVO teboOrder = teboReportMapper.selectPartnerShopReport(shopId);
        result.setTodayOrderNum(0);
        result.setTotalOrderNum(0);
        result.setTodayArriveOrderNum(0);
        result.setTodayRescueOrderNum(0);
        result.setTotalRescueOrderNum(0);

//        R<MallPartnerReportVO> mallRes = remoteMallReportService.getPartnerShopReport(shopId);
//        MallPartnerReportVO mallPartnerReportVO = mallRes.getData();
        result.setTodayMallOrderNum(0);
        result.setTotalMallOrderNum(0);

//        TeboReportPartnerVO battery = teboReportMapper.selectPartnerShopBatteryReport(shopId);
        result.setTodayBatteryNum(0);
        result.setTotalBatteryNum(0);


        result.setTodayBatteryRevenue(0L);
        result.setTotalBatteryRevenue(0L);

        result.setTodayRevenue(0L);
        result.setMonthRevenue(0L);
        result.setTotalRevenue(0L);

        result.setTotalProfit(0l);
        result.setTodayPreOrderNum("0");
        // redisService.setCacheObject(key, result, 5L, TimeUnit.MINUTES);
        return result;
    }

    @Override
    public TeboIndexReportVO getIndexInfo() {
        // 商城模块报表
        TeboIndexReportVO result = new TeboIndexReportVO();
        TeboIndexReportMallVO mall = remoteMallReportService.getIndexReport().getData();
        // 系统模块报表
        TeboSystemIndexReportVO system = remoteSystemReportService.platform().getData();
        TeboIndexReportRescueVO rescue =teboReportMapper.getIndexRescueReport();
        rescue.setTodayOrderNum(rescue.getTodayOrderNum());
        rescue.setTodayPendingRescueOrderNum(rescue.getTodayPendingRescueOrderNum());
        rescue.setTodayAcceptRescueOrderNum(rescue.getTodayAcceptRescueOrderNum());
        rescue.setTodayTimeoutRescueOrderNum(rescue.getTodayTimeoutRescueOrderNum());
        TeboIndexReportRescueVO subscribe =teboReportMapper.getIndexSubscribeReport();
        rescue.setTodayPendingSubscribeOrderNum(subscribe.getTodayPendingSubscribeOrderNum());
        rescue.setTodayAcceptSubscribeOrderNum(subscribe.getTodayAcceptSubscribeOrderNum());
        rescue.setTodayInvalidOrderNum(subscribe.getTodayInvalidOrderNum());
        result.setMall(mall);
        result.setSystem(system);
        result.setRescue(rescue);
        return result;
    }

    @Override
    public Map getSingleReport(String phoneNumber) {
        Map result = new HashMap();
        Map referenceInfo = teboReportMapper.getReferenceInfo(phoneNumber);
        result.put("谁推广的他", referenceInfo);
        List<Map> personInfo = teboReportMapper.getPersonInfo(phoneNumber);
        result.put("自己的信息", personInfo);
        List<Map> referencePersonInfo = teboReportMapper.getReferencePersonInfo(phoneNumber);
        result.put("推广信息", referencePersonInfo);
        return result;
    }


}
