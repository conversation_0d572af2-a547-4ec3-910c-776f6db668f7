package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 团购订单消费者表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
@Getter
@Setter
@TableName("tebo_group_purchase_customer")
public class TeboGroupPurchaseCustomerDO extends Model<TeboGroupPurchaseCustomerDO> {

    private static final long serialVersionUID = 1L;

    @TableField("id")
    private Long id;

    /**
     * 团购订单id
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 状态  0:待领取 1:已领取
     */
    @TableField("status")
    private Integer status;

    /**
     * 用户手机号
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 删除标识 1：已删除 0：未删除
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    // 25.04.29 增加订单门店快照字段
    /**
     * 门店名称
     */
    @TableField("shop_name")
    private String shopName;


    /**
     * 老板名称
     */
    @TableField("boss_name")
    private String bossName;

    /**
     * 商名称
     */
    @TableField("upper_name")
    private String upperName;

    /**
     * 商CRMS账号
     */
    @TableField("upper_code")
    private String upperCode;

    /**
     * 云门店id
     */
    @TableField("cloud_shop_id")
    private String cloudShopId;

    /**
     * 云门店手机号
     */
    @TableField("cloud_shop_phone")
    private String cloudShopPhone;

    /**
     * 云门店名称
     */
    @TableField("cloud_shop_name")
    private String cloudShopName;

    /**
     * 云门店老板名称
     */
    @TableField("cloud_shop_boss")
    private String cloudShopBoss;

    @Override
    public Serializable pkVal() {
        return null;
    }

}
