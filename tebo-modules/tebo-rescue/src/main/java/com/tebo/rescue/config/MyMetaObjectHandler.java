package com.tebo.rescue.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.tebo.common.security.util.OnlineUserUtil;
import com.tebo.common.security.utils.SecurityUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Configuration;
import java.time.LocalDateTime;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2023/12/5 12:37
 * @Desc : MP 自动填充
 */
@Configuration
public class MyMetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "createTime", () -> LocalDateTime.now(), LocalDateTime.class); // 起始版本 3.3.0(推荐使用)
        this.strictInsertFill(metaObject, "updateTime", () -> LocalDateTime.now(), LocalDateTime.class); // 起始版本 3.3.3(推荐)
        this.strictInsertFill(metaObject, "delFlag", () -> 0, Integer.class); // 起始版本 3.3.3(推荐)
        // this.strictInsertFill(metaObject, "tenantId", () -> SecurityUtils.getTenantId(), Long.class); // 起始版本 3.3.3(推荐)
        // this.strictInsertFill(metaObject, "accountId", () -> SecurityUtils.getUserId(), Long.class); // 起始版本 3.3.3(推荐)
        this.strictInsertFill(metaObject, "createBy", () -> OnlineUserUtil.getUsername(), String.class); // 起始版本 3.3.3(推荐)
        this.strictInsertFill(metaObject, "updateBy", () -> OnlineUserUtil.getUsername(), String.class); // 起始版本 3.3.3(推荐)
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "updateTime", () -> LocalDateTime.now(), LocalDateTime.class); // 起始版本 3.3.3(推荐)
        this.strictUpdateFill(metaObject, "updateBy", () -> OnlineUserUtil.getUsername(), String.class); // 起始版本 3.3.3(推荐)
    }
}
