package com.tebo.rescue.service.impl;

import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.rescue.entity.TeboReissueCycleOrderDO;
import com.tebo.rescue.mapper.TeboReissueCycleOrderMapper;
import com.tebo.rescue.service.TeboReissueCycleOrderService;
import lombok.extern.slf4j.Slf4j;
import org.ehcache.core.util.CollectionUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class TeboReissueCycleOrderServiceImpl implements TeboReissueCycleOrderService {
    @Resource
    private TeboReissueCycleOrderMapper teboReissueCycleOrderMapper;

    @Override
    public void reissueCycleOrder() {
        List<String> list = teboReissueCycleOrderMapper.getReissueCouponOrderNo();
        if (!CollectionUtils.isEmpty(list)){
            list.forEach(item ->{
                TeboReissueCycleOrderDO teboReissueCycleOrderDO = new TeboReissueCycleOrderDO();
                teboReissueCycleOrderDO.setId(SnowFlakeUtil.nextId());
                teboReissueCycleOrderDO.setOrderNo(item);
                teboReissueCycleOrderMapper.insert(teboReissueCycleOrderDO);
            });
        }

    }
}