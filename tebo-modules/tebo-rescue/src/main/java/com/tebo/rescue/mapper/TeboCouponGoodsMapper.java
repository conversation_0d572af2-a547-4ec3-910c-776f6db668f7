package com.tebo.rescue.mapper;

import com.tebo.rescue.entity.TeboCouponGoodsDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 卡券可用商品表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-22
 */
@Mapper
public interface TeboCouponGoodsMapper extends TeboBaseMapper<TeboCouponGoodsDO> {

    List<TeboCouponGoodsDO> selectByCouponId(Long couponId);

    int deleteByCouponId(Long couponId);

    List<TeboCouponGoodsDO> selectByCouponIdList(@Param("list") List<Long> list);

    /**
     * 批量插入
     */
    void batchInsert(@Param("list")List<TeboCouponGoodsDO> list);

}
