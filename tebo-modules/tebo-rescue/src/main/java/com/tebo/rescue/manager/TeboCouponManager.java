package com.tebo.rescue.manager;

import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.rescue.entity.TeboCouponCustomerDO;
import com.tebo.rescue.entity.TeboCouponCustomerRecordDO;
import com.tebo.rescue.entity.TeboCouponDO;
import com.tebo.rescue.mapper.TeboCouponCustomerMapper;
import com.tebo.rescue.mapper.TeboCouponCustomerRecordMapper;
import com.tebo.rescue.mapper.TeboCouponMapper;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponCustomerRecordDTO;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponQueryDTO;
import com.tebo.rescue.web.domain.view.TeboCouponCustomerRecordVO;
import com.tebo.system.api.model.TeboConsumer;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024/4/11 16:47
 * @Desc : 卡券
 */
@Component
public class TeboCouponManager {

    @Resource
    private TeboCouponCustomerRecordMapper teboCouponCustomerRecordMapper;
    @Resource
    private TeboCouponCustomerMapper teboCouponCustomerMapper;
    @Resource
    private TeboCouponMapper teboCouponMapper;

    public List<TeboCouponCustomerRecordVO> getCouponCustomerList(TeboCouponQueryDTO query) {
        // 默认排除已失效的领用数据
        if (ObjectUtils.isEmpty(query.getStatus())) {
            query.setStatus(99);
        }
        List<TeboCouponCustomerRecordDO> list = teboCouponCustomerRecordMapper.getCouponCustomerList(query);
        return BeanConvert.copyList(list, TeboCouponCustomerRecordVO::new);
    }

    public void createCouponCustomerRecord(String uniqueCode, TeboConsumer consumer){
        TeboCouponCustomerDO couponCustomer = teboCouponCustomerMapper.getDetailByCouponCode(uniqueCode);
        TeboCouponDO teboCouponDO = teboCouponMapper.selectById(couponCustomer.getCouponId());
        TeboCouponCustomerRecordDO item = new TeboCouponCustomerRecordDO();
        item.setId(SnowFlakeUtil.nextId());
        item.setUniqueCode(uniqueCode);
        item.setCouponId(couponCustomer.getCouponId());
        item.setCouponName(teboCouponDO.getCouponName());
        item.setCouponCode(teboCouponDO.getCouponCode());
        item.setTenantId(teboCouponDO.getTenantId());
        item.setTenantName(teboCouponDO.getTenantName());
        item.setSingle(teboCouponDO.getSingle());
        item.setUnionId(couponCustomer.getUnionId());
        item.setStatus(2);
        item.setReceiveTime(couponCustomer.getReceiveTime());
        item.setCustomerName(consumer.getNickName());
        item.setPhone(consumer.getPhoneNumber());
        teboCouponCustomerRecordMapper.insert(item);
    }

    /**
     * 更新卡券领用记录状态
     * @param recordDTO
     * @return
     */
    public Integer batchUpdateCouponRecordStatus(TeboCouponCustomerRecordDTO recordDTO){
        return teboCouponCustomerRecordMapper.batchUpdateByUniqueCode(recordDTO);
    }

    /**
     * 更新用户卡券为已作废
     * @param couponId
     * @return
     */
    @Transactional
    public Integer batchDelCouponStatus(Long couponId){
        teboCouponCustomerMapper.batchDelCouponStatus(couponId);
        teboCouponCustomerRecordMapper.batchDelCouponRecord(couponId);
        return 1;
    }

    /**
     * 获取卡券列表
     * @param query
     * @return
     */
    public List<TeboCouponDO> getCouponList(TeboCouponQueryDTO query){
        return teboCouponMapper.getCouponList(query);
    }


    /**
     * 批量更新券状态
     * @param ids
     * @param status 2 生效 3 失效
     * @return
     */
    public Integer batchUpdateCouponStatus(List<Long> ids, Integer status) {
        return teboCouponMapper.batchUpdateCouponStatus(ids, status);
    }


    /**
     * 更新用户卡券为已过期
     * @param couponIds
     * @return
     */
    @Transactional
    public Integer batchDelCouponExpire(List<Long> couponIds){
        teboCouponCustomerMapper.batchExpireCouponStatus(couponIds);
        teboCouponCustomerRecordMapper.batchExpireCouponRecord(couponIds);
        return 1;
    }

}
