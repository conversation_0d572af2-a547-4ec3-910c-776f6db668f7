package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 评价表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Getter
@Setter
@TableName("tebo_comment")
public class TeboCommentDO extends Model<TeboCommentDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 被评价人id
     */
    @TableField("commented_id")
    private Long commentedId;

    /**
     * 被评价人名
     */
    @TableField("commented_name")
    private String commentedName;

    /**
     * 对应单据id
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 对应单据类型
     */
    @TableField("order_business_type")
    private String orderBusinessType;

    /**
     * 总体评价0-10
     */
    @TableField("total_comment")
    private Integer totalComment;

    /**
     * 服务评价0-10
     */
    @TableField("service_comment")
    private Integer serviceComment;

    /**
     * 专业能力评价0-10
     */
    @TableField("competence_comment")
    private Integer competenceComment;

    /**
     * 态度评价0-10
     */
    @TableField("attitude_comment")
    private Integer attitudeComment;

    /**
     * 专业形象评价0-10
     */
    @TableField("image_comment")
    private Integer imageComment;

    /**
     * 质量评价0-5
     */
    @TableField("quantity_comment")
    private Integer quantityComment;

    /**
     * 使用须知
     */
    @TableField("remark")
    private String remark;


    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标识
     */
    @TableField("del_flag")
    private Boolean delFlag;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
