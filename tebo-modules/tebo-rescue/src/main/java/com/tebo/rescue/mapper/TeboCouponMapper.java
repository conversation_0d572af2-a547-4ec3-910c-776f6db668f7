package com.tebo.rescue.mapper;

import com.tebo.rescue.entity.TeboCouponDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 卡券表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-13
 */
@Mapper
public interface TeboCouponMapper extends TeboBaseMapper<TeboCouponDO> {

    List<TeboCouponDO> getCouponList(TeboCouponQueryDTO query);

    List<TeboCouponDO> getCouponListByIds(@Param("list") List<Long> ids);

    int batchUpdateCouponStatus(@Param("list") List<Long> ids, @Param("status") Integer status);

}
