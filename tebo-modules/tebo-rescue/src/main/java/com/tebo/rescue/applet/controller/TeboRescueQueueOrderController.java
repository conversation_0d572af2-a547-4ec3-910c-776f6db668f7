package com.tebo.rescue.applet.controller;

import cn.hutool.core.lang.Assert;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.constant.CacheConstants;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.rescue.applet.domain.dto.*;
import com.tebo.rescue.applet.domain.view.CustomerQueueStatusVO;
import com.tebo.rescue.entity.TeboRescueQueueOrderDO;
import com.tebo.rescue.service.ITeboRescueQueueOrderService;
import com.tebo.rescue.util.DistributedLock;
import com.tebo.system.api.domain.view.WechatPrepayResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 救援，预约上门服务单
 */
@Slf4j
@RestController
@RequestMapping("applet/rescue")
public class TeboRescueQueueOrderController extends BaseController {

    @Resource
    private HttpServletRequest request;


    @Autowired
    private ITeboRescueQueueOrderService rescueQueueOrderService;

    @Resource
    private DistributedLock distributedLock;

    /**
     * 返回当前消费者的救援排队单状态
     */
    @PostMapping("/queueStatus")
    public R<CustomerQueueStatusVO> queueStatus() {
        CustomerQueueStatusVO queueStatusVO = rescueQueueOrderService.customerQueueStatus();
        return R.ok(queueStatusVO);
    }


    /**
     * 创建救援单
     */
    @PostMapping("/create")
    public R<RescueQueueOrderSaveDTO> rescueOrderCreate(@RequestBody() RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO) {
        return R.ok(rescueQueueOrderService.createRescueOrder(rescueQueueOrderSaveDTO));
    }

    /**
     * 创建救援单
     */
    @PostMapping("/v2/create")
    public R<WechatPrepayResponse> rescueOrderCreateNew(@RequestBody RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO) {
        return R.ok(rescueQueueOrderService.createRescueOrderNew(rescueQueueOrderSaveDTO));
    }

    /**
     * 消费者取消救援单
     */
    @PostMapping("/cancel")
    public R rescueOrderCancel(@RequestBody() RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO) {
        rescueQueueOrderService.cancelRescueOrder(rescueQueueOrderSaveDTO);
        return R.ok();
    }

    /**
     * 创建预约上门单
     */
    @PostMapping("/preDoor/create")
    public R<RescueQueueOrderSaveDTO> preDoorOrderCreate(@RequestBody RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO) {

        return R.ok(rescueQueueOrderService.createPreDoorOrder(rescueQueueOrderSaveDTO));
    }


    /**
     * 创建预约到店单
     */
    @PostMapping("/preStore/create")
    public R<RescueQueueOrderSaveDTO> callQueueOrder(@RequestBody RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO) {
        return R.ok(rescueQueueOrderService.createPreStoreOrder(rescueQueueOrderSaveDTO));
    }


    /**
     * 救援预约服务单详情
     */
    @PostMapping("/order/detail")
    public R<RescueQueueOrderDetailDTO> orderDetail(@RequestBody RescueQueueOrderQueryDTO queryDTO) {
        RescueQueueOrderDetailDTO detailDTO = rescueQueueOrderService.orderDetail(queryDTO);
        return R.ok(detailDTO);
    }


    /**
     * 师傅端-预约上门单待接单列表
     *
     * @return
     */
    @PostMapping("unReceived/preDoor/order/list")
    public R<List<RescueQueueOrderItemDTO>> UnReceivedPreDoorOrderList() {
        return R.ok(rescueQueueOrderService.getUnReceivedPreDoorOrderList());
    }


    /**
     * 师傅端-救援单列表
     *
     * @return
     */
    @PostMapping("shop/rescue/order/list")
    public TableDataInfo shopRescueOrderList(@RequestBody RescueQueueOrderQueryDTO queryDTO) {
        if (queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            throw new GlobalException("分页参数不能为空");
        }
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        return getDataTable(rescueQueueOrderService.shopStoreRescueOrderList(queryDTO));
    }


    /**
     * 师傅端-预约上门单列表
     *
     * @return
     */
    @PostMapping("shop/preDoor/order/list")
    public TableDataInfo shopPreDoorOrderList(@RequestBody RescueQueueOrderQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        return getDataTable(rescueQueueOrderService.shopPreDoorOrderList(queryDTO));
    }


    /**
     * 消费者端 我的-预约上门，预约到店单列表
     *
     * @return
     */
    @PostMapping("/pre/order/list")
    public TableDataInfo preOrderList(@RequestBody RescueQueueOrderQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        return getDataTable(rescueQueueOrderService.getPreOrderListByUser(queryDTO));
    }

    /**
     * 消费者端 我的-救援单列表
     *
     * @return
     */
    @PostMapping("/rescue/order/list")
    public TableDataInfo rescueOrderList(@RequestBody RescueQueueOrderQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        return getDataTable(rescueQueueOrderService.getRescueOrderListByUser());
    }

    /**
     * 师傅接单
     */
    @PostMapping("receivingOrder")
    public R receivingOrder(@RequestBody() RescueQueueOrderReceiveDTO rescueQueueOrderReceiveDTO) {
        Assert.notNull(rescueQueueOrderReceiveDTO.getId(), "未指定接单单号");
        boolean tryLock = distributedLock.tryLock(CacheConstants.RECEIVING_ORDER_ID + rescueQueueOrderReceiveDTO.getId(), 30, 60);
        if (Boolean.TRUE.equals(tryLock)) {
            try {
                rescueQueueOrderService.receivingOrder(rescueQueueOrderReceiveDTO);
            } finally {
                distributedLock.unlock(CacheConstants.RECEIVING_ORDER_ID + rescueQueueOrderReceiveDTO.getId());
            }
        }
        return R.ok();
    }

    /**
     * 统计师傅端救援服务单和预约上门服务单数量
     */
    @PostMapping("getMaintenanceServiceOrderCount")
    public R getMaintenanceServiceOrderCount() {
        return R.ok( rescueQueueOrderService.getMaintenanceServiceOrderCount());
    }
}
