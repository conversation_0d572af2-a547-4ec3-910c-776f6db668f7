package com.tebo.rescue.mapper;

import com.tebo.rescue.entity.TeboGiftPackDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.rescue.web.domain.dto.pack.TeboGiftPackQueryDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 礼包表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Mapper
public interface TeboGiftPackMapper extends TeboBaseMapper<TeboGiftPackDO> {

    List<TeboGiftPackDO> getGiftPackList(TeboGiftPackQueryDTO queryDTO);
}
