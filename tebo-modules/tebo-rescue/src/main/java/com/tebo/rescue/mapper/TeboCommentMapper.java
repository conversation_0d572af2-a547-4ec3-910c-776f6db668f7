package com.tebo.rescue.mapper;

import com.tebo.rescue.entity.TeboCommentDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 评价表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@Mapper
public interface TeboCommentMapper extends TeboBaseMapper<TeboCommentDO> {


    List<TeboCommentDO> getByOrderId(@Param("orderId") Long OrderId);

}
