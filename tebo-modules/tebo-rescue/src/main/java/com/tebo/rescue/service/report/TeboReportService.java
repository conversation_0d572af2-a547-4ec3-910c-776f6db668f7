package com.tebo.rescue.service.report;

import com.tebo.mall.api.domain.view.TeboIndexReportMallVO;
import com.tebo.rescue.applet.domain.view.report.TeboReportPartnerVO;
import com.tebo.rescue.web.domain.view.TeboIndexReportVO;

import java.util.Map;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024/6/3 10:57
 * @Desc : 报表服务
 */
public interface TeboReportService {

    /**
     * 合伙人主报表
     * @param tenantId
     * @return
     */
    TeboReportPartnerVO partnerTotalInfo(Long tenantId);
    /**
     * 合伙人的门店报表
     * @param shopId
     * @return
     */
    TeboReportPartnerVO shopInfo(Long shopId);

    /**
     * PC 主页报表
     */
    TeboIndexReportVO getIndexInfo();

    /**
     * 运营查询助手
     * @param phoneNumber
     * @return
     */
    Map getSingleReport(String phoneNumber);

}
