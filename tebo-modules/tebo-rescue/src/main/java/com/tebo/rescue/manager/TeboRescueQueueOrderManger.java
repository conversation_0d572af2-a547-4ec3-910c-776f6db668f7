package com.tebo.rescue.manager;

import com.tebo.rescue.applet.domain.dto.RescueQueueOrderQueryDTO;
import com.tebo.rescue.applet.domain.view.MaintenanceQueueOrderNumberVO;
import com.tebo.rescue.entity.TeboRescueQueueOrderDO;

import java.util.List;

public interface TeboRescueQueueOrderManger {

    /**
     * 条件查询
     *
     * @param queueOrderQueryDTO
     * @return
     */
    List<TeboRescueQueueOrderDO> listBySO(RescueQueueOrderQueryDTO queueOrderQueryDTO);
    List<TeboRescueQueueOrderDO> listBySOOrderCreateTimeDesc(RescueQueueOrderQueryDTO queueOrderQueryDTO);

    TeboRescueQueueOrderDO getById(Long id);



    void updateById(TeboRescueQueueOrderDO update);

    /**
     * 统计师傅端救援服务单和预约上门服务单数量
     */
    List<MaintenanceQueueOrderNumberVO> getMaintenanceServiceOrderCount(Long shopId);


}
