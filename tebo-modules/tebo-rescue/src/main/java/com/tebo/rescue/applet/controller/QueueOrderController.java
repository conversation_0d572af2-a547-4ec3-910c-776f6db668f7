package com.tebo.rescue.applet.controller;

import cn.hutool.core.lang.Assert;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.common.util.applet.AppletUtil;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.rescue.applet.domain.dto.CallQueueOrderDTO;
import com.tebo.rescue.applet.domain.dto.QueueOrderDTO;
import com.tebo.rescue.applet.domain.dto.QueueOrderQueryDTO;
import com.tebo.rescue.applet.domain.view.QueueOrderCountVO;
import com.tebo.rescue.applet.domain.view.QueueOrderListVO;
import com.tebo.rescue.applet.domain.view.QueueOrderVO;
import com.tebo.rescue.applet.domain.view.QueueOrderWaitVO;
import com.tebo.rescue.service.IQueueOrderService;
import com.tebo.system.api.domain.view.WechatPrepayResponse;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * @Author：zhengmk
 * @Date 2023/12/12 18:53
 */
@RestController
@RequestMapping("/queue/order")
public class QueueOrderController extends BaseController {

    @Resource
    private HttpServletRequest request;

    @Resource
    private IQueueOrderService queueOrderService;


    /**
     * 排队订单列表
     */
    @PostMapping("/page")
    public TableDataInfo listQueueOrder(@RequestBody QueueOrderQueryDTO queryDTO) {
        if(queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            throw new GlobalException("分页参数不能为空");
        }
        String unionid = AppletUtil.getUnionIdByRequest(request);
        queryDTO.setUnionid(unionid);
        Page page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<QueueOrderListVO> list = queueOrderService.listQueueOrder(queryDTO);
        return getDataTable(list,page);
    }
    /**
     * 取号
     */
    @PostMapping("/addQueueOrder")
    public R<Boolean> addQueueOrder(@RequestBody QueueOrderDTO queueOrderDTO) {
        return R.ok(queueOrderService.addQueueOrder(queueOrderDTO));
    }

    /**
     * 绑定师傅和工位
     */
    @PostMapping("/bindAccountStation")
    public R bindAccountStation(@RequestBody CallQueueOrderDTO callQueueOrderDTO) {
        Assert.notNull(callQueueOrderDTO.getStationName(),"工位不能为空");
        queueOrderService.bindAccountStation(callQueueOrderDTO.getStationName());
        return R.ok();
    }


    /**
     * 叫号
     */
    @PostMapping("/callQueueOrder")
    public R<QueueOrderWaitVO> callQueueOrder(@RequestBody CallQueueOrderDTO callQueueOrderDTO) {
        return R.ok(queueOrderService.callQueueOrder(callQueueOrderDTO));
    }

    /**
     * 重叫
     */
    @PostMapping("/recall")
    public R<QueueOrderWaitVO> recall(@RequestBody CallQueueOrderDTO callQueueOrderDTO) {
        return R.ok(queueOrderService.recall(callQueueOrderDTO));
    }

    @GetMapping("/cancelQueueOrder/{id}")
    public R<Boolean> cancelQueueOrder(@PathVariable("id") Long id) {
        return R.ok(queueOrderService.cancelQueueOrder(id));
    }

    @PostMapping("/commentQueueOrder")
    public R<Boolean> commentQueueOrder(@RequestBody QueueOrderDTO consumerOrderDTO) {
        return R.ok(queueOrderService.commentQueueOrder(consumerOrderDTO));
    }

    @GetMapping("/getCurrentQueueOrder")
    public R<List<QueueOrderVO>> getCurrentQueueOrder() {
        String unionid = AppletUtil.getUnionIdByRequest(request);
        return R.ok(queueOrderService.getCurrentQueueOrder(unionid));
    }


    /**
     * 排队订单详情
     */
    @GetMapping("/detail/{id}")
    public R<QueueOrderVO> detail(@PathVariable("id") Long id) {
        return R.ok(queueOrderService.detailById(id));
    }

    /**
     * 排队订单各个状态数量
     */
    @GetMapping("/count")
    public R<QueueOrderCountVO> count() {
        String unionid = AppletUtil.getUnionIdByRequest(request);
        return R.ok(queueOrderService.count(unionid));
    }


    @PostMapping("/getShopCustomerNum")
    public R<Map<Long, Integer>> getShopCustomerNum(@RequestBody QueueOrderQueryDTO queryDTO) {
        return R.ok(queueOrderService.getShopCustomerNum(queryDTO));
    }
}
