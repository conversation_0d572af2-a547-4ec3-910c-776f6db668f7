package com.tebo.rescue.task;

import com.tebo.common.redis.service.RedisService;
import com.tebo.mall.api.util.DrawLotteryUtil;
import com.tebo.rescue.applet.domain.dto.GiftPackOrderQueryDTO;
import com.tebo.rescue.mapper.TeboGiftPackOrderMapper;
import com.tebo.rescue.util.DistributedLock;
import com.tebo.rescue.util.TeboRescueCommonCacheConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author：zhengmk
 * @Date 2023/12/27 14:51
 */
@Component
@Slf4j
public class PackOrderScheduleTask {

    @Resource
    private TeboGiftPackOrderMapper giftPackOrderMapper;

    @Resource
    private RedisService redisService;
    @Resource
    private DistributedLock distributedLock;


    /**
     * 取消未支付礼包订单
     */
    @Scheduled(cron = "0 0 1 * * ? ")
    public void cancelExpiredPackOrder() {
        if (!distributedLock.tryLock(TeboRescueCommonCacheConstant.getScheduledTaskPrefix("PackOrderScheduleTask:cancelExpiredPackOrder"), 10, 30)) {
            return;
        }
        GiftPackOrderQueryDTO queryDTO = new GiftPackOrderQueryDTO();
        queryDTO.setMaxTime(LocalDateTime.now());
        giftPackOrderMapper.cancelExpiredPackOrder(queryDTO);
    }

    /**
     * 9号把有抽奖资格的用户写入redis中
     */
    @Scheduled(cron = "0 0 23 8 * ? ")
    public void oldCouponSign() {
        if (!distributedLock.tryLock(TeboRescueCommonCacheConstant.getScheduledTaskPrefix("PackOrderScheduleTask:oldCouponSign"), 10, 30)) {
            return;
        }
        log.info("oldCouponSign begin");
        List<String> signOrderUnionIdList = giftPackOrderMapper.getOldCouponSign();
        signOrderUnionIdList.forEach(item ->{
            String key = DrawLotteryUtil.lotteryEligibilityKey + item;
            redisService.setCacheObject(key,item,2L, TimeUnit.DAYS);
        });
        log.info("oldCouponSign end");
    }

    /**
     * 每个月9号 每5分钟 把有抽奖资格的用户写入redis中
     */
  //  @Scheduled(cron = "* 0/1 ? 3 * ?")
    @Scheduled(cron = "0 0/5 * 9 * ? ")
    public void newCouponSign() {
        if (!distributedLock.tryLock(TeboRescueCommonCacheConstant.getScheduledTaskPrefix("PackOrderScheduleTask:newCouponSign"), 10, 30)) {
            return;
        }
        log.info("newCouponSign begin");
        List<String> signOrderUnionIdList = giftPackOrderMapper.getNewCouponSign();
        signOrderUnionIdList.forEach(item ->{
            String key = DrawLotteryUtil.lotteryEligibilityKey + item;
            redisService.setCacheObject(key,item,2L, TimeUnit.DAYS);
        });
        log.info("newCouponSign end");
    }

}
