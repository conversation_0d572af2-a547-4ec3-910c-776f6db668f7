package com.tebo.rescue.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.rescue.applet.domain.dto.CommentOrderDTO;
import com.tebo.rescue.entity.TeboCommentDO;
import com.tebo.rescue.entity.TeboServiceOrderDO;
import com.tebo.rescue.enums.OrderTableEnum;
import com.tebo.rescue.enums.ServiceOrderStatusEnum;
import com.tebo.rescue.manager.TeboServiceOrderManger;
import com.tebo.rescue.mapper.TeboCommentMapper;
import com.tebo.rescue.service.ICommentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

@Service
@Slf4j
public class CommentServiceImpl extends ServiceImpl<TeboCommentMapper, TeboCommentDO> implements ICommentService {

    @Autowired
    private TeboServiceOrderManger serviceOrderManger;

    @Transactional
    @Override
    public void commentServiceOrder(CommentOrderDTO commentOrderDTO) {
        Long orderId = commentOrderDTO.getOrderId();
        Assert.notNull(orderId, "评价单号不能为空");

        TeboServiceOrderDO serviceOrderDO = serviceOrderManger.getById(orderId);
        if (Objects.isNull(serviceOrderDO)) {
            throw new ServiceException("未匹配到工单");
        }
        if (!Objects.equals(serviceOrderDO.getOrderStatus(), ServiceOrderStatusEnum.COMPLETED.getCode())) {
            throw new GlobalException("只有已完成订单可以评价");
        }


        TeboCommentDO save = new TeboCommentDO();
        BeanConvert.copy(commentOrderDTO, save);
        save.setId(SnowFlakeUtil.nextId());
        save.setOrderId(orderId);
        save.setOrderBusinessType(OrderTableEnum.TEBO_SERVICE_ORDER.getTableName());
        save.setCommentedId(serviceOrderDO.getAccountId());
        save.setCommentedName(serviceOrderDO.getAccountName());
        save(save);


        //更新工单状态
        TeboServiceOrderDO update = new TeboServiceOrderDO();
        update.setId(orderId);
        update.setOrderStatus(ServiceOrderStatusEnum.COMMENTED.getCode());
        serviceOrderManger.updateById(update);


    }

}
