package com.tebo.rescue.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tebo.common.core.domain.R;
import com.tebo.rescue.applet.domain.dto.*;
import com.tebo.rescue.applet.domain.view.*;
import com.tebo.rescue.entity.TeboServiceOrderDO;
import com.tebo.rescue.lst.domain.vo.TeboOrderGoodsIdGroup;
import com.tebo.system.api.domain.view.WechatPrepayResponse;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @Author：zhengmk
 * @Date 2023/12/14 15:29
 * 师傅端服务工单信息
 */
public interface IServiceOrderService extends IService<TeboServiceOrderDO> {

    /**
     * 添加服务工单
     */
    Map<String, Object> addServiceOrder(ServiceOrderDTO serviceOrderDTO);

    /**
     * 救援，上门单开单
     *
     * @param serviceOrderDTO
     * @return
     */
    Map<String, Object> rescueOpenOrder(ServiceOrderDTO serviceOrderDTO);

    /**
     * 获取服务类型列表
     */
    List<ServiceTypeListVO> listServiceType(ServiceTypeQueryDTO serviceOrderDTO);

    /**
     * 获取服务工单列表
     */
    List<ServiceOrderListVO> listServiceOrder(ServiceOrderQueryDTO queryDTO);

    /**
     * 导出服务工单列表
     */
    void exportServiceOrder(HttpServletResponse response, ServiceOrderQueryDTO queryDTO);


    /**
     * 工单各状态计数
     *
     * @param unionId
     * @return
     */
    ServiceOrderCountVO customerCount(String unionId);

    /**
     * 已支付订单手动关单
     * 针对排队单
     */
    Boolean finishServiceOrder(Long queueOrderId);

    Boolean finishRescueQueueServiceOrder(Long queueOrderId);

    Boolean serviceOrderArrived(Long id);

    /**
     * 师傅出发
     *
     * @param id
     * @return
     */
    Boolean serviceOrderDeparted(Long id);

    /**
     * 获取服务订单详情
     */
    ServiceOrderVO detailServiceOrder(Long serviceOrderId);

    /**
     * 取消工单
     */
    Boolean cancelServiceOrder(Long serviceOrderId);

    /**
     * 新的取消接口，支持取消原因参数
     */
    Boolean cancelServiceOrder(ServiceOrderDTO serviceOrderDTO);


    /**
     * 支付成功后回调
     */
    Boolean afterPay(ServiceOrderAfterPayDTO afterPayDTO);


    /**
     * 师傅端关闭（完成）工单
     *
     * @param id
     */
    Boolean completeServiceOrder(Long id);

    /**
     * 获取师傅对应的未完结工单
     *
     * @return
     */
    List<ServiceOrderDTO> getUnFinishServiceOrderByAccount(List<Long> accountIds);

    /**
     * 获取师傅对应的未完结排队单
     *
     * @return
     */
    List<QueueOrderDTO> getUnFinishQueueOrderByAccount(List<Long> accountIds);


    /**
     * 快捷开单创建工单
     *
     * @param openQuickServiceOrderDTO
     */
    QuickOpenServiceOrderResultVO quickOpenOrder(OpenQuickServiceOrderDTO openQuickServiceOrderDTO);

    /**
     * 支付成功后，调用更新快捷工单状态
     */
    Boolean updateQuickServiceOrderStatus(String orderNo);


    /**
     * 获取快捷开单的跳转二维码
     * @param orderId
     * @return
     */
    String getQuickOrderQrCode(Long orderId);

    void initShopType();

    /**
     * 更新订单状态
     */
    Boolean updateOrderType(String orderNo);


    /**
     * 商品分组
     * @param queryDTO
     * @return
     */
    List<TeboOrderGoodsIdGroup> getOrderGoodsIdGroup(ServiceOrderQueryDTO queryDTO);
}
