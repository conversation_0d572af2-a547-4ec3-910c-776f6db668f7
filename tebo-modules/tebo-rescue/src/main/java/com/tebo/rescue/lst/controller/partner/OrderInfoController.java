package com.tebo.rescue.lst.controller.partner;


import com.alibaba.csp.sentinel.util.AssertUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.rescue.lst.domain.vo.OrderVo;
import com.tebo.rescue.lst.domain.vo.TeboShopInfoVo;
import com.tebo.rescue.lst.service.IOrderInfoService;
import com.tebo.rescue.lst.service.dto.OrderOperationDto;
import com.tebo.rescue.lst.service.dto.OrderQueryDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController("partnerOrderInfoController")
@RequestMapping("lstApi/partner/orderInfo")
public class OrderInfoController extends BaseController {


    @Autowired
    private IOrderInfoService orderInfoService;


    /**
     * 合伙人获取订单列表
     */
    @PostMapping("/list")
    public TableDataInfo getOrderList(@RequestBody OrderQueryDto orderQueryDto) {
        Page<Object> page = PageHelper.startPage(orderQueryDto.getPageNum(), orderQueryDto.getPageSize());
        return getDataTable(orderInfoService.partnerGetOrderVoList(orderQueryDto), page);
    }


    /**
     * 向下派单
     */
    @PostMapping("/delivery")
    public R<Void> deliveryOrder(@Valid @RequestBody OrderOperationDto orderOperationDto) {
        AssertUtil.notEmpty(orderOperationDto.getTeboShopCode(), "门店编码不能为空！");
        orderInfoService.partnerDeliveryOrder(orderOperationDto);
        return R.ok();
    }

    /**
     * 拒单
     */
    @PostMapping("/reject")
    public R<Void> rejectOrder(@Valid @RequestBody OrderOperationDto orderOperationDto) {
        orderInfoService.partnerRejectOrder(orderOperationDto);
        return R.ok();
    }

    /**
     * 回收订单
     */
    @PostMapping("/recycle")
    public R<Void> recycleOrder(@Valid @RequestBody OrderOperationDto orderOperationDto) {
        orderInfoService.partnerRecycleOrder(orderOperationDto);
        return R.ok();
    }

}
