package com.tebo.rescue.service;

import com.tebo.rescue.remote.domain.LstOrderDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class TeboLstOrderService {


    /***
     select * from tebo_lst.order_info oi
     where oi.tebo_shop_id in (
     select id from tebo_system_prod.tebo_shop ts
     where ts.shop_code in (
     select outside_code from bttn_lst.shop_info si
     where si.gys_code = '1001099'
     and si.outside_code is not null
     )
     )
     */
    List<LstOrderDetailVO> lstOrder(String shopCode) {
        return null;
    }

}
