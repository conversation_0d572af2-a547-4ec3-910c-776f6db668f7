package com.tebo.rescue.manager.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.rescue.applet.domain.dto.OrderFileQueryDTO;
import com.tebo.rescue.entity.TeboOrderFileDO;
import com.tebo.rescue.manager.TeboOrderFileManger;
import com.tebo.rescue.mapper.TeboOrderFileMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class TeboOrderFileMangerImpl implements TeboOrderFileManger {

    @Autowired
    private TeboOrderFileMapper orderFileMapper;

    @Override
    public Boolean insertBatch(List<TeboOrderFileDO> saveList) {
        if (CollectionUtil.isEmpty(saveList)) {
            return false;
        }
        orderFileMapper.insertBatch(saveList);
        return true;
    }

    @Override
    public List<TeboOrderFileDO> listBySo(OrderFileQueryDTO queryDTO) {
        LambdaQueryWrapper<TeboOrderFileDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(queryDTO.getOrderTable()), TeboOrderFileDO::getOrderTable, queryDTO.getOrderTable())
                .eq(TeboOrderFileDO::getDelFlag, Boolean.FALSE)
                .eq(Objects.nonNull(queryDTO.getOrderId()), TeboOrderFileDO::getOrderId, queryDTO.getOrderId());
        return orderFileMapper.selectList(queryWrapper);
    }
}
