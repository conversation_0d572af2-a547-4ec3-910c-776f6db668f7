package com.tebo.rescue.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.core.utils.bean.BeanUtils;
import com.tebo.common.redis.constant.TeboRescueCacheConstant;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.security.util.MaintainerOnlineUserUtil;
import com.tebo.common.util.DataUtils;
import com.tebo.common.util.applet.AppletRequestUrl;
import com.tebo.common.util.applet.AppletUtil;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.encryption.Sha256Utils;
import com.tebo.common.util.http.HttpTool;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.common.util.time.DateUtil;
import com.tebo.rescue.applet.domain.dto.*;
import com.tebo.rescue.applet.domain.view.*;
import com.tebo.rescue.applet.domain.view.excel.QueueOrderListExcelVO;
import com.tebo.rescue.entity.*;
import com.tebo.rescue.enums.*;
import com.tebo.rescue.manager.TeboRescueQueueOrderManger;
import com.tebo.rescue.manager.TeboServiceOrderManger;
import com.tebo.rescue.mapper.TeboQueueOrderMapper;
import com.tebo.rescue.mapper.TeboServiceOrderCouponMapper;
import com.tebo.rescue.mapper.TeboServiceOrderGoodsMapper;
import com.tebo.rescue.mapper.TeboServiceOrderMapper;
import com.tebo.rescue.service.IQueueOrderService;
import com.tebo.rescue.service.mq.DisMqService;
import com.tebo.rescue.util.TeboNumberGenerator;
import com.tebo.system.api.*;
import com.tebo.system.api.domain.dto.RemoteConsumerRecordDTO;
import com.tebo.system.api.domain.dto.TeboShopQueryDTO;
import com.tebo.system.api.domain.dto.WxPushDTO;
import com.tebo.system.api.domain.enums.RemoteConsumerRecordEnum;
import com.tebo.system.api.domain.view.TeboShopListVO;
import com.tebo.system.api.model.TeboAccountInfoVO;
import com.tebo.system.api.model.TeboConsumer;
import com.tebo.system.api.model.TeboShop;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Author：zhengmk
 * @Date 2023/12/13 16:25
 */
@Slf4j
@Service
public class QueueOrderService implements IQueueOrderService {

    @Value("${applet.appId_c}")
    private String appid;

    @Value("${applet.call.page}")
    private String page;

    @Value("${applet.call.template_id}")
    private String templateId;


    @Resource
    private HttpServletRequest request;

    @Resource
    private TeboQueueOrderMapper teboQueueOrderMapper;

    @Resource
    private TeboServiceOrderMapper teboServiceOrderMapper;

    @Resource
    private TeboServiceOrderManger teboServiceOrderManger;

    @Resource
    private TeboServiceOrderGoodsMapper teboServiceOrderGoodsMapper;

    @Resource
    private TeboServiceOrderCouponMapper serviceOrderCouponMapper;

    @Resource
    private RemoteCustomerService remoteCustomerService;

    @Resource
    private RemoteShopService remoteShopService;

    @Resource
    private RemoteAccountService remoteAccountService;

    @Resource
    private RemotePayService remotePayService;

    @Resource
    private RemoteWxPushService remoteWxPushService;

    @Resource
    private RedisService redisService;

    @Autowired
    private TeboRescueQueueOrderManger rescueQueueOrderManger;

    @Autowired
    private DisMqService disMqService;

    @Transactional
    @Override
    public Boolean addQueueOrder(QueueOrderDTO orderDTO) {
        if (orderDTO == null) {
            throw new GlobalException("参数为空,叫号失败");
        }
        if (orderDTO.getShopId() == null) {
            throw new GlobalException("店铺id为空,叫号失败");
        }
        TeboQueueOrderDO teboQueueOrderDO = new TeboQueueOrderDO();
        BeanConvert.copy(orderDTO, teboQueueOrderDO);

        String unionid = AppletUtil.getUnionIdByRequest(request);
        orderDTO.setUnionid(unionid);
        /**
         * 若有未处理或未支付的订单，提示不允许取号
         */
        checkIfExist(orderDTO);

        TeboShop teboShop = remoteShopService.getShopInfo(orderDTO.getShopId()).getData();
        if (teboShop == null) {
            int count = 1;
            while (count <= 5 && ObjectUtil.isEmpty(teboShop)) {
                teboShop = remoteShopService.getShopInfo(orderDTO.getShopId()).getData();
                count++;
                if (ObjectUtil.isNotEmpty(teboShop)){
                    break;
                }
            }
        }
        if (teboShop == null) {
            throw new GlobalException("店铺不存在");
        }
        if (Objects.equals(teboShop.getOpenWorkbench(), Boolean.FALSE) || Objects.equals(teboShop.getStatus(), 0)) {
            throw new GlobalException("该门店未开通叫号业务或门店已停业");
        }

        TeboConsumer teboConsumer = remoteCustomerService.selectByUnionId(unionid).getData();

        teboQueueOrderDO.setId(SnowFlakeUtil.nextId());
        teboQueueOrderDO.setNickName(teboConsumer.getNickName());
        teboQueueOrderDO.setPhoneNumber(teboConsumer.getPhoneNumber());
        teboQueueOrderDO.setUnionid(unionid);
        teboQueueOrderDO.setTenantId(teboShop.getTenantId());
        teboQueueOrderDO.setShopName(teboShop.getShopName());
        teboQueueOrderDO.setOrderNo(TeboNumberGenerator.generateRescueQueueOrderPdNo());
        teboQueueOrderDO.setOrderStatus(QueueOrderStatusEnum.UN_CALL.getCode());
        teboQueueOrderDO.setPickupType(PickupTypeEnum.PICKUP_TYPE_ONSITE.getCode());
        teboQueueOrderDO.setCreateBy(teboConsumer.getNickName());
        teboQueueOrderDO.setUpdateBy(teboConsumer.getNickName());
        //判断是否有预约到店单
        TeboRescueQueueOrderDO preOrder = getPreOrderDo(orderDTO, unionid);

        //原来的预约到店 变成完成
        if (Objects.nonNull(preOrder)) {
            TeboRescueQueueOrderDO update = new TeboRescueQueueOrderDO();
            update.setId(preOrder.getId());
            update.setOrderStatus(QueueOrderStatusEnum.COMPLETED.getCode());
            rescueQueueOrderManger.updateById(update);
        }

        // 生成排队号
        generateQueueNum(teboQueueOrderDO, preOrder);
        int x = teboQueueOrderMapper.insert(teboQueueOrderDO);
        if (x > 0) {
            // groupCode: 0=维修+保养+安装 1=洗车
            int groupCode = QueueOrderTypeEnum.getQueueOrderTypeEnum(teboQueueOrderDO.getOrderType()).getGroupCode();
            //存的是下一个待叫号的单的id
            String key = "";
            //根据是否有预约，放入预约相关的key中
            if (QueueOrderTypeEnum.WASH.getGroupCode() == groupCode) {
                key = Objects.isNull(preOrder) ? TeboRescueCacheConstant.TEBO_STORE_QUEUE_CAR_WASH + teboShop.getId() :
                        TeboRescueCacheConstant.TEBO_STORE_QUEUE_CAR_WASH_PRE + teboShop.getId();
            } else if (QueueOrderTypeEnum.REPAIR.getGroupCode() == groupCode) {
                key = Objects.isNull(preOrder) ? TeboRescueCacheConstant.TEBO_STORE_QUEUE_CAR_REPAIR + teboShop.getId() :
                        TeboRescueCacheConstant.TEBO_STORE_QUEUE_CAR_REPAIR_PRE + teboShop.getId()
                ;
            } else {
                throw new ServiceException("尚未支持的服务类型,请联系管理员");
            }


            //记录行为
            RemoteConsumerRecordDTO recordDTO = new RemoteConsumerRecordDTO();

            recordDTO.setLastType(RemoteConsumerRecordEnum.NATURAL_ARRIVAL_STORE.getType());
            recordDTO.setShopId(orderDTO.getShopId());
            recordDTO.setUnionId(unionid);
            disMqService.recordConsumerRecord(recordDTO);


            Long currentQueueId = redisService.getCacheObject(key);
            // 已有则判断是否是待叫号，不是则获取最新待叫号的订单
            if (currentQueueId != null) {
                TeboQueueOrderDO queueOrderDO = teboQueueOrderMapper.selectById(currentQueueId);
                if (Objects.isNull(queueOrderDO)) {
                    throw new ServiceException("未找到排队单");
                }
                if (!Objects.equals(queueOrderDO.getOrderStatus(), QueueOrderStatusEnum.UN_CALL.getCode())) {
                    Long nextQueueId = teboQueueOrderMapper.selectNextQueueId(teboQueueOrderDO);
                    if (nextQueueId != null) {
                        log.info("取号时存入下个带叫号key:{}", key);
                        redisService.setCacheObject(key, nextQueueId, 1L, TimeUnit.DAYS);
                    }
                }
                return true;
            }
            //当前这单设置为待叫号单
            log.info("取号时存入下个带叫号key:{}", key);
            redisService.setCacheObject(key, teboQueueOrderDO.getId(), 1L, TimeUnit.DAYS);
        }

        return true;
    }

    /**
     * 拿到预约排队单
     *
     * @param orderDTO
     * @param unionid
     */
    private TeboRescueQueueOrderDO getPreOrderDo(QueueOrderDTO orderDTO, String unionid) {
        TeboRescueQueueOrderDO preOrder = null;
        RescueQueueOrderQueryDTO queueOrderQueryDTO = new RescueQueueOrderQueryDTO();
        queueOrderQueryDTO.setOrderType(QueueBusinessTypeEnum.PRE_STORE.getCode());
        queueOrderQueryDTO.setUnionid(unionid);
        queueOrderQueryDTO.setShopId(orderDTO.getShopId());
        queueOrderQueryDTO.setOrderStatus(QueueOrderStatusEnum.PROCESSING.getCode());
        List<TeboRescueQueueOrderDO> rescueQueueOrderDOList = rescueQueueOrderManger.listBySO(queueOrderQueryDTO);
        if (CollectionUtil.isNotEmpty(rescueQueueOrderDOList)) {
            List<TeboRescueQueueOrderDO> preOrders = rescueQueueOrderDOList.stream().filter(e -> {
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime preDoorTime = e.getPreDoorTime();
                //当前时间>预约时间15min前  <预约时间15min后
                if (Objects.isNull(preDoorTime)) {
                    return false;
                }
                LocalDateTime preTime15after = preDoorTime.plusMinutes(15);
                LocalDateTime preTime15before = preDoorTime.plusMinutes(-15);
                if (now.isBefore(preTime15after) && now.isAfter(preTime15before)) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(preOrders)) {
                preOrder = preOrders.get(0);
            }
        }
        return preOrder;
    }

    private void checkIfExist(QueueOrderDTO orderDTO) {
        QueueOrderQueryDTO queryDTO = new QueueOrderQueryDTO();
        queryDTO.setServiceOrderType(QueueOrderTypeEnum.getQueueOrderTypeEnum(orderDTO.getOrderType()).getGroupCode());
        queryDTO.setUnionid(orderDTO.getUnionid());

        queryDTO.setOrderStatusList(Arrays.asList(QueueOrderStatusEnum.UN_CALL.getCode(), QueueOrderStatusEnum.PROCESSING.getCode(), QueueOrderStatusEnum.WAIT_PAY.getCode()));
        List<TeboQueueOrderDO> list = teboQueueOrderMapper.list(queryDTO);
        if (list.isEmpty()) {
            return;
        }
        throw new GlobalException("您的取号正在服务中，请完工或取消后再行取号！");
    }


    /**
     * 生成排队号
     */
    private void generateQueueNum(TeboQueueOrderDO teboQueueOrderDO, TeboRescueQueueOrderDO preOrder) {
        // 0:维修+安装+保养 1:洗车
        int groupCode = QueueOrderTypeEnum.getQueueOrderTypeEnum(teboQueueOrderDO.getOrderType()).getGroupCode();

        // 获取排队号
        String key = Objects.isNull(preOrder) ? TeboRescueCacheConstant.getRedisNextQueueNumberKey(teboQueueOrderDO.getShopId(), groupCode) :
                TeboRescueCacheConstant.getRedisNextQueueNumberKeyPre(teboQueueOrderDO.getShopId(), groupCode);

        Integer num = redisService.getCacheObject(key);
        String numStr;
        // 表示是今天第一个取号的人
        if (num == null) {
            redisService.setCacheObject(key, 1, 1L, TimeUnit.DAYS);
            num = 1;
            // 排队号码
            numStr = Objects.nonNull(preOrder) ? QueueOrderTypeEnum.generateCallCodeYuyue(teboQueueOrderDO.getOrderType(), num) : QueueOrderTypeEnum.generateCallCode(teboQueueOrderDO.getOrderType(), num);
        } else {
            AtomicInteger atomicInteger = new AtomicInteger(num);
            num = atomicInteger.incrementAndGet();
            numStr = Objects.nonNull(preOrder) ? QueueOrderTypeEnum.generateCallCodeYuyue(teboQueueOrderDO.getOrderType(), num) : QueueOrderTypeEnum.generateCallCode(teboQueueOrderDO.getOrderType(), num);
            redisService.setCacheObject(key, num, 1L, TimeUnit.DAYS);
        }
        teboQueueOrderDO.setQueueNumber(num);
        teboQueueOrderDO.setQueueNumberStr(numStr);
    }

    /**
     * 获取排队订单列表
     */
    @Override
    public List<QueueOrderListVO> listQueueOrder(QueueOrderQueryDTO queryDTO) {
        if (queryDTO == null) {
            throw new GlobalException("请求参数为空");
        }

        List<TeboQueueOrderDO> list = teboQueueOrderMapper.list(queryDTO);
        // 获取排队订单id->服务订单映射map
        Map<Long, TeboServiceOrderDO> serviceOrderMap = initMap(list);

        return list.parallelStream().map(item -> {
            QueueOrderListVO vo = new QueueOrderListVO();
            BeanUtils.copyProperties(item, vo);
            TeboShop teboShop = remoteShopService.getShopInfo(item.getShopId()).getData();
            if (teboShop != null) {
                vo.setShopPhoneNumber(teboShop.getPhoneNumber());
            }
            TeboServiceOrderDO serviceOrderDO = serviceOrderMap.get(vo.getId());
            if (serviceOrderDO != null) {
                vo.setIsAgentPay(serviceOrderDO.getIsAgentPay());
            }
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public void exportQueueOrder(HttpServletResponse response, QueueOrderQueryDTO queryDTO) {

        if (queryDTO == null) {
            throw new GlobalException("请求参数为空");
        }

        List<TeboQueueOrderDO> list = teboQueueOrderMapper.list(queryDTO);
        // 获取排队订单id->服务订单映射map
//        Map<Long, TeboServiceOrderDO> serviceOrderMap = initMap(list);


//        List<Long> shopIds = list.stream().filter(e -> Objects.nonNull(e.getShopId())).map(TeboQueueOrderDO::getShopId).collect(Collectors.toSet()).stream().collect(Collectors.toList());
//        List<TeboShopListVO> shopListVOS = new ArrayList<>();
//        if (CollectionUtil.isNotEmpty(shopIds)) {
//            DataUtils.splitHandle(shopIds, splitList -> {
//                TeboShopQueryDTO teboShopQueryDTO = new TeboShopQueryDTO();
//                teboShopQueryDTO.setIdList(splitList);
//                R<List<TeboShopListVO>> shopResult = remoteShopService.fullyQuery(teboShopQueryDTO);
//                List<TeboShopListVO> shopList = shopResult.getData();
//                if (CollectionUtil.isNotEmpty(shopList)) {
//                    shopListVOS.addAll(shopList);
//                }
//            }, 200);
//
//        }
//        final Map<Long, TeboShopListVO> shopMap = DataUtils.listToMap(shopListVOS, TeboShopListVO::getId);
        List<QueueOrderListVO> dataList = list.parallelStream().map(item -> {
            QueueOrderListVO vo = new QueueOrderListVO();
            BeanUtils.copyProperties(item, vo);
//            TeboShopListVO shopListVO = shopMap.get(item.getShopId());
//            if (shopListVO != null) {
//                vo.setShopPhoneNumber(shopListVO.getPhoneNumber());
//            }
//            TeboServiceOrderDO serviceOrderDO = serviceOrderMap.get(vo.getId());
//            if (serviceOrderDO != null) {
//                vo.setIsAgentPay(serviceOrderDO.getIsAgentPay());
//            }
            return vo;
        }).collect(Collectors.toList());

        dataList.forEach(item -> {
            switch (item.getOrderStatus()) {
                case 0:
                    item.setOrderStatusPc(0);
                    break;
                case 1:
                case 2:
                    item.setOrderStatusPc(1);
                    break;
                case 3:
                    item.setOrderStatusPc(2);
                    break;
                case 4:
                    item.setOrderStatusPc(3);
                    break;
            }
        });


        List<QueueOrderListExcelVO> listExcelVOS = BeanConvert.copyList(dataList, QueueOrderListExcelVO::new);
        for (QueueOrderListExcelVO listExcelVO : listExcelVOS) {
            Integer orderType = listExcelVO.getOrderType();
            if (Objects.nonNull(orderType)) {
                QueueOrderTypeEnum queueOrderTypeEnum = QueueOrderTypeEnum.getQueueOrderTypeEnum(orderType);
                if (Objects.nonNull(queueOrderTypeEnum)) {
                    listExcelVO.setOrderTypeName(queueOrderTypeEnum.getName());
                }
            }

            Integer orderStatusPc = listExcelVO.getOrderStatusPc();
            if (Objects.nonNull(orderStatusPc)) {
                QueueOrderStatusFrontEnum queueOrderTypeEnum = QueueOrderStatusFrontEnum.getQueueOrderTypeEnum((orderStatusPc));
                if (Objects.nonNull(queueOrderTypeEnum)) {
                    listExcelVO.setOrderStatusPcName(queueOrderTypeEnum.getMessage());
                }

            }
            Integer pickupType = listExcelVO.getPickupType();
            if (Objects.nonNull(pickupType)) {
                PickupTypeEnum pickTypeEnum = PickupTypeEnum.getPickTypeEnum(pickupType);
                if (Objects.nonNull(pickTypeEnum)) {
                    listExcelVO.setPickupTypeName(pickTypeEnum.getDesc());
                }
            }


        }

        // 将数据写入流中
        try {
            EasyExcel.write(response.getOutputStream(), QueueOrderListExcelVO.class).sheet("叫号管理").doWrite(listExcelVOS);
        } catch (IOException e) {
            log.error("导出文件异常", e);
            throw new ServiceException("导出文件异常,请联系管理员");
        }

    }

    /**
     * 获取排队订单id->服务订单映射map
     */
    private Map<Long, TeboServiceOrderDO> initMap(List<TeboQueueOrderDO> list) {
        Map<Long, TeboServiceOrderDO> map = new HashMap<>();
        if (list == null || list.isEmpty()) {
            return map;
        }
        List<Long> queueOrderIds = list.parallelStream().map(TeboQueueOrderDO::getId).collect(Collectors.toList());
        ServiceOrderQueryDTO serviceOrderQueryDTO = new ServiceOrderQueryDTO();
        serviceOrderQueryDTO.setQueueOrderIdList(queueOrderIds);
        return teboServiceOrderMapper.list(serviceOrderQueryDTO).stream().collect(Collectors.toMap(TeboServiceOrderDO::getQueueOrderId, item -> item));
    }

    @Override
    public Boolean commentQueueOrder(QueueOrderDTO queueOrderDTO) {
        String unionid = queueOrderDTO.getUnionid();
        TeboQueueOrderDO teboQueueOrderDO = teboQueueOrderMapper.selectById(queueOrderDTO.getId());
        if (teboQueueOrderDO == null) {
            throw new GlobalException("订单不存在");
        }
        if (!Objects.equals(teboQueueOrderDO.getOrderStatus(), QueueOrderStatusEnum.COMPLETED.getCode())) {
            throw new GlobalException("只有已完成订单可以评价");
        }

        if (teboQueueOrderDO.getCommentFinished() == 1) {
            throw new GlobalException("订单已评价");
        }
        BeanConvert.copy(queueOrderDTO, teboQueueOrderDO);
        teboQueueOrderDO.setCommentFinished(1);
        teboQueueOrderDO.setUpdateBy(unionid);
        teboQueueOrderDO.setUpdateTime(LocalDateTime.now());
        teboQueueOrderDO.setCommentTime(LocalDateTime.now());
        return teboQueueOrderMapper.updateById(teboQueueOrderDO) > 0;
    }

    @Override
    public Boolean cancelQueueOrder(Long id) {
        if (id == null) {
            throw new GlobalException("订单id为空");
        }
        TeboQueueOrderDO teboQueueOrderDO = teboQueueOrderMapper.selectById(id);
        if (!Objects.equals(teboQueueOrderDO.getOrderStatus(), QueueOrderStatusEnum.UN_CALL.getCode())
                && !Objects.equals(teboQueueOrderDO.getOrderStatus(), QueueOrderStatusEnum.PROCESSING.getCode())) {
            throw new GlobalException("只有未开单可取消");
        }
        teboQueueOrderDO.setOrderStatus(QueueOrderStatusEnum.CANCEL.getCode());
        teboQueueOrderDO.setCancelTime(LocalDateTime.now());
        return teboQueueOrderMapper.updateById(teboQueueOrderDO) > 0;
    }

    @Override
    public QueueOrderVO detailById(Long id) {
        if (id == null) {
            throw new GlobalException("订单id为空");
        }
        TeboQueueOrderDO teboQueueOrderDO = teboQueueOrderMapper.selectById(id);
        if (teboQueueOrderDO == null) {
            throw new GlobalException("订单不存在");
        }
        QueueOrderVO vo = new QueueOrderVO();
        BeanUtils.copyProperties(teboQueueOrderDO, vo);
        ServiceOrderTypeEnum serviceOrderTypeEnum = ServiceOrderTypeEnum.getEnum(teboQueueOrderDO.getOrderType());
        if (serviceOrderTypeEnum != null) {
            vo.setServiceOrderType(serviceOrderTypeEnum.getName());
        }

        // 用户相关信息
        TeboConsumer teboConsumer = remoteCustomerService.selectByUnionId(teboQueueOrderDO.getUnionid()).getData();
        vo.setNickName(teboConsumer.getNickName());
        vo.setPhoneNumber(teboConsumer.getPhoneNumber());

        // 门店相关信息
        TeboShop teboShop = remoteShopService.getShopInfo(vo.getShopId()).getData();
        if (teboShop != null) {
            vo.setShopNature(teboShop.getShopNature());
            vo.setShopAddress(teboShop.getAreaName().replace("-", "") + teboShop.getAddress());
            vo.setOpenTime(teboShop.getOpenTime());
            vo.setShopPic(teboShop.getShopPic());
            vo.setShopPhoneNumber(teboShop.getPhoneNumber());
        }


        // 待叫号订单获取前方人数
        if (Objects.equals(vo.getOrderStatus(), QueueOrderStatusEnum.UN_CALL.getCode())) {
            // 获取前方还有多少人
            getRank(vo);
        }
        // 填充关联的师傅端工单相关信息
        fillExtraInfo(vo);
        return vo;
    }

    @Override
    public void bindAccountStation(String stationName) {
        Long userId = MaintainerOnlineUserUtil.getUserId();
        R<TeboAccountInfoVO> accountInfoVOR = remoteAccountService.getAccountInfoById(userId);
        TeboAccountInfoVO accountInfoVO = accountInfoVOR.getData();
        if (Objects.isNull(accountInfoVO)) {
            throw new ServiceException("获取师傅信息失败");
        }
        Long shopId = accountInfoVO.getShopId();
        String accountStationKey = TeboRescueCacheConstant.getAccountStationKey(shopId, stationName);
        Object cacheObject = redisService.getCacheObject(accountStationKey);
        if (Objects.isNull(cacheObject)) {

            removeBeforeStation(userId, shopId);

            redisService.setCacheObject(accountStationKey, userId, 24L, TimeUnit.HOURS);


        } else {
            throw new ServiceException("该工位已有师傅在使用");
        }


    }

    /**
     * //去掉之前选的工位
     *
     * @param userId
     * @param shopId
     */
    private void removeBeforeStation(Long userId, Long shopId) {
        String keyPrefix = "tebo_rescue:store_queue:shop_account_station:" + DateUtil.genDateStr() + ":" + shopId;
        Collection<String> keys = redisService.keys(keyPrefix + "*");
        if (CollectionUtil.isNotEmpty(keys)) {
            for (String key : keys) {
                Long userInRedis = redisService.getCacheObject(key);
                if (userInRedis.equals(userId)) {
                    redisService.deleteObject(key);
                }
            }
        }
    }

    @Transactional
    @Override
    public QueueOrderWaitVO callQueueOrder(CallQueueOrderDTO callQueueOrderDTO) {
        Long shopId = callQueueOrderDTO.getShopId();

        if (callQueueOrderDTO == null || shopId == null || callQueueOrderDTO.getOrderType() == null) {
            throw new GlobalException("请求参数为空");
        }
        // 获取师傅端账号id
        Long accountId = MaintainerOnlineUserUtil.getUserId();

        R<TeboAccountInfoVO> accountInfoR = remoteAccountService.getAccountInfoById(accountId);
        TeboAccountInfoVO accountInfoVO = accountInfoR.getData();


        // 判断是否是否有未处理的工单
        if (teboServiceOrderManger.hasInMaintainProgressOrder(accountId)) {
            throw new GlobalException("有未处理的工单，请先处理");
        }

        // 判断是否有处理中的订单，有先设置为取消
        cancelQueueOrderIfExist(accountId, callQueueOrderDTO);

        //先从预约的key取数据，没有的话从非预约的拿
        Integer orderType = callQueueOrderDTO.getOrderType();
        Long queueId = null;
        String key = null;

        //是否是提前预约，默认是
        Boolean pre = true;
        if (ServiceOrderTypeEnum.WASH.getCode() == orderType) {
            key = TeboRescueCacheConstant.TEBO_STORE_QUEUE_CAR_WASH_PRE + shopId;
            queueId = redisService.getCacheObject(key);
            if (queueId == null) {
                pre = false;
                //从非预约的redis key获取
                key = TeboRescueCacheConstant.TEBO_STORE_QUEUE_CAR_WASH + shopId;
                queueId = redisService.getCacheObject(key);
                if (Objects.isNull(queueId)) {
                    log.error("暂无待叫号订单,key:{}", key);
                    throw new GlobalException("暂无待叫号订单");
                }
            }
        } else if (ServiceOrderTypeEnum.REPAIR.getCode() == orderType) {
            key = TeboRescueCacheConstant.TEBO_STORE_QUEUE_CAR_REPAIR_PRE + shopId;
            queueId = redisService.getCacheObject(key);
            if (queueId == null) {
                pre = false;
                //从非预约的redis key获取
                key = TeboRescueCacheConstant.TEBO_STORE_QUEUE_CAR_REPAIR + shopId;
                queueId = redisService.getCacheObject(key);
                if (Objects.isNull(queueId)) {
                    log.error("暂无待叫号订单,key:{}", key);
                    throw new GlobalException("暂无待叫号订单");
                }
            }
        } else {
            throw new ServiceException("未支持的服务类型，请联系管理员");
        }


        TeboQueueOrderDO teboQueueOrderDO = teboQueueOrderMapper.selectById(queueId);
        if (teboQueueOrderDO == null) {
            throw new GlobalException("待叫号订单不存在");
        }

        TeboQueueOrderDO current = getUnCallQueueOrder(teboQueueOrderDO, key);

        current.setStationName(callQueueOrderDTO.getStationName());
        current.setOrderStatus(QueueOrderStatusEnum.PROCESSING.getCode());
        current.setAccountId(accountId);
        if (Objects.nonNull(accountInfoVO)) {
            current.setAccountName(accountInfoVO.getAccountName());
        }


        current.setCallTime(LocalDateTime.now());
        if (Boolean.TRUE.equals(pre)) {
            current.setPickupType(PickupTypeEnum.PICKUP_TYPE_RESERVE.getCode());
        } else {
            current.setPickupType(PickupTypeEnum.PICKUP_TYPE_ONSITE.getCode());
        }

        teboQueueOrderMapper.updateById(current);
        //当前店铺，这个排队单下一个queue_number的单id
        Long nextQueueId = teboQueueOrderMapper.selectNextQueueId(teboQueueOrderDO);

        if (nextQueueId == null) {
            // 所有订单都已叫号
            redisService.deleteObject(key);
        } else {
            // 还有订单未叫号
            log.info("叫号后，下一个带叫号订单key:{}", key);
            redisService.setCacheObject(key, nextQueueId);
        }
        if (nextQueueId == null) {
            // 所有订单都已叫号
            redisService.deleteObject(key);
        } else {
            // 还有订单未叫号
            log.info("叫号后，下一个带叫号订单key:{}", key);
            redisService.setCacheObject(key, nextQueueId);
        }
        QueueOrderWaitVO result = new QueueOrderWaitVO();

        TeboConsumer teboConsumer = remoteCustomerService.selectByUnionId(teboQueueOrderDO.getUnionid()).getData();
        result.setQueueNumberStr(current.getQueueNumberStr());
        result.setNickName(teboConsumer.getNickName());
        result.setId(current.getId());
        result.setPhoneNumber(teboConsumer.getPhoneNumber());
        result.setOrderType(current.getOrderType());
        result.setStationName(callQueueOrderDTO.getStationName());

        TeboShop teboShop = remoteShopService.getShopInfo(teboQueueOrderDO.getShopId()).getData();
        result.setCid(teboShop.getCid());
        result.setUnionid(teboConsumer.getUnionid());
        result.setShopId(Long.parseLong(teboShop.getId()));
        // TODO 手动加多线程
        new Thread(() -> sendTemplateMessage(result)).start();
        new Thread(() -> pushMessage(result)).start();
        redisService.setCacheObject(result.getId() + "_times", 1);
        return result;
    }


    /**
     * 发送消息推送到指定cid设备
     */
    public void pushMessage(QueueOrderWaitVO waitVO) {
        // 暂时写死cid
        if (StringUtils.isEmpty(waitVO.getCid())) {
            waitVO.setCid("968b4998de299753561749cb6a587196");
        }

        log.info("inThread,waitVO={}", JSON.toJSONString(waitVO));
        String token = redisService.getCacheObject("getui_token");
        if (StringUtils.isEmpty(token)) {
            token = getCidToken();
        }
        Map<String, String> header = new HashMap<>();
        header.put("token", token);
        String phone = waitVO.getPhoneNumber();
        String phoneCall = getPhoneCall(phone);
        String typeName = getTypeName(waitVO.getOrderType());
        String content = "请手机尾号 " + phoneCall + " 的客户到"
                + waitVO.getStationName() + "号" + typeName + "工位" + typeName;
        disMqService.notifyAppMsg(waitVO.getShopId(), content);
    }

    private TeboQueueOrderDO getUnCallQueueOrder(TeboQueueOrderDO teboQueueOrderDO, String key) {
        if (!Objects.equals(teboQueueOrderDO.getOrderStatus(), QueueOrderStatusEnum.UN_CALL.getCode())) {
            // 订单已叫号 取下一个
            Long queueId = teboQueueOrderMapper.selectNextQueueId(teboQueueOrderDO);
            if (queueId == null) {
                // 所有订单都已叫号
                redisService.deleteObject(key);
                throw new GlobalException("暂无待叫号订单");
            }
            TeboQueueOrderDO queueOrderDO = teboQueueOrderMapper.selectById(queueId);
            return getUnCallQueueOrder(queueOrderDO, key);
        }
        return teboQueueOrderDO;

    }

    /**
     * 发送微信公众号消息提醒
     */
    private void sendTemplateMessage(QueueOrderWaitVO result) {
        String phone = result.getPhoneNumber();

        WxPushDTO pushDTO = new WxPushDTO();
        pushDTO.setTemplate_id(templateId);
        pushDTO.setAppid(appid);
        pushDTO.setPagepath(page);
        pushDTO.setOfficialAccount("泰博出行");
        pushDTO.setUnionid(result.getUnionid());
        HashMap<String, Object> data = new HashMap<>();

        JSONObject character_string2 = new JSONObject();
        character_string2.put("value", result.getQueueNumberStr());
        data.put("character_string2", character_string2);
        JSONObject character_string6 = new JSONObject();
        character_string6.put("value", phone.substring(phone.length() - 4));
        data.put("character_string6", character_string6);
        JSONObject character_string7 = new JSONObject();
        character_string7.put("value", result.getStationName());
        data.put("character_string7", character_string7);
        pushDTO.setData(data);
        log.info("pushDTO={}", JSON.toJSONString(pushDTO));
        remoteWxPushService.sendMessage(pushDTO);
    }


    /**
     * 获取当前待叫号订单
     */
    @Override
    public List<QueueOrderVO> getCurrentQueueOrder(String unionid) {
        QueueOrderQueryDTO queryDTO = new QueueOrderQueryDTO();
        queryDTO.setUnionid(unionid);
        queryDTO.setOrderStatus(QueueOrderStatusEnum.UN_CALL.getCode());
        List<TeboQueueOrderDO> list = teboQueueOrderMapper.list(queryDTO);
        if (list.isEmpty()) {
            return Collections.emptyList();
        }

        // TODO 时间紧迫，过两天改，先循环调用
        return list.stream().map(item -> detailById(item.getId())).collect(Collectors.toList());
    }

    @Transactional
    @Override
    public Boolean cancelExpiredQueueOrder() {
        log.info("==========执行定时任务中==========");
        QueueOrderQueryDTO queryDTO = new QueueOrderQueryDTO();
        queryDTO.setMaxTime(LocalDateTime.now());
        List<Long> list = teboQueueOrderMapper.listUnCallingbeforeToday(queryDTO)
                .stream().map(TeboQueueOrderDO::getId).collect(Collectors.toList());
        log.info("list={}", JSON.toJSONString(list));
        teboQueueOrderMapper.batchCancel(list);
        //清空redis
        String key = "tebo_rescue:store_queue:" + "car_";
        Collection<String> keys = redisService.keys(key + "*");
        redisService.deleteObject(keys);
        return true;
    }


    @Override
    public QueueOrderCountVO count(String unionid) {
        QueueOrderQueryDTO queryDTO = new QueueOrderQueryDTO();
        queryDTO.setUnionid(unionid);
        QueueOrderCountVO vo = new QueueOrderCountVO();
        List<QueueOrderStatusCountVO> voList = teboQueueOrderMapper.orderStatusCount(queryDTO);
        if (voList.isEmpty()) {
            return vo;
        }
        Map<Integer, Integer> map = voList.stream()
                .collect(Collectors.toMap(QueueOrderStatusCountVO::getOrderStatus, QueueOrderStatusCountVO::getNum));
        //vo.setProcessing(map.getOrDefault(QueueOrderStatusEnum.PROCESSING.getCode(),0));
        //vo.setCancel(map.getOrDefault(QueueOrderStatusEnum.CANCEL.getCode(),0));
        vo.setWaitPay(map.getOrDefault(QueueOrderStatusEnum.WAIT_PAY.getCode(), 0));
        Integer completed = map.getOrDefault(QueueOrderStatusEnum.COMPLETED.getCode(), 0);
        queryDTO.setOrderStatus(QueueOrderStatusEnum.COMPLETED.getCode());
        queryDTO.setCommentFinished(0);
        voList = teboQueueOrderMapper.orderStatusCount(queryDTO);
        if (!voList.isEmpty()) {
            Integer unCommented = voList.get(0).getNum();
            vo.setUnComment(unCommented);
        }
        //vo.setAll(completed + vo.getProcessing() + vo.getCancel() + vo.getWaitPay());
        return vo;
    }

    @Override
    public QueueOrderWaitVO recall(CallQueueOrderDTO callQueueOrderDTO) {
        TeboQueueOrderDO teboQueueOrderDO = teboQueueOrderMapper.selectById(callQueueOrderDTO.getId());

        QueueOrderWaitVO result = new QueueOrderWaitVO();
        result.setQueueNumberStr(teboQueueOrderDO.getQueueNumberStr());
        TeboConsumer teboConsumer = remoteCustomerService.selectByUnionId(teboQueueOrderDO.getUnionid()).getData();
        result.setNickName(teboConsumer.getNickName());
        result.setPhoneNumber(teboConsumer.getPhoneNumber());
        result.setOrderType(teboQueueOrderDO.getOrderType());
        result.setId(teboQueueOrderDO.getId());
        result.setStationName(teboQueueOrderDO.getStationName());
        result.setUnionid(teboConsumer.getUnionid());


        TeboShop teboShop = remoteShopService.getShopInfo(teboQueueOrderDO.getShopId()).getData();
        result.setCid(teboShop.getCid());
        result.setShopId(Long.parseLong(teboShop.getId()));
        // 语音播报消息推送
        // TODO 手动加多线程
        new Thread(() -> sendTemplateMessage(result)).start();
        new Thread(() -> pushMessage(result)).start();
        String key = TeboRescueCacheConstant.TEBO_RESCUE_PREFIX + TeboRescueCacheConstant.RECALL + result.getId().toString() + "_times";
        Integer times = redisService.getCacheObject(key) == null ?
                1 : redisService.getCacheObject(key);
        //已经大于3次，不在累加
        if (times >= 3) {
            result.setCanCall(1);
        } else {
            result.setCanCall(0);
            AtomicInteger atomicInteger = new AtomicInteger(times);
            redisService.setCacheObject(key, atomicInteger.incrementAndGet(), 1L, TimeUnit.DAYS);
        }

        return result;
    }

    @Override
    public Map<Long, Integer> getShopCustomerNum(QueueOrderQueryDTO queryDTO) {
        Map<Long, Integer> map = new HashMap<>();
        if (queryDTO.getShopIdList() == null || queryDTO.getShopIdList().isEmpty()) {
            return map;
        }
        return teboQueueOrderMapper.getShopCustomerNum(queryDTO).stream().collect(Collectors.toMap(ShopCustomerVO::getShopId, ShopCustomerVO::getNum));
    }

    /**
     * 取消未响应的客户订单
     */
    private void cancelQueueOrderIfExist(Long accountId, CallQueueOrderDTO callQueueOrderDTO) {
        if (accountId != null) {
            QueueOrderQueryDTO queryDTO = new QueueOrderQueryDTO();
            queryDTO.setAccountId(accountId);
            queryDTO.setOrderStatus(QueueOrderStatusEnum.PROCESSING.getCode());
            //queryDTO.setServiceOrderType(callQueueOrderDTO.getOrderType());
            List<TeboQueueOrderDO> list = teboQueueOrderMapper.list(queryDTO);
            if (!list.isEmpty()) {
                TeboQueueOrderDO teboQueueOrderDO = list.get(0);
                if (teboQueueOrderDO != null) {
                    teboQueueOrderDO.setOrderStatus(QueueOrderStatusEnum.CANCEL.getCode());
                    teboQueueOrderDO.setCancelTime(LocalDateTime.now());
                    teboQueueOrderMapper.updateById(teboQueueOrderDO);
                }
            }
        }
    }

    private void fillExtraInfo(QueueOrderVO vo) {
        TeboServiceOrderDO serviceOrderDO = teboServiceOrderMapper.selectByQueueOrderId(vo.getId());
        if (serviceOrderDO == null) {
            return;
        }
        vo.setIsAgentPay(serviceOrderDO.getIsAgentPay());

        ServiceTypeEnum serviceTypeEnum = ServiceTypeEnum.getServiceTypeEnumByCode(serviceOrderDO.getServiceType());
        if (serviceTypeEnum != null) {
            vo.setServiceType(serviceTypeEnum.getName());
        }
        TeboAccountInfoVO accountInfoVO = remoteAccountService.getAccountInfoById(serviceOrderDO.getAccountId()).getData();
        vo.setAccountName(accountInfoVO.getAccountName());
        vo.setServiceAmount(serviceOrderDO.getBaseAmount());
        vo.setServiceOrderCreateTime(serviceOrderDO.getCreateTime());
        ServiceOrderGoodsQueryDTO queryDTO = new ServiceOrderGoodsQueryDTO();
        queryDTO.setServiceOrderId(serviceOrderDO.getId().toString());
        List<TeboServiceOrderGoodsDO> orderGoodsList = teboServiceOrderGoodsMapper.list(queryDTO);
        if (orderGoodsList.isEmpty()) {
            return;
        }
        BigDecimal goodsAmount = BigDecimal.ZERO;
        for (TeboServiceOrderGoodsDO orderGoodsDO : orderGoodsList) {
            goodsAmount = goodsAmount.add(new BigDecimal(orderGoodsDO.getGoodsAmount()));
        }
        vo.setGoodsAmount(goodsAmount.intValue());
        vo.setOrderGoodsVOList(BeanConvert.copyList(orderGoodsList, ServiceOrderGoodsVO::new));

        // 优惠券信息填充
        // 优惠券信息
        List<TeboServiceOrderCouponDO> orderCouponList = serviceOrderCouponMapper.selectByServiceOrderId(serviceOrderDO.getId());
        BigDecimal couponAmount = BigDecimal.ZERO;
        if (!orderCouponList.isEmpty()) {
            for (TeboServiceOrderCouponDO orderCouponDO : orderCouponList) {
                couponAmount = couponAmount.add(new BigDecimal(orderCouponDO.getCouponPrice()));
            }
        }
        vo.setPayBy(serviceOrderDO.getPayBy());
        vo.setCouponAmount(couponAmount.intValue());
        vo.setCouponList(BeanConvert.copyList(orderCouponList, ServiceOrderCouponVO::new));
    }

    /**
     * 获取排队号，前方还有n人排队
     */
    private void getRank(QueueOrderVO vo) {
        QueueOrderRankQueryDTO queryDTO = new QueueOrderRankQueryDTO();
        queryDTO.setCreateTime(vo.getCreateTime());
        queryDTO.setShopId(vo.getShopId());
        queryDTO.setOrderType(vo.getOrderType());
        queryDTO.setQueueNumber(vo.getQueueNumber());
        queryDTO.setOrderStatusList(Collections.singletonList(QueueOrderStatusEnum.UN_CALL.getCode()));
        // 查询当前类型订单前还有多少个待叫号订单
        Integer rank = teboQueueOrderMapper.selectRank(queryDTO);
        vo.setRank(rank);
    }


    private String getTypeName(Integer orderType) {
        switch (orderType) {
            case 0:
            case 1:
            case 2:
                return "维修";
            case 3:
                return "洗车";
        }
        return null;
    }

    private String getPhoneCall(String phone) {
        Map<Character, String> map = new HashMap<>();
        map.put('0', "零");
        map.put('1', "一");
        map.put('2', "二");
        map.put('3', "三");
        map.put('4', "四");
        map.put('5', "五");
        map.put('6', "六");
        map.put('7', "七");
        map.put('8', "八");
        map.put('9', "九");
        String lastFour = phone.substring(phone.length() - 4);
        return map.get(lastFour.charAt(0)) + map.get(lastFour.charAt(1)) + map.get(lastFour.charAt(2)) + map.get(lastFour.charAt(3));

    }

    private String initPushParams(String content, String cid) {
        JSONObject settings = new JSONObject();
        settings.put("ttl", 7200000);
        JSONObject audience = new JSONObject();
        audience.put("cid", Collections.singletonList(cid));
        JSONObject pushMessage = new JSONObject();
        pushMessage.put("transmission", content);
        JSONObject request = new JSONObject();
        request.put("settings", settings);
        request.put("audience", audience);
        request.put("push_message", pushMessage);
        request.put("request_id", UUID.randomUUID().toString());
        return request.toJSONString();

    }

    private String getCidToken() {
        String url = AppletRequestUrl.GET_TOKEN_2;
        Long start = System.currentTimeMillis();
        System.out.println(start);
        String appKey = "GmNESCamrn5OCZPJ7tJU9";
        String mastersecret = "DP7L3xKfBR666CfBMzNaD8";

        String encryptedString = Sha256Utils.getSha256(appKey + start + mastersecret);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sign", encryptedString);
        jsonObject.put("appkey", appKey);
        jsonObject.put("timestamp", start.toString());

        String res = HttpTool.sendPost(url, jsonObject.toJSONString());
        JSONObject resObj = JSONObject.parseObject(res);
        JSONObject data = resObj.getJSONObject("data");
        String token = data.getString("token");
        redisService.setCacheObject("getui_token", token, 1L, TimeUnit.DAYS);
        return token;
    }


}
