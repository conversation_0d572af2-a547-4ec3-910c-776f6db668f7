package com.tebo.rescue.service;

import com.tebo.common.core.domain.R;
import com.tebo.rescue.applet.domain.dto.CallQueueOrderDTO;
import com.tebo.rescue.applet.domain.dto.QueueOrderDTO;
import com.tebo.rescue.applet.domain.dto.QueueOrderQueryDTO;
import com.tebo.rescue.applet.domain.view.QueueOrderCountVO;
import com.tebo.rescue.applet.domain.view.QueueOrderListVO;
import com.tebo.rescue.applet.domain.view.QueueOrderVO;
import com.tebo.rescue.applet.domain.view.QueueOrderWaitVO;
import com.tebo.system.api.domain.view.WechatPrepayResponse;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @Author：zhengmk
 * @Date 2023/12/13 16:08
 */
public interface IQueueOrderService {


    /**
     * 新增排队单（取号）
     * @return
     */
    Boolean addQueueOrder(QueueOrderDTO orderDTO);

    /**
     * 获取排队订单列表
     */
    List<QueueOrderListVO> listQueueOrder(QueueOrderQueryDTO queryDTO);

    /**
     * 导出排队单
     * @param queryDTO
     */
    void exportQueueOrder(HttpServletResponse response,QueueOrderQueryDTO queryDTO);

    /**
     * 评价订单
     */
    Boolean commentQueueOrder(QueueOrderDTO queueOrderDTO);

    /**
     * 取消订单
     */
    Boolean cancelQueueOrder(Long id);


    /**
     * 获取详情
     */
    QueueOrderVO detailById(Long id);

    /**
     * 绑定师傅和工位
     */
    void bindAccountStation(String  stationName);

    /**
     * 叫号
     */
    QueueOrderWaitVO callQueueOrder(CallQueueOrderDTO callQueueOrderDTO);

    /**
     * 获取当前未完成的排队订单
     */
    List<QueueOrderVO> getCurrentQueueOrder(String unionid);

    /**
     * 取消过期的排队订单
     */
    Boolean cancelExpiredQueueOrder();

    /**
     * 数量统计
     */
    QueueOrderCountVO count(String unionid);

    /**
     * 重叫
     */
    QueueOrderWaitVO recall(CallQueueOrderDTO callQueueOrderDTO);

    /**
     * 获取门店-客户数量映射
     * @param queryDTO
     * @return
     */
    Map<Long, Integer> getShopCustomerNum(QueueOrderQueryDTO queryDTO);
}
