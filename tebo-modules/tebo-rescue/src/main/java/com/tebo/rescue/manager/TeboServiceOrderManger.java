package com.tebo.rescue.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tebo.rescue.applet.domain.dto.ServiceOrderQueryDTO;
import com.tebo.rescue.entity.TeboRescueQueueOrderDO;
import com.tebo.rescue.entity.TeboServiceOrderDO;

import java.util.List;

public interface TeboServiceOrderManger extends IService<TeboServiceOrderDO> {

    /**
     * 通过排队救援单生成工单
     */
    Boolean createServiceOrderByRescueQueueOrder(TeboRescueQueueOrderDO rescueQueueOrderDO);


    /**
     * 生成工单号
     * @return
     */
    String buildServiceOrderNo();

    TeboServiceOrderDO getById(Long id);

//    Integer updateById(TeboServiceOrderDO update);

    /**
     * 这个师傅有进行中的工单 （救援+到店）
     * @param accountId
     */
    Boolean hasInMaintainProgressOrder(Long accountId);


    /**
     * 条件查询
     * @param queryDTO
     * @return
     */
    List<TeboServiceOrderDO> list(ServiceOrderQueryDTO queryDTO);
    Integer count(ServiceOrderQueryDTO queryDTO);

}
