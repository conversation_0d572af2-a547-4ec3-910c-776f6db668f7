package com.tebo.rescue.service;

import com.tebo.rescue.applet.domain.view.TeboGiftPackConsumerNewVO;
import com.tebo.rescue.applet.domain.view.TeboGiftPackConsumerVO;
import com.tebo.rescue.web.domain.dto.pack.TeboGiftPackChangeStatusDTO;
import com.tebo.rescue.web.domain.dto.pack.TeboGiftPackCreateDTO;
import com.tebo.rescue.web.domain.dto.pack.TeboGiftPackQueryDTO;
import com.tebo.rescue.web.domain.dto.pack.TeboGiftPackUpdateDTO;
import com.tebo.rescue.web.domain.view.TeboGiftPackVO;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2023/12/14 9:37
 * @Desc : 礼包服务
 */
public interface TeboGiftPackService {
    /**
     * 新增礼包
     * @param create 礼包信息
     * @return 新增结果
     */
    Integer create(TeboGiftPackCreateDTO create);

    List<TeboGiftPackVO> getGiftPackList(TeboGiftPackQueryDTO queryDTO);

    TeboGiftPackVO getInfo(Long id);

    TeboGiftPackConsumerNewVO getDetailById(Long id);

    Integer update(TeboGiftPackUpdateDTO update);

    Integer updateStatus(TeboGiftPackChangeStatusDTO update);

    /**
     * 扣减礼包库存
     * @param id
     * @return
     */
    Integer reduceGiftPack(Long id);
}
