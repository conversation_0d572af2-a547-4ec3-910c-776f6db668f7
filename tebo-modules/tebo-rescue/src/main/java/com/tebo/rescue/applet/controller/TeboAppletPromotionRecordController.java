package com.tebo.rescue.applet.controller;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.util.applet.AppletUtil;
import com.tebo.rescue.api.domain.dto.TeboPromoteRecordDTO;
import com.tebo.rescue.service.TeboPromoteRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


@RestController
@Slf4j
@RequestMapping("/applet/promote")
public class TeboAppletPromotionRecordController {
    @Resource
    private TeboPromoteRecordService teboPromoteRecordService;
    @Resource
    private HttpServletRequest request;

    /**
     * 激活绑定推广关系-异业
     */
    @PostMapping("/activateBinding")
    public R activateBinding(@RequestBody TeboPromoteRecordDTO teboPromoteRecordDTO) {
        log.info("activateBinding param:{}", JSONObject.toJSONString(teboPromoteRecordDTO));
        if (ObjectUtil.isEmpty(teboPromoteRecordDTO.getReferenceShopId())){
            throw new ServiceException("推广人门店id不能为空");
        }
        if (ObjectUtil.isEmpty(teboPromoteRecordDTO.getReferencePhone())){
            throw new ServiceException("推广人手机号不能为空");
        }
        String unionId = AppletUtil.getUnionIdByRequest(request);
        teboPromoteRecordDTO.setUnionId(unionId);
        teboPromoteRecordService.activateBinding(teboPromoteRecordDTO);
        return R.ok();
    }

    /**
     * 绑定推广关系-车销渠道
     */
    @PostMapping("/activeCarSaleChannel")
    public R activeCarSaleChannel(@RequestBody TeboPromoteRecordDTO teboPromoteRecordDTO) {
        log.info("activeCarSaleChannel param:{}", JSONObject.toJSONString(teboPromoteRecordDTO));
        if (ObjectUtil.isEmpty(teboPromoteRecordDTO.getPartnerCode())){
            throw new ServiceException("合伙人编码不能为空");
        }
        if (ObjectUtil.isEmpty(teboPromoteRecordDTO.getPromotedName())){
            throw new ServiceException("名称不能为空");
        }
        String unionId = AppletUtil.getUnionIdByRequest(request);
        teboPromoteRecordDTO.setUnionId(unionId);
        teboPromoteRecordDTO.setSource(1);
        teboPromoteRecordService.activeCarSaleChannel(teboPromoteRecordDTO);
        return R.ok();
    }

    @GetMapping("/selectByUnionId/{unionId}")
    public R selectByUnionId(@PathVariable("unionId") String unionId) {
        return R.ok(teboPromoteRecordService.selectByUnionId(unionId));
    }

}