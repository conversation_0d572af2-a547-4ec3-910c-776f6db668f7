package com.tebo.rescue.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.redis.service.RedisService;
import com.tebo.rescue.entity.TeboCouponCustomerDO;
import com.tebo.rescue.entity.TeboGroupPurchaseCustomerDO;
import com.tebo.rescue.enums.CouponIdEnum;
import com.tebo.rescue.mapper.TeboCouponCustomerMapper;
import com.tebo.rescue.mapper.TeboGroupPurchaseCustomerMapper;
import com.tebo.rescue.service.TeboAppletIndexService;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponCustomerQueryDTO;
import com.tebo.rescue.web.domain.view.TeboAppletIndexVO;
import com.tebo.system.api.RemoteCustomerService;
import com.tebo.system.api.model.TeboConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TeboAppletIndexServiceImpl implements TeboAppletIndexService {

    @Resource
    private RemoteCustomerService remoteCustomerService;
    @Resource
    private RedisService redisService;

    @Resource
    private TeboGroupPurchaseCustomerMapper teboGroupPurchaseCustomerMapper;
    @Resource
    private TeboCouponCustomerMapper teboCouponCustomerMapper;

    @Override
    public TeboAppletIndexVO countNumber(String unionId) {
        TeboAppletIndexVO result = new TeboAppletIndexVO();
        R<TeboConsumer> cusRes = remoteCustomerService.selectByUnionId(unionId);
        if (ObjectUtil.isEmpty(cusRes) || cusRes.getCode() != 200 || ObjectUtil.isEmpty(cusRes.getData()) ){
            return result;
        }
        TeboConsumer teboConsumer = cusRes.getData();
        if (!ObjectUtil.isEmpty(teboConsumer.getPhoneNumber())) {
            // 查询团购信息 start
            LambdaQueryWrapper<TeboGroupPurchaseCustomerDO> orderWrapper = new LambdaQueryWrapper<>();
            orderWrapper.eq(TeboGroupPurchaseCustomerDO::getPhoneNumber, teboConsumer.getPhoneNumber())
                    .eq(TeboGroupPurchaseCustomerDO::getDelFlag, 0)
                    .eq(TeboGroupPurchaseCustomerDO::getStatus, 0);
            List<TeboGroupPurchaseCustomerDO> orderList = teboGroupPurchaseCustomerMapper.selectList(orderWrapper);
            if (!CollectionUtils.isEmpty(orderList)) {
                result.setPackageNumber(orderList.size());
                result.setOrderId(orderList.get(0).getOrderId());
            }else {
                result.setPackageNumber(0);
            }
            // 查询团购信息 end
            // tebo_rescue:applet_index:login_one
            // tebo_rescue:applet_index:login_two
            result.setLoginOne(redisService.getCacheObject("tebo_rescue:applet_index:login_one"));
            result.setLoginTwo(redisService.getCacheObject("tebo_rescue:applet_index:login_two"));
        }
        return result;
    }

    @Override
    public Integer countNumberByPhoneNumber(String phoneNumber) {
        if (ObjectUtil.isEmpty(phoneNumber)) {
            return 0;
        }
        // 查询团购信息 start
        LambdaQueryWrapper<TeboGroupPurchaseCustomerDO> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.eq(TeboGroupPurchaseCustomerDO::getPhoneNumber, phoneNumber)
                .eq(TeboGroupPurchaseCustomerDO::getDelFlag, 0)
                .eq(TeboGroupPurchaseCustomerDO::getStatus, 0);
        Long count = teboGroupPurchaseCustomerMapper.selectCount(orderWrapper);
        return count > 0 ? count.intValue() : 0;
    }
}
