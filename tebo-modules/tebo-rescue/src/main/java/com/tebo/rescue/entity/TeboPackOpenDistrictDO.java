package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 会员卡开通区域
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-07
 */
@Getter
@Setter
@TableName("tebo_pack_open_district")
public class TeboPackOpenDistrictDO extends Model<TeboPackOpenDistrictDO> {

    private static final long serialVersionUID = 1L;

    @TableField("id")
    private Long id;

    @TableField("province")
    private String province;

    @TableField("city")
    private String city;

    @TableField("district")
    private String district;

    @TableField("del_flag")
    private Integer delFlag;

    @TableField("create_time")
    private LocalDateTime createTime;


    @Override
    public Serializable pkVal() {
        return null;
    }

}
