package com.tebo.rescue.service;

import com.tebo.rescue.applet.domain.view.TeboShopConsumerVO;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponCreateDTO;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponQueryDTO;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponUpdateDTO;
import com.tebo.rescue.web.domain.view.TeboCouponCustomerRecordVO;
import com.tebo.rescue.web.domain.view.TeboCouponVO;
import com.tebo.rescue.web.domain.view.TeboGiftPackCouponVO;

import java.util.List;

/**
 * <AUTHOR> Zhang<PERSON>an
 * @date : 2023/12/13 12:38
 * @Desc : 卡券管理服务接口
 */
public interface TeboCouponService {

    List<TeboCouponVO> getCouponList(TeboCouponQueryDTO query);

    List<TeboGiftPackCouponVO> getCouponListByIds(List<Long> ids);


    /**
     * 根据id查询卡券详情
     * @param id
     * @return
     */
    TeboCouponVO getInfo(Long id);

    /**
     * 创建卡券
     * @param create
     * @return
     */
    int create(TeboCouponCreateDTO create);

    int update(TeboCouponUpdateDTO update);

    /**
     * 批量更新卡券为被占用
     * @param ids
     * @return
     */
    int batchCouponInUse(List<Long> ids);

    /**
     * 批量更新卡券为不被占用
     * @param ids
     * @return
     */
    int batchCouponNotInUse(List<Long> ids);

    /**
     * 更新卡券状态
     * @param id
     * @param status  2已开始 3已过期 10已作废
     * @return
     */
    int updateCouponStatus(Long id,Integer status);

    /**
     * 根据卡券ID查询卡券的可用门店
     * @param couponId
     * @return
     */
    List<TeboShopConsumerVO>  queryCouponShop(Long couponId);

    /**
     * 查询卡券领用数据
     * @param couponId
     * @return
     */
    List<TeboCouponCustomerRecordVO> getCouponCustomerList(TeboCouponQueryDTO query);

    /**
     * 创建卡券领用记录
     * @param uniqueCode
     * @param unionId
     */
    void createCouponCustomerRecord(String uniqueCode, String unionId);
}
