package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;

import com.tebo.common.util.number.SnowFlakeUtil;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 卡券可用门店表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-13
 */
@Getter
@Setter
@TableName("tebo_coupon_shop")
public class TeboCouponShopDO extends Model<TeboCouponShopDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 卡券编号
     */
    @TableField("coupon_id")
    private Long couponId;

    /**
     * 门店ID
     */
    @TableField("shop_id")
    private Long shopId;

    public TeboCouponShopDO() {

    }

    public TeboCouponShopDO(Long couponId,Long shopId) {
        this.couponId = couponId;
        this.shopId = shopId;
        this.id = SnowFlakeUtil.nextId();
    }


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
