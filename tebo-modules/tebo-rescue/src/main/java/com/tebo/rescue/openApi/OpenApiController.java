package com.tebo.rescue.openApi;

import cn.hutool.core.util.ObjectUtil;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.rescue.applet.domain.dto.QueueOrderDTO;
import com.tebo.rescue.applet.domain.dto.ServiceOrderAfterPayDTO;
import com.tebo.rescue.applet.domain.dto.ServiceOrderDTO;
import com.tebo.rescue.applet.domain.dto.ServiceOrderQueryDTO;
import com.tebo.rescue.applet.domain.view.ServiceOrderVO;
import com.tebo.rescue.lst.domain.vo.TeboOrderGoodsIdGroup;
import com.tebo.rescue.service.IServiceOrderService;
import com.tebo.rescue.service.ITeboRescueQueueOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/openApi")
public class OpenApiController extends BaseController {

    @Autowired
    private ITeboRescueQueueOrderService rescueQueueOrderService;

    @Autowired
    private IServiceOrderService serviceOrderService;

    @GetMapping("/getUnFinishServiceOrderByAccount")
    public R<List<ServiceOrderDTO>> getUnFinishServiceOrderByAccount(@RequestParam("ids") List<Long> ids) {
        return R.ok(serviceOrderService.getUnFinishServiceOrderByAccount(ids));
    }

    /**
     * 订单详情
     */
    @GetMapping("/getServiceOrderById")
    public R<ServiceOrderDTO> getServiceOrderById(@RequestParam("id") Long id) {
        ServiceOrderVO serviceOrderVO = serviceOrderService.detailServiceOrder(id);
        ServiceOrderDTO serviceOrderDTO = new ServiceOrderDTO();
        BeanConvert.copy(serviceOrderVO,serviceOrderDTO);
        if (ObjectUtil.isNotEmpty(serviceOrderVO.getOrderAmount())){
            serviceOrderDTO.setOrderAmountStr(serviceOrderVO.getOrderAmount());
        }
        return R.ok(serviceOrderDTO);
    }

    @GetMapping("/getUnFinishQueueOrderByAccount")
    public R<List<QueueOrderDTO>> getUnFinishQueueOrderByAccount(@RequestParam("ids") List<Long> ids) {
        return R.ok(serviceOrderService.getUnFinishQueueOrderByAccount(ids));
    }



    /**
     * 快捷开单后 支付成功回调
     * @return
     */
    @PostMapping("/quickOrder/afterPaySuccess")
    public R<Boolean> updateQuickServiceOrderStatus(@RequestBody ServiceOrderAfterPayDTO serviceOrderAfterPayDTO) {
        Boolean aBoolean = serviceOrderService.updateQuickServiceOrderStatus(serviceOrderAfterPayDTO.getOrderNo());
        return R.ok(aBoolean);
    }

    /**
     * 更新工单类型为保险单
     */
    @PostMapping("/quickOrder/updateOrderType")
    public R<Boolean> updateOrderType(@RequestParam("orderNo") String orderNo) {
        Boolean result = serviceOrderService.updateOrderType(orderNo);
        return R.ok(result);
    }

    /**
     * 工单 商品销量聚合
     */
    @PostMapping("/orderGoodsIdGroup")
    public R<List<TeboOrderGoodsIdGroup>> orderGoodsIdGroup(@RequestBody ServiceOrderQueryDTO serviceOrderQueryDTO) {


        return R.ok(serviceOrderService.getOrderGoodsIdGroup(serviceOrderQueryDTO));
    }

}
