package com.tebo.rescue.applet.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author：zhengmk
 * @Date 2023/12/14 18:56
 */
@Data
public class ServiceOrderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 维修师傅id
     */
    private Long accountId;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 合伙人id
     */
    private Long tenantId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单类型0:维修 1:安装 2:保养 3:洗车
     */
    private Integer orderType;

    /**
     * 服务项目类别
     */
    private Integer serviceType;

    /**
     * 订单状态 0:维修中 1:已完成 2:已取消
     */
    private Integer orderStatus;

    /**
     * C端叫号单id
     */
    private Long queueOrderId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 订单金额,单位分
     */
    private Integer orderAmount;

    /**
     * 服务基础费用
     */
    private String baseAmount;

    /**
     * 处理结果
     */
    private String handleResult;

    /**
     * 是否代付 0:否 1:是
     */
    private Integer isAgentPay;


    // 配件列表
    private List<ServiceOrderGoodsDTO> goodsList;

    // 卡券唯一码列表
    private List<String> uniqueCodeList;


    /**
     * 折扣金额 元
     */
    private String discountAmount;

    /**
     * 取消原因
     */
    private String cancelReason;


    /**
     * 是否换新
     */
    private Boolean changeNew;

    /**
     * unionid
     */
    private String unionid;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 订单金额
     */
    private String orderAmountStr;
    // 取号人员
    private String nickName;

    // 联系电话
    private String phoneNumber;
}
