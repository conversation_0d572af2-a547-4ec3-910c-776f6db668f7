package com.tebo.rescue.applet.controller;


import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.util.number.DistanceUtil;
import com.tebo.rescue.applet.domain.dto.ProductCheckDTO;
import com.tebo.rescue.api.domain.view.CheckBatteryVO;
import com.tebo.rescue.applet.domain.view.ProductCheckVO;
import com.tebo.rescue.service.ISystemService;
import com.tebo.rescue.web.domain.dto.shop.TeboShopDistanceDTO;
import com.tebo.system.api.RemoteShopService;
import com.tebo.system.api.model.TeboShop;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 泰博出行  个人页面的接口 例如何种扫码，开关
 */
@RestController
@RequestMapping("/system")
public class SystemController {


    @Autowired
    private ISystemService systemService;

    @Resource
    private RemoteShopService shopService;
    /**
     * 防伪信息查询
     *
     * @param checkDTO
     * @return
     */
    @PostMapping("/check/product")
    public R<ProductCheckVO> checkProduct(@RequestBody ProductCheckDTO checkDTO) {
        ProductCheckVO checkVO = systemService.checkProduct(checkDTO);
        return R.ok(checkVO);
    }

    @PostMapping("/check/checkTianNengBattery")
    public R<CheckBatteryVO> checkTianNengBattery(@RequestBody ProductCheckDTO checkDTO) {
        CheckBatteryVO checkVO = systemService.checkTianNengBattery(checkDTO);
        return R.ok(checkVO);
    }

    /**
     * 校验电池码能否售卖保险
     */
    @PostMapping("/check/byInsurance")
    public R<Boolean> byInsurance(@RequestBody ProductCheckDTO checkDTO) {
        Boolean aBoolean = systemService.byInsurance(checkDTO);
        return R.ok(aBoolean);
    }

    /**
     * 获取门店与指定经纬度的距离
     *
     * @param
     * @return
     */
    @PostMapping("/getDistance")
    public R<?> getDistance(@RequestBody TeboShopDistanceDTO dto) {
        R<TeboShop> teboShopR = shopService.getShopInfo(dto.getShopId());
        if (teboShopR.getCode() != 200 ||teboShopR.getData() == null){
            throw new GlobalException("门店不存在");
        }
        TeboShop teboShop = teboShopR.getData();
        if (ObjectUtils.isEmpty(teboShop.getLatitude()) || ObjectUtils.isEmpty(teboShop.getLongitude())){
            return R.ok(0);
        }
        return R.ok(DistanceUtil.getDistance(teboShop.getLatitude(), teboShop.getLongitude(), dto.getLat(), dto.getLgt()));
    }

}
