package com.tebo.rescue.applet.controller;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.common.util.applet.AppletUtil;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.DistanceUtil;
import com.tebo.rescue.api.domain.dto.lst.ShopDto;
import com.tebo.rescue.applet.domain.dto.TeboCustomerShopQueryDTO;
import com.tebo.rescue.applet.domain.view.TeboShopConsumerVO;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponCustomerQueryDTO;
import com.tebo.system.api.RemoteShopService;
import com.tebo.system.api.domain.dto.TeboShopQueryDTO;
import com.tebo.system.api.domain.view.TeboShopListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ZhangFan
 * @date : 2023/12/24 10:00
 * @Desc : 消费者门店接口
 */
@Slf4j
@RestController
@RequestMapping("/customer/shop")
public class TeboCustomerShopController extends BaseController {
    @Resource
    private RemoteShopService remoteShopService;

    /**
     * 附近门店
     * @return
     */
    @PostMapping("/getList")
    public TableDataInfo getList(@RequestBody TeboCustomerShopQueryDTO query) {
        if (query.getLgt().equals("0") || query.getLnt().equals("0")) {
            throw new ServiceException("未能获取到用户位置信息，请先允许小程序获取您的位置");
        }
        Page page = PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<TeboShopListVO> shopListVOList = remoteShopService.fullyQuery(new TeboShopQueryDTO()).getData();
        if (CollectionUtils.isEmpty(shopListVOList)) {
            return getDataTable(Collections.emptyList(), page);
        }
        shopListVOList = shopListVOList.stream().filter(item -> item.getShowNearbyShop() != 0 && item.getStatus() == 1).collect(Collectors.toList());
        List<TeboShopConsumerVO> shopList = BeanConvert.copyList(shopListVOList, TeboShopConsumerVO::new);
        shopList.stream().forEach(item -> {
            // 计算距离
            Double distance = DistanceUtil.getDistance(query.getLgt(), query.getLnt(), item.getLongitude(), item.getLatitude());
            item.setDistance(distance);
        });
        List<TeboShopConsumerVO> result = shopList.stream().sorted(Comparator.comparing(TeboShopConsumerVO::getDistance)).limit(300).collect(Collectors.toList());
        return getDataTable(result, page);
    }


    /**
     * 修车救援-附近门店
     * @return
     */
    @PostMapping("/rescueNearbyStore")
    public TableDataInfo rescueNearbyStore(@RequestBody TeboCustomerShopQueryDTO query) {
        if (query.getLgt().equals("0") || query.getLnt().equals("0")) {
            throw new ServiceException("未能获取到用户位置信息，请先允许小程序获取您的位置");
        }
        Page page = PageHelper.startPage(query.getPageNum(), query.getPageSize());
        TeboShopQueryDTO param = new TeboShopQueryDTO();
        BeanConvert.copy(query,param);
        param.setOpenRescue(1);
        List<TeboShopListVO> shopListVOList = remoteShopService.rescueNearbyStore(param).getData();
        if (CollectionUtils.isEmpty(shopListVOList)) {
            return getDataTable(Collections.emptyList(), page);
        }
        List<TeboShopConsumerVO> shopList = BeanConvert.copyList(shopListVOList, TeboShopConsumerVO::new);
        shopList.stream().forEach(item -> {
            // 计算距离
            Double distance = DistanceUtil.getDistance(query.getLgt(), query.getLnt(), item.getLongitude(), item.getLatitude());
            item.setDistance(distance);
        });
        List<TeboShopConsumerVO> result = shopList.stream().sorted(Comparator.comparing(TeboShopConsumerVO::getDistance)).limit(300).collect(Collectors.toList());
        return getDataTable(result, page);
    }

}
