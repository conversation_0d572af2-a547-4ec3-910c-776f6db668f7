package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.tebo.common.core.utils.uuid.UUID;
import com.tebo.common.util.number.SnowFlakeUtil;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 消费者优惠券表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
@Getter
@Setter
@TableName("tebo_coupon_customer")
public class TeboCouponCustomerDO extends Model<TeboCouponCustomerDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 卡券订单编号
     */
    @TableField("order_no")
    private String orderNo;
    /**
     * 卡券编码
     */
    @TableField("unique_code")
    private String uniqueCode;

    /**
     * 卡券id
     */
    @TableField("coupon_id")
    private Long couponId;

    /**
     * 礼包id
     */
    @TableField("pack_id")
    private Long packId;

    /**
     * 领取优惠卷的会员id
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 状态0未领取1已领取未使用2已使用3已失效 10 分享中 11 已分享 20 已退款
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 占用状态 1 未占用 2已占用
     */
    @TableField("`occ_status`")
    private Integer occStatus;

    /**
     * 占用订单号
     */
    @TableField("`occ_number`")
    private String occNumber;

    /**
     * 状态0未领取1已领取未使用2已使用3已失效
     */
    @TableField("`source`")
    private Integer source;

    /**
     * 领取时间
     */
    @TableField("receive_time")
    private LocalDateTime receiveTime;

    /**
     * 有效期开始日期
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 有效期结束日期
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 核销时间
     */
    @TableField("write_off_time")
    private LocalDateTime writeOffTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    public TeboCouponCustomerDO() {

    }

    public TeboCouponCustomerDO(Long couponId, Long packId, String unionId) {
        this.id = SnowFlakeUtil.nextId();
        this.couponId = couponId;
        this.packId = packId;
        this.unionId = unionId;
    }

}
