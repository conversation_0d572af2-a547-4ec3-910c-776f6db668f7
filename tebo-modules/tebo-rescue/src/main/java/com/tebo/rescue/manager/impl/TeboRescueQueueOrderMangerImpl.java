package com.tebo.rescue.manager.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.redis.service.RedisService;
import com.tebo.rescue.applet.domain.dto.RescueQueueOrderQueryDTO;
import com.tebo.rescue.applet.domain.view.MaintenanceQueueOrderNumberVO;
import com.tebo.rescue.entity.TeboRescueQueueOrderDO;
import com.tebo.rescue.manager.TeboRescueQueueOrderManger;
import com.tebo.rescue.mapper.TeboRescueQueueOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class TeboRescueQueueOrderMangerImpl implements TeboRescueQueueOrderManger {

    @Autowired
    private TeboRescueQueueOrderMapper teboRescueQueueOrderMapper;


    @Override
    public List<TeboRescueQueueOrderDO> listBySO(RescueQueueOrderQueryDTO queueOrderQueryDTO) {
//        LambdaQueryWrapper<TeboRescueQueueOrderDO> wrapper = getWrapper(queueOrderQueryDTO);
        return teboRescueQueueOrderMapper.listBySO(queueOrderQueryDTO);
    }

    @Override
    public List<TeboRescueQueueOrderDO> listBySOOrderCreateTimeDesc(RescueQueueOrderQueryDTO queueOrderQueryDTO) {
//        LambdaQueryWrapper<TeboRescueQueueOrderDO> wrapper = getWrapper(queueOrderQueryDTO);
//        wrapper.orderByDesc(TeboRescueQueueOrderDO::getCreateTime);
        return teboRescueQueueOrderMapper.listBySODesc(queueOrderQueryDTO);
    }

    private static LambdaQueryWrapper<TeboRescueQueueOrderDO> getWrapper(RescueQueueOrderQueryDTO queueOrderQueryDTO) {
        LambdaQueryWrapper<TeboRescueQueueOrderDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(queueOrderQueryDTO.getOrderStatus()), TeboRescueQueueOrderDO::getOrderStatus, queueOrderQueryDTO.getOrderStatus())
                .eq(TeboRescueQueueOrderDO::getDelFlag, Boolean.FALSE)
                .eq(Objects.nonNull(queueOrderQueryDTO.getOrderType()), TeboRescueQueueOrderDO::getOrderType, queueOrderQueryDTO.getOrderType())
                .in(CollectionUtil.isNotEmpty(queueOrderQueryDTO.getOrderTypeList()), TeboRescueQueueOrderDO::getOrderType, queueOrderQueryDTO.getOrderTypeList())
                .eq(Objects.nonNull(queueOrderQueryDTO.getServiceItem()), TeboRescueQueueOrderDO::getServiceItem, queueOrderQueryDTO.getServiceItem())
                .ge(Objects.nonNull(queueOrderQueryDTO.getPreDoorTimeStart()), TeboRescueQueueOrderDO::getPreDoorTime, queueOrderQueryDTO.getPreDoorTimeStart())
                .le(Objects.nonNull(queueOrderQueryDTO.getPreDoorTimeEnd()), TeboRescueQueueOrderDO::getPreDoorTime, queueOrderQueryDTO.getPreDoorTimeEnd())

                //精确到秒
                .ge(Objects.nonNull(queueOrderQueryDTO.getCreateTimeStartSecond()), TeboRescueQueueOrderDO::getCreateTime, queueOrderQueryDTO.getCreateTimeStartSecond())
                .lt(Objects.nonNull(queueOrderQueryDTO.getCreateTimeEndSecond()), TeboRescueQueueOrderDO::getCreateTime, queueOrderQueryDTO.getCreateTimeEndSecond())


                .ge(Objects.nonNull(queueOrderQueryDTO.getInitiationTimeStart()), TeboRescueQueueOrderDO::getInitiationTime, queueOrderQueryDTO.getInitiationTimeStart())
                .le(Objects.nonNull(queueOrderQueryDTO.getInitiationTimeEnd()), TeboRescueQueueOrderDO::getInitiationTime, queueOrderQueryDTO.getInitiationTimeEnd())

                .eq(StringUtils.isNotEmpty(queueOrderQueryDTO.getOrderNo()), TeboRescueQueueOrderDO::getOrderNo, queueOrderQueryDTO.getOrderNo())
                .eq(Objects.nonNull(queueOrderQueryDTO.getShopId()), TeboRescueQueueOrderDO::getShopId, queueOrderQueryDTO.getShopId())
                .eq(Objects.nonNull(queueOrderQueryDTO.getShopType()), TeboRescueQueueOrderDO::getShopType, queueOrderQueryDTO.getShopType())
                .eq(StringUtils.isNotEmpty(queueOrderQueryDTO.getUnionid()), TeboRescueQueueOrderDO::getUnionid, queueOrderQueryDTO.getUnionid())
                .eq(Objects.nonNull(queueOrderQueryDTO.getTenantId()), TeboRescueQueueOrderDO::getTenantId, queueOrderQueryDTO.getTenantId())
                .in(CollectionUtil.isNotEmpty(queueOrderQueryDTO.getOrderStatusList()), TeboRescueQueueOrderDO::getOrderStatus, queueOrderQueryDTO.getOrderStatusList())
        ;
        return wrapper;
    }

    @Override
    public TeboRescueQueueOrderDO getById(Long id) {
        if (Objects.isNull(id)) {
            throw new ServiceException("未指定排队单");
        }
        return teboRescueQueueOrderMapper.selectById(id);
    }

    @Override
    public void updateById(TeboRescueQueueOrderDO update) {
        if (Objects.isNull(update) || Objects.isNull(update.getId())) {
            throw new ServiceException("未指定排队单");
        }
        teboRescueQueueOrderMapper.updateById(update);
    }

    @Override
    public List<MaintenanceQueueOrderNumberVO> getMaintenanceServiceOrderCount(Long shopId) {
        return teboRescueQueueOrderMapper.getMaintenanceServiceOrderCount(shopId);
    }
}
