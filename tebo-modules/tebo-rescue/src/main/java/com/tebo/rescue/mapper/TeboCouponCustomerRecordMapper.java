package com.tebo.rescue.mapper;

import com.tebo.rescue.entity.TeboCouponCustomerRecordDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponCustomerRecordDTO;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 消费者优惠券记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Mapper
public interface TeboCouponCustomerRecordMapper extends TeboBaseMapper<TeboCouponCustomerRecordDO> {


    List<TeboCouponCustomerRecordDO> getCouponCustomerList(TeboCouponQueryDTO query);

    int batchUpdateByUniqueCode(TeboCouponCustomerRecordDTO recordDTO);

    int batchDelCouponRecord(@Param("couponId")Long couponId);

    int batchExpireCouponRecord(@Param("list")List<Long> couponIds);


}
