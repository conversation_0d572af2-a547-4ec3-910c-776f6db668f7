package com.tebo.rescue.manager.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.tebo.rescue.entity.TeboCommentDO;
import com.tebo.rescue.manager.TeboCommentManger;
import com.tebo.rescue.mapper.TeboCommentMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class TeboCommentMangerImpl implements TeboCommentManger {

    @Autowired
    private TeboCommentMapper commentMapper;

    @Override
    public TeboCommentDO getCommentsByOrderId(Long orderId) {
        if (Objects.isNull(orderId)) {
            return null;
        }
        List<TeboCommentDO> teboCommentDOS = commentMapper.getByOrderId(orderId);
        if (CollectionUtil.isNotEmpty(teboCommentDOS)) {
            return teboCommentDOS.get(0);
        }
        return null;
    }
}
