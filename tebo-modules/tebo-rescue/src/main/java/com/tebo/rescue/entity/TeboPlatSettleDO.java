package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p> 团购订单平台结算表
 * <AUTHOR>
 * @since 2024-01-17
 */
@Getter
@Setter
@TableName("tebo_group_order_settle_plat")
public class TeboPlatSettleDO extends Model<TeboPlatSettleDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 订单id
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 1钜惠版2安心版
     */
    @TableField("type")
    private Integer type;

    /**
     * 状态1未处理2处理中3失败4成功
     */
    @TableField("status")
    private Integer status;

    /**
     * 分账金额
     */
    @TableField("amount")
    private Integer amount;

    /**
     * 订单商品数
     */
    @TableField("goods_amount")
    private Integer goodsAmount;

    /**
     * 领取数
     */
    @TableField("receive_amount")
    private Integer receiveAmount;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
