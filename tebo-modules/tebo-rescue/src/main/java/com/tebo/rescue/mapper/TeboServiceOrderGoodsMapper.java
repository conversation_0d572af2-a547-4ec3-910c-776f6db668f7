package com.tebo.rescue.mapper;

import com.tebo.rescue.applet.domain.dto.QueueOrderQueryDTO;
import com.tebo.rescue.applet.domain.dto.ServiceOrderGoodsQueryDTO;
import com.tebo.rescue.entity.TeboServiceOrderGoodsDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.rescue.lst.domain.vo.TeboOrderGoodsIdGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 师傅端服务订单商品表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Mapper
public interface TeboServiceOrderGoodsMapper extends TeboBaseMapper<TeboServiceOrderGoodsDO> {

    /**
     * 批量插入订单商品表
     */
    int batchInsert(@Param("list") List<TeboServiceOrderGoodsDO> serviceOrderGoodsDOList);

    /**
     * 获取订单商品列表
     */
    List<TeboServiceOrderGoodsDO> list(ServiceOrderGoodsQueryDTO queryDTO);

    /**
     * 商品编码分组聚合
     * @param queryDTO
     * @return
     */
    List<TeboOrderGoodsIdGroup> getOrderGoodsIdGroup(ServiceOrderGoodsQueryDTO queryDTO);

}
