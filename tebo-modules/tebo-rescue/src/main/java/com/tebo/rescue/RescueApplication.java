package com.tebo.rescue;

import com.tebo.common.security.annotation.EnableCustomConfig;
import com.tebo.common.security.annotation.EnableRyFeignClients;
import com.tebo.rescue.lst.utils.JdConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2023/12/5 10:26
 * @Desc : 救援模块启动类
 */
@EnableCustomConfig
@EnableScheduling
@EnableRyFeignClients
@SpringBootApplication
@EnableAsync
@EnableConfigurationProperties(JdConfig.class)
public class RescueApplication {
    public static void main(String[] args) {
        SpringApplication.run(RescueApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  救援模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                " .-------.       ____     __        \n" +
                " |  _ _   \\      \\   \\   /  /    \n" +
                " | ( ' )  |       \\  _. /  '       \n" +
                " |(_ o _) /        _( )_ .'         \n" +
                " | (_,_).' __  ___(_ o _)'          \n" +
                " |  |\\ \\  |  ||   |(_,_)'         \n" +
                " |  | \\ `'   /|   `-'  /           \n" +
                " |  |  \\    /  \\      /           \n" +
                " ''-'   `'-'    `-..-'              ");
    }
}
