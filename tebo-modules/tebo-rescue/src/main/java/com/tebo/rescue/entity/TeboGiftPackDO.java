package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 礼包表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Getter
@Setter
@TableName("tebo_gift_pack")
public class TeboGiftPackDO extends Model<TeboGiftPackDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 礼包编号
     */
    @TableField("pack_code")
    private String packCode;

    /**
     * 礼包名称
     */
    @TableField("pack_name")
    private String packName;

    /**
     * 礼包归属（0 天能 1合伙人 2天能+合伙人）
     */
    @TableField("vest")
    private Integer vest;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 发行主体
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 礼包类型（0 会员礼包 1骑士礼包 2 家庭礼包 3 会员权益礼包）
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 有效期（单位：月）
     */
    @TableField("month_num")
    private Integer monthNum;

    /**
     * 适用区域
     */
    @TableField("area")
    private String area;

    /**
     * 适用区域编码
     */
    @TableField("area_code")
    private String areaCode;

    /**
     * 适用区域编码
     */
    @TableField("area_name")
    private String areaName;

    /**
     * 总发行量
     */
    @TableField("publish_num")
    private Integer publishNum;

    /**
     * 已使用
     */
    @TableField("use_num")
    private Integer useNum;

    /**
     * 剩余量
     */
    @TableField("residue_num")
    private Integer residueNum;

    /**
     * 礼包金额（分）
     */
    @TableField("par_value")
    private Integer parValue;

    /**
     * 售价（分）
     */
    @TableField("price")
    private Integer price;

    /**
     * 支付方式 0微信支付
     */
    @TableField("pay_type")
    private Integer payType;

    /**
     * 购买数量0不限制1限制
     */
    @TableField("buy_limit")
    private Integer buyLimit;

    /**
     * 个人最大限购数
     */
    @TableField("buy_limit_num")
    private Integer buyLimitNum;

    /**
     * 状态 1 未开始 2已开始 3已过期 10已作废
     */
    @TableField("`status`")
    private Integer status;


    /**
     * 主图
     */
    @TableField("main_image")
    private String mainImage;

    /**
     * 使用须知
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by",fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by",fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    /**
     * 删除标识
     */
    @TableField("del_flag")
    private Integer delFlag;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
