package com.tebo.rescue.applet.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.generator.config.INameConvert;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.domain.AjaxResult;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.common.util.applet.AppletUtil;
import com.tebo.rescue.applet.domain.dto.group.TeboGroupPurchaseOrderDTO;
import com.tebo.rescue.entity.TeboGroupPurchaseCustomerDO;
import com.tebo.rescue.mapper.TeboGroupPurchaseCustomerMapper;
import com.tebo.rescue.service.TeboGroupOrderService;
import com.tebo.rescue.web.domain.dto.groupOrder.TeboGroupOrderDTO;
import com.tebo.rescue.web.domain.dto.groupOrder.TeboGroupOrderQueryDTO;
import com.tebo.rescue.web.domain.view.TeboGroupOrderVO;
import com.tebo.system.api.RemoteCustomerService;
import com.tebo.system.api.model.TeboConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@Slf4j
@RequestMapping("/applet/groupOrder")
public class TeboAppletGroupPurchaseOrderController extends BaseController {

    @Resource
    private HttpServletRequest request;

    @Resource
    private TeboGroupOrderService teboGroupOrderService;

    @Resource
    private TeboGroupPurchaseCustomerMapper teboGroupPurchaseCustomerMapper;

    @Resource
    private RemoteCustomerService remoteCustomerService;

    /**
     * 创建团购订单
     */
    @PostMapping("/createOrder")
    public R createOrder(@RequestBody TeboGroupOrderDTO teboGroupOrderDTO) {
        if (ObjectUtils.isEmpty(teboGroupOrderDTO.getType())){
            throw new ServiceException("团购类型不能为空");
        }
        if (ObjectUtils.isEmpty(teboGroupOrderDTO.getPurchaseQuantity())){
            throw new ServiceException("团购数量不能为空");
        }
        if (ObjectUtils.isEmpty(teboGroupOrderDTO.getShopId())){
            throw new ServiceException("门店id不能为空");
        }
        return R.ok(teboGroupOrderService.createAppletOrder(teboGroupOrderDTO));
    }

    /**
     * 订单详情
     */
    @GetMapping("/getTeboGroupOrderDetail/{id}")
    public R getTeboGroupOrderDetail(@PathVariable Long id) {
        return R.ok(teboGroupOrderService.getTeboGroupOrderDetail(id));
    }

    /**
     * 商家版团购订单列表
     */
    @PostMapping("/page")
    public TableDataInfo page(@RequestBody TeboGroupOrderQueryDTO teboGroupOrderQueryDTO) {
        if (teboGroupOrderQueryDTO.getPageNum() == null || teboGroupOrderQueryDTO.getPageSize() == null) {
            throw new ServiceException("分页参数不能为空");
        }
        if (ObjectUtils.isEmpty(teboGroupOrderQueryDTO.getShopId())){
            throw new ServiceException("门店id不能为空");
        }
        Page page = PageHelper.startPage(teboGroupOrderQueryDTO.getPageNum(), teboGroupOrderQueryDTO.getPageSize());
        List<TeboGroupOrderVO> result = teboGroupOrderService.getGroupOrderList(teboGroupOrderQueryDTO);
        return getDataTable(result, page);
    }


    /**
     * 领取卡券
     * @return
     */
    @PostMapping("/receive")
    public AjaxResult receive(@RequestBody TeboGroupPurchaseOrderDTO teboGroupPurchaseOrderDTO) {
        log.info("group order receive param:{}", JSONObject.toJSONString(teboGroupPurchaseOrderDTO));
        Long orderId = teboGroupPurchaseOrderDTO.getOrderId();
        if (ObjectUtils.isEmpty(orderId)){
            throw new ServiceException("订单编号不能为空");
        }
        String unionId = AppletUtil.getUnionIdByRequest(request);
        teboGroupPurchaseOrderDTO.setUnionId(unionId);
        teboGroupPurchaseOrderDTO.setStatus(0);
        R<TeboConsumer> consumerR = remoteCustomerService.selectByUnionId(unionId);
        if (!ObjectUtils.isEmpty(consumerR) && !ObjectUtils.isEmpty(consumerR.getData())){
            teboGroupPurchaseOrderDTO.setPhoneNumber(consumerR.getData().getPhoneNumber());
        }
        return success(teboGroupOrderService.receive(teboGroupPurchaseOrderDTO));
    }

    /**
     * 领取卡券
     * @return
     */
    @PostMapping("/merchantReceive")
    public AjaxResult merchantReceive(@RequestBody TeboGroupPurchaseOrderDTO teboGroupPurchaseOrderDTO) {
        log.info("merchantReceive param:{}", JSONObject.toJSONString(teboGroupPurchaseOrderDTO));
        Long orderId = teboGroupPurchaseOrderDTO.getOrderId();
        if (ObjectUtils.isEmpty(orderId)){
            throw new ServiceException("订单编号不能为空");
        }
        String unionId = AppletUtil.getUnionIdByRequest(request);
        teboGroupPurchaseOrderDTO.setUnionId(unionId);
        teboGroupPurchaseOrderDTO.setStatus(0);
        R<TeboConsumer> consumerR = remoteCustomerService.selectByUnionId(unionId);
        if (!ObjectUtils.isEmpty(consumerR) && !ObjectUtils.isEmpty(consumerR.getData())){
            teboGroupPurchaseOrderDTO.setPhoneNumber(consumerR.getData().getPhoneNumber());
        }
        return success(teboGroupOrderService.merchantReceive(teboGroupPurchaseOrderDTO));
    }

    /**
     * 用户有几张待领取卡券
     */
    @GetMapping("/getUnclaimedCoupons/{orderId}")
    public R getUnclaimedCoupons(@PathVariable("orderId")Long orderId) {
        String unionId = AppletUtil.getUnionIdByRequest(request);
        TeboGroupPurchaseOrderDTO teboGroupPurchaseOrderDTO = new TeboGroupPurchaseOrderDTO();
        teboGroupPurchaseOrderDTO.setStatus(0);
        teboGroupPurchaseOrderDTO.setOrderId(orderId);
        R<TeboConsumer> consumerR = remoteCustomerService.selectByUnionId(unionId);
        if (!ObjectUtils.isEmpty(consumerR) && !ObjectUtils.isEmpty(consumerR.getData())){
            teboGroupPurchaseOrderDTO.setPhoneNumber(consumerR.getData().getPhoneNumber());
        }
        List<TeboGroupPurchaseCustomerDO> list = teboGroupPurchaseCustomerMapper.getOrder(teboGroupPurchaseOrderDTO);
        if (CollectionUtils.isEmpty(list)){
            return R.ok(0,"当前账号暂无领取权限");
        }
        return R.ok(list.size());
    }

}