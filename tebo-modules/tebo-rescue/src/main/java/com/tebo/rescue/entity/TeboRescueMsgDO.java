package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 工单超时消息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Getter
@Setter
@TableName("tebo_rescue_msg")
public class TeboRescueMsgDO extends Model<TeboRescueMsgDO> {

    private static final long serialVersionUID = 1L;

    @TableField("id")
    private Long id;

    /**
     * 消息类型 1:救援单超时消息
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 合伙人id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 订单id
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 是否已读 0:未读 1:已读
     */
    @TableField("`reader`")
    private Integer reader;

    /**
     * 消息
     */
    @TableField("msg")
    private String msg;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    @Override
    public Serializable pkVal() {
        return null;
    }

}
