package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 礼包订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-24
 */
@Getter
@Setter
@TableName("tebo_gift_pack_order")
public class TeboGiftPackOrderDO extends Model<TeboGiftPackOrderDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 团购订单id
     */
    @TableField("group_order_id")
    private Long groupOrderId;

    /**
     * 礼包id
     */
    @TableField("pack_id")
    private Long packId;

    /**
     * 用户unionid
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 订单状态 0:待支付 1:已支付,2:已取消 3已退款
     */
    @TableField("order_status")
    private Integer orderStatus;

    /**
     * 订单金额,单位分
     */
    @TableField("order_amount")
    private Integer orderAmount;

    /**
     * 订单来源-推广人手机号码
     */
    @TableField("reference_phone")
    private String referencePhone;

    /**
     * 推广人钱包id
     */
    @TableField("reference_wallet_id")
    private Long referenceWalletId;

    /**
     * 推广来源0:自行购买 1:S 集团 2:B 供应商 3:b 门店 4:C 消费者 5:X异业 6:车销 7:团购
     */
    @TableField("type")
    private Integer type;

    /**
     * 1:普通订单 2:团购订单 3:合并下单订单
     */
    @TableField("order_type")
    private Integer orderType;

    /**
     * 上级手机号也即是合伙人手机号
     */
    @TableField("partner_phone")
    private String partnerPhone;

    /**
     * 区域
     */
    @TableField("district")
    private String district;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    private LocalDateTime payTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建者
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 退款时间
     */
    @TableField(value = "refund_time")
    private LocalDateTime refundTime;

    /**
     * 核销状态 0 未核销 1:核销 2:已退款
     */
    @TableField(value = "verification_status")
    private Integer verificationStatus;

    /**
     * 大米核销状态 0 未核销 1:核销 2:已退款
     */
    @TableField(value = "rice_verification_status")
    private Integer riceVerificationStatus;

    /**
     * 电池核销状态 0 未核销 1:核销 2:已退款
     */
    @TableField(value = "battery_verification_status")
    private Integer batteryVerificationStatus;

    /**
     * 骑行卡核销状态 0 未核销 1:核销 2:已退款
     */
    @TableField(value = "cycle_verification_status")
    private Integer cycleVerificationStatus;

    /**
     * 核销时间
     */
    @TableField(value = "verification_time")
    private LocalDateTime verificationTime;

    /**
     * 大米核销时间
     */
    @TableField(value = "rice_verification_time")
    private LocalDateTime riceVerificationTime;

    /**
     * 电池核销时间
     */
    @TableField(value = "battery_verification_time")
    private LocalDateTime batteryVerificationTime;

    /**
     * 骑行险核销时间
     */
    @TableField(value = "cycle_verification_time")
    private LocalDateTime cycleVerificationTime;

    /**
     * 核销门店
     */
    @TableField(value = "verification_shop_id")
    private Long verificationShopId;

    /**
     * 核销门店所属合伙人
     */
    @TableField(value = "verification_tenant_id")
    private Long verificationTenantId;

    /**
     * 用户的团购订单id
     */
    @TableField(value = "group_customer_order_id")
    private Long groupCustomerOrderId;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
