package com.tebo.rescue.applet.domain.view;

import com.tebo.common.util.number.MoneyUtil;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author：zhengmk
 * @Date 2023/12/13 18:44
 */
@Data
public class QueueOrderListVO implements Serializable {

    /**
     * 唯一主键
     */
    private Long id;


    /**
     * 联系方式
     */
    private String phoneNumber;

    /**
     * 微信名
     */
    private String nickName;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 门店logo
     */
    private String shopPic;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 维修师傅id
     */
    private Long accountId;

    /**
     * 师傅名称
     */
    private String accountName;

    /**
     * 订单类型0:维修 1:安装 2:保养 3:洗车
     */
    private Integer orderType;

    /**
     * 订单状态 0:未叫号 1:处理中 2:待付款 3:已完成 4:已取消
     */
    private Integer orderStatus;

    //订单状态pc 0:未叫号 1：已叫号 2:已完成 3:已过号，跟orderStatus映射
    // 0-0 1-1,2 2-3 3-4
    private Integer orderStatusPc;

    /**
     * 总体评价0-5
     */
    private Integer totalComment;

    /**
     * 服务评价0-5
     */
    private Integer serviceComment;

    /**
     * 质量0-5
     */
    private Integer quantityComment;

    /**
     * 备注
     */
    private String remark;

    /**
     * 订单金额,单位分
     */

    private Integer orderAmount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 取号码
     */
    private String queueNumberStr;

    /**
     * 工位
     */
    private String stationName;

    /**
     * 是否已评价
     */
    private Integer commentFinished;

    /**
     * 叫号时间
     */

    private LocalDateTime callTime;

    /**
     * 支付时间（完成时间）
     */
    private LocalDateTime payTime;

    /**
     * 取号方式
     */
    private Integer pickupType;

    /**
     * 门店电话
     */
    private String shopPhoneNumber;

    /**
     * 是否代付
     */
    private Integer isAgentPay;

    public String getOrderAmount() {
        if(orderAmount == null) {
            return "0.00";
        }
        return MoneyUtil.fenToYuan(orderAmount);
    }
}
