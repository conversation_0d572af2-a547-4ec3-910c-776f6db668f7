package com.tebo.rescue.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.mall.api.RemoteCycleInsuranceOrderService;
import com.tebo.mall.api.RemoteIntegralOrderService;
import com.tebo.mall.api.domain.dto.CloudShopIntegralVo;
import com.tebo.mall.api.domain.dto.TeboCloudShopUserIntegralQueryDTO;
import com.tebo.mall.api.domain.dto.TeboIntegralDTO;
import com.tebo.mall.api.domain.dto.cycling.TeboCyclingOrderDTO;
import com.tebo.rescue.applet.domain.dto.GiftPackOrderDTO;
import com.tebo.rescue.applet.domain.dto.group.TeboGroupPurchaseOrderDTO;
import com.tebo.rescue.applet.domain.view.TeboGroupReceiveOrderVO;
import com.tebo.rescue.entity.TeboGroupPurchaseCustomerDO;
import com.tebo.rescue.entity.TeboGroupPurchaseOrderDO;
import com.tebo.rescue.enums.PackIdEnum;
import com.tebo.rescue.mapper.TeboCouponCustomerMapper;
import com.tebo.rescue.mapper.TeboGroupPurchaseCustomerMapper;
import com.tebo.rescue.mapper.TeboGroupPurchaseOrderMapper;
import com.tebo.rescue.redisson.queue.RedisDelayQueueEnum;
import com.tebo.rescue.redisson.queue.RedisDelayQueueUtil;
import com.tebo.rescue.service.IGiftPackOrderService;
import com.tebo.rescue.service.TeboCouponCustomerService;
import com.tebo.rescue.service.TeboGroupOrderService;
import com.tebo.rescue.service.mq.DisMqService;
import com.tebo.rescue.util.DistributedLock;
import com.tebo.rescue.util.TeboCommissionUtil;
import com.tebo.rescue.web.domain.dto.groupOrder.TeboGroupOrderDTO;
import com.tebo.rescue.web.domain.dto.groupOrder.TeboGroupOrderQueryDTO;
import com.tebo.rescue.web.domain.view.TeboCouponCustomerVO;
import com.tebo.rescue.web.domain.view.TeboGroupOrderVO;
import com.tebo.system.api.RemoteCustomerService;
import com.tebo.system.api.RemoteShopService;
import com.tebo.system.api.RemoteWalletService;
import com.tebo.system.api.domain.dto.wallet.TeboWalletPlanDTO;
import com.tebo.system.api.model.TeboConsumer;
import com.tebo.system.api.model.TeboShop;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class TeboGroupOrderServiceImpl implements TeboGroupOrderService {
    @Resource
    private TeboGroupPurchaseOrderMapper teboGroupPurchaseOrderMapper;

    @Resource
    private TeboGroupPurchaseCustomerMapper teboGroupPurchaseCustomerMapper;

    @Resource
    private IGiftPackOrderService giftPackOrderService;

    @Resource
    private DisMqService disMqService;

    @Resource
    private RemoteIntegralOrderService remoteIntegralOrderService;

    @Resource
    private TeboCommissionUtil teboCommissionUtil;

    @Resource
    private RemoteWalletService remoteWalletService;
    @Resource
    private RedisDelayQueueUtil redisDelayQueueUtil;
    @Resource
    private RemoteShopService remoteShopService;

    @Resource
    private DistributedLock distributedLock;

    @Resource
    private TeboCouponCustomerMapper teboCouponCustomerMapper;

    @Resource
    private RemoteCycleInsuranceOrderService cycleInsuranceOrderService;

    @Resource
    private TeboCouponCustomerService couponCustomerService;

    @Resource
    private RemoteCustomerService remoteCustomerService;

    @Override
    public Long createOrder(TeboGroupOrderDTO teboGroupOrderDTO) {
        TeboGroupPurchaseOrderDO teboGroupPurchaseOrderDO = new TeboGroupPurchaseOrderDO();
        BeanConvert.copy(teboGroupOrderDTO,teboGroupPurchaseOrderDO);
        if (StringUtils.isNotEmpty(teboGroupOrderDTO.getUnitPrice())){
            teboGroupPurchaseOrderDO.setUnitPrice(MoneyUtil.yuanToFen(teboGroupOrderDTO.getUnitPrice()));
        }
        teboGroupPurchaseOrderDO.setId(SnowFlakeUtil.nextId());
        if (teboGroupOrderDTO.getPurchaserType() == 1){
            teboGroupPurchaseOrderDO.setPurchaserName("天能集团");
        }
        /**
         * 满赠
         */
        teboGroupPurchaseOrderDO.setFullDeliveryQuantity((teboGroupOrderDTO.getPurchaseQuantity()/10)*4);
        /**
         * 商品总数量
         */
        teboGroupPurchaseOrderDO.setTotalGoodsNumber(teboGroupPurchaseOrderDO.getPurchaseQuantity()+teboGroupPurchaseOrderDO.getFullDeliveryQuantity());
        teboGroupPurchaseOrderDO.setOrderNo("TGDD"+teboGroupPurchaseOrderDO.getId());
        if (StringUtils.isNotEmpty(teboGroupOrderDTO.getUnitPrice())){
            teboGroupPurchaseOrderDO.setUnitPrice(MoneyUtil.yuanToFen(teboGroupOrderDTO.getUnitPrice()));
        }
        /**
         * 订单金额
         */
        teboGroupPurchaseOrderDO.setOrderAmount(teboGroupPurchaseOrderDO.getUnitPrice() * teboGroupPurchaseOrderDO.getPurchaseQuantity() );

        if (teboGroupPurchaseOrderDO.getOrderAmount() > 500000){
            throw new ServiceException("单笔订单不可超过5000元");
        }
        teboGroupPurchaseOrderDO.setOrderStatus(1);
        teboGroupPurchaseOrderDO.setActiveStatus(1);
        teboGroupPurchaseOrderMapper.insert(teboGroupPurchaseOrderDO);
        return teboGroupPurchaseOrderDO.getId();
    }

    @Override
    public Long createAppletOrder(TeboGroupOrderDTO teboGroupOrderDTO) {
        TeboGroupPurchaseOrderDO teboGroupPurchaseOrderDO = new TeboGroupPurchaseOrderDO();
        BeanConvert.copy(teboGroupOrderDTO,teboGroupPurchaseOrderDO);
        if (StringUtils.isNotEmpty(teboGroupOrderDTO.getUnitPrice())){
            teboGroupPurchaseOrderDO.setUnitPrice(MoneyUtil.yuanToFen(teboGroupOrderDTO.getUnitPrice()));
        }
        if (teboGroupOrderDTO.getType() == 1){
            teboGroupPurchaseOrderDO.setGoodsName("天能久久券-钜惠版（15张）");
            teboGroupPurchaseOrderDO.setUnitPrice(49900);
            teboGroupPurchaseOrderDO.setUnitIntegralPrice(249);
            teboGroupPurchaseOrderDO.setTotalIntegral(249*teboGroupPurchaseOrderDO.getPurchaseQuantity());
        }else if (teboGroupOrderDTO.getType() == 2){
            teboGroupPurchaseOrderDO.setGoodsName("天能久久券-安心版（15张）");
            teboGroupPurchaseOrderDO.setUnitPrice(99900);
            teboGroupPurchaseOrderDO.setUnitIntegralPrice(500);
            teboGroupPurchaseOrderDO.setTotalIntegral(500*teboGroupPurchaseOrderDO.getPurchaseQuantity());
        }
        teboGroupPurchaseOrderDO.setId(SnowFlakeUtil.nextId());
        teboGroupPurchaseOrderDO.setPurchaserType(1);
        teboGroupPurchaseOrderDO.setPurchaserName("天能集团");
        /**
         * 商品总数量
         */
        teboGroupPurchaseOrderDO.setTotalGoodsNumber(teboGroupPurchaseOrderDO.getPurchaseQuantity()*15);
        teboGroupPurchaseOrderDO.setOrderNo("TGDD"+teboGroupPurchaseOrderDO.getId());
        if (StringUtils.isNotEmpty(teboGroupOrderDTO.getUnitPrice())){
            teboGroupPurchaseOrderDO.setUnitPrice(MoneyUtil.yuanToFen(teboGroupOrderDTO.getUnitPrice()));
        }
        R<TeboShop> shopR = remoteShopService.getShopInfo(teboGroupOrderDTO.getShopId());
        TeboCloudShopUserIntegralQueryDTO teboCloudShopUserIntegralQueryDTO = new TeboCloudShopUserIntegralQueryDTO();
        teboCloudShopUserIntegralQueryDTO.setUnionId(shopR.getData().getCloudShopId());
        teboCloudShopUserIntegralQueryDTO.setSource(1);
        R<CloudShopIntegralVo> integralVoR = remoteIntegralOrderService.getUserIntegral(teboCloudShopUserIntegralQueryDTO);
        if (integralVoR.getCode() != 200 || ObjectUtils.isEmpty(integralVoR) || ObjectUtils.isEmpty(integralVoR.getData()) ){
           throw new ServiceException("获取门店积分失败");
        }
        CloudShopIntegralVo cloudShopIntegralVo = integralVoR.getData();
        Integer integral = cloudShopIntegralVo.getIntegral();
        if (integral < teboGroupPurchaseOrderDO.getTotalIntegral()){
            throw new ServiceException("门店积分不够");
        }
        /**
         * 订单金额
         */
        teboGroupPurchaseOrderDO.setOrderAmount(teboGroupPurchaseOrderDO.getUnitPrice() * teboGroupPurchaseOrderDO.getPurchaseQuantity() );
        teboGroupPurchaseOrderDO.setOrderStatus(1);
        if (!ObjectUtils.isEmpty(shopR) && !ObjectUtils.isEmpty(shopR.getData())){
            teboGroupPurchaseOrderDO.setPhoneNumber(shopR.getData().getPhoneNumber());
            teboGroupPurchaseOrderDO.setContact(shopR.getData().getShopBossName());
        }
        teboGroupPurchaseOrderDO.setActiveStatus(1);
        teboGroupPurchaseOrderMapper.insert(teboGroupPurchaseOrderDO);
        Map<String, String> param = new HashMap<>();
        param.put("orderId", teboGroupPurchaseOrderDO.getId().toString());
        param.put("remark", "商城订单支付超时，自动取消订单");
        // 添加订单支付超时，自动取消订单延迟队列。为了测试效果，延迟10秒钟
        redisDelayQueueUtil.addDelayQueue(param, 30, TimeUnit.MINUTES, RedisDelayQueueEnum.TEBO_PACK_ORDER_PAY_TIMEOUT.getCode());
        return teboGroupPurchaseOrderDO.getId();
    }

    @Override
    public List<TeboGroupOrderVO> getGroupOrderList(TeboGroupOrderQueryDTO teboGroupOrderQueryDTO) {
        if (Objects.nonNull(teboGroupOrderQueryDTO.getCreateStartTime())) {
            teboGroupOrderQueryDTO.setCreateStartTimeSecond(teboGroupOrderQueryDTO.getCreateStartTime().atStartOfDay());
        }
        if (Objects.nonNull(teboGroupOrderQueryDTO.getCreateEndTime())) {
            teboGroupOrderQueryDTO.setCreateEndTimeSecond(teboGroupOrderQueryDTO.getCreateEndTime().plusDays(1).atStartOfDay());
        }
        List<TeboGroupPurchaseOrderDO> list = teboGroupPurchaseOrderMapper.getGroupOrderList(teboGroupOrderQueryDTO);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        List<TeboGroupOrderVO> result = new ArrayList<>();
        list.forEach(item ->{
            TeboGroupOrderVO teboGroupOrderVO = new TeboGroupOrderVO();
            BeanConvert.copy(item,teboGroupOrderVO);
            if (!ObjectUtils.isEmpty(item.getOrderAmount())){
                teboGroupOrderVO.setOrderAmount(MoneyUtil.fenToYuan(item.getOrderAmount()));
            }
            if (!ObjectUtils.isEmpty(item.getUnitPrice())){
                teboGroupOrderVO.setUnitPrice(MoneyUtil.fenToYuan(item.getUnitPrice()));
            }
            result.add(teboGroupOrderVO);
        });
        return result;
    }

    @Override
    public void delete(Long id) {
        TeboGroupPurchaseOrderDO purchaseOrderDO = teboGroupPurchaseOrderMapper.selectById(id);
        if (!ObjectUtils.isEmpty(purchaseOrderDO)){
            purchaseOrderDO.setDelFlag(1);
            teboGroupPurchaseOrderMapper.updateById(purchaseOrderDO);
        }
    }

    @Override
    public TeboGroupOrderVO getTeboGroupOrderDetail(Long id) {
        TeboGroupPurchaseOrderDO orderDO = teboGroupPurchaseOrderMapper.selectById(id);
        if (ObjectUtils.isEmpty(orderDO)){
            return null;
        }
        TeboGroupOrderVO teboGroupOrderVO = new TeboGroupOrderVO();
        BeanConvert.copy(orderDO,teboGroupOrderVO);
        if (!ObjectUtils.isEmpty(orderDO.getOrderAmount())){
            teboGroupOrderVO.setOrderAmount(MoneyUtil.fenToYuan(orderDO.getOrderAmount()));
        }
        if (!ObjectUtils.isEmpty(orderDO.getUnitPrice())){
            teboGroupOrderVO.setUnitPrice(MoneyUtil.fenToYuan(orderDO.getUnitPrice()));
        }
        return teboGroupOrderVO;
    }

    @Override
    @Transactional
    public String receive(TeboGroupPurchaseOrderDTO teboGroupPurchaseOrderDTO) {
        /**
         * 1：判断这个人有没有领取资格
         *:2：创建个人订单
         * 3:发放卡券
         */
        List<TeboGroupPurchaseCustomerDO> list = teboGroupPurchaseCustomerMapper.getOrder(teboGroupPurchaseOrderDTO);
        if (CollectionUtils.isEmpty(list)){
            throw new ServiceException("当前账号暂无领取权限");
        }
        /**
         * 随机找一个进行领取,将状态设置为已领取
         */
        TeboGroupPurchaseCustomerDO teboGroupPurchaseCustomerDO = list.get(0);
        teboGroupPurchaseCustomerDO.setStatus(1);
        teboGroupPurchaseCustomerMapper.updateById(teboGroupPurchaseCustomerDO);
        /**
         * 判断团购订单有没有都激活完成
         */
        Long orderId = teboGroupPurchaseCustomerDO.getOrderId();

        // 团购单
        TeboGroupPurchaseOrderDO teboGroupPurchaseOrderDO = teboGroupPurchaseOrderMapper.selectById(orderId);
        /**
         * 查询消费者订单状态是否都激活，如果都激活则将团购单设置为已完成，否则进行中
         */
        TeboGroupPurchaseOrderDTO purchaseOrderDTO = new TeboGroupPurchaseOrderDTO();
        purchaseOrderDTO.setOrderId(orderId);
        List<TeboGroupPurchaseCustomerDO> groupOrderList = teboGroupPurchaseCustomerMapper.getOrder(purchaseOrderDTO);
        /**
         * 已激活的用户订单
         */
        Long activeSize = groupOrderList.stream().filter(item ->item.getStatus() == 1).count();
        if (groupOrderList.size() == activeSize){
            teboGroupPurchaseOrderDO.setActiveStatus(3);
        }else {
            teboGroupPurchaseOrderDO.setActiveStatus(2);
        }
        teboGroupPurchaseOrderMapper.updateById(teboGroupPurchaseOrderDO);
        GiftPackOrderDTO giftPackOrderDTO = new GiftPackOrderDTO();
        giftPackOrderDTO.setPackId(Long.parseLong(PackIdEnum.JU_HUI.getCode()));
        giftPackOrderDTO.setLgt(teboGroupPurchaseOrderDTO.getLgt());
        giftPackOrderDTO.setLnt(teboGroupPurchaseOrderDTO.getLnt());
        giftPackOrderDTO.setUnionId(teboGroupPurchaseOrderDTO.getUnionId());
        giftPackOrderDTO.setId(SnowFlakeUtil.nextId());
        giftPackOrderDTO.setOrderNo("GPD" + giftPackOrderDTO.getId());
        giftPackOrderDTO.setOrderType(2);
        giftPackOrderDTO.setOrderStatus(1);
        giftPackOrderDTO.setType(7);
        giftPackOrderDTO.setGroupCustomerOrderId(teboGroupPurchaseCustomerDO.getId());
        giftPackOrderDTO.setGroupOrderId(teboGroupPurchaseCustomerDO.getOrderId());
        giftPackOrderService.createGroupGiftOrder(giftPackOrderDTO);
        /**
         * 发放卡券
         */
        disMqService.grantPackCoupon(giftPackOrderDTO.getPackId(), giftPackOrderDTO.getUnionId(),giftPackOrderDTO.getOrderNo());
        /**
         * 钜惠版分佣
         */
        if (giftPackOrderDTO.getPackId().toString().equals(PackIdEnum.JU_HUI.getCode())){
            /**
             * 天能久久卡-钜惠版 发放200积分
             */
            log.info("payGiftPpackAfterNotify addUserIntegral {}", giftPackOrderDTO.getUnionId());
            remoteIntegralOrderService.addUserIntegral(new TeboIntegralDTO(200, giftPackOrderDTO.getUnionId(),giftPackOrderDTO.getId()));
        }
        /**
         * 分佣
         */
        TeboWalletPlanDTO teboWalletPlanDTO = teboCommissionUtil.buildGroupRiceParam(teboGroupPurchaseOrderDO.getOrderNo(),teboGroupPurchaseOrderDTO.getUnionId(),giftPackOrderDTO.getOrderNo());
        log.info("group order wallet param :{}", JSONObject.toJSONString(teboWalletPlanDTO));
        remoteWalletService.splitPlan(teboWalletPlanDTO);
        return giftPackOrderDTO.getOrderNo();
    }

    @Override
    @Transactional
    public TeboGroupReceiveOrderVO merchantReceive(TeboGroupPurchaseOrderDTO teboGroupPurchaseOrderDTO) {
        TeboGroupReceiveOrderVO teboGroupOrderVO = new TeboGroupReceiveOrderVO();
        String lockKey = "tebo_rescue:groupOrder:"+ teboGroupPurchaseOrderDTO.getUnionId()+":"+teboGroupPurchaseOrderDTO.getOrderId();
        try {
            boolean locked = distributedLock.tryLock(lockKey, 10, 30);
            if (!locked) {
                throw  new ServiceException("操作太频繁");
            }
            /**
             * 1：判断这个人有没有领取资格
             *:2：创建个人订单
             * 3:发放卡券
             */
            List<TeboGroupPurchaseCustomerDO> list = teboGroupPurchaseCustomerMapper.getOrder(teboGroupPurchaseOrderDTO);
            if (CollectionUtils.isEmpty(list)){
                throw new ServiceException("该券已被撤回,无领取权限");
            }
            /**
             * 随机找一个进行领取,将状态设置为已领取
             */
            TeboGroupPurchaseCustomerDO teboGroupPurchaseCustomerDO = list.get(0);
            teboGroupPurchaseCustomerDO.setStatus(1);
            teboGroupPurchaseCustomerMapper.updateById(teboGroupPurchaseCustomerDO);
            /**
             * 判断团购订单有没有都激活完成
             */
            Long orderId = teboGroupPurchaseCustomerDO.getOrderId();

            // 团购单
            TeboGroupPurchaseOrderDO teboGroupPurchaseOrderDO = teboGroupPurchaseOrderMapper.selectById(orderId);
            /**
             * 查询消费者订单状态是否都激活，如果都激活则将团购单设置为已完成，否则进行中
             */
            TeboGroupPurchaseOrderDTO purchaseOrderDTO = new TeboGroupPurchaseOrderDTO();
            purchaseOrderDTO.setOrderId(orderId);
            List<TeboGroupPurchaseCustomerDO> groupOrderList = teboGroupPurchaseCustomerMapper.getOrder(purchaseOrderDTO);
            /**
             * 已激活的用户订单
             */
            if (!CollectionUtils.isEmpty(groupOrderList)){
                Long activeSize = groupOrderList.stream().filter(item ->item.getStatus() == 1).count();
                if (groupOrderList.size() == activeSize){
                    teboGroupPurchaseOrderDO.setActiveStatus(3);
                }else {
                    teboGroupPurchaseOrderDO.setActiveStatus(2);
                }
            }
            teboGroupPurchaseOrderMapper.updateById(teboGroupPurchaseOrderDO);
            GiftPackOrderDTO giftPackOrderDTO = new GiftPackOrderDTO();
            // 0/1 钜惠版 2 安心版
            if (teboGroupPurchaseOrderDO.getType() == 1){
                giftPackOrderDTO.setPackId(Long.parseLong(PackIdEnum.JU_HUI.getCode()));
            }else if (teboGroupPurchaseOrderDO.getType() == 0){
                giftPackOrderDTO.setPackId(Long.parseLong(PackIdEnum.JU_HUI.getCode()));
            }else if (teboGroupPurchaseOrderDO.getType() == 2){
                giftPackOrderDTO.setPackId(Long.parseLong(PackIdEnum.AN_XIN.getCode()));
            }else
            giftPackOrderDTO.setLgt(teboGroupPurchaseOrderDTO.getLgt());
            giftPackOrderDTO.setLnt(teboGroupPurchaseOrderDTO.getLnt());
            giftPackOrderDTO.setUnionId(teboGroupPurchaseOrderDTO.getUnionId());
            giftPackOrderDTO.setId(SnowFlakeUtil.nextId());
            giftPackOrderDTO.setOrderNo("GPD" + giftPackOrderDTO.getId());
            giftPackOrderDTO.setOrderType(2);
            giftPackOrderDTO.setOrderStatus(1);
            giftPackOrderDTO.setType(7);
            giftPackOrderDTO.setGroupCustomerOrderId(teboGroupPurchaseCustomerDO.getId());
            giftPackOrderDTO.setGroupOrderId(teboGroupPurchaseCustomerDO.getOrderId());
            giftPackOrderService.createGroupGiftOrder(giftPackOrderDTO);
            /**
             * 发放卡券
             */
            disMqService.grantPackCoupon(giftPackOrderDTO.getPackId(), giftPackOrderDTO.getUnionId(),giftPackOrderDTO.getOrderNo());
            /**
             * 钜惠版分佣
             */
            Integer integral = null;
            if (giftPackOrderDTO.getId().toString().equals(PackIdEnum.JU_HUI.getCode())){
                integral = 200;
            }else if (giftPackOrderDTO.getId().toString().equals(PackIdEnum.AN_XIN.getCode())){
                integral = 400;
                TeboCouponCustomerVO teboCouponCustomerVO = couponCustomerService.getCustomerCycleCoupon(giftPackOrderDTO.getUnionId(),giftPackOrderDTO.getOrderNo());
                if (ObjectUtil.isNotEmpty(teboCouponCustomerVO)){
                    TeboConsumer consumer = remoteCustomerService.selectByUnionId(giftPackOrderDTO.getUnionId()).getData();
                    cycleInsuranceOrderService.synchronizeCycleOrder(new TeboCyclingOrderDTO(teboCouponCustomerVO.getUniqueCode(), consumer.getPhoneNumber()));
                }

            }
        //    remoteIntegralOrderService.addUserIntegral(new TeboIntegralDTO(integral, giftPackOrderDTO.getUnionId(),giftPackOrderDTO.getId()));


            /**
             * 分佣
             */
            TeboWalletPlanDTO teboRiceWalletPlanDTO = teboCommissionUtil.buildGroupRiceParam(teboGroupPurchaseOrderDO.getOrderNo(),teboGroupPurchaseOrderDTO.getUnionId(),giftPackOrderDTO.getOrderNo());
            log.info("group order rice wallet param :{}", JSONObject.toJSONString(teboRiceWalletPlanDTO));
            remoteWalletService.splitPlan(teboRiceWalletPlanDTO);
            if (teboGroupPurchaseOrderDO.getType() == 2){
                TeboWalletPlanDTO teboCycleWalletPlanDTO = teboCommissionUtil.buildGroupCycleParam(teboGroupPurchaseOrderDO.getOrderNo(),teboGroupPurchaseOrderDTO.getUnionId(),giftPackOrderDTO.getOrderNo());
                log.info("group order cycle wallet param :{}", JSONObject.toJSONString(teboRiceWalletPlanDTO));
                remoteWalletService.splitPlan(teboCycleWalletPlanDTO);
            }
            R<TeboShop> teboShopR = remoteShopService.getShopInfo(teboGroupPurchaseOrderDO.getShopId());
            String tenantName = "";
            if (!ObjectUtils.isEmpty(teboShopR) && !ObjectUtils.isEmpty(teboShopR.getData())){
                TeboShop teboShop = teboShopR.getData();
                tenantName = teboShop.getTenantName();
                teboGroupOrderVO.setTenantName(tenantName);
            }
            teboGroupOrderVO.setOrderNo(giftPackOrderDTO.getOrderNo());
            teboGroupOrderVO.setOrderType(2);
            /***
             *
             * 安心版团购订单删除电池券
             */
            if (teboGroupPurchaseOrderDO.getType() == 2){
                teboCouponCustomerMapper.deleteBatteryCoupon(giftPackOrderDTO.getOrderNo());
            }
            return teboGroupOrderVO;
        } finally {
            distributedLock.unlock(lockKey);
        }

    }
}