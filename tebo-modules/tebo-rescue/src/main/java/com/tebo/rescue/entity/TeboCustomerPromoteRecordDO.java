package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户推广记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Getter
@Setter
@TableName("tebo_customer_promote_record")
public class TeboCustomerPromoteRecordDO extends Model<TeboCustomerPromoteRecordDO> {

    private static final long serialVersionUID = 1L;

    @TableField("id")
    private Long id;

    /**
     * 推广人手机号
     */
    @TableField("reference_phone")
    private String referencePhone;

    /**
     * 推广人钱包id
     */
    @TableField("reference_wallet_id")
    private Long referenceWalletId;

    /**
     * 推广来源  1: S 集团  2：B 供应商  3：b 门店  4：C 消费者 5:X
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 被推广用户微信唯一标识
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 被推广人手机号
     */
    @TableField("promoted_phone")
    private String promotedPhone;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    @Override
    public Serializable pkVal() {
        return null;
    }

}
