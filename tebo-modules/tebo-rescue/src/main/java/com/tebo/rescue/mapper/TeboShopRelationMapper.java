package com.tebo.rescue.mapper;

import com.tebo.rescue.applet.domain.dto.TeboCustomerShopQueryDTO;
import com.tebo.rescue.applet.domain.view.TeboShopRelationDetailVO;
import com.tebo.rescue.applet.domain.view.TeboShopRelationVO;
import com.tebo.rescue.entity.TeboShopRelationDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 泰博门店表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-29
 */
@Mapper
public interface TeboShopRelationMapper extends TeboBaseMapper<TeboShopRelationDO> {
    /**
     * 获取门店列表
     * @param query
     * @return
     */
    List<TeboShopRelationVO> getList(TeboCustomerShopQueryDTO query);

    /**
     * 获取门店列表
     * @param query
     * @return
     */
    List<TeboShopRelationVO> getListNew(TeboCustomerShopQueryDTO query);

    /**
     * 获取订单详情
     * @param orderId
     * @return
     */
    TeboShopRelationDetailVO orderInfo(@Param("orderId") Long orderId);

    /**
     * 批量插入团购名单
     * @param param
     * @return
     */
    int insertBatch(Map<String, Object> param);

    /**
     * 根据订单id查询团购激活名单
     * @param orderId
     * @return
     */
    List selectDetailByOrderId(@Param("orderId") Long orderId);
}
