package com.tebo.rescue.service;

import com.tebo.system.api.domain.dto.PayOrderDTO;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import com.wechat.pay.java.service.payments.jsapi.model.Amount;
import com.wechat.pay.java.service.payments.jsapi.model.Payer;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayWithRequestPaymentResponse;

import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Zhang<PERSON>an
 * @date : 2024/8/24 9:06
 * @Desc :
 */
@Service
@Slf4j
public class WechatPayTempService {

    /**
     * 商户号
     */
    public static String merchantId = "1680683173";

    /**
     * 商户证书序列号
     */
    public static String merchantSerialNumber = "65B7D5D382C35D4AD81D73A406BADBDF7F5D85B2";
    /**
     * 商户APIV3密钥
     */
    public static String apiV3Key = "Fan1251787298zhangfan19931997fan";

    private static final String appId = "wx42e36eb3f3dd94fe";

    private static final String configKey = "wx_config_key";

    // private static final String notifyUrl = "https://s849p29949.zicp.fun/applet/order/wechat/notify";

    private static final String notifyUrl = "https://tntbcx.yicp.fun/orange-api/applet/order/wechat/notify";

    public PrepayWithRequestPaymentResponse prePay(PayOrderDTO payOrderDTO) {
        log.info("WechatPayTempService payOrderDTO {}", payOrderDTO);
        return prepayWithRequestPayment(payOrderDTO);
    }


    /**
     * 微信预支付
     *
     * @return
     */
    public PrepayWithRequestPaymentResponse prepayWithRequestPayment(PayOrderDTO payOrderDTO) {
        // 使用自动更新平台证书的RSA配置
        // 一个商户号只能初始化一个配置，否则会因为重复的下载任务报错
        RSAAutoCertificateConfig config =
                new RSAAutoCertificateConfig.Builder()
                        .merchantId(merchantId)
                        .privateKey(key())
                        .merchantSerialNumber(merchantSerialNumber)
                        .apiV3Key(apiV3Key)
                        .build();
        // 构建service
        JsapiServiceExtension service = new JsapiServiceExtension.Builder().config(config).build();
        PrepayRequest request = new PrepayRequest();
        Amount amount = new Amount();
        amount.setTotal(payOrderDTO.getAmount());
        request.setAmount(amount);
        request.setAppid(appId);
        request.setMchid(merchantId);
        request.setDescription("订单描述");
        request.setNotifyUrl(notifyUrl);
        request.setOutTradeNo(String.valueOf(System.currentTimeMillis()));
        Payer payer = new Payer();
        payer.setOpenid(payOrderDTO.getOpenId());
        request.setPayer(payer);
        log.info("request{}", request);
        // response包含了调起支付所需的所有参数，可直接用于前端调起支付
        PrepayWithRequestPaymentResponse response = service.prepayWithRequestPayment(request);
        log.info("WechatPayTempService response {}", response);
        return response;
    }

    public String key() {
        try {
            ClassPathResource classPathResource = new ClassPathResource("apiclient_key.pem");
            InputStream in = classPathResource.getInputStream();
            String config = IOUtils.toString(in);
            return config;
        } catch (IOException e) {
            throw new RuntimeException("加载密钥失败");
        }
    }
}
