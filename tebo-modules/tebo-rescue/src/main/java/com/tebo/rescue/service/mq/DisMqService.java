package com.tebo.rescue.service.mq;

import com.tebo.system.api.domain.dto.RemoteConsumerRecordDTO;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024/1/15 13:52
 * @Desc : Dis 消息接口封装-统一发送入口
 */
public interface DisMqService {

    /**
     * 调用发放礼包卡券
     * @param packId
     * @param unionId
     * @return
     */
    Boolean grantPackCoupon(Long packId, String unionId,String orderNo);


    /**
     * 调用发放单独卡券
     * @param couponId
     * @return
     */
    Boolean grantSingleCoupon(Long couponId);


    /**
     * 调用记录消费者行为
     * @param recordDTO
     * @return
     */
    Boolean recordConsumerRecord(RemoteConsumerRecordDTO recordDTO);

    /**
     * 通知安卓应用消息
     * @param shopId 门店id
     * @param msg 消息内容
     * @return
     */
    Boolean notifyAppMsg(Long shopId, String msg);

    /**
     * 超时工单通知
     * @param tenantId
     * @param msg
     * @return
     */
    Boolean notifyOrderTimeout(Long tenantId, String msg);


}
