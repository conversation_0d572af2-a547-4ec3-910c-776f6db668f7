package com.tebo.rescue.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.util.DateUtils;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.core.utils.uuid.UUID;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.util.DataUtils;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.DistanceUtil;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.common.util.position.AutoNaviPositionUtil;
import com.tebo.common.util.time.DateUtil;
import com.tebo.common.util.time.LocalDateUtil;
import com.tebo.mall.api.RemoteCycleInsuranceOrderService;
import com.tebo.mall.api.RemoteIntegralOrderService;
import com.tebo.mall.api.RemoteLotteryService;
import com.tebo.mall.api.domain.dto.RewardsPointOrderQueryDTO;
import com.tebo.mall.api.domain.dto.TeboMemberActLotteryRecordDTO;
import com.tebo.mall.api.domain.dto.cycling.TeboCyclingOrderDTO;
import com.tebo.mall.api.domain.view.JiFenOrderVO;
import com.tebo.mall.api.domain.view.TeboCycleInsuranceOrderVO;
import com.tebo.mall.api.domain.view.TeboGoodsVO;
import com.tebo.rescue.api.domain.dto.OrderCouponDTO;
import com.tebo.rescue.api.domain.view.TeboCouponSignRecordVO;
import com.tebo.rescue.applet.domain.dto.ServiceOrderGoodsDTO;
import com.tebo.rescue.applet.domain.dto.coupon.*;
import com.tebo.rescue.applet.domain.view.TeboShopConsumerVO;
import com.tebo.rescue.applet.domain.view.coupon.TeboMallCouponCustomerVO;
import com.tebo.rescue.entity.*;
import com.tebo.rescue.enums.CouponIdEnum;
import com.tebo.rescue.enums.PackIdEnum;
import com.tebo.rescue.manager.TeboCouponManager;
import com.tebo.rescue.mapper.*;
import com.tebo.rescue.service.TeboCouponCustomerService;
import com.tebo.rescue.service.TeboCouponService;
import com.tebo.rescue.service.TeboGiftPackService;
import com.tebo.rescue.util.TeboCommissionUtil;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponCustomerQueryDTO;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponCustomerRecordDTO;
import com.tebo.rescue.api.domain.dto.UnfreezeFundsQueryDTO;
import com.tebo.rescue.web.domain.view.*;
import com.tebo.system.api.RemoteCustomerService;
import com.tebo.system.api.RemotePartnerService;
import com.tebo.system.api.RemoteShopService;
import com.tebo.system.api.RemoteWalletService;
import com.tebo.system.api.domain.dto.RemoteConsumerQueryDTO;
import com.tebo.system.api.domain.dto.TeboCustomerQueryDTO;
import com.tebo.system.api.domain.dto.TeboPositionQueryDTO;
import com.tebo.system.api.domain.dto.TeboShopQueryDTO;
import com.tebo.system.api.domain.dto.wallet.TeboWalletPlanDTO;
import com.tebo.system.api.domain.dto.wallet.TeboWalletPlanDetailDTO;
import com.tebo.system.api.domain.view.MerchantsWalletVO;
import com.tebo.system.api.domain.view.RemoteConsumerVO;
import com.tebo.system.api.domain.view.TeboShopListVO;
import com.tebo.system.api.model.TeboConsumer;
import com.tebo.system.api.model.TeboShop;
import com.tebo.system.api.remote.RemoteTeboConsumerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ZhangFan
 * @date : 2023/12/21 18:27
 * @Desc :
 */
@Slf4j
@Service
public class TeboCouponCustomerServiceImpl implements TeboCouponCustomerService {

    @Resource
    private TeboCouponCustomerMapper teboCouponCustomerMapper;
    @Resource
    private TeboCouponMapper teboCouponMapper;

    @Resource
    private TeboGiftPackService teboGiftPackService;
    @Resource
    private TeboCouponService teboCouponService;
    @Resource
    private RemoteShopService remoteShopService;
    @Resource
    private TeboCouponManager teboCouponManager;
    @Resource
    private RemoteTeboConsumerService remoteTeboConsumerService;

    @Resource
    private TeboCouponGoodsMapper teboCouponGoodsMapper;
    @Resource
    private TeboGiftPackOrderMapper giftPackOrderMapper;
    @Resource
    private TeboCouponSignRecordMapper couponSignRecordMapper;
    @Resource
    private RedisService redisService;

    @Resource
    private RemoteCustomerService customerService;
    @Resource
    private RemoteCycleInsuranceOrderService cycleInsuranceOrderService;
    @Resource
    private RemoteLotteryService remoteLotteryService;
    @Resource
    private RemoteWalletService remoteWalletService;
    @Resource
    private TeboUnfreezeFundsRecordMapper fundsRecordMapper;
    @Resource
    private TeboCommissionUtil teboCommissionUtil;

    @Resource
    private RemoteIntegralOrderService integralOrderService;
    @Resource
    private RemotePartnerService remotePartnerService;

    @Resource
    private TeboCouponSkuMappingMapper teboCouponSkuMappingMapper;

    @Resource
    private TeboGroupPurchaseOrderMapper teboGroupPurchaseOrderMapper;

    @Resource
    private TeboGroupPurchaseCustomerMapper teboGroupPurchaseCustomerMapper;

    @Resource(name = "couponRiceExecutor")
    private Executor couponRiceExecutor;

    @Override
    public List<TeboCouponCustomerVO> getList(TeboCouponCustomerQueryDTO query) {
        List<TeboCouponCustomerDO> list = teboCouponCustomerMapper.getList(query);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<TeboCouponCustomerVO> result = BeanConvert.copyList(list, TeboCouponCustomerVO::new);
        List<TeboGiftPackCouponVO> couponVOS = teboCouponService.getCouponListByIds(list.stream().map(TeboCouponCustomerDO::getCouponId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        List<TeboCouponSkuMappingDO> skuList = teboCouponSkuMappingMapper.getList();
        Map<Long, TeboCouponSkuMappingDO> skuDOMap;
        if (!CollectionUtils.isEmpty(skuList)){
            skuDOMap = DataUtils.listToMap(skuList, TeboCouponSkuMappingDO::getCouponId);
        } else {
            skuDOMap = new HashMap();
        }
        Map<Long, TeboGiftPackCouponVO> couponMap = couponVOS.stream().collect(Collectors.toMap(TeboGiftPackCouponVO::getId, v -> v));
        result.stream().forEach(v -> {
            v.setCouponName(couponMap.get(v.getCouponId()).getCouponName());
            v.setParValue(couponMap.get(v.getCouponId()).getParValue());
            v.setRemark(couponMap.get(v.getCouponId()).getRemark());
            if (!CollectionUtils.isEmpty(skuDOMap)){
                TeboCouponSkuMappingDO couponSkuDO = skuDOMap.get(v.getCouponId());
                if (!ObjectUtils.isEmpty(couponSkuDO)){
                    v.setSkuId(couponSkuDO.getSkuId());
                }
            }
            v.setType(couponMap.get(v.getCouponId()).getType());
        });
        return result;
    }

    @Override
    public List<TeboCouponCustomerVO> getBatteryList(TeboCouponCustomerQueryDTO query) {
        List<TeboCouponCustomerDO> list = teboCouponCustomerMapper.getBatteryList(query);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<TeboCouponCustomerVO> result = BeanConvert.copyList(list, TeboCouponCustomerVO::new);
        List<TeboGiftPackCouponVO> couponVOS = teboCouponService.getCouponListByIds(list.stream().map(TeboCouponCustomerDO::getCouponId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        List<TeboCouponSkuMappingDO> skuList = teboCouponSkuMappingMapper.getList();
        Map<Long, TeboCouponSkuMappingDO> skuDOMap;
        if (!CollectionUtils.isEmpty(skuList)){
            skuDOMap = DataUtils.listToMap(skuList, TeboCouponSkuMappingDO::getCouponId);
        } else {
            skuDOMap = new HashMap();
        }
        Map<Long, TeboGiftPackCouponVO> couponMap = couponVOS.stream().collect(Collectors.toMap(TeboGiftPackCouponVO::getId, v -> v));
        result.stream().forEach(v -> {
            v.setCouponName(couponMap.get(v.getCouponId()).getCouponName());
            v.setParValue(couponMap.get(v.getCouponId()).getParValue());
            v.setRemark(couponMap.get(v.getCouponId()).getRemark());
            if (!CollectionUtils.isEmpty(skuDOMap)){
                TeboCouponSkuMappingDO couponSkuDO = skuDOMap.get(v.getCouponId());
                if (!ObjectUtils.isEmpty(couponSkuDO)){
                    v.setSkuId(couponSkuDO.getSkuId());
                }
            }
            if (v.getStatus() == 1 && v.getOccStatus() == 2 ){
                v.setStatus(2);
            }
            v.setType(couponMap.get(v.getCouponId()).getType());
        });
        return result;
    }

    @Override
    public List<TeboCouponCustomerVO> getMemberList(TeboCouponCustomerQueryDTO query) {
        List<TeboGiftPackOrderDO> orderDOList = giftPackOrderMapper.selectByUnionId(query.getUnionId());
        if (CollectionUtils.isEmpty(orderDOList)) {
            return Collections.emptyList();
        }
        QueryWrapper<TeboCouponCustomerDO> couponQuery = new QueryWrapper<>();
        couponQuery.lambda().eq(TeboCouponCustomerDO::getUnionId, query.getUnionId());
        couponQuery.lambda().in(TeboCouponCustomerDO::getPackId, orderDOList.stream().map(TeboGiftPackOrderDO::getPackId).collect(Collectors.toList()));
        List<TeboCouponCustomerDO> list = teboCouponCustomerMapper.selectList(couponQuery);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<TeboCouponCustomerVO> result = BeanConvert.copyList(list, TeboCouponCustomerVO::new);
        List<TeboGiftPackCouponVO> couponVOS = teboCouponService.getCouponListByIds(list.stream().map(TeboCouponCustomerDO::getCouponId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        Map<Long, TeboGiftPackCouponVO> couponMap = couponVOS.stream().collect(Collectors.toMap(TeboGiftPackCouponVO::getId, v -> v));
        result.stream().forEach(v -> {
            v.setCouponName(couponMap.get(v.getCouponId()).getCouponName());
            v.setParValue(couponMap.get(v.getCouponId()).getParValue());
            v.setRemark(couponMap.get(v.getCouponId()).getRemark());
        });
        return result;
    }

    @Override
    public TeboCouponCustomerVO getDetailByCouponCode(String couponCode) {
        TeboCouponCustomerDO couponCustomerDO = teboCouponCustomerMapper.getDetailByCouponCode(couponCode);
        if (ObjectUtils.isEmpty(couponCustomerDO)) {
            throw new ServiceException("该卡券无效，请确认是否可用！");
        }
        TeboCouponVO couponVO = teboCouponService.getInfo(couponCustomerDO.getCouponId());
        TeboCouponCustomerVO result= new TeboCouponCustomerVO();
        BeanConvert.copy(couponCustomerDO, result);
        result.setCouponName(couponVO.getCouponName());
        result.setOrderNo(couponCustomerDO.getOrderNo());
        result.setParValue(couponVO.getParValue());
        result.setRemark(couponVO.getRemark());
        result.setMainImage(couponVO.getMainImage());
        result.setGoodsList(couponVO.getGoodsList());
        result.setGoodLimit(couponVO.getGoodLimit());
        result.setUseType(couponVO.getUseType());
        result.setTenantId(couponVO.getTenantId());
        if (!StringUtils.isEmpty(couponVO.getUseReduce())) {
            result.setUseReduce(MoneyUtil.yuanToFen(couponVO.getUseReduce()));
        }
        // 24.9.29 查详情去掉门店
//        // TODO 24.8.8 活动专属
//        if (couponCustomerDO.getPackId().equals(1820740873973923840L)) {
//            List<TeboShopConsumerVO> shop = queryCouponShop();
//            result.setShopList(shop);
//        } else {
//            List<TeboShopConsumerVO> shop = teboCouponService.queryCouponShop(couponCustomerDO.getCouponId());
//            result.setShopList(shop.stream().limit(20).collect(Collectors.toList()));
//        }
        return result;
    }

    private List<TeboShopConsumerVO>  queryCouponShop() {
        // 全部门店
        TeboShopQueryDTO shopQueryDTO = new TeboShopQueryDTO();
        shopQueryDTO.setShopType(2);
        List<TeboShopListVO> shopListVOList = remoteShopService.fullyQuery(shopQueryDTO).getData();
        return BeanConvert.copyList(shopListVOList, TeboShopConsumerVO::new);
    }

    @Override
    public Integer shareCouponCode(String couponCode) {
        TeboCouponCustomerDO couponCustomerDO = teboCouponCustomerMapper.getDetailByCouponCode(couponCode);
        if (couponCustomerDO.getStatus() != 1) {
            throw new ServiceException("该卡券无效，不可分享！");
        }
        couponCustomerDO.setStatus(10);
        couponCustomerDO.setUpdateTime(LocalDateTime.now());
        return teboCouponCustomerMapper.updateById(couponCustomerDO);
    }

    @Override
    public Map<String,Object> receive(TeboCouponShareDTO teboCouponShareDTO) {
        Map<String,Object> map = new HashMap<>();
        TeboCouponCustomerDO couponCustomerDO = teboCouponCustomerMapper.getDetailByCouponCode(teboCouponShareDTO.getCouponCode());
        // 状态不是分享中，返回0，前端处理
        if(!Objects.equals(couponCustomerDO.getStatus(),10)) {
            map.put("result",0);
            return map;
        }
//        if (couponCustomerDO.getPackId().toString().equals(PackIdEnum.AN_XIN.getCode()) ||couponCustomerDO.getPackId().toString().equals(PackIdEnum.JU_HUI.getCode()) ){
//            String provinceCityDistrict = AutoNaviPositionUtil.getProvinceCityDistrict(teboCouponShareDTO.getLgt(), teboCouponShareDTO.getLnt());
//            List<String> allDistrictList = teboPackOpenDistrictMapper.getAllDistrictList();
//            if (!org.springframework.util.StringUtils.isEmpty(provinceCityDistrict)) {
//                String[] strArray = provinceCityDistrict.split("-");
//                String province = strArray[0];
//                String city = strArray[1];
//                String district = strArray[2];
//                String detail = province + "-" + city + "-" + district;
//                Boolean open = false;
//                for (String str : allDistrictList) {
//                    if (detail.contains(str)) {
//                        open = true;
//                        break;
//                    }
//                }
//                if (!open) {
//                    throw new ServiceException("当前区域不在卡券服务范围，不可领取");
//                }
//            }
//        }
        // 先将分享方的卡券过期，再帮领取方新增一条卡券
        TeboCouponCustomerDO item = new TeboCouponCustomerDO();
        BeanConvert.copy(couponCustomerDO, item);
        couponCustomerDO.setStatus(11);
        couponCustomerDO.setUniqueCode(String.valueOf(System.currentTimeMillis()));
        int x = teboCouponCustomerMapper.updateById(couponCustomerDO);
        item.setId(SnowFlakeUtil.nextId());
        item.setUnionId(teboCouponShareDTO.getUnionId());
        item.setReceiveTime(LocalDateTime.now());
        item.setUpdateTime(LocalDateTime.now());
        item.setStatus(1);
        teboCouponCustomerMapper.insert(item);
        /**
         * 如果是骑行保卡券,调用誉好接口,更新权益人手机号
         */
        R<TeboConsumer> customerVOResult = customerService.selectByUnionId(teboCouponShareDTO.getUnionId());
        if (ObjectUtil.isEmpty(customerVOResult) || ObjectUtil.isEmpty(customerVOResult.getData())){
            throw new ServiceException("用户不存在");
        }
        TeboCyclingOrderDTO teboCyclingOrderDTO = new TeboCyclingOrderDTO();
        teboCyclingOrderDTO.setOrderNo(teboCouponShareDTO.getCouponCode());
        teboCyclingOrderDTO.setStakeholderPhoneNumber(customerVOResult.getData().getPhoneNumber());
        cycleInsuranceOrderService.synchronizeOriginators(teboCyclingOrderDTO);

        if(x > 0) {
          map.put("result",1);
        } else {
          map.put("result",0);
        }
        return map;
    }

    @Override
    public Integer grantPackCoupon(Long packId, String unionId,String orderNo) {
        TeboGiftPackVO packVO = teboGiftPackService.getInfo(packId);
        if (packVO == null) {
            return 0;
        }
        if (CollectionUtils.isEmpty(packVO.getCoupons())) {
            return 0;
        }
        // 卡券有效期开始 + 结束
        LocalDateTime startTime = LocalDateTime.now();
        LocalDateTime endTime = startTime.minusDays(1).plusMonths(packVO.getMonthNum());
        List<TeboCouponCustomerDO> itemList = new ArrayList<>();
        packVO.getCoupons().stream().forEach(item -> {
           for (int i = 0; i < item.getNum(); i++) {
               TeboCouponCustomerDO couponCustomerDO = new TeboCouponCustomerDO(item.getId(), packId, unionId);
               couponCustomerDO.setCouponId(item.getId());
               couponCustomerDO.setReceiveTime(startTime);
               couponCustomerDO.setUpdateTime(LocalDateTime.now());
               couponCustomerDO.setStartTime(startTime);
               couponCustomerDO.setEndTime(endTime);
               couponCustomerDO.setOrderNo(orderNo);
               // 24.8.8 活动专属,特定 24.9.8那天
               if (packVO.getId().equals(1820740873973923840L)) {
                   couponCustomerDO.setEndTime(LocalDateTime.of(2024,9,9,0,0,0));
               }
               couponCustomerDO.setStatus(1);
               String uniqueCode = UUID.randomUUID().toString();
               couponCustomerDO.setUniqueCode(uniqueCode);
               itemList.add(couponCustomerDO);
           }
        });
        Integer num = teboCouponCustomerMapper.insertBatch(itemList);
        // 礼包数量扣减 1
        teboGiftPackService.reduceGiftPack(packId);
        itemList.forEach(item ->{
            createCouponCustomerRecord(item.getUniqueCode(), item.getUnionId());
        });
        return num;
    }

    @Override
    public Integer grantSingleCoupon(Long couponId) {
        TeboCouponVO couponVO = teboCouponService.getInfo(couponId);
        if (couponVO == null) {
            return 0;
        }
        // 如果不是单独卡券，则直接不发放
        if (couponVO.getSingle() != 2) {
            return 0;
        }
        // 查找平台所有用户
        RemoteConsumerQueryDTO query = new RemoteConsumerQueryDTO(Boolean.TRUE);
        R<List<RemoteConsumerVO>> res = remoteTeboConsumerService.getConsumerList(query);
        List<String> unionIds = res.getData().stream().map(RemoteConsumerVO::getUnionid).collect(Collectors.toList());
        // 卡券有效期开始 + 结束
        LocalDateTime startTime = LocalDateTime.now();
        LocalDateTime endTime = couponVO.getEndTime().atStartOfDay();
        List<TeboCouponCustomerDO> itemList = new ArrayList<>();
        unionIds.stream().forEach(unionId -> {
            TeboCouponCustomerDO couponCustomerDO = new TeboCouponCustomerDO(couponId, 0L, unionId);
            couponCustomerDO.setCouponId(couponId);
            couponCustomerDO.setReceiveTime(startTime);
            couponCustomerDO.setStartTime(startTime);
            couponCustomerDO.setEndTime(endTime);
            couponCustomerDO.setStatus(1);
            String uniqueCode = UUID.randomUUID().toString();
            couponCustomerDO.setUniqueCode(uniqueCode);
            itemList.add(couponCustomerDO);
        });
        Integer num = teboCouponCustomerMapper.insertBatch(itemList);
        itemList.forEach(item ->{
            createCouponCustomerRecord(item.getUniqueCode(), item.getUnionId());
        });
        return num;
    }
    @Override
    public void grantAllStaffCoupon(TeboCouponAllStaffDTO teboCouponAllStaffDTO){
        R<List<TeboConsumer>> allUserR = customerService.selectAllUser();
        if (!ObjectUtils.isEmpty(allUserR)){
            List<TeboConsumer> allUserList = allUserR.getData();
            allUserList.forEach(item ->{
                grantPackCoupon(teboCouponAllStaffDTO.getPackId(),item.getUnionid(),"");
            });
        }
    }
    private void createCouponCustomerRecord(String uniqueCode, String unionId) {
        teboCouponService.createCouponCustomerRecord(uniqueCode, unionId);
    }

    @Override
    public TeboCouponCustomerVO checkCouponRule(TeboCouponCheckDTO checkDTO) {
        TeboCouponCustomerVO customerVO = getDetailByCouponCode(checkDTO.getUniqueCode());
        // 校验卡券状态
        if (customerVO.getStatus()!= 1) {
            throw new ServiceException("卡券已被使用或已过期");
        }
        if (customerVO.getEndTime().isBefore(LocalDateTime.now())) {
            throw new ServiceException("卡券已过期");
        }
        // 卡券发行人和当前门店是否归属同一个合伙人
        R<TeboShop> res = remoteShopService.getShopInfo(checkDTO.getShopId());
        if (res.getCode() == 200) {
            TeboShop teboShop = res.getData();
            Long tenantId = customerVO.getTenantId();
            log.info("checkCouponRule tenantId {}", tenantId);
            if (tenantId != 0L && !teboShop.getTenantId().equals(tenantId)) {
                throw new ServiceException("卡券在该门店不可用!");
            }
        }
        // List<TeboShopConsumerVO> shopList = customerVO.getShopList();
        // if (!shopList.contains(checkDTO.getShopId())) {
        //     throw new ServiceException("卡券在该门店不可用！");
        // }
        // 检查商品是否可用
        // 选中的配件
        List<ServiceOrderGoodsDTO> serviceGoodsList = checkDTO.getGoodsList();
        List<String> goodsNoList = serviceGoodsList.stream().map(ServiceOrderGoodsDTO::getGoodsNo).collect(Collectors.toList());
        // 卡券可用商品
        if (customerVO.getGoodLimit() == 1) {
            List<TeboGoodsVO> goodsList = customerVO.getGoodsList();
            log.info("goodsList{}", goodsList);
            AtomicInteger sum = new AtomicInteger(0);
            goodsList.stream().forEach(item -> {
                if (goodsNoList.contains(item.getGoodsNo())) {
                    sum.getAndIncrement();
                }
            });
            if (sum.get() == 0) {
                throw new ServiceException("卡券商品不满足卡券使用条件！");
            }
        }

        // 检查是否达到满减金额
        if (customerVO.getUseType() == 1) {
            AtomicInteger moneySum = new AtomicInteger(0);
            if (!StringUtils.isEmpty(checkDTO.getBaseAmount())) {
                moneySum.set(MoneyUtil.yuanToFen(checkDTO.getBaseAmount()));
            }
            serviceGoodsList.stream().forEach(item -> {
                BigDecimal goodsAmount = new BigDecimal(item.getGoodsNum())
                        .multiply(new BigDecimal(MoneyUtil.yuanToFen(item.getGoodsPrice())));
                moneySum.addAndGet(goodsAmount.intValue());
            });
            Integer useReduce = customerVO.getUseReduce();
            if (moneySum.get() < useReduce) {
                throw new ServiceException("商品未到满减门槛！");
            }
        }

        return customerVO;
    }

    @Override
    public Integer batchUpdateCouponStatus(List<String> uniqueCode, Integer status) {
        // TODO 考虑各种状态券是否可用
        if (CollectionUtils.isEmpty(uniqueCode)) {
            return 0;
        }
        return teboCouponCustomerMapper.batchUpdateByUniqueCode(uniqueCode, status);
    }

    @Override
    public Integer updateCouponStatusAndTime(OrderCouponDTO orderCouponDTO) {
        return teboCouponCustomerMapper.updateCouponStatusAndTime(orderCouponDTO);
    }

    @Override
    public Map<String,Object> cancelShareCouponCode(String couponCode) {
        Map<String,Object> res = new HashMap<>();
        TeboCouponCustomerDO couponCustomerDO = teboCouponCustomerMapper.getDetailByCouponCode(couponCode);
        // 状态不是分享中，返回0，前端处理
        if(!Objects.equals(couponCustomerDO.getStatus(),10)) {
            res.put("result",0);
            return res;
        }
        couponCustomerDO.setStatus(1);
        int x = teboCouponCustomerMapper.updateById(couponCustomerDO);
        if(x > 0) {
            res.put("result",1);
        } else {
            res.put("result",0);
        }
        return res;
    }

    @Override
    public TeboMallCouponCustomerVO getCouponCustomerList(String unionId) {
        TeboMallCouponCustomerVO result = new TeboMallCouponCustomerVO();
        // 获取消费者有效久久电池券
        List<TeboCouponCustomerDO> list = teboCouponCustomerMapper.getCouponCustomerList(unionId);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        // 券信息是否存在
        List<TeboCouponDO> couponDOList = teboCouponMapper.getCouponListByIds(list.stream().map(TeboCouponCustomerDO::getCouponId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(couponDOList)) {
            return result;
        }
        Map<Long, TeboCouponDO> couponMap = couponDOList.stream().collect(Collectors.toMap(TeboCouponDO::getId, item -> item));
        List<TeboAppletCouponCustomerVO> couponList = BeanConvert.copyList(list, TeboAppletCouponCustomerVO::new);
        couponList.stream().forEach(item ->{
            TeboCouponDO couponDO = couponMap.get(item.getCouponId());
            item.setCouponName(couponDO.getCouponName());
            item.setParValue(MoneyUtil.fenToYuan(couponDO.getParValue()));
            item.setRemark(couponDO.getRemark());
        });
        result.setCouponList(couponList);
        return result;
    }

    @Override
    public List<TeboShopConsumerVO> getCouponShop(String uniqueCode, String lgt, String lnt) {
        TeboCouponCustomerDO couponCustomerDO = teboCouponCustomerMapper.getDetailByCouponCode(uniqueCode);
        if (ObjectUtil.isEmpty(couponCustomerDO)){
            return null;
        }
        List<TeboShopConsumerVO> result = new ArrayList<>();
        // 会员卡电池券特殊处理
        if (couponCustomerDO.getCouponId().toString().equals(CouponIdEnum.JU_HUI_BATTERY.getCode()) || couponCustomerDO.getCouponId() == 1839262014409539584L) {
            TeboPositionQueryDTO teboShopQueryDTO = new TeboPositionQueryDTO();
            teboShopQueryDTO.setLgt(lgt);
            teboShopQueryDTO.setLnt(lnt);
            teboShopQueryDTO.setVip(1);
            R<List<TeboShopListVO>> res = remotePartnerService.getMallShopByDistrictCodeList(teboShopQueryDTO);
            List<TeboShopListVO> list = res.getData();
            if(!CollectionUtils.isEmpty(list)){
                result.addAll(BeanConvert.copyList(list, TeboShopConsumerVO::new));
                for(TeboShopConsumerVO item :result ){
                    if (StringUtils.isNotEmpty(item.getLatitude()) && StringUtils.isNotEmpty(item.getLongitude())){
                        item.setDistance(DistanceUtil.getDistance(item.getLatitude(), item.getLongitude(), lnt, lgt));
                    }
                }
            }
        }else {
            List<TeboShopConsumerVO> shop = teboCouponService.queryCouponShop(couponCustomerDO.getCouponId());
            if (CollectionUtils.isEmpty(shop)) {
                return Collections.emptyList();
            }
            if (StringUtils.isAnyBlank(lgt, lnt)) {
                return shop;
            }
            shop.stream().forEach(item -> {
                if (StringUtils.isNotEmpty(item.getLatitude()) && StringUtils.isNotEmpty(item.getLongitude())){
                    item.setDistance(DistanceUtil.getDistance(item.getLatitude(), item.getLongitude(), lnt, lgt));
                    result.add(item);
                }
            });
        }
        List<TeboShopConsumerVO> finalResult =  result.stream().sorted(Comparator.comparing(TeboShopConsumerVO::getDistance)).collect(Collectors.toList());
        return finalResult.stream().filter(item -> item.getStatus() == 1).limit(20).collect(Collectors.toList());
    }

    @Override
    public TeboCouponSignRecordDO getCouponRecord(String uniqueCode) {
        QueryWrapper<TeboCouponSignRecordDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("coupon_code", uniqueCode);
        List<TeboCouponSignRecordDO> list = couponSignRecordMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new TeboCouponSignRecordDO();
        }
        return list.get(0);
    }

    @Override
    public Boolean refundGiftPackCoupon(String orderNo, String unionId) {
        log.info("refundGiftPackCoupon orderNo {}", orderNo);
        QueryWrapper<TeboCouponCustomerDO> couponQuery = new QueryWrapper<>();
        couponQuery.lambda().eq(TeboCouponCustomerDO::getUnionId, unionId);
        couponQuery.lambda().in(TeboCouponCustomerDO::getOrderNo, orderNo);
//        couponQuery.lambda().eq(TeboCouponCustomerDO::getStatus, 1);
        List<TeboCouponCustomerDO> list = teboCouponCustomerMapper.selectList(couponQuery);
        if (CollectionUtils.isEmpty(list)) {
            log.info("refundGiftPackCoupon orderNo {}", orderNo);
            throw new ServiceException("未查到卡券，不可退款");
        }
        for (TeboCouponCustomerDO item : list) {
            if (item.getStatus() == 2 || item.getStatus() == 11) {
                if (item.getCouponId().toString().equals(CouponIdEnum.JU_HUI_BATTERY.getCode())){
                    throw new ServiceException("抱歉，电池券已核销或被转赠，无法退款;");
                }
                if (item.getCouponId().toString().equals(CouponIdEnum.RICE.getCode())){
                    throw new ServiceException("抱歉，大米已提货，无法退款;");
                }
                if (item.getCouponId().toString().equals(CouponIdEnum.CYCLE.getCode())){
                    throw new ServiceException("抱歉，骑行无忧券已激活或被转赠，无法退款。");
                }
            }
            if (ObjectUtil.isNotEmpty(item.getOccStatus()) && item.getOccStatus() == 2) {
                throw new ServiceException("抱歉，电池券已使用，无法退款;");
            }
        }
        List<Long> ids = list.stream().map(TeboCouponCustomerDO::getId).collect(Collectors.toList());
        LambdaUpdateWrapper<TeboCouponCustomerDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(TeboCouponCustomerDO::getId, ids);
        updateWrapper.set(TeboCouponCustomerDO::getStatus, 20);
        updateWrapper.set(TeboCouponCustomerDO::getUpdateTime, LocalDateTimeUtil.now());
        teboCouponCustomerMapper.update(updateWrapper);
        return Boolean.TRUE;
    }

    @Override
    public Boolean grantLvccCoupon(String unionId) {
        String key = "tebo_rescue:lvcc:coupon:unionId:" + unionId;
        if (redisService.hasKey(key)) {
            return Boolean.TRUE;
        }
        // 卡券有效期开始 + 结束
        LocalDateTime startTime = LocalDateTime.now();
        LocalDateTime endTime = startTime.plusYears(1);
        TeboCouponCustomerDO couponCustomerDO = new TeboCouponCustomerDO(1844564556404228096L, 0L, unionId);
        couponCustomerDO.setReceiveTime(startTime);
        couponCustomerDO.setStartTime(startTime);
        couponCustomerDO.setEndTime(endTime);
        couponCustomerDO.setStatus(1);
        String uniqueCode = UUID.randomUUID().toString();
        couponCustomerDO.setUniqueCode(uniqueCode);
        teboCouponCustomerMapper.insert(couponCustomerDO);
        redisService.setCacheObject(key, true);
        return Boolean.TRUE;
    }

    @Override
    public TeboCouponCustomerNumberVO getCustomerCouponNumber(String unionId) {
        R<TeboConsumer> consumerR = customerService.selectByUnionId(unionId);
        TeboCouponCustomerNumberVO teboCouponCustomerNumberVO = new TeboCouponCustomerNumberVO();
        /**
         * 电池
         */
        TeboCouponCustomerQueryDTO battery = new TeboCouponCustomerQueryDTO();
        List<Long> couponIdList = new ArrayList<>();
        couponIdList.add(Long.parseLong(CouponIdEnum.AN_XIN_BATTERY.getCode()));
        couponIdList.add(Long.parseLong(CouponIdEnum.JU_HUI_BATTERY.getCode()));
        // 老电池券
        couponIdList.add(1839262014409539584L);
        // 25.3.26 专供品券
        couponIdList.add(10001L);
        battery.setCouponIdList(couponIdList);
        battery.setUnionId(unionId);
        List<TeboCouponCustomerDO> batteryList = teboCouponCustomerMapper.getUnUsedCustomerCouponList(battery);
        teboCouponCustomerNumberVO.setBatteryCouponNumber(CollectionUtils.isEmpty(batteryList)? 0:batteryList.size());
        /**
         * 大米
         */
        TeboCouponCustomerQueryDTO rice = new TeboCouponCustomerQueryDTO();
        List<Long> riceCouponIdList = new ArrayList<>();
        riceCouponIdList.add(Long.parseLong(CouponIdEnum.RICE.getCode()));
        rice.setUnionId(unionId);
        rice.setCouponIdList(riceCouponIdList);
        rice.setStatus(1);
        List<TeboCouponCustomerDO> riceList = teboCouponCustomerMapper.getCustomerCouponList(rice);
        teboCouponCustomerNumberVO.setRiceCouponNumber(CollectionUtils.isEmpty(riceList) ? 0:riceList.size());
        /**
         * 中奖未领取的奖品数量
         */
        TeboMemberActLotteryRecordDTO lotteryRecordDTO = new TeboMemberActLotteryRecordDTO();
        lotteryRecordDTO.setUnionId(unionId);
        lotteryRecordDTO.setLotteryRecordStatus(1);
        R<Integer> result = remoteLotteryService.selectUnclaimedPrizeByUnionId(lotteryRecordDTO);
        if (result.getCode() == 200){
            teboCouponCustomerNumberVO.setUnclaimedPrizeNumber(result.getData());
        }
        /**
         * 骑行保
         */
        TeboCouponCustomerQueryDTO cycle = new TeboCouponCustomerQueryDTO();
        cycle.setCouponId(Long.parseLong(CouponIdEnum.CYCLE.getCode()));
        cycle.setUnionId(unionId);
        cycle.setStatus(1);
        List<TeboCouponCustomerDO> cycleList = teboCouponCustomerMapper.getList(cycle);
        teboCouponCustomerNumberVO.setCycleCouponNumber(CollectionUtils.isEmpty(cycleList) ? 0:cycleList.size());
        /**
         * 用户佣金
         */
        if (consumerR.getCode() == 200 && ObjectUtil.isNotEmpty(consumerR.getData())){
            TeboConsumer consumer = consumerR.getData();
            if (ObjectUtil.isNotEmpty(consumer.getWalletId())){
                R<MerchantsWalletVO> merchantsWalletVOR = remoteWalletService.getPersonalWallet(consumer.getWalletId());
                if (ObjectUtil.isNotEmpty(merchantsWalletVOR) && ObjectUtil.isNotEmpty(merchantsWalletVOR.getData()) ){
                    MerchantsWalletVO merchantsWalletVO = merchantsWalletVOR.getData();
                    if(ObjectUtil.isNotEmpty(merchantsWalletVO)){
                        if (ObjectUtil.isNotEmpty(merchantsWalletVO.getAvaBalance())){
                            merchantsWalletVO.setAvaBalanceStr(MoneyUtil.fenToYuan(merchantsWalletVO.getAvaBalance()));
                        }
                        if (ObjectUtil.isNotEmpty(merchantsWalletVO.getFreezeAmount())){
                            merchantsWalletVO.setFreezeAmountStr(MoneyUtil.fenToYuan(merchantsWalletVO.getFreezeAmount()));
                        }
                        if (ObjectUtil.isNotEmpty(merchantsWalletVO.getTotalAmount())){
                            merchantsWalletVO.setTotalAmountStr(MoneyUtil.fenToYuan(merchantsWalletVO.getTotalAmount()));
                        }
                        if (ObjectUtil.isNotEmpty(merchantsWalletVO.getWithdrawAmount())){
                            merchantsWalletVO.setWithdrawAmountStr(MoneyUtil.fenToYuan(merchantsWalletVO.getWithdrawAmount()));
                        }
                    }
                    teboCouponCustomerNumberVO.setMerchantsWalletVO(merchantsWalletVO);
                }
            }
        }
        return teboCouponCustomerNumberVO;
    }

    public List<TeboCouponCustomerVO> getRiceCouponList(TeboCouponCustomerQueryDTO queryDTO){
        /**
         * 大米
         */
        TeboCouponCustomerQueryDTO rice = new TeboCouponCustomerQueryDTO();
        List<Long> riceCouponIdList = new ArrayList<>();
        riceCouponIdList.add(Long.parseLong(CouponIdEnum.RICE.getCode()));
        rice.setUnionId(queryDTO.getUnionId());
        rice.setCouponIdList(riceCouponIdList);
        rice.setStatusList(queryDTO.getStatusList());
        List<TeboCouponCustomerDO> riceList = teboCouponCustomerMapper.getCustomerRiceCouponList(rice);
        List<TeboCouponCustomerVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(riceList)){
            list = BeanConvert.copyList(riceList,TeboCouponCustomerVO::new);
            TeboCouponDO riceCoupon = teboCouponMapper.selectById(list.get(0).getCouponId());
            list.forEach(item ->{
            riceCoupon.setStatus(item.getStatus());
            if (ObjectUtil.isNotEmpty(riceCoupon)){
                BeanConvert.copy(riceCoupon,item);
            }
            item.setParValue(MoneyUtil.fenToYuan(riceCoupon.getParValue()));
            });
        }
       // asyncEnrichOrderInfo(list,queryDTO.getUnionId());
        return list;
    }

    // 异步查询并填充订单信息
    private void asyncEnrichOrderInfo(List<TeboCouponCustomerVO> list, String unionId) {
        // 创建线程池 (实际项目中应该使用配置的线程池)
      //  ExecutorService executor = Executors.newFixedThreadPool(10);

        // 收集需要查询订单的VO
        List<TeboCouponCustomerVO> needQueryItems = list.stream()
                .filter(item -> item.getStatus() == 2 && StringUtils.isNotEmpty(item.getOrderNo()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(needQueryItems)) {
            return;
        }

        // 创建CompletableFuture列表
        List<CompletableFuture<Void>> futures = needQueryItems.stream()
                .map(item -> CompletableFuture.runAsync(() -> {
                    RewardsPointOrderQueryDTO dto = new RewardsPointOrderQueryDTO();
                    dto.setOrderNo(item.getOrderNo());
                    dto.setUnionId(unionId);

                    // 执行订单查询
                    R<JiFenOrderVO> result = integralOrderService.getOrderByOrderNo(dto);

                    // 设置查询结果
                    if (ObjectUtil.isNotEmpty(result) && ObjectUtil.isNotEmpty(result.getData())) {
                        item.setJiFenOrderVO(result.getData());
                    }
                }, couponRiceExecutor))
                .collect(Collectors.toList());

        // 等待所有异步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }
    public TeboCouponCustomerVO checkMallCouponRule(TeboCouponCustomerVO couponCustomer, Long tenantId,Long shopId, String goodsNo,Long goodsId,String total) {
        log.info("checkMallCouponRule couponCustomer {}", couponCustomer);
        Long couponId = couponCustomer.getCouponId();
        TeboCouponDO couponDO = teboCouponMapper.selectById(couponId);
        // 校验卡券状态
        if (couponCustomer.getStatus() != 1) {
            log.info("checkMallCouponRule status couponName {}", couponCustomer.getCouponName());
            return null;
        }
        // 只筛选电池券
        if (couponCustomer.getType() != 11) {
            log.info("checkMallCouponRule type couponName {}", couponCustomer.getCouponName());
            return null;
        }
        // if (couponCustomer.getEndTime().isBefore(LocalDateTime.now())) {
        //     log.info("checkMallCouponRule endTime couponName {}", couponCustomer.getCouponName());
        //     return null;
        // }
        // 卡券发行人和当前门店是否归属同一个合伙人或者该券是平台发行的
        if (couponCustomer.getTenantId() != 0L && !couponCustomer.getTenantId().equals(tenantId)) {
            log.info("checkMallCouponRule tenantId couponName {}", couponCustomer.getCouponName());
            return null;
        }
        // 卡券可用商品

        /**
        if (couponCustomer.getGoodLimit() == 1) {
            TeboCouponCustomerVO customerVO = getDetailByCouponCode(couponCustomer.getUniqueCode());
            List<TeboGoodsVO> goodsList = customerVO.getGoodsList();
            log.info("goodsList{}", goodsList);
            AtomicInteger sum = new AtomicInteger(0);
            goodsList.stream().forEach(item -> {
                if (goodsNo.contains(item.getGoodsNo())) {
                    sum.getAndIncrement();
                }
            });
            if (sum.get() == 0) {
                log.info("checkMallCouponRule goodsLimit couponName {}", couponCustomer.getCouponName());
                return null;
            }
        }
         */

        // 检查是否达到满减金额
        if (couponCustomer.getUseType() == 1) {
            Integer moneySum = 0;
            if (!StringUtils.isEmpty(total)) {
                moneySum = MoneyUtil.yuanToFen(total);
            }
            if (moneySum < couponCustomer.getUseReduce()) {
                log.info("checkMallCouponRule useType couponName {}", couponCustomer.getCouponName());
                return null;
            }
        }

        /**
         * 校验门店是否满足要求
         */
        if (couponDO.getVest() == 1) {
            List<TeboShopConsumerVO> shopList = teboCouponService.queryCouponShop(couponCustomer.getCouponId());
            List<Long> shopIdList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(shopList)){
               shopIdList = shopList.stream().map(item -> item.getId()).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(shopList) && !shopIdList.contains(shopId) ){
                return null;
            }
        }

        /**
         * 校验平台商品是否满足要求
         */
        if (couponDO.getGoodLimit() == 1) {
            List<String> goodsNoList = teboCouponGoodsMapper.selectByCouponId(couponId).stream().map(TeboCouponGoodsDO::getGoodsNo).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(goodsNoList) && !goodsNoList.contains(goodsNo) ){
                return null;
            }
        }
        /**
         * 校验非平台商品是否满足需求
         */
        if (couponDO.getGoodLimit() == 2) {
            List<Long> goodsIdList = teboCouponGoodsMapper.selectByCouponId(couponId).stream().map(TeboCouponGoodsDO::getGoodsId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(goodsIdList) && !goodsIdList.contains(goodsId) ){
                return null;
            }
        }
        return couponCustomer;
    }

    @Override
    public Integer updateCouponStatus(OrderCouponDTO orderCouponDTO) {
        TeboCouponCustomerRecordDTO dto = new TeboCouponCustomerRecordDTO(Lists.newArrayList(orderCouponDTO.getCouponCode()), 3,orderCouponDTO.getShopId(),orderCouponDTO.getShopName(),orderCouponDTO.getOrderNo());
        teboCouponManager.batchUpdateCouponRecordStatus(dto);
        return batchUpdateCouponStatus(Lists.newArrayList(orderCouponDTO.getCouponCode()),2);
    }

    @Override
    public Integer updateCouponListStatus(OrderCouponDTO orderCouponDTO) {
        return batchUpdateCouponStatus(Lists.newArrayList(orderCouponDTO.getCouponCode()),2);
    }

    @Override
    public Integer updateCouponOccStatus(OrderCouponDTO orderCouponDTO) {
        TeboCouponCustomerDO couponCustomerDO = teboCouponCustomerMapper.getDetailByCouponCode(orderCouponDTO.getCouponCode());
        couponCustomerDO.setOccStatus(orderCouponDTO.getOccStatus());
        couponCustomerDO.setOccNumber(orderCouponDTO.getOccNumber());
        couponCustomerDO.setUpdateTime(LocalDateTime.now());
        return teboCouponCustomerMapper.updateById(couponCustomerDO);
    }

    @Override
    public Integer updateCouponStatusNotUsed(OrderCouponDTO orderCouponDTO){
        return batchUpdateCouponStatus(Lists.newArrayList(orderCouponDTO.getCouponCode()),1);
    }

    @Override
    public List<TeboCouponCustomerVO> getCyclingInsuranceCouponList(TeboCouponCustomerQueryDTO queryDTO){
        queryDTO.setCouponId(Long.parseLong(CouponIdEnum.CYCLE.getCode()));
        List<TeboCouponCustomerDO> couponCustomerList = teboCouponCustomerMapper.getCustomerCouponList(queryDTO);
        if (CollectionUtils.isEmpty(couponCustomerList)){
            return null;
        }
        List<TeboCouponCustomerVO> result = BeanConvert.copyList(couponCustomerList,TeboCouponCustomerVO::new);
        result.forEach(item ->{
            TeboCycleInsuranceOrderVO insuranceOrderVO = cycleInsuranceOrderService.getOrderByCouponCode(item.getUniqueCode()).getData();
            item.setCycleInsuranceOrderVO(insuranceOrderVO);
        });
        return result;
    }
    @Override
    public void signRecord(TeboCouponCloudShopDTO dto){
        TeboCouponCustomerVO customerVO = getDetailByCouponCode(dto.getCouponCode());
        if (ObjectUtils.isEmpty(customerVO)) {
            throw new ServiceException("该卡券无效，请确认是否可用！");
        }
        // 未使用的券或者 分享中的券可以用
        if (customerVO.getStatus() != 1) {
            if (customerVO.getStatus() != 10) {
                throw new ServiceException("该卡券已使用或已失效");
            }
        }
        if (ObjectUtil.isNotEmpty(customerVO.getOccStatus()) && customerVO.getOccStatus() == 2) {
            throw new ServiceException("该卡券已使用");
        }
        // 更新券状态
        batchUpdateCouponStatus(Collections.singletonList(dto.getCouponCode()), 2);
        /**
         * 更新订单状态为已核销
         */
        TeboGiftPackOrderDO teboGiftPackOrderDO = giftPackOrderMapper.selectByOrderNo(customerVO.getOrderNo());
        if (StringUtils.isNotEmpty(customerVO.getOrderNo())){
            if (ObjectUtil.isNotEmpty(teboGiftPackOrderDO)){
                teboGiftPackOrderDO.setVerificationStatus(1);
                teboGiftPackOrderDO.setBatteryVerificationStatus(1);
                teboGiftPackOrderDO.setBatteryVerificationTime(LocalDateTimeUtil.now());
                if (ObjectUtils.isEmpty(teboGiftPackOrderDO.getVerificationTime())){
                    teboGiftPackOrderDO.setVerificationTime(LocalDateTime.now());
                }
                R<TeboShop> shopR = remoteShopService.getShopInfo(dto.getShopId());
                if (ObjectUtil.isNotEmpty(shopR) && ObjectUtil.isNotEmpty(shopR.getData())){
                    TeboShop shop = shopR.getData();
                    teboGiftPackOrderDO.setVerificationShopId(Long.parseLong(shop.getId()));
                    teboGiftPackOrderDO.setVerificationTenantId(shop.getTenantId());
                }
                giftPackOrderMapper.updateById(teboGiftPackOrderDO);
            }

        }
        // 插入核销记录
        couponSignRecordMapper.insert(buildSignRecord(dto));
        TeboCouponCustomerRecordDTO recordDTO = new TeboCouponCustomerRecordDTO(Collections.singletonList(dto.getCouponCode()), 3, null, null, null);
        teboCouponManager.batchUpdateCouponRecordStatus(recordDTO);
        Date date = DateUtil.transferString2Date("2024-11-10");
        if (customerVO.getReceiveTime().isBefore(LocalDateTimeUtil.of(date))){
            return;
        }
        UnfreezeFundsQueryDTO queryParam = new UnfreezeFundsQueryDTO();
        queryParam.setOrderNo(customerVO.getOrderNo());
        List<TeboUnfreezeFundsRecordDO> list = fundsRecordMapper.getByOrderNo(queryParam);
        if (teboGiftPackOrderDO.getOrderType() == 1){
            if (ObjectUtil.isEmpty(list)){
                /**
                 * 佣金解冻
                 */
                TeboWalletPlanDTO teboWalletPlanDTO = teboCommissionUtil.buildCommissionParam(customerVO.getOrderNo());
                log.info("signRecord signRecord commission param:{}",JSONObject.toJSONString(teboWalletPlanDTO));
                remoteWalletService.startSplitPlan(teboWalletPlanDTO);
                /**
                 * 超服店解冻
                 */
                teboCommissionUtil.buildVipShopCommissionParam(customerVO.getOrderNo());
            }
            /**
             * 电池解冻
             */
            TeboWalletPlanDTO teboBatteryWalletPlanDTO = teboCommissionUtil.buildBatteryParam(dto);
            log.info("signRecord signRecord battery param:{}",JSONObject.toJSONString(teboBatteryWalletPlanDTO));
            TeboUnfreezeFundsRecordDO insertDO = new TeboUnfreezeFundsRecordDO();
            insertDO.setType(2);
            insertDO.setOrderNo(customerVO.getOrderNo());
            fundsRecordMapper.insert(insertDO);
        }else {
            /**
             * 团购电池解冻
             */
            Long groupCustomerOrderId = teboGiftPackOrderDO.getGroupCustomerOrderId();
            TeboGroupPurchaseCustomerDO teboGroupPurchaseCustomerDO = teboGroupPurchaseCustomerMapper.selectById(groupCustomerOrderId);
            if (ObjectUtil.isNotEmpty(teboGroupPurchaseCustomerDO)){
                TeboGroupPurchaseOrderDO purchaseOrderDO = teboGroupPurchaseOrderMapper.selectById(teboGroupPurchaseCustomerDO.getOrderId()) ;
                if (ObjectUtil.isNotEmpty(purchaseOrderDO)){
                    dto.setGroupPurchaseOrderNo(purchaseOrderDO.getOrderNo());
                    TeboWalletPlanDTO teboBatteryWalletPlanDTO = teboCommissionUtil.groupPurchaseOrderBuildBatteryParam(dto);
                    log.info("signRecord signRecord teboBatteryWalletPlanDTO param:{}",JSONObject.toJSONString(teboBatteryWalletPlanDTO));
                }
            }

        }

    }

    @Override
    public void signRecordNew(TeboCouponCloudShopDTO dto) {
        log.info("signRecordNew dto{}", dto);
        TeboCouponCustomerVO customerVO = getDetailByCouponCode(dto.getCouponCode());
        TeboCouponCustomerDO teboCouponCustomerDO = new TeboCouponCustomerDO();
        teboCouponCustomerDO.setId(customerVO.getId());
        teboCouponCustomerDO.setUpdateTime(LocalDateTime.now());
        teboCouponCustomerDO.setWriteOffTime(LocalDateTime.now());
        teboCouponCustomerMapper.updateById(teboCouponCustomerDO);
        if (ObjectUtils.isEmpty(customerVO)) {
            return;
        }
        /**
         * 更新订单状态为已核销
         */
        TeboGiftPackOrderDO teboGiftPackOrderDO = giftPackOrderMapper.selectByOrderNo(customerVO.getOrderNo());
        if (StringUtils.isNotEmpty(customerVO.getOrderNo())){
            if (ObjectUtil.isNotEmpty(teboGiftPackOrderDO)){
                teboGiftPackOrderDO.setVerificationStatus(1);
                teboGiftPackOrderDO.setBatteryVerificationStatus(1);
                teboGiftPackOrderDO.setBatteryVerificationTime(LocalDateTimeUtil.now());
                if (ObjectUtils.isEmpty(teboGiftPackOrderDO.getVerificationTime())){
                    teboGiftPackOrderDO.setVerificationTime(LocalDateTime.now());
                }
                R<TeboShop> shopR = remoteShopService.getShopInfo(dto.getShopId());
                if (ObjectUtil.isNotEmpty(shopR) && ObjectUtil.isNotEmpty(shopR.getData())){
                    TeboShop shop = shopR.getData();
                    teboGiftPackOrderDO.setVerificationShopId(Long.parseLong(shop.getId()));
                    teboGiftPackOrderDO.setVerificationTenantId(shop.getTenantId());
                }
                giftPackOrderMapper.updateById(teboGiftPackOrderDO);
            }

        }
        // 插入核销记录
        couponSignRecordMapper.insert(buildSignRecord(dto));
        log.info("signRecordNew end");
    }

    public TeboCouponSignRecordDO buildSignRecord(TeboCouponCloudShopDTO dto) {
        TeboCouponSignRecordDO signRecord = new TeboCouponSignRecordDO();
        signRecord.setId(SnowFlakeUtil.nextId());
        signRecord.setCouponCode(dto.getCouponCode());
        signRecord.setShopName(dto.getShopName());
        signRecord.setShopPhone(dto.getShopPhone());
        signRecord.setWriteOffTime(LocalDateTime.now());
        signRecord.setBatteryType(dto.getBatteryType());
        signRecord.setBatteryModel(dto.getBatteryModel());
        signRecord.setBatteryCode(dto.getBatteryCode());
        signRecord.setBatteryNum(dto.getBatteryNum());
        signRecord.setCreateTime(LocalDateTime.now());
        return signRecord;
    }

    /**
     * 解冻大米供应商&佣金
     */
    public Boolean unfreezeRiceFunds(String orderNo){
        TeboGiftPackOrderDO giftPackOrderDO = giftPackOrderMapper.selectByOrderNo(orderNo);
        if (ObjectUtil.isEmpty(giftPackOrderDO)){
            throw new ServiceException("订单不存在");
        }
        /**
         * 更新核销时间
         */
        if (giftPackOrderDO.getVerificationTime() == null){
            giftPackOrderDO.setVerificationTime(LocalDateTime.now());
        }
        giftPackOrderDO.setVerificationStatus(1);
        giftPackOrderDO.setRiceVerificationStatus(1);
        giftPackOrderDO.setRiceVerificationTime(LocalDateTime.now());
        giftPackOrderMapper.updateById(giftPackOrderDO);
        Date date = DateUtil.transferString2Date("2024-11-10");
        if (giftPackOrderDO.getCreateTime().isBefore(LocalDateTimeUtil.of(date))){
            log.info("this order is too early not participate in commission param :{}",JSONObject.toJSONString(giftPackOrderDO));
            return false;
        }
        TeboCouponCustomerQueryDTO queryDTO = new TeboCouponCustomerQueryDTO();
        queryDTO.setOrderNo(orderNo);
        List<TeboCouponCustomerDO> customerCouponList = teboCouponCustomerMapper.getCustomerCouponList(queryDTO);
        if (ObjectUtil.isEmpty(customerCouponList)){
            return false;
        }
        UnfreezeFundsQueryDTO queryParam = new UnfreezeFundsQueryDTO();
        queryParam.setOrderNo(orderNo);
        List<TeboUnfreezeFundsRecordDO> recordList = fundsRecordMapper.getByOrderNo(queryParam);

        /**
         * 解冻大米
         */
        if (giftPackOrderDO.getOrderType() == 1){
            TeboWalletPlanDTO teboRiceWalletPlanDTO = teboCommissionUtil.buildRiceParam(orderNo);
            log.info("unfreezeRiceFunds param:{}", JSONObject.toJSONString(teboRiceWalletPlanDTO));
            remoteWalletService.startSplitPlan(teboRiceWalletPlanDTO);
        } else if (giftPackOrderDO.getOrderType() == 2) {
            TeboGroupPurchaseOrderDO teboGroupPurchaseOrderDO = teboGroupPurchaseOrderMapper.selectById(giftPackOrderDO.getGroupOrderId());
            TeboWalletPlanDTO teboRiceWalletPlanDTO = teboCommissionUtil.buildGroupRiceParam(teboGroupPurchaseOrderDO.getOrderNo(),giftPackOrderDO.getUnionId(),giftPackOrderDO.getOrderNo());
            log.info("unfreezeRiceFunds groupOrder param:{}", JSONObject.toJSONString(teboRiceWalletPlanDTO));
            remoteWalletService.startSplitPlanForGroup(teboRiceWalletPlanDTO);
        }else if (giftPackOrderDO.getOrderType() == 3){
            TeboWalletPlanDTO teboRiceWalletPlanDTO = teboCommissionUtil.buildRiceParam(giftPackOrderDO.getCreateBy());
            log.info("unfreezeRiceFunds param:{}", JSONObject.toJSONString(teboRiceWalletPlanDTO));
            remoteWalletService.startSplitPlan(teboRiceWalletPlanDTO);
        }
        /**
         * 普通订单解冻佣金和超服店
         */
        if (giftPackOrderDO.getOrderType() == 1){
            if (CollectionUtils.isEmpty(recordList)){
                TeboWalletPlanDTO teboWalletPlanDTO = new TeboWalletPlanDTO();
                teboWalletPlanDTO.setOrderNo(orderNo);
                List<TeboWalletPlanDetailDTO> list = new ArrayList<>();
                TeboWalletPlanDTO commissionWallet = teboCommissionUtil.buildCommissionParam(orderNo);
                if (ObjectUtil.isNotEmpty(commissionWallet)){
                    list.addAll(commissionWallet.getDetails());
                }
                teboWalletPlanDTO.setDetails(list);
                if (!CollectionUtils.isEmpty(list)){
                    log.info("unfreezeRiceFunds splitPlan param :{}",JSONObject.toJSONString(teboWalletPlanDTO));
                    remoteWalletService.startSplitPlan(teboWalletPlanDTO);
                    /**
                     * 超服店分佣
                     */
                    teboCommissionUtil.buildVipShopCommissionParam(orderNo);
                }
            }
        }
        TeboUnfreezeFundsRecordDO insertDO = new TeboUnfreezeFundsRecordDO();
        insertDO.setType(1);
        insertDO.setOrderNo(orderNo);
        /**
         * 插入大米冻结记录
         */
        fundsRecordMapper.insert(insertDO);
        return true;
    }

   public List<TeboCouponSignRecordVO> signRecordList(String phoneNumber){
       List<TeboCouponSignRecordDO> list = couponSignRecordMapper.signRecordList(phoneNumber);
       if (CollectionUtils.isEmpty(list)){
           return null;
       }
       List<String> couponCodeList = list.stream().map(item ->item.getCouponCode()).collect(Collectors.toList());
       List<TeboCouponCustomerDO> couponCustomerList = teboCouponCustomerMapper.getCouponListByUniqueList(couponCodeList);
       Map<String,TeboCouponCustomerDO> couponCustomerMap = DataUtils.listToMap(couponCustomerList,TeboCouponCustomerDO::getUniqueCode);
       List<String> unionIdList = couponCustomerList.stream().map(item ->item.getUnionId()).collect(Collectors.toList());
       TeboCustomerQueryDTO queryDTO = new TeboCustomerQueryDTO();
       queryDTO.setUnionIdList(unionIdList);
       Map<String,TeboConsumer> teboCustomerMap = new HashMap<>();
       R<List<TeboConsumer>> customerRList = customerService.selectByUnionIdList(queryDTO);
       if (ObjectUtil.isNotEmpty(customerRList) && !CollectionUtils.isEmpty(customerRList.getData())){
           List<TeboConsumer> customerList = customerRList.getData();
           teboCustomerMap = DataUtils.listToMap(customerList, TeboConsumer::getUnionid);
       }
       List<TeboCouponSignRecordVO> result = BeanConvert.copyList(list,TeboCouponSignRecordVO::new);
       Map<String, TeboConsumer> finalTeboCustomerMap = teboCustomerMap;
       result.forEach(item ->{
           if (!CollectionUtils.isEmpty(couponCustomerMap)){
               TeboCouponCustomerDO teboCouponCustomerDO = couponCustomerMap.get(item.getCouponCode());
               if (ObjectUtil.isNotEmpty(teboCouponCustomerDO)){
                   String unionId = teboCouponCustomerDO.getUnionId();
                   TeboConsumer consumer = finalTeboCustomerMap.get(unionId);
                   if (ObjectUtil.isNotEmpty(consumer)){
                       item.setPhoneNumber(consumer.getPhoneNumber());
                   }
               }
           }
       });
       return result;
   }

    public TeboCouponCustomerVO getCustomerCycleCoupon(String unionId,String orderNo){
        TeboCouponCustomerDO teboCouponCustomerDO = teboCouponCustomerMapper.getCustomerCycleCoupon(unionId,orderNo);
        if (ObjectUtil.isEmpty(teboCouponCustomerDO)){
            return null;
        }
        TeboCouponCustomerVO teboCouponCustomerVO = new TeboCouponCustomerVO();
        BeanConvert.copy(teboCouponCustomerDO,teboCouponCustomerVO);
        return teboCouponCustomerVO;
    }

    public TeboGiftPackOrderVO getCustomerCouponByFlowId(Long flowId){
        String orderNo = teboCouponCustomerMapper.getOrderNoByFlowId(flowId);
        TeboGiftPackOrderDO teboGiftPackOrderDO = giftPackOrderMapper.selectByOrderNo(orderNo);
        if (ObjectUtil.isEmpty(teboGiftPackOrderDO)){
            return null;
        }
        TeboGiftPackOrderVO teboGiftPackVO = new TeboGiftPackOrderVO();
        BeanConvert.copy(teboGiftPackOrderDO,teboGiftPackVO);
        R<TeboConsumer> customerR = customerService.selectByUnionId(teboGiftPackVO.getUnionId());
        if (ObjectUtil.isNotEmpty(customerR) && ObjectUtil.isNotEmpty(customerR.getData()) ){
            teboGiftPackVO.setPhoneNumber(customerR.getData().getPhoneNumber());
        }
        List<TeboCouponCustomerDO> list = teboCouponCustomerMapper.getCustomerCouponByOrderNo(orderNo);
        if (!CollectionUtils.isEmpty(list)){
            List<TeboCouponCustomerVO> couponCustomerList = BeanConvert.copyList(list,TeboCouponCustomerVO::new);
            teboGiftPackVO.setCouponsList(couponCustomerList);
        }
        return teboGiftPackVO;
    }

    @Override
    public Boolean checkBatteryCode(String batteryCode) {
        LambdaQueryWrapper<TeboCouponSignRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeboCouponSignRecordDO::getBatteryCode, batteryCode);
        return couponSignRecordMapper.selectCount(queryWrapper) > 0;
    }

}
