package com.tebo.rescue.applet.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author：zhengmk
 * @Date 2023/12/27 14:38
 */
@Data
public class GiftPackOrderQueryDTO implements Serializable {

    private String unionid;

    // 订单状态 0:待支付，1：已支付
    private Integer orderStatus;

    /**
     * 礼包id
     */
    private Long packId;

    // 时间
    private LocalDateTime maxTime;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 类型
     */
    private List<Integer> typeList;

    /**
     * 渠道
     */
    private Integer channel;

    /**
     * 推荐人手机号
     */
    private List<String> referencePhoneList;

    /**
     * 订单类型 1:普通订单 2:团购订单 3:合并下单
     */
    private Integer orderType;
}
