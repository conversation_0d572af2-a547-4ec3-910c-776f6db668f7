package com.tebo.rescue.mapper;

import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.rescue.entity.TeboReissueCycleOrderDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 需要补发的骑行订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Mapper
public interface TeboReissueCycleOrderMapper extends TeboBaseMapper<TeboReissueCycleOrderDO> {

    List<String> getReissueCouponOrderNo();
}
