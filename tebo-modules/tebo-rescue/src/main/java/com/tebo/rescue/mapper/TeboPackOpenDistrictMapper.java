package com.tebo.rescue.mapper;

import com.tebo.rescue.entity.TeboPackOpenDistrictDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 会员卡开通区域 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-07
 */
@Mapper
public interface TeboPackOpenDistrictMapper extends TeboBaseMapper<TeboPackOpenDistrictDO> {
    List<String> getAllDistrictList();
}
