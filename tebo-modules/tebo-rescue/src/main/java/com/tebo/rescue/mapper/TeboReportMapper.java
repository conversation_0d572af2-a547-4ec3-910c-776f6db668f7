package com.tebo.rescue.mapper;

import com.tebo.rescue.api.domain.view.TeboIndexReportRescueVO;
import com.tebo.rescue.applet.domain.view.report.TeboReportPartnerVO;
import com.tebo.system.api.model.SystemPartnerReportVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 公众号用户绑定记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-27
 */
@Mapper
public interface TeboReportMapper {

    TeboReportPartnerVO selectPartnerReport(@Param("tenantId") Long tenantId);

    TeboReportPartnerVO selectPartnerBatteryReport(@Param("tenantId") Long tenantId);

    TeboReportPartnerVO selectPartnerShopReport(@Param("shopId") Long shopId);

    TeboReportPartnerVO selectPartnerShopBatteryReport(@Param("shopId") Long shopId);

    TeboIndexReportRescueVO getIndexRescueReport();

    TeboIndexReportRescueVO getIndexSubscribeReport();


    Map getReferenceInfo(@Param("phoneNumber") String phoneNumber);

    List<Map> getPersonInfo(@Param("phoneNumber") String phoneNumber);

    List<Map> getReferencePersonInfo(@Param("phoneNumber") String phoneNumber);
}
