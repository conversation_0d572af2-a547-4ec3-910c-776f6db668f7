package com.tebo.rescue.mapper;

import com.tebo.rescue.applet.domain.dto.ServiceOrderQueryDTO;
import com.tebo.rescue.entity.TeboQueueOrderDO;
import com.tebo.rescue.entity.TeboServiceOrderDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.rescue.remote.domain.LstOrderDetailInfoVO;
import com.tebo.rescue.remote.domain.LstOrderDetailVO;
import com.tebo.rescue.remote.domain.LstOrderQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 师傅端服务订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Mapper
public interface TeboServiceOrderMapper extends TeboBaseMapper<TeboServiceOrderDO> {

    /**
     * 服务订单列表
     */
    List<TeboServiceOrderDO> list(ServiceOrderQueryDTO queryDTO);
    Integer count(ServiceOrderQueryDTO queryDTO);

    TeboServiceOrderDO selectByQueueOrderId(@Param("queueOrderId") Long queueOrderId);

    TeboServiceOrderDO selectByOrderNo(@Param("orderNo") String orderNo);

    List<LstOrderDetailVO> getLstOrderList(LstOrderQueryDTO queryDTO);

    LstOrderDetailInfoVO selectLstOrderDetailInfo(@Param("orderId")Long orderId);
}
