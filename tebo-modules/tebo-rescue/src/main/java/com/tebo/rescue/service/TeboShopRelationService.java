package com.tebo.rescue.service;

import com.tebo.rescue.applet.domain.dto.TeboCustomerShopQueryDTO;
import com.tebo.rescue.applet.domain.view.TeboShopRelationDetailVO;
import com.tebo.rescue.applet.domain.view.TeboShopRelationVO;

import java.util.List;

public interface TeboShopRelationService {

    /**
     * 可分配门店
     * @param query
     * @return
     */
    List<TeboShopRelationVO> getList(TeboCustomerShopQueryDTO query);

    /**
     * 订单激活明细
     * @param query
     * @return
     */
    List getDetailList(TeboCustomerShopQueryDTO query);

    /**
     * 团购订单分配
     * @param idList
     * @param orderId
     */
    void splitList(List<Long> idList, Long orderId);

    /**
     * 团购订单分配
     * @param id
     * @param orderId
     * @param number
     */
    void splitSingle(Long id, Long orderId, Integer number);

    /**
     * 取消分配
     * @param id
     * @param orderId
     */
    void cancelSplitSingle(Long id, Long orderId);

    /**
     * 订单统计数据
     * @param orderId
     * @return
     */
    TeboShopRelationDetailVO orderInfo(Long orderId);
}
