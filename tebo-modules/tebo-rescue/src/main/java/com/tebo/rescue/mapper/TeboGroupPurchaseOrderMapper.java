package com.tebo.rescue.mapper;

import com.tebo.rescue.entity.TeboGroupOrderSettleDO;
import com.tebo.rescue.entity.TeboGroupPurchaseOrderDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.rescue.web.domain.dto.groupOrder.TeboGroupOrderQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 团购订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Mapper
public interface TeboGroupPurchaseOrderMapper extends TeboBaseMapper<TeboGroupPurchaseOrderDO> {
    /**
     * 订单列表
     */
    List<TeboGroupPurchaseOrderDO> getGroupOrderList(TeboGroupOrderQueryDTO teboGroupOrderQueryDTO);

    /**
     *根据订单号查询订单信息
     */
    TeboGroupPurchaseOrderDO selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 查找超时订单
     */
    List<TeboGroupPurchaseOrderDO> getPayTimeOutOrder(@Param("time") LocalDateTime time);

    List<TeboGroupPurchaseOrderDO> selectOrderForSettle(@Param("type") Integer type);

    Integer selectCountByOrderId(@Param("orderId") Long orderId);

    Integer selectOrderAmountByOrderNo(@Param("orderNo")String orderNo);

    Integer insertConfigLog(TeboGroupOrderSettleDO item);

}
