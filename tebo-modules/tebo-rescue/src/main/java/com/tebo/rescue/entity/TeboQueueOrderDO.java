package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * C端服务订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-13
 */
@Getter
@Setter
@TableName("tebo_queue_order")
public class TeboQueueOrderDO extends Model<TeboQueueOrderDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * unionid
     */
    @TableField("unionid")
    private String unionid;

    /**
     * 用户名称
     */
    @TableField("nick_name")
    private String nickName;

    /**
     * 用户手机号
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 门店id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 门店名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 合伙人id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 维修师傅id
     */
    @TableField("account_id")
    private Long accountId;

    /**
     * 维修师傅名称
     */
    @TableField("account_name")
    private String accountName;

    /**
     * 工位名称
     */
    @TableField("station_name")
    private String stationName;

    /**
     * 订单类型0:维修 1:安装 2:保养 3:洗车
     */
    @TableField("order_type")
    private Integer orderType;

    /**
     * 取号类型 0：现场取号 1：预约取号
     */
    @TableField("pickup_type")
    private Integer pickupType;

    /**
     * 排队号，如1
     */
    @TableField("queue_number")
    private Integer queueNumber;

    /**
     * 排队号 如X0001
     */
    @TableField("queue_number_str")
    private String queueNumberStr;

    /**
     * 订单状态 0:未叫号 1:处理中 2:待付款 3:已完成 4:已取消
     */
    @TableField("order_status")
    private Integer orderStatus;

    /**
     * 处理结果
     */
    @TableField("handle_result")
    private String handleResult;

    /**
     * 总体评价0-10
     */
    @TableField("total_comment")
    private Integer totalComment;

    /**
     * 服务评价0-10
     */
    @TableField("service_comment")
    private Integer serviceComment;

    /**
     * 专业形象评价0-10
     */
    @TableField("image_comment")
    private Integer imageComment;

    /**
     * 专业能力评价0-10
     */
    @TableField("competence_comment")
    private Integer competenceComment;

    /**
     * 态度评价0-10
     */
    @TableField("attitude_comment")
    private Integer attitudeComment;

    /**
     * 是否评价完成0-未完成 1-已完成
     */
    @TableField("comment_finished")
    private Integer commentFinished;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 订单金额,单位分
     */
    @TableField("order_amount")
    private Integer orderAmount;

    /**
     * 实际支付金额,单位分
     */
    @TableField("actual_pay_amount")
    private Integer actualPayAmount;

    /**
     * 支付时间
     */
    @TableField(value = "pay_time")
    private LocalDateTime payTime;

    /**
     * 叫号时间
     */
    @TableField(value = "call_time")
    private LocalDateTime callTime;

    /**
     * 取消时间
     */
    @TableField(value = "cancel_time")
    private LocalDateTime cancelTime;

    /**
     * 评价时间
     */
    @TableField(value = "comment_time")
    private LocalDateTime commentTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField(value = "del_flag",fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建者
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
