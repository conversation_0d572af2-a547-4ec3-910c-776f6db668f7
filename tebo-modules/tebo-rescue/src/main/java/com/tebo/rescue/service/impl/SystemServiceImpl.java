package com.tebo.rescue.service.impl;

import cn.hutool.core.lang.Assert;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.time.DateUtil;
import com.tebo.rescue.applet.domain.dto.ProductCheckDTO;
import com.tebo.rescue.applet.domain.view.CheckBatteryRemoteVO;
import com.tebo.rescue.api.domain.view.CheckBatteryVO;
import com.tebo.rescue.applet.domain.view.ProductCheckVO;
import com.tebo.rescue.manager.ProdManger;
import com.tebo.rescue.service.ISystemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Date;


@Service
@Slf4j
public class SystemServiceImpl implements ISystemService {

    @Autowired
    private ProdManger prodManger;

    @Override
    public ProductCheckVO checkProduct(ProductCheckDTO checkDTO) {
        Assert.notNull(checkDTO.getProductCode(), "产品码不能为空");
        CheckBatteryRemoteVO checkBatteryRemoteVO = prodManger.checkBattery(checkDTO.getProductCode());
        ProductCheckVO checkVO = new ProductCheckVO();
        BeanConvert.copy(checkBatteryRemoteVO, checkVO);
        checkVO.setHasCode(true);
        checkVO.setIsVerifyCorrect(StringUtils.equals(checkBatteryRemoteVO.getCode().substring(15,16), checkBatteryRemoteVO.getYanzhengcode()));
        return checkVO;
    }
    @Override
    public CheckBatteryVO checkTianNengBattery(ProductCheckDTO checkDTO){
        Assert.notNull(checkDTO.getProductCode(), "产品码不能为空");
        CheckBatteryVO checkBatteryVO = prodManger.checkTianNengBattery(checkDTO.getProductCode());
        return checkBatteryVO;
    }
    @Override
    public Boolean byInsurance(ProductCheckDTO checkDTO){
        Assert.notNull(checkDTO.getProductCode(), "产品码不能为空");
        CheckBatteryRemoteVO productCheckVO = prodManger.checkBattery(checkDTO.getProductCode());
        String[] wuLiaoArray = productCheckVO.getWuliao().split("/");
        String batterySeries = wuLiaoArray[1];
        String[] styleArray = productCheckVO.getStyle().split("V");
        /**
         * 电压
         */
        Integer voltage = Integer.parseInt(styleArray[0]);
        /**
         * 电流
         */
        Double current = Double.parseDouble(styleArray[1].replace("AH",""));
        String style = voltage.toString()+current.intValue();
        String[] styleList = {"4812","4820","6020","7220"};
        String[] batterySeriesList = {"A+","泰博金刚","汇源动力王","汇源聚能王"};
        if (!Arrays.asList(styleList).contains(style)){
          return  false;
        }
        if (!Arrays.asList(batterySeriesList).contains(batterySeries)){
            return  false;
        }
        /**
         * 系统校验电池三包日期距离当前时间不允许超过两个月,超过2个月的无法识别,并做出提示“该电池日期已超出可购买保险范围,请选择2个月内电池上传
         */
        Date prodDate = DateUtil.transferString2Date(productCheckVO.getProdDate());
        LocalDate prodLocalDate = DateUtil.dateToLocalDateTime(prodDate).toLocalDate();
        LocalDate currentDate = LocalDate.now();
        if (prodLocalDate.isBefore(currentDate)) {
            throw new ServiceException("该电池日期已超出可购买保险范围,请选择2个月内电池上传");
        }else {
            long monthsBetween = ChronoUnit.MONTHS.between(prodLocalDate,currentDate);
            if (monthsBetween >= 2){
            throw new ServiceException("该电池日期已超出可购买保险范围,请选择2个月内电池上传");
            }
        }
        return true;
    }

}
