package com.tebo.rescue.applet.controller;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.domain.AjaxResult;
import com.tebo.common.util.applet.AppletUtil;
import com.tebo.rescue.api.domain.dto.TeboGiftPackOrderDTO;
import com.tebo.rescue.api.domain.dto.TeboRemoteGiftOrderDTO;
import com.tebo.rescue.applet.domain.dto.GiftPackOrderAfterPayDTO;
import com.tebo.rescue.applet.domain.dto.GiftPackOrderDTO;
import com.tebo.rescue.service.IGiftPackOrderService;
import com.tebo.rescue.web.domain.view.TeboGiftPackOrderVO;
import com.tebo.system.api.domain.view.WechatPrepayResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @Author：zhengmk
 * @Date 2023/12/24 9:21
 */
@RestController
@RequestMapping("/giftPackOrder")
@Slf4j
public class GiftPackOrderController extends BaseController {

    @Resource
    private IGiftPackOrderService giftPackOrderService;
    @Resource
    private HttpServletRequest request;

    /**
     * 创建礼包订单
     */
    @PostMapping("/create")
    public R<Long> create(@RequestBody GiftPackOrderDTO packOrderDTO) {
        return R.ok(giftPackOrderService.createGiftOrder(packOrderDTO));
    }

    /**
     * 创建合并订单
     */
    @PostMapping("/createMergeOrder")
    public R<Long> createMergeOrder(@RequestBody TeboRemoteGiftOrderDTO orderDTO) {
        return R.ok(giftPackOrderService.createMergeOrder(orderDTO));
    }

    /**
     * 判断是否是C会员
     * @return
     */
    @GetMapping("/checkIsMember")
    public AjaxResult checkIsMember() {
        String unionId = AppletUtil.getUnionIdByRequest(request);
        return success(giftPackOrderService.getGiftPackOrderList(unionId));
    }

    /**
     * 判断是否是vip,是:发送积分
     */
    @GetMapping("/isVip")
    public AjaxResult isVip() {
        String unionId = AppletUtil.getUnionIdByRequest(request);
        return success(giftPackOrderService.isVip(unionId));
    }

    /**
     * 某个用户会员礼包
     * @return
     */
    @GetMapping("/getGiftPackOrderList")
    public AjaxResult getGiftPackOrderList() {
        String unionId = AppletUtil.getUnionIdByRequest(request);
        return success(giftPackOrderService.getList(unionId));
    }


    /**
     * 用户礼包订单退款
     * @return
     */
    @GetMapping("/refundGiftPack")
    public AjaxResult refundGiftPack(@RequestParam String orderNo) {
//        throw new ServiceException("功能开发中...2024年12月15日前开通，敬请谅解！");
        String unionId = AppletUtil.getUnionIdByRequest(request);
        return success(giftPackOrderService.refundGiftPack(orderNo,unionId));
    }

    /**
     * 更新订单核销状态和核销时间
     */
    @GetMapping("/updateOrderStatus/{orderNo}")
    public R updateOrderStatus(@PathVariable("orderNo") String orderNo) {
        TeboGiftPackOrderDTO giftPackOrderDTO = new TeboGiftPackOrderDTO();
        giftPackOrderDTO.setOrderNo(orderNo);
        giftPackOrderDTO.setCycleVerificationStatus(1);
        giftPackOrderDTO.setCycleVerificationTime(LocalDateTimeUtil.now());
        giftPackOrderService.updateOrderStatus(giftPackOrderDTO);
        return R.ok();
    }

    /**
     * 更新久久券订单状态为已退款
     * @param giftPackOrderNo 久久券订单号
     * @return
     */
    @PostMapping("/updateOrderStatusToRefunded/{giftPackOrderNo}")
    public R<Boolean> updateOrderStatusToRefunded(@PathVariable("giftPackOrderNo") String giftPackOrderNo){
        log.info("更新久久券订单状态为已退款，订单号：{}", giftPackOrderNo);
        return R.ok(giftPackOrderService.updateOrderStatusToRefunded(giftPackOrderNo));
    }

}
