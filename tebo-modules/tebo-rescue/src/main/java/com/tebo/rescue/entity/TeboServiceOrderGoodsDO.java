package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 师傅端服务订单商品表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Getter
@Setter
@TableName("tebo_service_order_goods")
public class TeboServiceOrderGoodsDO extends Model<TeboServiceOrderGoodsDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 维修师傅id
     */
    @TableField("account_id")
    private Long accountId;

    /**
     * 门店id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 订单号
     */
    @TableField("service_order_id")
    private Long serviceOrderId;

    /**
     * 是否手动添加 0:否 1:是
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 商品id，当是选择配件时，有id
     */
    @TableField("goods_id")
    private Long goodsId;

    /**
     * 商品名称
     */
    @TableField("goods_name")
    private String goodsName;

    /**
     * 品牌名称
     */
    @TableField("brand_name")
    private String brandName;

    /**
     * 商品sku
     */
    @TableField("sku")
    private String sku;

    /**
     * 商品价格,单位分
     */
    @TableField("goods_price")
    private Integer goodsPrice;

    /**
     * 商品数量
     */
    @TableField("goods_num")
    private Integer goodsNum;

    /**
     * 该商品总金额
     */
    @TableField("goods_amount")
    private Integer goodsAmount;

    /**
     * 商品颜色
     */
    @TableField("color")
    private String color;

    /**
     * 以旧换新价
     */
    @TableField("trade_in_price")
    private Integer tradeInPrice;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField(value = "del_flag",fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建者
     */
    @TableField(value = "create_by",fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by",fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
