package com.tebo.rescue.mapper;

import com.tebo.rescue.applet.domain.dto.RescueQueueOrderQueryDTO;
import com.tebo.rescue.applet.domain.view.MaintenanceQueueOrderNumberVO;
import com.tebo.rescue.entity.TeboRescueQueueOrderDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 救援上门服务订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@Mapper
public interface TeboRescueQueueOrderMapper extends TeboBaseMapper<TeboRescueQueueOrderDO> {
    List<TeboRescueQueueOrderDO> listBySO(RescueQueueOrderQueryDTO queueOrderQueryDTO);

    List<TeboRescueQueueOrderDO> listBySODesc(RescueQueueOrderQueryDTO queueOrderQueryDTO);

    List<MaintenanceQueueOrderNumberVO> getMaintenanceServiceOrderCount(Long shopId);
}
