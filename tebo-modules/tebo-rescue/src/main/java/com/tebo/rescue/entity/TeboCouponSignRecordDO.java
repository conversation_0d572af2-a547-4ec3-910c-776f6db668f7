package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 电池核销记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Getter
@Setter
@TableName("tebo_coupon_sign_record")
public class TeboCouponSignRecordDO extends Model<TeboCouponSignRecordDO> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 卡券唯一码
     */
    @TableField("coupon_code")
    private String couponCode;

    /**
     * 门店名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 门店手机号
     */
    @TableField("shop_phone")
    private String shopPhone;

    /**
     * 核销时间
     */
    @TableField("write_off_time")
    private LocalDateTime writeOffTime;

    /**
     * 电池型号
     */
    @TableField("battery_type")
    private String batteryType;

    /**
     * 电池系列
     */
    @TableField("battery_model")
    private String batteryModel;

    /**
     * 电池编码
     */
    @TableField("battery_code")
    private String batteryCode;

    /**
     * 电池数量
     */
    @TableField("battery_num")
    private Integer batteryNum;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
