package com.tebo.rescue.mapper;

import com.tebo.rescue.entity.TeboUnfreezeFundsRecordDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.rescue.api.domain.dto.UnfreezeFundsQueryDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 解冻资金记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-14
 */
@Mapper
public interface TeboUnfreezeFundsRecordMapper extends TeboBaseMapper<TeboUnfreezeFundsRecordDO> {

   public List<TeboUnfreezeFundsRecordDO> getByOrderNo(UnfreezeFundsQueryDTO unfreezeFundsQueryDTO);

}
