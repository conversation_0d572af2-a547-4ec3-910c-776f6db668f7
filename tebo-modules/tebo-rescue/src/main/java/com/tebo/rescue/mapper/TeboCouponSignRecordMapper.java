package com.tebo.rescue.mapper;

import com.tebo.rescue.entity.TeboCouponSignRecordDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 电池核销记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Mapper
public interface TeboCouponSignRecordMapper extends TeboBaseMapper<TeboCouponSignRecordDO> {
    List<TeboCouponSignRecordDO> signRecordList(String phoneNumber);

}
