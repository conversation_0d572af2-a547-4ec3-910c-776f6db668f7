package com.tebo.rescue.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.rescue.applet.domain.dto.TeboCustomerShopQueryDTO;
import com.tebo.rescue.applet.domain.view.TeboShopRelationDetailVO;
import com.tebo.rescue.applet.domain.view.TeboShopRelationVO;
import com.tebo.rescue.entity.TeboGroupPurchaseCustomerDO;
import com.tebo.rescue.entity.TeboGroupPurchaseOrderDO;
import com.tebo.rescue.entity.TeboShopRelationDO;
import com.tebo.rescue.mapper.*;
import com.tebo.rescue.service.TeboShopRelationService;
import com.tebo.rescue.util.DistributedLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TeboShopRelationServiceImpl implements TeboShopRelationService {

    @Resource
    private TeboShopRelationMapper teboShopRelationMapper;

    @Resource
    private TeboGroupPurchaseOrderMapper orderMapper;
    @Resource
    private TeboGroupPurchaseCustomerMapper customerMapper;
    @Resource
    private DistributedLock lock;


    @Override
    public List<TeboShopRelationVO>  getList(TeboCustomerShopQueryDTO query) {
        List<TeboShopRelationVO> res;
        if (query.getStatus() == 1) {
            // 未分配的，商下的全量门店
            res = teboShopRelationMapper.getList(query);
        }else {
            // 已分配的手机号
            res = teboShopRelationMapper.getListNew(query);
            // 未匹配到门店优化
            if (res.stream().allMatch(Objects::isNull)) {
                return Collections.emptyList();
            }
        }
        if (CollectionUtils.isEmpty(res)) {
            return Collections.emptyList();
        }
        if (query.getStatus() == 1) {
            res.stream().forEach(item -> {
                item.setUpdateTime(null);
            });
        }
        if (query.getStatus() == 2) {
            res.stream().forEach(item -> {
                if (!ObjectUtils.isEmpty(item.getApplyTime())) {
                    item.setUpdateTime(item.getApplyTime());
                }
            });
        }
        return res;
    }

    @Override
    public List getDetailList(TeboCustomerShopQueryDTO query) {
        return teboShopRelationMapper.selectDetailByOrderId(query.getOrderId());
    }

    @Transactional
    @Override
    public void splitList(List<Long> idList, Long orderId) {
        // 弃用 ，改用 splitSingle，避免老版本超分配，注释代码
//        TeboGroupPurchaseOrderDO order = orderMapper.selectById(orderId);
//        if (ObjectUtils.isEmpty(order)) {
//            throw new ServiceException("订单不存在");
//        }
//        if (order.getOrderStatus() < 2) {
//            throw new ServiceException("请先支付订单再进行分配");
//        }
//        // 1 钜惠 2安心
//        Integer type = order.getType();
//        LambdaQueryWrapper<TeboShopRelationDO> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.in(TeboShopRelationDO::getId, idList);
//        if (type == 1) {
//            queryWrapper.eq(TeboShopRelationDO::getLowStatus, 1);
//        }
//        if (type == 2) {
//            queryWrapper.eq(TeboShopRelationDO::getSeniorStatus, 1);
//        }
//        // 筛选输入门店中未分配的门店
//        List<TeboShopRelationDO> shopList = teboShopRelationMapper.selectList(queryWrapper);
//        if (CollectionUtils.isEmpty(shopList)) {
//            return;
//        }
//        Map<String, Object> param = new HashMap<>();
//        param.put("orderId", orderId);
//        List<String> numberList = shopList.stream().map(item -> item.getCloudShopPhone()).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(numberList)) {
//            throw new ServiceException("门店未维护手机号，无法分配");
//        }
//        param.put("numberList", numberList);
//        // 分配步骤 1. 更新门店为已分配 2. 向分配记录表插入数据
//        // 1. 向分配记录表插入数据
//        teboShopRelationMapper.insertBatch(param);
//        shopList.forEach(shopRelationDO -> {
//            if (type == 1) {
//                shopRelationDO.setLowStatus(2);
//                shopRelationDO.setLowId(orderId);
//            }
//            if (type == 2) {
//                shopRelationDO.setSeniorStatus(2);
//                shopRelationDO.setSeniorId(orderId);
//            }
//            // todo 待优化
//            shopRelationDO.setApplyTime(LocalDateTime.now());
//            // 更新门店记录
//            teboShopRelationMapper.updateById(shopRelationDO);
//        });
    }

    @Transactional
    @Override
    public void splitSingle(Long id, Long orderId, Integer number) {
        if (!lock.tryLock(orderId + ":" + id, 5, 5)) {
            throw new ServiceException("请勿重复操作！");
        }
        TeboGroupPurchaseOrderDO order = orderMapper.selectById(orderId);
        if (ObjectUtils.isEmpty(order)) {
            throw new ServiceException("订单不存在");
        }
        if (order.getOrderStatus() < 2) {
            throw new ServiceException("请先支付订单再进行分配");
        }
//        // 1 钜惠 2安心
//        Integer type = order.getType();
        LambdaQueryWrapper<TeboShopRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeboShopRelationDO::getId, id);
        TeboShopRelationDO shopRelationDO = teboShopRelationMapper.selectById(id);
        if (ObjectUtils.isEmpty(shopRelationDO)) {
            throw new ServiceException("门店未找到");
        }
        if (ObjectUtils.isEmpty(shopRelationDO.getCloudShopPhone())) {
            throw new ServiceException("门店未维护手机号，无法分配");
        }
        // 1. 单门店不超过 5张
        LambdaQueryWrapper<TeboGroupPurchaseCustomerDO> customerQueryWrapper = new LambdaQueryWrapper<>();
        customerQueryWrapper.eq(TeboGroupPurchaseCustomerDO::getPhoneNumber, shopRelationDO.getCloudShopPhone());
        customerQueryWrapper.eq(TeboGroupPurchaseCustomerDO::getDelFlag, 0);
        Long count = customerMapper.selectCount(customerQueryWrapper);
        // 如果5张已全部分配
        if (count >= 5) {
            throw new ServiceException("当前门店剩余可分配额度为 0 张，您已超出分配上限，请重新调整分配");
        }
        // 如果分配数小于5张，计算还剩几张可发
        if (count + number > 5) {
            throw new ServiceException("当前门店剩余可分配额度为 " + (5 - count) + " 张，您已超出分配上限，请重新调整分配");
        }
        // 2. 订单总数量不超限
        customerQueryWrapper = new LambdaQueryWrapper<>();
        customerQueryWrapper.eq(TeboGroupPurchaseCustomerDO::getOrderId, orderId);
        customerQueryWrapper.eq(TeboGroupPurchaseCustomerDO::getDelFlag, 0);
        Long orderCount = customerMapper.selectCount(customerQueryWrapper);
        if (order.getTotalGoodsNumber() < number + orderCount) {
            throw new ServiceException("该笔订单为可分配额度为 " + order.getTotalGoodsNumber() + " 张，目前已分配 " + orderCount + " 张，无法再分配" + number + "张");
        }
        if (order.getTotalGoodsNumber() == number + orderCount){
            order.setOrderStatus(3);
            orderMapper.updateById(order);
        }
        Map<String, Object> param = new HashMap<>();
        param.put("orderId", orderId);
        List<TeboShopRelationDO> shopList = new ArrayList<>();
        for (int i = 0; i < number; i++) {
            shopList.add(shopRelationDO);
        }
        param.put("shopList", shopList);
        // 分配步骤 1. 更新门店为已分配 2. 向分配记录表插入数据
        // 1. 向分配记录表插入数据
        teboShopRelationMapper.insertBatch(param);
        shopRelationDO.setApplyTime(LocalDateTime.now());
        // 更新门店记录
        teboShopRelationMapper.updateById(shopRelationDO);

    }

    @Override
    @Transactional
    public void cancelSplitSingle(Long id, Long orderId) {
        if (!lock.tryLock("cancelSplitSingle:" + orderId + ":" + id, 5, 5)) {
            throw new ServiceException("请勿重复操作！");
        }
        TeboGroupPurchaseOrderDO order = orderMapper.selectById(orderId);
        if (ObjectUtils.isEmpty(order)) {
            throw new ServiceException("订单不存在");
        }
        /**
         * 订单变成待分配
         */
        order.setOrderStatus(2);
        orderMapper.updateById(order);
        TeboGroupPurchaseCustomerDO customerDO = customerMapper.selectById(id);
        if (ObjectUtils.isEmpty(customerDO) || customerDO.getDelFlag() != 0) {
            throw new ServiceException("分配信息未找到");
        }
//        LambdaQueryWrapper<TeboGroupPurchaseCustomerDO> customerQueryWrapper = new LambdaQueryWrapper<>();
//        customerQueryWrapper.eq(TeboGroupPurchaseCustomerDO::getPhoneNumber, customerDO.getCloudShopPhone());
//        customerQueryWrapper.eq(TeboGroupPurchaseCustomerDO::getOrderId, orderId);
//        customerQueryWrapper.eq(TeboGroupPurchaseCustomerDO::getStatus, 0);
//        customerQueryWrapper.eq(TeboGroupPurchaseCustomerDO::getDelFlag, 0);
//        if (customerMapper.selectCount(customerQueryWrapper) == 0) {
//            throw new ServiceException("劵已被领取，不支持撤回!");
//        }
        if (customerDO.getStatus() != 0) {
            throw new ServiceException("劵已被领取，不支持撤回!");
        }
//        List<TeboGroupPurchaseCustomerDO> list = customerMapper.selectList(customerQueryWrapper);
//        TeboGroupPurchaseCustomerDO customer = list.get(0);
        customerDO.setDelFlag(1);
        customerDO.setUpdateTime(LocalDateTime.now());
        customerMapper.updateById(customerDO);
    }

    @Override
    public TeboShopRelationDetailVO orderInfo(Long orderId) {
        TeboGroupPurchaseOrderDO order = orderMapper.selectById(orderId);
        if (ObjectUtils.isEmpty(order)) {
            throw new RuntimeException("订单不存在");
        }
        TeboShopRelationDetailVO res = teboShopRelationMapper.orderInfo(orderId);
        res.setTotal(order.getTotalGoodsNumber());
        res.setSurplus(order.getTotalGoodsNumber() - res.getUse());
        return res;
    }

}
