package com.tebo.rescue.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024/1/9 11:09
 * @Desc :
 */

@Configuration
public class RedissonConfiguration {

    @Value("${spring.redis.host}")
    private String address;

    @Value("${spring.redis.port}")
    private String port;

    // @Value("${spring.redis.username}")
    // private String username;

    @Value("${spring.redis.password}")
    private String password;

    // @Value("${spring.redis.database}")
    // private Integer database;


    @Bean
    public RedissonClient redisson() {
        Config config = new Config();
        config.useSingleServer()
                .setAddress(address())
                // .setUsername(username)
                .setPassword(password);
                // .setDatabase(database);
        return Redisson.create(config);
    }

    /**
     * 生成address
     *
     * @return
     */
    private String address() {
        return "redis://" + address + ":" + port;
    }
}
