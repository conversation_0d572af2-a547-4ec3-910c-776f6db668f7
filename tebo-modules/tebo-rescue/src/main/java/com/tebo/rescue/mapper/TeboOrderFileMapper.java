package com.tebo.rescue.mapper;

import com.tebo.rescue.entity.TeboCouponCustomerDO;
import com.tebo.rescue.entity.TeboOrderFileDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 订单图片关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-08
 */
@Mapper
public interface TeboOrderFileMapper extends TeboBaseMapper<TeboOrderFileDO> {

    int insertBatch(List<TeboOrderFileDO> list);

}
