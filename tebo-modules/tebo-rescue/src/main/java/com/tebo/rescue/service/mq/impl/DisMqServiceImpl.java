package com.tebo.rescue.service.mq.impl;

import com.lmax.disruptor.RingBuffer;
import com.tebo.common.util.constant.TeboDisMqConstant;
import com.tebo.rescue.mq.disruptor.domain.DisMsgData;
import com.tebo.rescue.service.mq.DisMqService;
import com.tebo.system.api.domain.dto.RemoteConsumerRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> ZhangFan
 * @date : 2024/1/15 13:53
 * @Desc :  Dis 消息接口封装-统一发送入口
 */
@Slf4j
@Service
public class DisMqServiceImpl implements DisMqService {


    @Resource
    private RingBuffer<DisMsgData> ringBuffer;

    @Override
    public Boolean grantPackCoupon(Long packId, String unionId,String orderNo) {
        Boolean sendResult = Boolean.TRUE;
        log.info("grantPackCoupon packId:{}unionId:{}", packId, unionId);
        // 获取下一个Event槽的下标
        long sequence = ringBuffer.next();
        try {
            // 给Event填充数据
            DisMsgData event = ringBuffer.get(sequence);
            // 封装参数
            Map<String, Object> paramMap = new HashMap<String, Object>(){{
                put("packId", packId);
                put("unionId", unionId);
                put("orderNo", orderNo);
            }};
            event.init(TeboDisMqConstant.DIS_MQ_TOPIC_RESCUE, TeboDisMqConstant.COUPON_GRANT_PACK_COUPON, paramMap);
            log.info("往消息队列中添加消息：{}", event);
        } catch (Exception e) {
            sendResult = Boolean.FALSE;
            log.error("failed to add event to messageModelRingBuffer for : e = {},{}", e, e.getMessage());
        } finally {
            // 发布Event，激活观察者去消费，将sequence传递给改消费者
            // 注意最后的publish方法必须放在finally中以确保必须得到调用；如果某个请求的sequence未被提交将会堵塞后续的发布操作或者其他的producer
            ringBuffer.publish(sequence);
            return sendResult;
        }
    }

    @Override
    public Boolean grantSingleCoupon(Long couponId) {
        Boolean sendResult = Boolean.TRUE;
        log.info("grantSingleCoupon couponId:{}", couponId);
        // 获取下一个Event槽的下标
        long sequence = ringBuffer.next();
        try {
            // 给Event填充数据
            DisMsgData event = ringBuffer.get(sequence);
            // 封装参数
            Map<String, Object> paramMap = new HashMap<String, Object>(){{
                put("couponId", couponId);
            }};
            event.init(TeboDisMqConstant.DIS_MQ_TOPIC_RESCUE, TeboDisMqConstant.COUPON_GRANT_SINGLE_COUPON, paramMap);
            log.info("往消息队列中添加消息：{}", event);
        } catch (Exception e) {
            sendResult = Boolean.FALSE;
            log.error("failed to add event to messageModelRingBuffer for : e = {},{}", e, e.getMessage());
        } finally {
            // 发布Event，激活观察者去消费，将sequence传递给改消费者
            // 注意最后的publish方法必须放在finally中以确保必须得到调用；如果某个请求的sequence未被提交将会堵塞后续的发布操作或者其他的producer
            ringBuffer.publish(sequence);
            return sendResult;
        }
    }

    @Override
    public Boolean recordConsumerRecord(RemoteConsumerRecordDTO recordDTO) {
        Boolean sendResult = Boolean.TRUE;
        log.info("recordConsumerRecord the recordDTO:{}", recordDTO);
        // 获取下一个Event槽的下标
        long sequence = ringBuffer.next();
        try {
            // 给Event填充数据
            DisMsgData event = ringBuffer.get(sequence);
            // 封装参数
            Map<String, Object> paramMap = new HashMap<String, Object>(){{
                put("unionId", recordDTO.getUnionId());
                put("shopId", recordDTO.getShopId());
                put("lastType", recordDTO.getLastType());
            }};
            event.init(TeboDisMqConstant.DIS_MQ_TOPIC_RESCUE, TeboDisMqConstant.CONSUMER_SERVICE_RECORD, paramMap);
            log.info("往消息队列中添加消息：{}", event);
        } catch (Exception e) {
            sendResult = Boolean.FALSE;
            log.error("failed to add event to messageModelRingBuffer for : e = {},{}", e, e.getMessage());
        } finally {
            // 发布Event，激活观察者去消费，将sequence传递给改消费者
            // 注意最后的publish方法必须放在finally中以确保必须得到调用；如果某个请求的sequence未被提交将会堵塞后续的发布操作或者其他的producer
            ringBuffer.publish(sequence);
            return sendResult;
        }
    }

    @Override
    public Boolean notifyAppMsg(Long shopId, String msg) {
        Boolean sendResult = Boolean.TRUE;
        log.info("recordConsumerRecord the shopId:{} msg:{}", shopId, msg);
        // 获取下一个Event槽的下标
        long sequence = ringBuffer.next();
        try {
            // 给Event填充数据
            DisMsgData event = ringBuffer.get(sequence);
            // 封装参数
            Map<String, Object> paramMap = new HashMap<String, Object>(){{
                put("shopId", shopId);
                put("msg", msg);
            }};
            event.init(TeboDisMqConstant.DIS_MQ_TOPIC_RESCUE, TeboDisMqConstant.NOTIFY_APP_MSG, paramMap);
            log.info("往消息队列中添加消息：{}", event);
        } catch (Exception e) {
            sendResult = Boolean.FALSE;
            log.error("failed to add event to messageModelRingBuffer for : e = {},{}", e, e.getMessage());
        } finally {
            // 发布Event，激活观察者去消费，将sequence传递给改消费者
            // 注意最后的publish方法必须放在finally中以确保必须得到调用；如果某个请求的sequence未被提交将会堵塞后续的发布操作或者其他的producer
            ringBuffer.publish(sequence);
            return sendResult;
        }
    }

    @Override
    public Boolean notifyOrderTimeout(Long tenantId, String msg) {
        Boolean sendResult = Boolean.TRUE;
        log.info("notifyOrderTimeout tenantId:{} msg:{}", tenantId, msg);
        // 获取下一个Event槽的下标
        long sequence = ringBuffer.next();
        try {
            // 给Event填充数据
            DisMsgData event = ringBuffer.get(sequence);
            // 封装参数
            Map<String, Object> paramMap = new HashMap<String, Object>() {{
                put("tenantId", tenantId);
                put("msg", msg);
            }};
            event.init(TeboDisMqConstant.DIS_MQ_TOPIC_RESCUE, TeboDisMqConstant.NOTIFY_ORDER_TIMEOUT, paramMap);
            log.info("往消息队列中添加消息：{}", event);
        } catch (Exception e) {
            sendResult = Boolean.FALSE;
            log.error("failed to add event to messageModelRingBuffer for : e = {},{}", e, e.getMessage());
        } finally {
            // 发布Event，激活观察者去消费，将sequence传递给改消费者
            // 注意最后的publish方法必须放在finally中以确保必须得到调用；如果某个请求的sequence未被提交将会堵塞后续的发布操作或者其他的producer
            ringBuffer.publish(sequence);
            return sendResult;
        }
    }


}
