package com.tebo.rescue.applet.controller.report;

import com.tebo.common.core.domain.R;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.rescue.applet.domain.view.report.TeboReportPartnerVO;
import com.tebo.rescue.service.report.TeboReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024/6/3 10:22
 * @Desc :
 */
@Slf4j
@RestController
@RequestMapping("/applet/report/partner")
public class TeboReportPartnerController  extends BaseController {

    @Resource
    private TeboReportService teboReportService;


    /**
     * 合伙人小程序主报表
     * @param tenantId
     * @return
     */
    @GetMapping("/{tenantId}")
    public R<TeboReportPartnerVO> info(@PathVariable Long tenantId){
        return R.ok(teboReportService.partnerTotalInfo(tenantId));
    }

    /**
     * 合伙人指定门店小程序报表
     * @param shopId
     * @return
     */
    @GetMapping("/shop/{shopId}")
    public R<TeboReportPartnerVO> shopInfo(@PathVariable Long shopId){
        return R.ok(teboReportService.shopInfo(shopId));
    }

}
