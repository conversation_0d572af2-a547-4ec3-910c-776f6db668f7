package com.tebo.rescue.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.util.applet.AppletUtil;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.rescue.api.domain.dto.TeboCustomerPromoteRecordDTO;
import com.tebo.rescue.api.domain.dto.TeboPromoteRecordDTO;
import com.tebo.rescue.api.domain.view.TeboPromoteRecordVO;
import com.tebo.rescue.entity.TeboCustomerPromoteRecordDO;
import com.tebo.rescue.entity.TeboPromoteRecordDO;
import com.tebo.rescue.mapper.TeboCustomerPromoteRecordMapper;
import com.tebo.rescue.mapper.TeboPromoteRecordMapper;
import com.tebo.rescue.service.TeboCustomerPromoteRecordService;
import com.tebo.system.api.RemoteCustomerService;
import com.tebo.system.api.model.TeboConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;

@Slf4j
@Service
public class TeboCustomerPromoteRecordServiceImpl implements TeboCustomerPromoteRecordService {
    @Resource
    private TeboCustomerPromoteRecordMapper customerPromoteRecordMapper;

    @Resource
    private TeboPromoteRecordMapper teboPromoteRecordMapper;

    @Resource
    private RemoteCustomerService remoteCustomerService;

    @Resource
    private HttpServletRequest httpServletRequest;

    @Override
    public void insert(TeboCustomerPromoteRecordDTO customerPromoteRecordDTO) {
        if (StringUtils.isEmpty(customerPromoteRecordDTO.getUnionId())){
            String unionId = AppletUtil.getUnionIdByRequest(httpServletRequest);
            customerPromoteRecordDTO.setUnionId(unionId);
        }
        TeboCustomerPromoteRecordDO customerDO = new TeboCustomerPromoteRecordDO();
        BeanConvert.copy(customerPromoteRecordDTO,customerDO);
        customerDO.setId(SnowFlakeUtil.nextId());
        /**
         * 推广人钱包id
         */
        R<TeboConsumer> consumerR = remoteCustomerService.selectByPhoneNumber(customerPromoteRecordDTO.getReferencePhone());
        if (ObjectUtil.isEmpty(customerPromoteRecordDTO.getReferenceWalletId())){
            if (ObjectUtil.isNotEmpty(consumerR) && ObjectUtil.isNotEmpty(consumerR.getData())){
                customerDO.setReferenceWalletId(consumerR.getData().getWalletId());
            }
        }
        /**
         * 被推广人手机号
         */
        R<TeboConsumer> promotedConsumerR = remoteCustomerService.selectByUnionId(customerPromoteRecordDTO.getUnionId());
        customerDO.setPromotedPhone(promotedConsumerR.getData().getPhoneNumber());
        if (customerPromoteRecordDTO.getType() == 4){
            TeboPromoteRecordDTO teboPromoteRecordDTO = new TeboPromoteRecordDTO();
            teboPromoteRecordDTO.setPromotedPhone(customerPromoteRecordDTO.getReferencePhone());
            TeboPromoteRecordDO record = teboPromoteRecordMapper.getPromoteRecord(teboPromoteRecordDTO);
            if (ObjectUtil.isNotEmpty(record) && record.getChannel() == 1 ){
                customerDO.setType(5);
            }else if(ObjectUtil.isNotEmpty(record) && record.getChannel() == 2) {
                customerDO.setType(6);
            }
        }
        TeboCustomerPromoteRecordDO updateRecord = customerPromoteRecordMapper.selectByUnionId(customerPromoteRecordDTO.getUnionId());
        if (ObjectUtil.isEmpty(updateRecord)){
            if (customerDO.getPromotedPhone().equals(customerDO.getReferencePhone()) && customerPromoteRecordDTO.getType() != 3){
                log.info("promotedPhone equal referencePhone not record param:{}", JSONObject.toJSONString(customerDO));
                return;
            }
            customerPromoteRecordMapper.insert(customerDO);
        }else {
            updateRecord.setUpdateTime(LocalDateTime.now());
            updateRecord.setReferencePhone(customerPromoteRecordDTO.getReferencePhone());
            if (ObjectUtil.isEmpty(customerPromoteRecordDTO.getReferenceWalletId())){
                if (ObjectUtil.isNotEmpty(consumerR) && ObjectUtil.isNotEmpty(consumerR.getData())){
                    updateRecord.setReferenceWalletId(consumerR.getData().getWalletId());
                }
            }else {
                updateRecord.setReferenceWalletId(customerPromoteRecordDTO.getReferenceWalletId());
            }
            updateRecord.setType(customerDO.getType());
            if (customerDO.getPromotedPhone().equals(customerDO.getReferencePhone()) && customerPromoteRecordDTO.getType() != 3){
//            if (customerDO.getPromotedPhone().equals(customerDO.getReferencePhone())){
                log.info("promotedPhone equal referencePhone not record param:{}", JSONObject.toJSONString(customerDO));
                return;
            }
            customerPromoteRecordMapper.updateById(updateRecord);
        }

    }

    @Override
    public TeboPromoteRecordVO selectByUnionId(String unionId) {
        TeboPromoteRecordVO teboPromoteRecordVO = new TeboPromoteRecordVO();
        TeboCustomerPromoteRecordDO customerPromoteRecordDO = customerPromoteRecordMapper.selectByUnionId(unionId);
        if (ObjectUtil.isEmpty(customerPromoteRecordDO)){
            return null;
        }
        BeanConvert.copy(customerPromoteRecordDO,teboPromoteRecordVO);
        return teboPromoteRecordVO;
    }
}