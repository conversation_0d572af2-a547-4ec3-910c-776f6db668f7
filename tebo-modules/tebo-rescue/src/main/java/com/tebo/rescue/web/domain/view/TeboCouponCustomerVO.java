package com.tebo.rescue.web.domain.view;

import com.tebo.mall.api.domain.view.JiFenOrderVO;
import com.tebo.mall.api.domain.view.TeboCycleInsuranceOrderVO;
import com.tebo.mall.api.domain.view.TeboGoodsVO;
import com.tebo.rescue.applet.domain.view.TeboShopConsumerVO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2023/12/21 18:41
 * @Desc :
 */
@Data
public class TeboCouponCustomerVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 合伙人id
     */
    private Long tenantId;

    /**
     * 卡券编码
     */
    private String uniqueCode;

    /**
     * 卡券id
     */
    private Long couponId;

    /***
     * 卡券对应的skuId
     */
    private String skuId;

    /**
     * 卡券名称
     */
    private String couponName;

    /**
     * 面额（元）
     */
    private String parValue;

    /**
     * 来源 0 购买 1转赠
     */
    private Integer source;

    /**
     * 礼包id
     */
    private Long packId;

    /**
     * 卡券码
     */
    private String qrCode;

    /**
     * 领取优惠卷的会员id
     */
    private String unionId;

    /**
     * 状态0未领取1未使用2已使用3已失效 10已分享
     */
    private Integer status;

    /**
     * 占用状态 1 未占用 2已占用
     */
    private Integer occStatus;

    /**
     * 占用单号
     */
    private String occNumber;

    /**
     * 领取时间
     */
    private LocalDateTime receiveTime;

    /**
     * 有效期开始日期
     */
    private LocalDateTime startTime;

    /**
     * 有效期结束日期
     */
    private LocalDateTime endTime;

    /**
     * 更新时间
     */
    private LocalDateTime  updateTime;

    /**
     * 使用须知
     */
    private String remark;

    /**
     * 主图
     */
    private String mainImage;

    /**
     * 可用门店
     */
    private List<TeboShopConsumerVO> shopList;

    /**
     * 限制商品（0全场通用 1指定商品 2 指定分类）
     */
    private Integer goodLimit;

    /**
     * 商品列表
     */
    private List<TeboGoodsVO> goodsList;


    /**
     * 使用方式 0 无门槛 1 满额使用
     */
    private Integer useType;

    /**
     * 满 xx 可用(分)
     */
    private Integer useReduce;


    /**
     * 卡券类型（11 电池 12维修）
     */
    private Integer type;

    /**
     * 卡券归属（0 天能 1合伙人 2天能+合伙人）
     */
    private Integer vest;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 核销时间
     */
    private LocalDateTime writeOffTime;
    /**
     * 骑行单
     */
    private TeboCycleInsuranceOrderVO cycleInsuranceOrderVO;

    /**
     * 大米订单
     */
    private JiFenOrderVO jiFenOrderVO;




}
