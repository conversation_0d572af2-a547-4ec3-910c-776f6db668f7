package com.tebo.rescue.applet.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.shaded.com.google.gson.JsonObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.domain.AjaxResult;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.http.HttpTool;
import com.tebo.common.util.number.DistanceUtil;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.common.util.position.AutoNaviUrlConstant;
import com.tebo.rescue.applet.domain.view.TeboShopConsumerVO;
import com.tebo.rescue.service.TeboGiftPackService;
import com.tebo.rescue.web.domain.dto.pack.TeboGiftPackQueryDTO;
import com.tebo.rescue.web.domain.view.TeboGiftPackNewVO;
import com.tebo.rescue.web.domain.view.TeboGiftPackVO;
import com.tebo.system.api.RemotePartnerService;
import com.tebo.system.api.RemoteShopService;
import com.tebo.system.api.domain.dto.TeboPositionQueryDTO;
import com.tebo.system.api.domain.dto.TeboShopQueryDTO;
import com.tebo.system.api.domain.view.TeboPartnerInfoVO;
import com.tebo.system.api.domain.view.TeboShopListVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ZhangFan
 * @date : 2023/12/14 18:36
 * @Desc : C 端消费者礼包接口
 */
@Slf4j
@RestController
@RequestMapping("/consumer/pack")
public class TeboConsumerGiftPackController extends BaseController {

    @Resource
    private TeboGiftPackService giftPackService;
    @Resource
    private RemotePartnerService remotePartnerService;
    @Resource
    private RemoteShopService remoteShopService;
    @Autowired
    private RedisService redisService;

    /**
     * 小程序福利包列表
     * @return
     */
    @GetMapping("/getList")
    public TableDataInfo getList(@RequestParam("lgt") String lgt,@RequestParam("lat") String lat) {
        TeboGiftPackQueryDTO  query = new TeboGiftPackQueryDTO();
//        if (StringUtils.isEmpty(lat) || StringUtils.isEmpty(lgt)) {
//            query.setTenantId(0L);
//        }else {
//            // String result = HttpTool.sendGet(AutoNaviUrlConstant.GEOCODE_REGEO_URL + "?key=c1848f11fe9efd196c6177a64872acf5&location=" + lgt + "," + lat);
//            // JSONObject json = JSONObject.parseObject(result);
//            // if (json.getInteger("status") != 0) {
//            //     TeboPositionQueryDTO queryDTO = new TeboPositionQueryDTO();
//            //     queryDTO.setLgt(lgt);
//            //     queryDTO.setLnt(lat);
//            //     TeboPartnerInfoVO partnerInfoVO = remotePartnerService.getPartnerByDistrictCode(queryDTO).getData();
//            //     if (!ObjectUtils.isEmpty(partnerInfoVO)) {
//            //         query.setTenantId(partnerInfoVO.getId());
//            //     }else {
//            //         query.setTenantId(0L);
//            //     }
//            // }else {
//            //     query.setTenantId(0L);
//            // }
//            // 240613 改为找最近门店的合伙人的券
//            List<TeboShopListVO> shopListVOList = remoteShopService.fullyQuery(new TeboShopQueryDTO()).getData();
//            if (CollectionUtils.isEmpty(shopListVOList)) {
//                query.setTenantId(0L);
//            }else {
//                shopListVOList = shopListVOList.stream().filter(item -> item.getShowNearbyShop() != 0).collect(Collectors.toList());
//                List<TeboShopConsumerVO> shopList = BeanConvert.copyList(shopListVOList, TeboShopConsumerVO::new);
//                shopList.stream().forEach(item -> {
//                    // 计算距离
//                    Double distance = DistanceUtil.getDistance(lgt, lat, item.getLongitude(), item.getLatitude());
//                    item.setDistance(distance);
//                });
//                TeboShopConsumerVO shop = shopList.stream().sorted(Comparator.comparing(TeboShopConsumerVO::getDistance)).limit(1).collect(Collectors.toList()).get(0);
//                query.setTenantId(shop.getTenantId());
//            }
//        }
        query.setStatus(10);
        query.setTenantId(0L);
        List<TeboGiftPackVO> res = new ArrayList<>();
        String key = "TEBO_RESCUE:PACK";
        String pack = redisService.getCacheObject(key);
        if (StringUtils.isNotEmpty(pack)){
            res = JSONArray.parseArray(pack,TeboGiftPackVO.class);
        }else {
           res = giftPackService.getGiftPackList(query);
           redisService.setCacheObject(key, JSONObject.toJSONString(res), 24L, TimeUnit.HOURS);
        }
        List<TeboGiftPackNewVO> result = new ArrayList<>();
        res.stream().forEach(item ->{
            TeboGiftPackNewVO teboGiftPackNewVO = new TeboGiftPackNewVO();
            BeanConvert.copy(item,teboGiftPackNewVO);
            teboGiftPackNewVO.setPrice(MoneyUtil.fenToYuan(item.getPrice()));
            teboGiftPackNewVO.setParValue(MoneyUtil.fenToYuan(item.getParValue()));
            result.add(teboGiftPackNewVO);
        });
        return getDataTable(result);
    }


    /**
     * 小程序礼包详情
     * @param id
     * @return
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo(@RequestParam("id") Long id) {
        return success(giftPackService.getDetailById(id));
    }
}
