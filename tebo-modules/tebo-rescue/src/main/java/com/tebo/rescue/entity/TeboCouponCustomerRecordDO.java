package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 消费者优惠券记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Getter
@Setter
@TableName("tebo_coupon_customer_record")
public class TeboCouponCustomerRecordDO extends Model<TeboCouponCustomerRecordDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 用户卡券唯一码
     */
    @TableField("unique_code")
    private String uniqueCode;

    /**
     * 合伙人id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 合伙人名称
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 卡券id
     */
    @TableField("coupon_id")
    private Long couponId;

    /**
     * 0 单独卡券 1礼拜卡券
     */
    @TableField("single")
    private Integer single;

    /**
     * 卡券名称
     */
    @TableField("coupon_name")
    private String couponName;

    /**
     * 卡券编码
     */
    @TableField("coupon_code")
    private String couponCode;

    /**
     * 会员id
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 会员名称
     */
    @TableField("customer_name")
    private String customerName;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 核销门店id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 核销门店名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 状态1未领取2已领取3已使用10已失效
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 领取时间
     */
    @TableField("receive_time")
    private LocalDateTime receiveTime;

    /**
     * 核销时间
     */
    @TableField("use_time")
    private LocalDateTime useTime;

    /**
     * 订单编号
     */
    @TableField("order_code")
    private String orderCode;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
