package com.tebo.rescue.remote;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson2.util.DateUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.domain.AjaxResult;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.common.util.number.DistanceUtil;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.rescue.api.domain.dto.OrderCouponDTO;
import com.tebo.rescue.applet.domain.dto.coupon.TeboCouponCloudShopDTO;
import com.tebo.rescue.applet.domain.view.TeboShopConsumerVO;
import com.tebo.rescue.entity.TeboGiftPackDO;
import com.tebo.rescue.entity.TeboGiftPackOrderDO;
import com.tebo.rescue.manager.TeboCouponManager;
import com.tebo.rescue.mapper.TeboGiftPackOrderMapper;
import com.tebo.rescue.entity.TeboCouponSignRecordDO;
import com.tebo.rescue.mapper.TeboCouponSignRecordMapper;
import com.tebo.rescue.entity.TeboGiftPackDO;
import com.tebo.rescue.entity.TeboGiftPackOrderDO;
import com.tebo.rescue.mapper.TeboGiftPackOrderMapper;
import com.tebo.rescue.mapper.TeboServiceOrderMapper;
import com.tebo.rescue.remote.domain.LstOrderDetailInfoVO;
import com.tebo.rescue.remote.domain.LstOrderQueryDTO;
import com.tebo.rescue.service.TeboCouponCustomerService;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponCustomerRecordDTO;
import com.tebo.rescue.web.domain.view.TeboCouponCustomerVO;
import com.tebo.system.api.RemoteCustomerService;
import com.tebo.system.api.RemoteWalletService;
import com.tebo.system.api.domain.dto.PayWechatRecordQueryDTO;
import com.tebo.system.api.domain.dto.wallet.TeboWalletPlanDTO;
import com.tebo.system.api.domain.view.PayWechatVO;
import com.tebo.system.api.model.TeboConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/open/cloud")
public class TeboRemoteCloudShopController extends BaseController {
    @Resource
    private TeboCouponCustomerService couponCustomerService;
    @Resource
    private RemoteCustomerService customerService;
    @Resource
    private TeboCouponSignRecordMapper couponSignRecordMapper;
    @Resource
    private TeboGiftPackOrderMapper giftPackOrderMapper;
    @Resource
    private TeboCouponManager teboCouponManager;
    @Resource
    private TeboServiceOrderMapper teboServiceOrderMapper;

    /**
     * 根据券号卡券详情
     * @param couponCode
     * @return
     */
    @GetMapping("/coupon/getInfo/{couponCode}")
    public AjaxResult getInfo(@PathVariable("couponCode") String couponCode) {
        TeboCouponCustomerVO customerVO = couponCustomerService.getDetailByCouponCode(couponCode);
        customerVO.setShopList(null);
        customerVO.setGoodsList(null);
        R<TeboConsumer> res = customerService.selectByUnionId(customerVO.getUnionId());
        if(Objects.equals(res.getCode(),200)) {
            customerVO.setPhoneNumber(res.getData().getPhoneNumber());
        }
        return success(customerVO);
    }
    /**
     * 根据券号卡券详情
     * @param couponCode
     * @return
     */
    @GetMapping("/coupon/writeOff/{couponCode}")
    public AjaxResult writeOff(@PathVariable("couponCode") String couponCode) {
        TeboCouponCustomerVO customerVO = couponCustomerService.getDetailByCouponCode(couponCode);
        if (ObjectUtils.isEmpty(customerVO)) {
            throw new ServiceException("该卡券无效，请确认是否可用！");
        }
        if (customerVO.getStatus() != 1) {
            throw new ServiceException("该卡券已使用或已失效");
        }
        OrderCouponDTO orderCouponDTO = new OrderCouponDTO();
        orderCouponDTO.setCouponCode(couponCode);
        orderCouponDTO.setStatus(2);
        orderCouponDTO.setWriteOffTime(LocalDateTime.now());
        orderCouponDTO.setUpdateTime(LocalDateTime.now());
        couponCustomerService.updateCouponStatusAndTime(orderCouponDTO);
        if (customerVO.getOrderNo() != null){
            TeboGiftPackOrderDO teboGiftPackOrderDO = giftPackOrderMapper.selectByOrderNo(customerVO.getOrderNo());
            teboGiftPackOrderDO.setRiceVerificationStatus(1);
            giftPackOrderMapper.updateById(teboGiftPackOrderDO);
        }
        return success(true);
    }

    /**
     * 核销卡券
     * @param dto
     * @return
     */
    @PostMapping("/coupon/writeOff/signRecord")
    public AjaxResult signRecord(@RequestBody TeboCouponCloudShopDTO dto) {
        log.info("signRecord dto {}", dto);
        couponCustomerService.signRecord(dto);
        return success(true);
    }
    /**
     * 核销卡券-专攻品使用
     * @param dto
     * @return
     */
    @PostMapping("/coupon/writeOff/signRecordNew")
    public AjaxResult signRecordNew(@RequestBody TeboCouponCloudShopDTO dto) {
        log.info("signRecord dto {}", dto);
        couponCustomerService.signRecordNew(dto);
        return success(true);
    }

    /**
     * 核销记录
     */
    @GetMapping("/coupon/writeOff/signRecordList/{phoneNumber}")
    public R signRecordList(@PathVariable("phoneNumber") String phoneNumber) {
        return R.ok(couponCustomerService.signRecordList(phoneNumber));
    }

    /**
     * 查询礼包订单
     * @param id
     * @return
     */
    @GetMapping("/giftPackOrder/{id}")
    public R giftPackInfo(@PathVariable("id") Long id) {
        log.info("giftPackInfo {}", id);
        TeboGiftPackOrderDO giftPackOrderDO = giftPackOrderMapper.selectById(id);
        if (ObjectUtils.isEmpty(giftPackOrderDO)) {
            throw new ServiceException("订单不存在");
        }
        return R.ok(giftPackOrderDO);
    }

    /**
     * 根据券号判断卡券是否有效
     * @param couponCode
     * @return
     */
    @GetMapping("/coupon/getInfoEffective/{couponCode}")
    public AjaxResult getInfoEffective(@PathVariable("couponCode") String couponCodeStr) {
//        log.info("getInfoEffective couponCode {}", couponCodeStr);
        if (ObjectUtils.isEmpty(couponCodeStr)) {
            throw new ServiceException("券号不能为空");
        }
        if (couponCodeStr.split(",").length != 2) {
            throw new ServiceException("券号格式不正确");
        }
        String [] couponCodes = couponCodeStr.split(",");
        String couponCode = couponCodes[0];
        String phone = couponCodes[1];
//        log.info("getInfoEffective couponCode {} phone {}", couponCode, phone);
        TeboCouponCustomerVO customerVO = couponCustomerService.getDetailByCouponCode(couponCode);
//        log.info("getInfoEffective customerVO {}", customerVO);

        if (ObjectUtils.isEmpty(customerVO)) {
            return success(false);
        }
        // 未使用的券或者 分享中的券可以用
        if (customerVO.getStatus() != 1) {
            if (customerVO.getStatus() != 10) {
                return success(false);
            }
        }
        R<TeboConsumer> res = customerService.selectByUnionId(customerVO.getUnionId());
        if (!Objects.equals(res.getCode(), 200)) {
            return success(false);
        }
        if (!res.getData().getPhoneNumber().equals(phone)) {
            throw new ServiceException("该卡券已分享给他人，无法核销");
        }
        return success(true);
    }

    /**
     * 管家零售通订单列表
     * @param queryDTO
     * @return
     */
    @PostMapping("/lstOrder")
    public TableDataInfo lstOrder(@RequestBody LstOrderQueryDTO queryDTO) {
        if (queryDTO == null || queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            throw new GlobalException("参数错误");
        }
        if (ObjectUtils.isEmpty(queryDTO.getGysCode())) {
            throw new GlobalException("供应商编码不可为空");
        }
        Page page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        return getDataTable(teboServiceOrderMapper.getLstOrderList(queryDTO), page);
    }

    /**
     * 查询商家联盟订单详情
     * @param id
     * @return
     */
    @GetMapping("/lstOrderInfo/{id}")
    public R<LstOrderDetailInfoVO> lstOrderInfo(@PathVariable("id") Long id) {
        return R.ok(teboServiceOrderMapper.selectLstOrderDetailInfo(id));
    }
}
