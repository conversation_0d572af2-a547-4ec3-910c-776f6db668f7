package com.tebo.rescue.mapper;

import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.rescue.entity.TeboCommentDO;
import com.tebo.rescue.entity.TeboPlatSettleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 评价表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@Mapper
public interface TeboPatSettleMapper extends TeboBaseMapper<TeboPlatSettleDO> {

}
