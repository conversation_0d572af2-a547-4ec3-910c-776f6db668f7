package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 团购订单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Getter
@Setter
@TableName("tebo_group_purchase_order")
public class TeboGroupPurchaseOrderDO extends Model<TeboGroupPurchaseOrderDO> {

    private static final long serialVersionUID = 1L;

    @TableField("id")
    private Long id;

    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 团购类型 1:钜惠 2:安心
     */
    @TableField("type")
    private Integer type;

    /**
     * 合伙id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 商品名称
     */
    @TableField("goods_name")
    private String goodsName;

    /**
     * 购方类型 1:天能内部 2：其他
     */
    @TableField("purchaser_type")
    private Integer purchaserType;

    /**
     * 购方名称
     */
    @TableField("purchaser_name")
    private String purchaserName;

    /**
     * 单价
     */
    @TableField("unit_price")
    private Integer unitPrice;

    /**
     * 积分
     */
    @TableField("unit_integral_price")
    private Integer unitIntegralPrice;

    /**
     * 总积分
     */
    @TableField("total_integral")
    private Integer totalIntegral;

    /**
     * 选购数量
     */
    @TableField("purchase_quantity")
    private Integer purchaseQuantity;

    /**
     * 满赠数量
     */
    @TableField("full_delivery_quantity")
    private Integer fullDeliveryQuantity;

    /**
     * 联系人
     */
    @TableField("contact")
    private String contact;

    /**
     * 联系电话
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 商品总数量
     */
    @TableField("total_goods_number")
    private Integer totalGoodsNumber;

    /**
     * 订单金额
     */
    @TableField("order_amount")
    private Integer orderAmount;

    /**
     * 订单状态1:待付款 2:待分配 3:交易完成 4:交易关闭
     */
    @TableField("order_status")
    private Integer orderStatus;

    /**
     * 激活状态 1:待激活 2：进行中 3：已完成
     */
    @TableField("active_status")
    private Integer activeStatus;

    /**
     * 激活码
     */
    @TableField("active_qr_code")
    private String activeQrCode;

    /**
     * 结算状态1未结算2已结算
     */
    @TableField("settle_status")
    private Integer settleStatus;

    /**
     * 删除状态
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @TableField(value = "pay_time")
    private LocalDateTime payTime;

    @Override
    public Serializable pkVal() {
        return null;
    }

}
