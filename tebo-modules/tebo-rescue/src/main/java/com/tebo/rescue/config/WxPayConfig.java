 package com.tebo.rescue.config;

 import lombok.Data;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.io.IOUtils;
 import org.springframework.boot.context.properties.ConfigurationProperties;
 import org.springframework.core.io.ClassPathResource;
 import org.springframework.stereotype.Component;

 import javax.annotation.PostConstruct;
 import java.io.InputStream;

 /**
  * 微信支付配置
  */
 @Data
 @Component
 @ConfigurationProperties(prefix = "wx.pay")
 @Slf4j
 public class WxPayConfig {
     /**
      * 支付环境标识
      */
     private String envPay;
     /**
      * 支付回调地址
      */
     private String payNotifyUrl;
     /**
      * 供应商商户id
      */
     private String merchantId;
     /***
      * 商户API私钥
      */
     private String privateKey;
     /***
      * 商户证书序列号
      */
     private String merchantSerialNumber;
     /**
      * 商户APIv3密钥
      */
     private String apiV3Key;
     @PostConstruct
     private void initPrivateKey(){
         String fileContent = "";
         try {
             ClassPathResource classPathResource = new ClassPathResource("apiclient_key.pem");
             InputStream in = classPathResource.getInputStream();
             fileContent = IOUtils.toString(in);
             this.privateKey = fileContent;
         } catch (Exception e) {
             log.error("initPrivateKey error {}",e);
         }

     }

 }

