package com.tebo.rescue.service;

import com.tebo.rescue.api.domain.dto.OrderCouponDTO;
import com.tebo.rescue.api.domain.view.TeboCouponSignRecordVO;
import com.tebo.rescue.applet.domain.dto.coupon.*;
import com.tebo.rescue.applet.domain.view.TeboShopConsumerVO;
import com.tebo.rescue.applet.domain.view.coupon.TeboMallCouponCustomerVO;
import com.tebo.rescue.entity.TeboCouponSignRecordDO;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponCustomerQueryDTO;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponQueryDTO;
import com.tebo.rescue.web.domain.view.TeboCouponCustomerNumberVO;
import com.tebo.rescue.web.domain.view.TeboCouponCustomerVO;
import com.tebo.rescue.web.domain.view.TeboGiftPackOrderVO;
import com.tebo.rescue.web.domain.view.TeboGiftPackVO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> ZhangFan
 * @date : 2023/12/21 18:27
 * @Desc : 消费者卡券服务接口
 */
public interface TeboCouponCustomerService {

    /**
     * 查询用户卡券列表
     * @param query
     * @return
     */
    List<TeboCouponCustomerVO> getList(TeboCouponCustomerQueryDTO query);


    List<TeboCouponCustomerVO> getBatteryList(TeboCouponCustomerQueryDTO query);

    /**
     * 会员权益礼包
     * @param query
     * @return
     */
    List<TeboCouponCustomerVO> getMemberList(TeboCouponCustomerQueryDTO query);

    /**
     * 查询用户卡券详情
     * @param couponCode
     * @return
     */
    TeboCouponCustomerVO getDetailByCouponCode(String couponCode);

    /**
     * 更新卡券为分享状态
     * @param couponCode
     * @return
     */
    Integer shareCouponCode(String couponCode);

    /**
     * 领取卡券
     * @return
     */
    Map<String,Object>  receive(TeboCouponShareDTO teboCouponShareDTO);


    /**
     * 礼包支付成功调用发放卡券
     * @param packId 礼包id
     * @param unionId 微信用户id
     * @return
     */
    Integer grantPackCoupon(Long packId, String unionId,String orderNo);


    /**
     * 调用发放单独卡券
     * @param couponId 卡券id
     * @return
     */
    Integer grantSingleCoupon(Long couponId);

    /**
     * 全员发放卡券
     */
    void grantAllStaffCoupon(TeboCouponAllStaffDTO teboCouponAllStaffDTO);

    /**
     * 校验卡券是否符合核销规则
     * @param checkDTO
     * @return
     */
    TeboCouponCustomerVO checkCouponRule(TeboCouponCheckDTO checkDTO);

    /**
     * 批量更新用户卡券状态
     * @param uniqueCode 唯一码
     * @param status 状态更新为：
     * 1未使用 2已使用 11 已占用
     * @return
     */
    Integer batchUpdateCouponStatus(List<String> uniqueCode, Integer status);

    /**
     * 更新卡券状态以及核销时间
     */
    Integer updateCouponStatusAndTime(OrderCouponDTO orderCouponDTO);
    /**
     * 取消分享
     */
    Map<String, Object> cancelShareCouponCode(String couponCode);
    /**
     * 更新优惠券状态
     * @param uniqueCode
     * @param status
     * @return
     */
    Integer updateCouponStatus(OrderCouponDTO orderCouponDTO);

    /**
     * 更新优惠券状态
     * @param uniqueCode
     * @param status
     * @return
     */
    Integer updateCouponListStatus(OrderCouponDTO orderCouponDTO);
 /**
     * 更新优惠券占用状态
     * @param uniqueCode
     * @param occStatus
     * @return
     */
    Integer updateCouponOccStatus(OrderCouponDTO orderCouponDTO);

    /**
     * 更新卡券状态为未使用
     */
    Integer updateCouponStatusNotUsed(OrderCouponDTO orderCouponDTO);

    /**
     * 专供品可用电池卡券
     * @param unionId
     * @return
     */
    TeboMallCouponCustomerVO getCouponCustomerList(String unionId);

    /**
     * 卡券可用门店
     * @param uniqueCode 唯一码
     * @return
     */
    List<TeboShopConsumerVO> getCouponShop(String uniqueCode, String lgt, String lnt);

    /**
     * 卡券核销记录
     * @param uniqueCode
     * @return
     */
    TeboCouponSignRecordDO getCouponRecord(String uniqueCode);

    /**
     * 作废指定礼包的卡券
     * @param packId
     * @param unionId
     * @return
     */
    Boolean refundGiftPackCoupon(String orderNo, String unionId);


    /**
     * 调用发放单独卡券
     * @param unionId
     * @return
     */
    Boolean grantLvccCoupon(String unionId);

    /**
     * 统计用户名下有多少张大米券，电池券
     * @param unionId
     * @return
     */
    TeboCouponCustomerNumberVO getCustomerCouponNumber(String unionId);

    /**
     * 大米券
     */
    List<TeboCouponCustomerVO> getRiceCouponList(TeboCouponCustomerQueryDTO queryDTO);

    /**
     * 获取骑行券
     * @param unionId
     * @return
     */
    List<TeboCouponCustomerVO> getCyclingInsuranceCouponList(TeboCouponCustomerQueryDTO queryDTO);

    /**
     * 核销卡券
     */

    void signRecord(TeboCouponCloudShopDTO couponCloudShopDTO);

    /**
     * 核销卡券
     */

    void signRecordNew(TeboCouponCloudShopDTO couponCloudShopDTO);

    /**
     * 解冻大米资金
     * @param orderNo
     * @return
     */
    Boolean unfreezeRiceFunds(String orderNo);

    List<TeboCouponSignRecordVO> signRecordList(String phoneNumber);

    /**
     * 获取用户的最新的骑行卡券
     */
    TeboCouponCustomerVO getCustomerCycleCoupon(String unionId,String orderNo);

    /**
     * 根据流水号查询订单信息
     * @param flowId
     * @return
     */
    TeboGiftPackOrderVO getCustomerCouponByFlowId(Long flowId);

    /**
     * 校验电池是否已在核销表中
     * @param batteryCode
     * @return
     */
    Boolean checkBatteryCode(String batteryCode);
}
