package com.tebo.rescue.service.pay.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.enums.PayWechatOrderBusinessTypeEnum;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.SpringUtils;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.redis.constant.TeboRescueCacheConstant;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.util.applet.AppletRequestUrl;
import com.tebo.common.util.http.HttpTool;
import com.tebo.mall.api.*;
import com.tebo.mall.api.domain.dto.*;
import com.tebo.mall.api.domain.dto.cycling.TeboCyclingOrderDTO;
import com.tebo.mall.api.domain.view.TeboGoodsVO;
import com.tebo.rescue.applet.domain.dto.GiftPackOrderAfterPayDTO;
import com.tebo.rescue.applet.domain.dto.RescueQueueOrderSaveDTO;
import com.tebo.rescue.applet.domain.dto.ServiceOrderAfterPayDTO;
import com.tebo.rescue.applet.domain.dto.ServiceOrderGoodsQueryDTO;
import com.tebo.rescue.applet.domain.dto.ServiceOrderPayDTO;
import com.tebo.rescue.entity.*;
import com.tebo.rescue.enums.*;
import com.tebo.rescue.manager.TeboCouponManager;
import com.tebo.rescue.manager.TeboRescueQueueOrderManger;
import com.tebo.rescue.mapper.*;
import com.tebo.rescue.service.ITeboRescueQueueOrderService;
import com.tebo.rescue.service.TeboCouponCustomerService;
import com.tebo.rescue.service.mq.DisMqService;
import com.tebo.rescue.service.pay.TeboWechatPayService;
import com.tebo.rescue.util.TeboCommissionUtil;
import com.tebo.rescue.util.TeboNumberGenerator;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponCustomerRecordDTO;
import com.tebo.rescue.web.domain.view.TeboCouponCustomerVO;
import com.tebo.system.api.*;
import com.tebo.system.api.domain.dto.PayOrderDTO;
import com.tebo.system.api.domain.dto.wallet.TeboWalletPlanDTO;
import com.tebo.system.api.domain.dto.wallet.TeboWalletPlanDetailDTO;
import com.tebo.system.api.domain.view.WechatPrepayResponse;
import com.tebo.system.api.model.TeboConsumer;
import com.tebo.system.api.model.TeboShop;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ZhangFan
 * @date : 2024/1/19 13:03
 * @Desc :
 */
@Slf4j
@Service
public class TeboWechatPayServiceImpl implements TeboWechatPayService {

    @Resource
    private TeboServiceOrderMapper serviceOrderMapper;
    @Resource
    private TeboGiftPackOrderMapper giftPackOrderMapper;

    @Resource
    private TeboGroupPurchaseOrderMapper teboGroupPurchaseOrderMapper;

    @Resource
    private TeboServiceOrderCouponMapper serviceOrderCouponMapper;

    @Resource
    private TeboRescueQueueOrderManger rescueQueueOrderManger;

    @Resource
    private RemoteCustomerService remoteCustomerService;
    @Resource
    private TeboCouponCustomerService couponCustomerService;

    @Resource
    private RemotePayService remotePayService;
    @Resource
    private DisMqService disMqService;

    @Resource
    private TeboQueueOrderMapper queueOrderMapper;
    @Resource
    private TeboGiftPackMapper teboGiftPackMapper;
    @Resource
    private TeboServiceOrderGoodsMapper serviceOrderGoodsMapper;

    @Resource
    private RemoteGoodsService remoteGoodsService;
    @Resource
    private TeboCouponManager teboCouponManager;

    @Autowired
    private RemoteShopService remoteShopService;

    @Autowired
    private RemoteIntegralOrderService remoteIntegralOrderService;
    @Resource
    private RedisService redisService;
    @Resource
    private RemoteWalletService remoteWalletService;

    @Resource
    private TeboCustomerPromoteRecordMapper customerPromoteRecordMapper;
    @Resource
    private RemoteCommissionService remoteCommissionService;

    @Resource
    private TeboCommissionUtil teboCommissionUtil;

    @Resource
    private RemoteCycleInsuranceOrderService cycleInsuranceOrderService;

    @Resource
    private RemoteQrCodeService remoteQrCodeService;

    @Resource
    private RemoteMallOrderWechatPayBackService mallOrderWechatPayBackService;

    // b端appid
    @Value("${applet.appId_b}")
    private String appId;

    //
    @Value("${applet.secret_b}")
    private String secret;


    // c端appid
    @Value("${applet.appId_c}")
    private String appIdc;

    //
    @Value("${applet.secret_c}")
    private String secretc;
    @Value("${partner.secondCategoryId}")
    private String secondCategoryId;

    @Override
    public R<WechatPrepayResponse> serviceOrderPrePay(Long orderId) {
        TeboServiceOrderDO teboServiceOrderDO = serviceOrderMapper.selectById(orderId);
        if (teboServiceOrderDO.getOrderStatus() != 3) {
            throw new GlobalException("只有待支付订单能支付");
        }
        TeboConsumer consumer = remoteCustomerService.selectByUnionId(teboServiceOrderDO.getUnionid()).getData();
        PayOrderDTO payOrderDTO = new PayOrderDTO();
        payOrderDTO.setSource(1);
        payOrderDTO.setBusinessType(1);
        payOrderDTO.setOrderNo(teboServiceOrderDO.getOrderNo());
        payOrderDTO.setAmount(teboServiceOrderDO.getOrderAmount());
        payOrderDTO.setOpenId(consumer.getOpenid());
        payOrderDTO.setDescription("服务工单支付");
        payOrderDTO.setShopId(teboServiceOrderDO.getShopId());
        payOrderDTO.setShopName(teboServiceOrderDO.getShopName());
        payOrderDTO.setTenantId(teboServiceOrderDO.getTenantId());
        payOrderDTO.setReceiver(getReceiverTypeByOrderId(orderId));
        log.info("payOrderDTO={}", JSON.toJSONString(payOrderDTO));
        return remotePayService.wechatPrePay(payOrderDTO);
    }
    @Override
    public R<WechatPrepayResponse> quickBillServiceOrderPrePay(Long orderId,String openId) {
        TeboServiceOrderDO teboServiceOrderDO = serviceOrderMapper.selectById(orderId);
        if (teboServiceOrderDO.getOrderStatus() != 3) {
            throw new GlobalException("只有待支付订单能支付");
        }
        PayOrderDTO payOrderDTO = new PayOrderDTO();
        payOrderDTO.setSource(1);
        payOrderDTO.setBusinessType(1);
        payOrderDTO.setOrderNo(teboServiceOrderDO.getOrderNo());
        payOrderDTO.setAmount(teboServiceOrderDO.getOrderAmount());
        payOrderDTO.setOpenId(openId);
        payOrderDTO.setDescription("快捷开单支付");
        payOrderDTO.setShopId(teboServiceOrderDO.getShopId());
        payOrderDTO.setShopName(teboServiceOrderDO.getShopName());
        payOrderDTO.setTenantId(teboServiceOrderDO.getTenantId());
        payOrderDTO.setReceiver(getReceiverTypeByOrderId(orderId));
        log.info("payOrderDTO={}", JSON.toJSONString(payOrderDTO));
        return remotePayService.wechatPrePay(payOrderDTO);
    }
    @Override
    public Boolean payOrderAfterNotify(ServiceOrderAfterPayDTO afterPayDTO) {
        log.info("payOrderAfterNotify 支付回调，{}", com.alibaba.fastjson2.JSON.toJSONString(afterPayDTO));

        String orderNo = afterPayDTO.getOrderNo();
        TeboServiceOrderDO serviceOrderDO = serviceOrderMapper.selectByOrderNo(orderNo);
        Integer queueBusinessType = serviceOrderDO.getQueueBusinessType();


        QueueBusinessTypeEnum anEnum = Objects.isNull(queueBusinessType) ? QueueBusinessTypeEnum.ARRIVED : QueueBusinessTypeEnum.getEnum(queueBusinessType);
        if (Objects.isNull(anEnum)) {
            throw new ServiceException("工单类型异常，请联系管理员");
        }
        if (QueueBusinessTypeEnum.getArriveTypeList().contains(anEnum.getCode())) {
            afterPayArrive(serviceOrderDO);
        } else if (QueueBusinessTypeEnum.getUnArrivedTypeList().contains(queueBusinessType)) {
            afterPayUnArrive(serviceOrderDO);
        }
        mathJifen(serviceOrderDO);
        // 使用券则把券设置为已使用
        List<TeboServiceOrderCouponDO> orderCouponDOList = serviceOrderCouponMapper.selectByServiceOrderId(serviceOrderDO.getId());
        if (orderCouponDOList.isEmpty()) {
            return true;
        }
        List<String> uniqueList = orderCouponDOList.stream().map(TeboServiceOrderCouponDO::getUniqueCode).collect(Collectors.toList());
        couponCustomerService.batchUpdateCouponStatus(uniqueList, 2);
        // 更新领用流水
        TeboCouponCustomerRecordDTO dto = new TeboCouponCustomerRecordDTO(uniqueList, 3,serviceOrderDO.getShopId(),serviceOrderDO.getShopName(),serviceOrderDO.getOrderNo());
        teboCouponManager.batchUpdateCouponRecordStatus(dto);

        return true;
    }



    /**
     * 算积分
     * @param serviceOrderDO
     */
    private void mathJifen(TeboServiceOrderDO serviceOrderDO) {
        TeboShop teboShop = remoteShopService.getShopInfo(serviceOrderDO.getShopId()).getData();
        if(Objects.isNull(teboShop)){
            throw new ServiceException("未匹配到门店");
        }
        BigDecimal integrationCoefficient = teboShop.getIntegrationCoefficient();

        ServiceOrderGoodsQueryDTO queryDTO = new ServiceOrderGoodsQueryDTO();
        queryDTO.setServiceOrderId(String.valueOf(serviceOrderDO.getId()));
        List<TeboServiceOrderGoodsDO> orderGoodsDOS = serviceOrderGoodsMapper.list(queryDTO);
        if (CollectionUtil.isNotEmpty(orderGoodsDOS)){
            for (TeboServiceOrderGoodsDO orderGoodsDO : orderGoodsDOS) {
                TeboGoodsQueryDTO remoteQuery = new TeboGoodsQueryDTO();
                remoteQuery.setGoodsIdList(Collections.singletonList(orderGoodsDO.getGoodsId()));
                List<TeboGoodsVO> goodsVOS = remoteGoodsService.list(remoteQuery).getData();
                if (CollectionUtil.isNotEmpty(goodsVOS)){
                    //算积分
                    Integer basicIntegral = goodsVOS.get(0).getBasicIntegral();
                    if (Objects.nonNull(basicIntegral) && basicIntegral > 0) {
                        if (Objects.isNull(integrationCoefficient)) {
                            throw new ServiceException("门店积分系数为空");
                        }
                        BigDecimal multiply = new BigDecimal(basicIntegral).multiply(integrationCoefficient);
                        TeboUserIntegralDTO userIntegralDTO =  new TeboUserIntegralDTO();
                        userIntegralDTO.setOrderId(serviceOrderDO.getId());
                        userIntegralDTO.setIntegral(multiply.intValue());
                        userIntegralDTO.setType(1);
                        userIntegralDTO.setUserAccount(serviceOrderDO.getShopId());
                        remoteIntegralOrderService.addIntegral(userIntegralDTO);
                    }
                }
            }
        }
    }


    @Override
    public R giftPackPrePay(Long orderId, String unionId) throws InterruptedException {
        TeboGiftPackOrderDO giftPackOrderDO = giftPackOrderMapper.selectById(orderId);
        TeboGiftPackDO giftPackDO = teboGiftPackMapper.selectById(giftPackOrderDO.getPackId());
        TeboConsumer consumer = remoteCustomerService.selectByUnionId(unionId).getData();
        PayOrderDTO payOrderDTO = new PayOrderDTO();
        payOrderDTO.setSource(1);
        payOrderDTO.setBusinessType(2);
        payOrderDTO.setOrderNo(giftPackOrderDO.getOrderNo());
        payOrderDTO.setAmount(giftPackOrderDO.getOrderAmount());
        payOrderDTO.setOpenId(consumer.getOpenid());
        payOrderDTO.setUnionId(unionId);
        payOrderDTO.setDescription("礼包订单支付");
        payOrderDTO.setTenantId(giftPackDO.getTenantId());
        if (giftPackDO.getType() == 3) {
            // 会员权益礼包收款到智联科技
            payOrderDTO.setReceiver(3);
        }else {
            payOrderDTO.setReceiver(2);
        }
        log.info("payOrderDTO={}", JSON.toJSONString(payOrderDTO));
        if (giftPackOrderDO.getOrderAmount() == 0) {
            GiftPackOrderAfterPayDTO afterPayDTO = new GiftPackOrderAfterPayDTO();
            afterPayDTO.setOrderNo(giftPackOrderDO.getOrderNo());
            payGiftPackAfterNotify(afterPayDTO);
            return R.ok(true);
        }
        if (giftPackDO.getType() == 3){
            return remotePayService.appletPay(payOrderDTO);
        }else {
            return remotePayService.wechatPrePay(payOrderDTO);
        }

    }

    /**
     * 团购
     */
    @Override
    @Transactional
    public R groupOrderPrePay(Long orderId, String unionId) {
        TeboGroupPurchaseOrderDO teboGroupPurchaseOrderDO = teboGroupPurchaseOrderMapper.selectById(orderId);
        if (ObjectUtil.isEmpty(teboGroupPurchaseOrderDO)){
            throw new ServiceException("订单不存在");
        }
        if (teboGroupPurchaseOrderDO.getOrderStatus() != 1){
            throw new ServiceException("订单状态不是待付款，不能发起付款");
        }
        TeboConsumer consumer = remoteCustomerService.selectByUnionId(unionId).getData();
        PayOrderDTO payOrderDTO = new PayOrderDTO();
        payOrderDTO.setSource(1);
        payOrderDTO.setBusinessType(11);
        teboGroupPurchaseOrderDO.setOrderNo(TeboNumberGenerator.generateAppletGroupOrderNo());
        payOrderDTO.setOrderNo(teboGroupPurchaseOrderDO.getOrderNo());
        payOrderDTO.setAmount(teboGroupPurchaseOrderDO.getOrderAmount());
        payOrderDTO.setOpenId(consumer.getOpenid());
        payOrderDTO.setUnionId(unionId);
        payOrderDTO.setDescription("c端小程序团购订单支付");
        payOrderDTO.setReceiver(3);
        teboGroupPurchaseOrderMapper.updateById(teboGroupPurchaseOrderDO);
        log.info("groupOrderPrePay={}", JSON.toJSONString(payOrderDTO));
        return remotePayService.appletPay(payOrderDTO);
    }

    @Override
    @Transactional
    public R groupOrderPrePayMerchant(Long orderId, String unionId,String openId) {
        TeboGroupPurchaseOrderDO teboGroupPurchaseOrderDO = teboGroupPurchaseOrderMapper.selectById(orderId);
        if (ObjectUtil.isEmpty(teboGroupPurchaseOrderDO)){
            throw new ServiceException("订单不存在");
        }
        if (teboGroupPurchaseOrderDO.getOrderStatus() != 1){
            throw new ServiceException("订单状态不是待付款，不能发起付款");
        }
        PayOrderDTO payOrderDTO = new PayOrderDTO();
        payOrderDTO.setSource(1);
        payOrderDTO.setBusinessType(11);
        teboGroupPurchaseOrderDO.setOrderNo(TeboNumberGenerator.generateAppletGroupOrderNo());
        payOrderDTO.setOrderNo(teboGroupPurchaseOrderDO.getOrderNo());
        payOrderDTO.setAmount(teboGroupPurchaseOrderDO.getOrderAmount());
        payOrderDTO.setOpenId(openId);
        payOrderDTO.setUnionId(unionId);
        payOrderDTO.setDescription("c端小程序团购订单支付");
        payOrderDTO.setReceiver(3);
        teboGroupPurchaseOrderMapper.updateById(teboGroupPurchaseOrderDO);
        log.info("groupOrderPrePayMerchant={}", JSON.toJSONString(payOrderDTO));
        return remotePayService.appletPayMerchant(payOrderDTO);
    }

    @Override
    public Boolean payGiftPackAfterNotify(GiftPackOrderAfterPayDTO afterPayDTO) {
        TeboGiftPackOrderDO teboGiftPackOrderDO = giftPackOrderMapper.selectByOrderNo(afterPayDTO.getOrderNo());
        teboGiftPackOrderDO.setOrderStatus(1);
        teboGiftPackOrderDO.setPayTime(LocalDateTime.now());
        giftPackOrderMapper.updateById(teboGiftPackOrderDO);
        // 异步发券
        disMqService.grantPackCoupon(teboGiftPackOrderDO.getPackId(), teboGiftPackOrderDO.getUnionId(),teboGiftPackOrderDO.getOrderNo());
        TeboGiftPackDO giftPackDO = teboGiftPackMapper.selectById(teboGiftPackOrderDO.getPackId());
        /**
         * 钜惠版分佣
         */
        if (giftPackDO.getId().toString().equals(PackIdEnum.JU_HUI.getCode())){
            /**
             * 天能久久卡-钜惠版 发放200积分
             */
            log.info("payGiftPpackAfterNotify addUserIntegral {}", teboGiftPackOrderDO.getUnionId());
            remoteIntegralOrderService.addUserIntegral(new TeboIntegralDTO(200, teboGiftPackOrderDO.getUnionId(),teboGiftPackOrderDO.getId()));
        }
        if (giftPackDO.getId().toString().equals(PackIdEnum.AN_XIN.getCode())){
            remoteIntegralOrderService.addUserIntegral(new TeboIntegralDTO(400, teboGiftPackOrderDO.getUnionId(),teboGiftPackOrderDO.getId()));
            TeboCouponCustomerVO teboCouponCustomerVO = couponCustomerService.getCustomerCycleCoupon(teboGiftPackOrderDO.getUnionId(),teboGiftPackOrderDO.getOrderNo());
            TeboConsumer consumer = remoteCustomerService.selectByUnionId(teboGiftPackOrderDO.getUnionId()).getData();
            if (ObjectUtil.isNotEmpty(teboCouponCustomerVO)){
              cycleInsuranceOrderService.synchronizeCycleOrder(new TeboCyclingOrderDTO(teboCouponCustomerVO.getUniqueCode(), consumer.getPhoneNumber()));
            }
        }
        new Thread(() ->commissionSharing(teboGiftPackOrderDO)).start();
        return true;
    }


    public Boolean groupPackOrderAfterNotify(GiftPackOrderAfterPayDTO afterPayDTO){
        TeboGroupPurchaseOrderDO teboGroupPurchaseOrderDO = teboGroupPurchaseOrderMapper.selectByOrderNo(afterPayDTO.getOrderNo());
        if (ObjectUtils.isEmpty(teboGroupPurchaseOrderDO)){
            throw new ServiceException("订单不存在");
        }
        teboGroupPurchaseOrderDO.setOrderStatus(2);
        teboGroupPurchaseOrderDO.setPayTime(LocalDateTime.now());
        /**
         * pc端团购需要二维码,商家版小程序不需要二维码
         */
        if (teboGroupPurchaseOrderDO.getShopId() == 0 ){
            R<String> groupOrderQrCode = remoteQrCodeService.generateGroupPurchaseOrderCode(teboGroupPurchaseOrderDO.getId());
            /**
             * 更新订单状态已支付，生成激活二维码，
             */

            if (Objects.nonNull(groupOrderQrCode) && StringUtils.isNotEmpty(groupOrderQrCode.getData())){
                teboGroupPurchaseOrderDO.setActiveQrCode(groupOrderQrCode.getData());
            }
        }
        teboGroupPurchaseOrderMapper.updateById(teboGroupPurchaseOrderDO);
        /**
         * 商家版小程序团购订单扣减积分
         */
        if (teboGroupPurchaseOrderDO.getShopId() != 0){
            R<TeboShop> shopR = remoteShopService.getShopInfo(teboGroupPurchaseOrderDO.getShopId());
            if (ObjectUtil.isNotEmpty(shopR) && ObjectUtil.isNotEmpty(shopR.getData())){
                TeboIntegralDTO teboIntegralDTO = new TeboIntegralDTO();
                teboIntegralDTO.setUnionId(shopR.getData().getCloudShopId());
                teboIntegralDTO.setIntegral(teboGroupPurchaseOrderDO.getTotalIntegral());
                teboIntegralDTO.setGroupBuyOrderId(teboGroupPurchaseOrderDO.getOrderNo());
                remoteIntegralOrderService.reduceGroupOrderIntegral(teboIntegralDTO);
            }
        }
        return true;
    }

    /**
     *  分佣&发放积分
     */
    void commissionSharing(TeboGiftPackOrderDO teboGiftPackOrderDO){{
        TeboCustomerPromoteRecordDO customerPromoteRecordDO = customerPromoteRecordMapper.selectByUnionId(teboGiftPackOrderDO.getUnionId());
        /*
         * 创建分账计划 供应商付款&推广人佣金
         */
        TeboWalletPlanDTO teboWalletPlanDTO = new TeboWalletPlanDTO();
        teboWalletPlanDTO.setOrderNo(teboGiftPackOrderDO.getOrderNo());
        List<TeboWalletPlanDetailDTO> detailList = new ArrayList<>();
        /**
         * 大米供应商打款
         */
        TeboWalletPlanDTO riceWallet = teboCommissionUtil.buildRiceParam(teboGiftPackOrderDO.getOrderNo());
        if (ObjectUtil.isNotEmpty(riceWallet)){
            detailList.addAll(riceWallet.getDetails());
        }

        /**
         * 骑行保
         */
        TeboWalletPlanDTO cycleWallet = teboCommissionUtil.buildCycleParam(teboGiftPackOrderDO.getOrderNo());
        if (ObjectUtil.isNotEmpty(cycleWallet)){
            detailList.addAll(cycleWallet.getDetails());
        }
        /**
         * 佣金
         */
        if (teboGiftPackOrderDO.getType() == 4 || teboGiftPackOrderDO.getType() == 5 ||  teboGiftPackOrderDO.getType() == 6 ){
            TeboWalletPlanDTO commissionWallet = teboCommissionUtil.buildCommissionParam(teboGiftPackOrderDO.getOrderNo());
            if (ObjectUtil.isNotEmpty(commissionWallet)){
                detailList.addAll(commissionWallet.getDetails());
            }
        }
        teboWalletPlanDTO.setDetails(detailList);
        if (!CollectionUtils.isEmpty(detailList)){
            log.info("payGiftPackAfterNotify splitPlan param :{}",JSONObject.toJSONString(teboWalletPlanDTO));
            remoteWalletService.splitPlan(teboWalletPlanDTO);
        }

        /**
         * 超服店分佣
         */
        log.info("payGiftPackAfterNotify customerPromoteRecordDO result :{}",JSONObject.toJSONString(customerPromoteRecordDO));
        if (ObjectUtil.isNotEmpty(teboGiftPackOrderDO) && (teboGiftPackOrderDO.getType() == 3 ||teboGiftPackOrderDO.getType()== 2)){
            R<TeboConsumer> customerR = remoteCustomerService.selectByUnionId(teboGiftPackOrderDO.getUnionId());
            CouponCommissionShareDTO commissionShareDTO = new CouponCommissionShareDTO();
            commissionShareDTO.setUnionId(teboGiftPackOrderDO.getUnionId());
            commissionShareDTO.setOrderNo(teboGiftPackOrderDO.getOrderNo());
            commissionShareDTO.setOrderId(teboGiftPackOrderDO.getId());
            commissionShareDTO.setPhoneNumber(customerR.getData().getPhoneNumber());
            commissionShareDTO.setPacketId(teboGiftPackOrderDO.getPackId());
            R<TeboShop> shopR = remoteShopService.getShopByPhoneNumber(customerPromoteRecordDO.getReferencePhone());
            int count = 1;
            if (Objects.isNull(shopR) || ObjectUtil.isEmpty(shopR.getData()) ){
                log.error("getShopByPhoneNumber error param:{}",customerPromoteRecordDO.getReferencePhone());
                while (count <= 5 && ObjectUtil.isEmpty(shopR.getData())) {
                    shopR = remoteShopService.getShopByPhoneNumber(customerPromoteRecordDO.getReferencePhone());
                    count++;
                    if (ObjectUtil.isNotEmpty(shopR)){
                        break;
                    }
                }
            }
            /**
             * 根据推广人手机号查询推广人门店id
             */
            if (ObjectUtil.isNotEmpty(shopR.getData())){
                commissionShareDTO.setCloudShopId(shopR.getData().getCloudShopId());
            }
            log.info("payGiftPackAfterNotify saleCardAmount param :{}",JSONObject.toJSONString(commissionShareDTO));
            remoteCommissionService.saleCardAmount(commissionShareDTO);
        }
     }
   }

    @Override
    public R<WechatPrepayResponse> serviceOrderBehalfPrePay(ServiceOrderPayDTO payDTO) {
        TeboServiceOrderDO serviceOrderDO = serviceOrderMapper.selectById(payDTO.getId());
        if (!Objects.equals(serviceOrderDO.getOrderStatus(), ServiceOrderStatusEnum.WAIT_PAY.getCode())) {
            throw new GlobalException("订单状态异常，不允许支付");
        }

        String reqUrl = AppletRequestUrl.JSON_CODE_2_SESSION + "?appid=" + appId + "&secret=" + secret + "&js_code=" + payDTO.getCode();
        log.info("reqUrl={}", reqUrl);
        String result = HttpTool.sendGet(reqUrl);
        log.info("result={}", result);
        JSONObject jsonObject = com.alibaba.fastjson2.JSON.parseObject(result);
        String openId = jsonObject.getString("openid");
        PayOrderDTO payOrderDTO = new PayOrderDTO();
        payOrderDTO.setSource(2);
        //默认维修工单支付
        payOrderDTO.setBusinessType(Objects.nonNull(payDTO.getBusinessType()) ? payDTO.getBusinessType() : PayWechatOrderBusinessTypeEnum.REPAIR.getCode());
        payOrderDTO.setOrderNo(serviceOrderDO.getOrderNo());
        payOrderDTO.setAmount(serviceOrderDO.getOrderAmount());
        payOrderDTO.setOpenId(openId);
        payOrderDTO.setDescription("服务工单代支付");
        payOrderDTO.setShopId(serviceOrderDO.getShopId());
        payOrderDTO.setShopName(serviceOrderDO.getShopName());
        payOrderDTO.setTenantId(serviceOrderDO.getTenantId());
        payOrderDTO.setReceiver(getReceiverTypeByOrderId(payDTO.getId()));
        return remotePayService.wechatPrePay(payOrderDTO);
    }

    @Override
    public R<WechatPrepayResponse> rescueOrderBehalfPrePay(RescueQueueOrderSaveDTO saveDTO) {
        TeboConsumer consumer = remoteCustomerService.selectByUnionId(saveDTO.getUnionid()).getData();
        PayOrderDTO payOrderDTO = new PayOrderDTO();
        payOrderDTO.setSource(1);
        payOrderDTO.setBusinessType(20);
        payOrderDTO.setOrderNo(String.valueOf(saveDTO.getId()));
        // 固定上门费服务费 20元
        payOrderDTO.setAmount(1);
        payOrderDTO.setOpenId(consumer.getOpenid());
        payOrderDTO.setDescription("救援上门服务费");
        // payOrderDTO.setShopId(0L);
        // payOrderDTO.setShopName("");
        payOrderDTO.setTenantId(saveDTO.getTenantId());
        payOrderDTO.setReceiver(2);
        payOrderDTO.setUniqueId(saveDTO.getId());
        log.info("rescueOrderBehalfPrePay  payOrderDTO{}", JSON.toJSONString(payOrderDTO));
        redisService.setCacheObject(TeboRescueCacheConstant.getRescueOrderBehalfPrePayKey(String.valueOf(saveDTO.getId())), saveDTO, 30L, TimeUnit.MINUTES);
        return remotePayService.wechatPrePay(payOrderDTO);
    }

    @Override
    public Boolean payRescueFeeAfterNotify(ServiceOrderAfterPayDTO afterPayDTO) {
        log.info("payRescueFeeAfterNotify  afterPayDTO{}", JSON.toJSONString(afterPayDTO));
        RescueQueueOrderSaveDTO saveDTO = redisService.getCacheObject(TeboRescueCacheConstant.getRescueOrderBehalfPrePayKey(afterPayDTO.getOrderNo()));
        log.info("payRescueFeeAfterNotify  saveDTO{}", JSON.toJSONString(saveDTO));
        if (!ObjectUtils.isEmpty(saveDTO)) {
            SpringUtils.getBean(ITeboRescueQueueOrderService.class).createRescueOrder(saveDTO);
        }
        return true;
    }

    @Override
    public R<Boolean> generalHfPayNotify(TeboGeneralPayNotifyDTO afterPayDTO) {
        String orderNo = afterPayDTO.getOrderNo();
        if (orderNo.startsWith("TBCXA")){
            TeboMallOrderDTO mallOrderDTO = new TeboMallOrderDTO();
            mallOrderDTO.setOrderNo(afterPayDTO.getOrderNo());
            return mallOrderWechatPayBackService.payMallOrderAfterNotify(mallOrderDTO);
        }
        return R.fail();
    }

    private void afterPayArrive(TeboServiceOrderDO serviceOrderDO) {


        LocalDateTime now = LocalDateTime.now();
        serviceOrderDO.setPayTime(now);
        serviceOrderDO.setPayType(0);

        serviceOrderDO.setActualPayAmount(serviceOrderDO.getOrderAmount());
        serviceOrderDO.setOrderStatus(ServiceOrderStatusEnum.COMPLETED.getCode());
        serviceOrderDO.setFinishTime(LocalDateTime.now());
        serviceOrderMapper.updateById(serviceOrderDO);

        TeboQueueOrderDO queueOrderDO = queueOrderMapper.selectById(serviceOrderDO.getQueueOrderId());

        if (Objects.equals(serviceOrderDO.getIsAgentPay(), 1)) {
            serviceOrderDO.setPayBy(serviceOrderDO.getAccountName());
        } else {
            if (Objects.nonNull(queueOrderDO)){
                serviceOrderDO.setPayBy(queueOrderDO.getNickName());
            }
        }
        if (Objects.isNull(queueOrderDO)){
            return;
        }
        queueOrderDO.setOrderStatus(QueueOrderStatusEnum.COMPLETED.getCode());
        queueOrderDO.setPayTime(now);
        queueOrderDO.setActualPayAmount(serviceOrderDO.getOrderAmount());
        queueOrderMapper.updateById(queueOrderDO);
    }

    private void afterPayUnArrive(TeboServiceOrderDO serviceOrderDO) {


        TeboRescueQueueOrderDO rescueQueueOrderDO = rescueQueueOrderManger.getById(serviceOrderDO.getQueueOrderId());

        LocalDateTime now = LocalDateTime.now();
        serviceOrderDO.setPayTime(now);
        serviceOrderDO.setPayType(0);
        if (Objects.equals(serviceOrderDO.getIsAgentPay(), 1)) {
            serviceOrderDO.setPayBy(serviceOrderDO.getAccountName());
        } else {
            if (Objects.nonNull(rescueQueueOrderDO)){
                serviceOrderDO.setPayBy(rescueQueueOrderDO.getNickName());
            }

        }
        serviceOrderDO.setActualPayAmount(serviceOrderDO.getOrderAmount());
        serviceOrderDO.setOrderStatus(ServiceOrderStatusEnum.COMPLETED.getCode());
        serviceOrderDO.setFinishTime(LocalDateTime.now());
        serviceOrderDO.setMaintainProcess(ServiceOrderMaintainProcessEnum.COMPLETED.getCode());
        serviceOrderMapper.updateById(serviceOrderDO);


        if (Objects.isNull(rescueQueueOrderDO)){
            return;
        }

        TeboRescueQueueOrderDO update = new TeboRescueQueueOrderDO();
        update.setId(serviceOrderDO.getQueueOrderId());
        update.setOrderStatus(QueueOrderStatusEnum.COMPLETED.getCode());
        update.setPayTime(now);
        update.setActualPayAmount(serviceOrderDO.getOrderAmount());
        rescueQueueOrderManger.updateById(update);
    }



    /**
     * 根据订单号获取支付对象
     * @param orderId
     * @return 1 门店 2 合伙人
     */
    private Integer getReceiverTypeByOrderId(Long orderId){
        ServiceOrderGoodsQueryDTO queryDTO = new ServiceOrderGoodsQueryDTO();
        queryDTO.setServiceOrderId(orderId.toString());
        List<TeboServiceOrderGoodsDO> orderGoodsList = serviceOrderGoodsMapper.list(queryDTO);
        // 查不到商品，说明商品是手填的
        if (CollectionUtils.isEmpty(orderGoodsList)) {
            return 1;
        }
        List goodIds = orderGoodsList.stream().map(TeboServiceOrderGoodsDO::getGoodsId).collect(Collectors.toList());
        goodIds.removeAll(Collections.singleton(null));
        if (CollectionUtils.isEmpty(goodIds)) {
            return 1;
        }
        // 根据商品ids 查询商品列表
        TeboGoodsQueryDTO goodsQueryDTO = new TeboGoodsQueryDTO();
        goodsQueryDTO.setGoodsIdList(goodIds);
        List<TeboGoodsVO> goodsVOList = remoteGoodsService.list(goodsQueryDTO).getData();
        if (CollectionUtils.isEmpty(goodsVOList)) {
            return 1;
        }
        List<String> secondCategoryList = Arrays.asList(secondCategoryId.split(","));
        // 未找到系统配置，默认支付到门店
        if (CollectionUtils.isEmpty(secondCategoryList)){
            return 1;
        }
        for (TeboGoodsVO teboGoodsVO : goodsVOList) {
            // 二级分类id包含系统配置的二级分类id，支付到合伙人（目前仅支持单商品，可以这么判断）
            if (secondCategoryList.contains(teboGoodsVO.getSecondCategoryId().toString())) {
                return 2;
            }
        }
        return 1;
    }
}
