package com.tebo.rescue.mapper;

import com.tebo.rescue.entity.TeboGiftPackCouponDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import feign.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 礼包卡券关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Mapper
public interface TeboGiftPackCouponMapper extends TeboBaseMapper<TeboGiftPackCouponDO> {

    int deleteByPackId(Long packId);

    List<TeboGiftPackCouponDO> selectByPackId(Long packId);

    List<TeboGiftPackCouponDO> selectByCouponIds(@Param("list") List<Long> couponIds);
}
