package com.tebo.rescue.remote.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class LstOrderDetailInfoVO {

    /**
     * 订单id
     */
    private Long id;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品数量
     */
    private Integer goodsNumber;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 门店编码
     */
    private String shopCode;

    /**
     * 订单状态 0待分配 1待接单 2服务中 3已完结 4已核销 5已结算 6已提现 7挂起 8已取消 9服务中-待审核
     */
    private Integer orderStatus;

    /**
     * 收货详细地址
     */
    private String customerAddress;

    /**
     * 收货人姓名
     */
    private String customerName;

    /**
     * 收货人电话
     */
    private String customerPhone;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 结算时间
     */
    private LocalDateTime settleDate;

    /**
     * 结算价格
     */
    private BigDecimal orderPrice;

    /**
     * 挂起备注
     */
    private String hangingRemarks;

    /**
     * 挂起原因
     */
    private String reasonHang;
}
