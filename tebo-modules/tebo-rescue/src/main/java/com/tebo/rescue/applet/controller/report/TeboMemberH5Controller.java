package com.tebo.rescue.applet.controller.report;

import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.domain.AjaxResult;
import com.tebo.rescue.service.report.TeboReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/member/h5/report")
public class TeboMemberH5Controller extends BaseController {
    @Resource
    private TeboReportService teboReportService;

    @GetMapping("/getSingleReport/{phoneNumber}")
    public AjaxResult getSingleReport(@PathVariable String phoneNumber) {
        return success(teboReportService.getSingleReport(phoneNumber));
    }


}
