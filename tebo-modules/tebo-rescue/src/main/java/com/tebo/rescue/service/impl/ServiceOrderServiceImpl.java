package com.tebo.rescue.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.enums.WeChatOfficialAccountEnum;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.core.utils.excel.ExcelUtil;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.security.util.MaintainerOnlineUserUtil;
import com.tebo.common.util.DataUtils;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.mall.api.RemoteGoodsService;
import com.tebo.mall.api.RemoteInsurancePolicyOrderService;
import com.tebo.mall.api.RemoteIntegralOrderService;
import com.tebo.mall.api.domain.dto.TeboGoodsQueryDTO;
import com.tebo.mall.api.domain.dto.TeboUserIntegralDTO;
import com.tebo.mall.api.domain.view.TeboGoodsVO;
import com.tebo.mall.api.domain.view.TeboServiceInsurancePolicyOrderVO;
import com.tebo.rescue.applet.domain.dto.*;
import com.tebo.rescue.applet.domain.dto.coupon.TeboCouponCheckDTO;
import com.tebo.rescue.applet.domain.view.*;
import com.tebo.rescue.applet.domain.view.excel.ServiceOrderListExcelVO;
import com.tebo.rescue.entity.*;
import com.tebo.rescue.enums.*;
import com.tebo.rescue.lst.domain.vo.TeboOrderGoodsIdGroup;
import com.tebo.rescue.manager.TeboCommentManger;
import com.tebo.rescue.manager.TeboCouponManager;
import com.tebo.rescue.manager.TeboOrderFileManger;
import com.tebo.rescue.manager.TeboRescueQueueOrderManger;
import com.tebo.rescue.manager.TeboServiceOrderManger;
import com.tebo.rescue.mapper.*;
import com.tebo.rescue.service.IServiceOrderService;
import com.tebo.rescue.service.TeboCouponCustomerService;
import com.tebo.rescue.service.mq.DisMqService;
import com.tebo.rescue.util.TeboNumberGenerator;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponCustomerRecordDTO;
import com.tebo.system.api.*;
import com.tebo.system.api.domain.dto.PayOrderDTO;
import com.tebo.system.api.domain.dto.RemoteConsumerQueryDTO;
import com.tebo.system.api.domain.dto.TeboShopQueryDTO;
import com.tebo.system.api.domain.dto.WxPushDTO;
import com.tebo.system.api.domain.view.PayWechatVO;
import com.tebo.system.api.domain.view.RemoteConsumerVO;
import com.tebo.system.api.domain.view.TeboShopListVO;
import com.tebo.system.api.model.TeboAccountInfoVO;
import com.tebo.system.api.model.TeboConsumer;
import com.tebo.system.api.model.TeboShop;
import com.tebo.system.api.remote.RemoteTeboConsumerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：zhengmk
 * @Date 2023/12/14 18:23
 */
@Slf4j
@Service
public class ServiceOrderServiceImpl extends ServiceImpl<TeboServiceOrderMapper, TeboServiceOrderDO> implements IServiceOrderService {

    @Value("${applet.appId_c}")
    private String appid;

    @Value("${applet.pay.page}")
    private String page;

    @Value("${applet.pay.template_id}")
    private String templateId;

    @Value("${applet.rescue.arrive_template_id}")
    private String arriveTemplateId;

    @Value("${applet.rescue.depart_template_id}")
    private String departTemplateId;

    @Resource
    private TeboServiceOrderMapper serviceOrderMapper;

    @Resource
    private TeboServiceOrderGoodsMapper serviceOrderGoodsMapper;

    @Resource
    private TeboServiceOrderCouponMapper serviceOrderCouponMapper;

    @Resource
    private TeboCouponCustomerMapper couponCustomerMapper;

    @Resource
    private TeboCouponMapper couponMapper;

    @Resource
    private TeboQueueOrderMapper queueOrderMapper;

    @Resource
    private RemoteCustomerService remoteCustomerService;

    @Resource
    private RemoteAccountService remoteAccountService;

    @Resource
    private RemotePayService remotePayService;

    @Resource
    private TeboCouponCustomerService couponCustomerService;

    @Resource
    private RemoteShopService remoteShopService;

    @Resource
    private RemoteWxPushService remoteWxPushService;

    @Autowired
    private TeboServiceOrderManger serviceOrderManger;

    @Autowired
    private TeboRescueQueueOrderManger rescueQueueOrderManger;

    @Autowired
    private TeboOrderFileManger orderFileManger;

    @Autowired
    private TeboCommentManger commentManger;
    @Resource
    private TeboCouponManager teboCouponManager;


    @Resource
    private RedisService redisService;

    @Resource
    private HttpServletRequest request;

    @Autowired
    private DisMqService disMqService;

    @Autowired
    private RemoteGoodsService remoteGoodsService;


    @Autowired
    private RemoteIntegralOrderService remoteIntegralOrderService;

    @Autowired
    private RemoteTeboConsumerService remoteTeboConsumerService;

    @Autowired
    private RemoteWxPayService remoteWxPayService;

    @Resource
    private RemoteInsurancePolicyOrderService insurancePolicyOrderService;


    @Transactional
    @Override
    public Map<String, Object> addServiceOrder(ServiceOrderDTO serviceOrderDTO) {
        if (serviceOrderDTO == null || serviceOrderDTO.getOrderType() == null
                || serviceOrderDTO.getQueueOrderId() == null) {
            throw new IllegalArgumentException("参数错误");
        }
        // 修改C端叫号单据状态
        TeboQueueOrderDO queueOrderDO = queueOrderMapper.selectById(serviceOrderDTO.getQueueOrderId());
        if (queueOrderDO == null || !Objects.equals(queueOrderDO.getOrderStatus(), QueueOrderStatusEnum.PROCESSING.getCode())) {
            throw new GlobalException("号码已过期或已处理");
        }
        // 校验卡券是否可用
        checkCouponRule(serviceOrderDTO);
        Long accountId = MaintainerOnlineUserUtil.getUserId();
        TeboAccountInfoVO accountInfoVO = remoteAccountService.getAccountInfoById(accountId).getData();
        TeboShop teboShop = remoteShopService.getShopInfo(queueOrderDO.getShopId()).getData();
        TeboServiceOrderDO serviceOrderDO = new TeboServiceOrderDO();
        BeanConvert.copy(serviceOrderDTO, serviceOrderDO);

        if (Objects.equals(serviceOrderDTO.getOrderType(), QueueOrderTypeEnum.WASH.getCode())) {
            serviceOrderDO.setServiceType(ServiceTypeEnum.WASH_CAR.getCode());
        }
        serviceOrderDO.setShopName(teboShop.getShopName());
        serviceOrderDO.setShopType(teboShop.getShopType());
        serviceOrderDO.setId(SnowFlakeUtil.nextId());
        serviceOrderDO.setOrderNo(TeboNumberGenerator.generateServiceOrderNo());
        serviceOrderDO.setAccountId(accountId);
        serviceOrderDO.setTenantId(accountInfoVO.getTenantId());
        serviceOrderDO.setNickName(queueOrderDO.getNickName());
        serviceOrderDO.setStationName(queueOrderDO.getStationName());
        serviceOrderDO.setPhoneNumber(queueOrderDO.getPhoneNumber());
        serviceOrderDO.setAccountName(accountInfoVO.getAccountName());
        serviceOrderDO.setOrderStatus(ServiceOrderStatusEnum.WAIT_PAY.getCode());
        serviceOrderDO.setBaseAmount(StringUtils.isEmpty(serviceOrderDTO.getBaseAmount()) ? 0 : MoneyUtil.yuanToFen(serviceOrderDTO.getBaseAmount()));
        String queueNumberStr = queueOrderDO.getQueueNumberStr();
        if (StringUtils.isNotEmpty(queueNumberStr) && queueNumberStr.startsWith("PRE")) {
            serviceOrderDO.setQueueBusinessType(QueueBusinessTypeEnum.PRE_STORE.getCode());
        } else {
            serviceOrderDO.setQueueBusinessType(QueueBusinessTypeEnum.ARRIVED.getCode());
        }

        serviceOrderDO.setOpenOrderTime(LocalDateTime.now());
        serviceOrderDO.setUnionid(queueOrderDO.getUnionid());
        List<TeboCouponDO> couponList;
        List<TeboCouponCustomerDO> couponCustomerDOS = new ArrayList<>();
        Map<Long, TeboCouponDO> idCouponMap = new HashMap<>();
        // 计算优惠
        if (serviceOrderDTO.getUniqueCodeList() != null && !serviceOrderDTO.getUniqueCodeList().isEmpty()) {
            couponCustomerDOS = couponCustomerMapper.getCouponListByUniqueList(serviceOrderDTO.getUniqueCodeList());
            List<Long> couponIdList = couponCustomerDOS.stream().map(TeboCouponCustomerDO::getCouponId).collect(Collectors.toList());
            couponList = couponMapper.selectBatchIds(couponIdList);
            idCouponMap = couponList.stream().collect(Collectors.toMap(TeboCouponDO::getId, item -> item));

        }

        // 维修
        List<ServiceOrderGoodsDTO> goodsList = serviceOrderDTO.getGoodsList() == null ? new ArrayList<>() : serviceOrderDTO.getGoodsList();

        List<TeboServiceOrderGoodsDO> serviceOrderGoodsDOList = new ArrayList<>();
        List<TeboServiceOrderCouponDO> serviceOrderCouponDOList = new ArrayList<>();
        BigDecimal sum = new BigDecimal(serviceOrderDO.getBaseAmount());
        for (ServiceOrderGoodsDTO goods : goodsList) {
            TeboServiceOrderGoodsDO orderGoodsDO = new TeboServiceOrderGoodsDO();
            BeanConvert.copy(goods, orderGoodsDO);
            orderGoodsDO.setAccountId(accountId);
            orderGoodsDO.setGoodsId(goods.getId());
            orderGoodsDO.setId(SnowFlakeUtil.nextId());
            orderGoodsDO.setServiceOrderId(serviceOrderDO.getId());
            orderGoodsDO.setShopId(serviceOrderDTO.getShopId());
            orderGoodsDO.setGoodsPrice(MoneyUtil.yuanToFen(goods.getGoodsPrice()));

            String tradeInPrice = goods.getTradeInPrice();
            //是以旧换新的场景  赋值换新价
            if (Boolean.TRUE.equals(serviceOrderDTO.getChangeNew()) && StringUtils.isNotEmpty(tradeInPrice)) {

                orderGoodsDO.setTradeInPrice(MoneyUtil.yuanToFen(tradeInPrice));
                BigDecimal goodsAmount = new BigDecimal(goods.getGoodsNum())
                        .multiply(new BigDecimal(MoneyUtil.yuanToFen(tradeInPrice)));
                orderGoodsDO.setGoodsAmount(goodsAmount.intValue());
                sum = sum.add(goodsAmount);
            } else {
                BigDecimal goodsAmount = new BigDecimal(goods.getGoodsNum())
                        .multiply(new BigDecimal(MoneyUtil.yuanToFen(goods.getGoodsPrice())));
                orderGoodsDO.setGoodsAmount(goodsAmount.intValue());
                sum = sum.add(goodsAmount);
            }

            serviceOrderGoodsDOList.add(orderGoodsDO);
        }
        for (TeboCouponCustomerDO teboCouponCustomerDO : couponCustomerDOS) {
            TeboCouponDO couponDO = idCouponMap.get(teboCouponCustomerDO.getCouponId());
            TeboServiceOrderCouponDO serviceOrderCouponDO = new TeboServiceOrderCouponDO();
            serviceOrderCouponDO.setId(SnowFlakeUtil.nextId());
            serviceOrderCouponDO.setCouponId(teboCouponCustomerDO.getCouponId());
            serviceOrderCouponDO.setServiceOrderId(serviceOrderDO.getId());

            serviceOrderCouponDO.setCouponName(couponDO.getCouponName());
            serviceOrderCouponDO.setCouponPrice(couponDO.getParValue());
            serviceOrderCouponDO.setPackId(teboCouponCustomerDO.getPackId());
            serviceOrderCouponDO.setCreateBy(accountInfoVO.getAccountName());
            serviceOrderCouponDO.setUpdateBy(accountInfoVO.getAccountName());
            serviceOrderCouponDO.setUniqueCode(teboCouponCustomerDO.getUniqueCode());
            serviceOrderCouponDO.setCouponCode(couponDO.getCouponCode());
            serviceOrderCouponDO.setDelFlag(0);
            serviceOrderCouponDOList.add(serviceOrderCouponDO);
            sum = sum.subtract(new BigDecimal(couponDO.getParValue()));
        }
        if (sum.compareTo(new BigDecimal(0)) <= 0
                && !Objects.equals(queueOrderDO.getOrderType(), QueueOrderTypeEnum.WASH.getCode())) {
            sum = new BigDecimal(0);
            serviceOrderDO.setOrderStatus(ServiceOrderStatusEnum.COMPLETED.getCode());
            serviceOrderDO.setFinishTime(LocalDateTime.now());


            mathJifen(serviceOrderDO);
        }
        // 折扣金额不为空
        if (!StringUtils.isEmpty(serviceOrderDTO.getDiscountAmount())) {
            int discountAmount = MoneyUtil.yuanToFen(serviceOrderDTO.getDiscountAmount());
            sum = sum.subtract(new BigDecimal(discountAmount));
            serviceOrderDO.setDiscountAmount(discountAmount);
        }
        if (sum.compareTo(new BigDecimal(0)) < 0) {
            sum = new BigDecimal(0);
        }
        serviceOrderDO.setOrderAmount(sum.intValue());

        if (Objects.equals(serviceOrderDTO.getOrderType(), QueueOrderTypeEnum.WASH.getCode())) {
            serviceOrderDO.setOrderType(QueueOrderTypeEnum.WASH.getCode());
        }
        int x = serviceOrderMapper.insert(serviceOrderDO);
        // 订单插入成功后，插入商品订单表和优惠券表
        if (x > 0) {
//            TeboCouponCheckDTO checkDTO = new TeboCouponCheckDTO();
//            checkDTO.setShopId(serviceOrderDO.getShopId());
//            checkDTO.setGoodsList();
//            checkDTO.setBaseAmount();
//            checkDTO.setUniqueCode();
//            couponCustomerService.checkCouponRule()


//            for (TeboServiceOrderCouponDO teboServiceOrderCouponDO : serviceOrderCouponDOList) {
//                // 检查是否达到满减金额
//
//            }


            // 金额不为0且不是代付时
            if (sum.compareTo(BigDecimal.ZERO) > 0 && Objects.equals(serviceOrderDO.getIsAgentPay(), 0)) {
                openOrderSendTemplateMessage(queueOrderDO.getUnionid(), queueOrderDO.getShopName(), serviceOrderDO);
            }
            if (!serviceOrderGoodsDOList.isEmpty()) {
                serviceOrderGoodsMapper.batchInsert(serviceOrderGoodsDOList);
            }

            if (!serviceOrderCouponDOList.isEmpty()) {
                serviceOrderCouponMapper.batchInsert(serviceOrderCouponDOList);
            }

            queueOrderDO.setOrderAmount(serviceOrderDO.getOrderAmount());
            queueOrderDO.setOrderStatus(QueueOrderStatusEnum.WAIT_PAY.getCode());
            queueOrderDO.setAccountId(accountId);
            queueOrderDO.setAccountName(accountInfoVO.getAccountName());
            queueOrderDO.setHandleResult(serviceOrderDTO.getHandleResult());
            queueOrderMapper.updateById(queueOrderDO);

            // 将优惠券占用
            if (serviceOrderDTO.getUniqueCodeList() != null && !serviceOrderDTO.getUniqueCodeList().isEmpty()) {
                couponCustomerService.batchUpdateCouponStatus(serviceOrderDTO.getUniqueCodeList(), 11);
            }
            if (sum.compareTo(new BigDecimal(0)) <= 0) {
                finishServiceOrder(queueOrderDO.getId());
            }
            Map<String, Object> map = new HashMap<>();
            map.put("serviceOrderId", serviceOrderDO.getId());
            map.put("needPay", sum.intValue() > 0 ? 1 : 0);
            return map;
        }

        throw new GlobalException("开单失败");
    }

    private void checkCouponRule(ServiceOrderDTO serviceOrderDTO) {
        if (CollectionUtils.isEmpty(serviceOrderDTO.getUniqueCodeList())) {
            return;
        }
        TeboCouponCheckDTO checkDTO = new TeboCouponCheckDTO();
        checkDTO.setShopId(serviceOrderDTO.getShopId());
        checkDTO.setGoodsList(serviceOrderDTO.getGoodsList());
        checkDTO.setBaseAmount(serviceOrderDTO.getBaseAmount());
        for (String uniqueCode : serviceOrderDTO.getUniqueCodeList()) {
            checkDTO.setUniqueCode(uniqueCode);
            couponCustomerService.checkCouponRule(checkDTO);
        }
    }

    @Transactional
    @Override
    public Map<String, Object> rescueOpenOrder(ServiceOrderDTO serviceOrderDTO) {
        if (serviceOrderDTO == null || serviceOrderDTO.getQueueOrderId() == null || Objects.isNull(serviceOrderDTO.getId())) {
            throw new IllegalArgumentException("参数错误");
        }
        // 修改C端叫号单据状态
        TeboRescueQueueOrderDO rescueQueueOrderDO = rescueQueueOrderManger.getById(serviceOrderDTO.getQueueOrderId());
        if (rescueQueueOrderDO == null || !Objects.equals(rescueQueueOrderDO.getOrderStatus(), QueueOrderStatusEnum.PROCESSING.getCode())) {
            throw new GlobalException("号码已过期或已处理");
        }

        Long accountId = MaintainerOnlineUserUtil.getUserId();
        TeboAccountInfoVO accountInfoVO = remoteAccountService.getAccountInfoById(accountId).getData();
        if (Objects.isNull(accountInfoVO)) {
            throw new ServiceException("获取师傅信息失败，请联系管理员");
        }
        TeboServiceOrderDO serviceOrderDO = new TeboServiceOrderDO();
        BeanConvert.copy(serviceOrderDTO, serviceOrderDO);
        serviceOrderDO.setOrderStatus(ServiceOrderStatusEnum.WAIT_PAY.getCode());
        serviceOrderDO.setBaseAmount(StringUtils.isEmpty(serviceOrderDTO.getBaseAmount()) ? 0 : MoneyUtil.yuanToFen(serviceOrderDTO.getBaseAmount()));
        serviceOrderDO.setOpenOrderTime(LocalDateTime.now());
        serviceOrderDO.setOrderNo(getById(serviceOrderDTO.getId()).getOrderNo());
        List<TeboCouponDO> couponList;
        List<TeboCouponCustomerDO> couponCustomerDOS = new ArrayList<>();
        Map<Long, TeboCouponDO> idCouponMap = new HashMap<>();
        // 计算优惠
        if (serviceOrderDTO.getUniqueCodeList() != null && !serviceOrderDTO.getUniqueCodeList().isEmpty()) {
            couponCustomerDOS = couponCustomerMapper.getCouponListByUniqueList(serviceOrderDTO.getUniqueCodeList());
            List<Long> couponIdList = couponCustomerDOS.stream().map(TeboCouponCustomerDO::getCouponId).collect(Collectors.toList());
            couponList = couponMapper.selectBatchIds(couponIdList);
            idCouponMap = couponList.stream().collect(Collectors.toMap(TeboCouponDO::getId, item -> item));

        }

        // 维修
        List<ServiceOrderGoodsDTO> goodsList = serviceOrderDTO.getGoodsList() == null ? new ArrayList<>() : serviceOrderDTO.getGoodsList();

        List<TeboServiceOrderGoodsDO> serviceOrderGoodsDOList = new ArrayList<>();
        List<TeboServiceOrderCouponDO> serviceOrderCouponDOList = new ArrayList<>();
        BigDecimal sum = new BigDecimal(serviceOrderDO.getBaseAmount());
        for (ServiceOrderGoodsDTO goods : goodsList) {
            TeboServiceOrderGoodsDO orderGoodsDO = new TeboServiceOrderGoodsDO();
            BeanConvert.copy(goods, orderGoodsDO);
            orderGoodsDO.setAccountId(accountId);
            orderGoodsDO.setGoodsId(goods.getId());
            orderGoodsDO.setId(SnowFlakeUtil.nextId());
            orderGoodsDO.setServiceOrderId(serviceOrderDO.getId());
            orderGoodsDO.setShopId(serviceOrderDTO.getShopId());
            orderGoodsDO.setGoodsPrice(MoneyUtil.yuanToFen(goods.getGoodsPrice()));

            String tradeInPrice = goods.getTradeInPrice();
            //是以旧换新的场景  赋值换新价
            if (Boolean.TRUE.equals(serviceOrderDTO.getChangeNew()) && StringUtils.isNotEmpty(tradeInPrice)) {

                orderGoodsDO.setTradeInPrice(MoneyUtil.yuanToFen(tradeInPrice));
                BigDecimal goodsAmount = new BigDecimal(goods.getGoodsNum())
                        .multiply(new BigDecimal(MoneyUtil.yuanToFen(tradeInPrice)));
                orderGoodsDO.setGoodsAmount(goodsAmount.intValue());
                sum = sum.add(goodsAmount);
            } else {
                BigDecimal goodsAmount = new BigDecimal(goods.getGoodsNum())
                        .multiply(new BigDecimal(MoneyUtil.yuanToFen(goods.getGoodsPrice())));
                orderGoodsDO.setGoodsAmount(goodsAmount.intValue());
                sum = sum.add(goodsAmount);
            }

            serviceOrderGoodsDOList.add(orderGoodsDO);
        }
        for (TeboCouponCustomerDO teboCouponCustomerDO : couponCustomerDOS) {
            TeboCouponDO couponDO = idCouponMap.get(teboCouponCustomerDO.getCouponId());
            TeboServiceOrderCouponDO serviceOrderCouponDO = new TeboServiceOrderCouponDO();
            serviceOrderCouponDO.setId(SnowFlakeUtil.nextId());
            serviceOrderCouponDO.setCouponId(teboCouponCustomerDO.getCouponId());
            serviceOrderCouponDO.setServiceOrderId(serviceOrderDO.getId());
            serviceOrderCouponDO.setCouponName(couponDO.getCouponName());
            serviceOrderCouponDO.setCouponPrice(couponDO.getParValue());
            serviceOrderCouponDO.setPackId(teboCouponCustomerDO.getPackId());
            serviceOrderCouponDO.setCreateBy(accountInfoVO.getAccountName());
            serviceOrderCouponDO.setUpdateBy(accountInfoVO.getAccountName());
            serviceOrderCouponDO.setUniqueCode(teboCouponCustomerDO.getUniqueCode());
            serviceOrderCouponDO.setCouponCode(couponDO.getCouponCode());
            serviceOrderCouponDO.setDelFlag(0);
            serviceOrderCouponDOList.add(serviceOrderCouponDO);
            sum = sum.subtract(new BigDecimal(couponDO.getParValue()));
        }
        if (sum.compareTo(new BigDecimal(0)) <= 0) {
            sum = new BigDecimal(0);
            serviceOrderDO.setOrderStatus(ServiceOrderStatusEnum.COMPLETED.getCode());
            serviceOrderDO.setFinishTime(LocalDateTime.now());
            serviceOrderDO.setMaintainProcess(ServiceOrderMaintainProcessEnum.COMPLETED.getCode());

            mathJifen(serviceOrderDO);
        }
        // 折扣金额不为空
        if (!StringUtils.isEmpty(serviceOrderDTO.getDiscountAmount())) {
            Integer yuanToFen = MoneyUtil.yuanToFen(serviceOrderDTO.getDiscountAmount());
            sum = sum.subtract(new BigDecimal(yuanToFen));
            serviceOrderDO.setDiscountAmount(yuanToFen);
        }

        if (sum.compareTo(new BigDecimal(0)) < 0) {
            sum = new BigDecimal(0);
        }
        serviceOrderDO.setOrderAmount(sum.intValue());


        int x = serviceOrderMapper.updateById(serviceOrderDO);
        // 订单插入成功后，插入商品订单表和优惠券表
        if (x > 0) {
            // 金额不为0且不是代付时
            if (sum.compareTo(BigDecimal.ZERO) > 0 && Objects.equals(serviceOrderDO.getIsAgentPay(), 0)) {
                openOrderSendTemplateMessage(rescueQueueOrderDO.getUnionid(), rescueQueueOrderDO.getShopName(), serviceOrderDO);
            }
            if (!serviceOrderGoodsDOList.isEmpty()) {
                serviceOrderGoodsMapper.batchInsert(serviceOrderGoodsDOList);
            }

            if (!serviceOrderCouponDOList.isEmpty()) {
                serviceOrderCouponMapper.batchInsert(serviceOrderCouponDOList);
            }

            rescueQueueOrderDO.setOrderAmount(serviceOrderDO.getOrderAmount());
            rescueQueueOrderDO.setOrderStatus(QueueOrderStatusEnum.WAIT_PAY.getCode());
            rescueQueueOrderDO.setAccountId(accountId);
            rescueQueueOrderDO.setAccountName(accountInfoVO.getAccountName());
            rescueQueueOrderManger.updateById(rescueQueueOrderDO);

            // 将优惠券占用
            if (serviceOrderDTO.getUniqueCodeList() != null && !serviceOrderDTO.getUniqueCodeList().isEmpty()) {
                couponCustomerService.batchUpdateCouponStatus(serviceOrderDTO.getUniqueCodeList(), 11);
            }
            if (sum.compareTo(new BigDecimal(0)) <= 0) {
                finishRescueQueueServiceOrder(rescueQueueOrderDO.getId());
            }
            Map<String, Object> map = new HashMap<>();
            map.put("serviceOrderId", serviceOrderDO.getId());
            map.put("needPay", sum.intValue() > 0 ? 1 : 0);
            return map;
        }

        throw new GlobalException("开单失败");
    }


    private void openOrderSendTemplateMessage(String unionId, String shopName, TeboServiceOrderDO serviceOrderDO) {

        WxPushDTO pushDTO = new WxPushDTO();
        pushDTO.setTemplate_id(templateId);
        pushDTO.setAppid(appid);
        pushDTO.setPagepath(page);
        pushDTO.setUnionid(unionId);
        pushDTO.setOfficialAccount("泰博出行");
        HashMap<String, Object> data = new HashMap<>();
        JSONObject character_string11 = new JSONObject();
        character_string11.put("value", serviceOrderDO.getOrderNo());
        data.put("character_string11", character_string11);
        JSONObject amount9 = new JSONObject();
        amount9.put("value", MoneyUtil.fenToYuan(serviceOrderDO.getOrderAmount()) + "元");
        data.put("amount9", amount9);
        JSONObject thing17 = new JSONObject();
        thing17.put("value", shopName);
        data.put("thing17", thing17);
        pushDTO.setData(data);
        log.info("pushDTO={}", JSON.toJSONString(pushDTO));
        remoteWxPushService.sendMessage(pushDTO);
    }


    @Override
    public List<ServiceTypeListVO> listServiceType(ServiceTypeQueryDTO serviceTypeQueryDTO) {
        if (serviceTypeQueryDTO == null || serviceTypeQueryDTO.getOrderType() == null) {
            throw new IllegalArgumentException("参数错误");
        }
        return ServiceTypeEnum.getServiceTypeEnumByServiceType(serviceTypeQueryDTO.getOrderType()).stream()
                .map(item -> {
                    ServiceTypeListVO listVO = new ServiceTypeListVO();
                    listVO.setServiceType(item.getCode());
                    listVO.setName(item.getName());
                    return listVO;
                }).collect(Collectors.toList());
    }

    @Override
    public List<ServiceOrderListVO> listServiceOrder(ServiceOrderQueryDTO queryDTO) {
        if (queryDTO == null) {
            throw new IllegalArgumentException("参数错误");
        }

        List<TeboServiceOrderDO> serviceOrderDOList = serviceOrderMapper.list(queryDTO);
        List<Long> shopIdList = serviceOrderDOList.stream().map(item -> item.getShopId()).collect(Collectors.toList());
        Map<Long, TeboShopListVO> shopMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(shopIdList)) {
            TeboShopQueryDTO teboShopQueryDTO = new TeboShopQueryDTO();
            teboShopQueryDTO.setIdList(shopIdList);
            R<List<TeboShopListVO>> shopResult = remoteShopService.fullyQuery(teboShopQueryDTO);
            List<TeboShopListVO> shopList = shopResult.getData();
            if (CollectionUtil.isNotEmpty(shopList)) {
                shopMap = DataUtils.listToMap(shopList, TeboShopListVO::getId);
            }
        }
        List<ServiceOrderListVO> resList = BeanConvert.copyList(serviceOrderDOList, ServiceOrderListVO::new);
        //到店的ids
        List<Long> arriveIDs = resList.stream()
                .filter(e -> QueueBusinessTypeEnum.getArriveTypeList().contains(e.getQueueBusinessType())).map(ServiceOrderListVO::getQueueOrderId).collect(Collectors.toSet()).stream().collect(Collectors.toList());
        //救援的ids
        List<Long> unArriveIDs = resList.stream()
                .filter(e -> QueueBusinessTypeEnum.getUnArrivedTypeList().contains(e.getQueueBusinessType())).map(ServiceOrderListVO::getQueueOrderId).collect(Collectors.toSet()).stream().collect(Collectors.toList());


        //到店
        List<TeboQueueOrderDO> queueOrderDOS = new ArrayList<>();
        DataUtils.splitHandle(arriveIDs, (list) -> {
            queueOrderDOS.addAll(queueOrderMapper.selectBatchIds(list));
        }, 200);

        //救援
        List<TeboRescueQueueOrderDO> rescueQueueOrderDOS = new ArrayList<>();
        DataUtils.splitHandle(unArriveIDs, (list) -> {
            RescueQueueOrderQueryDTO queueOrderQueryDTO = new RescueQueueOrderQueryDTO();
            queueOrderQueryDTO.setIds(list);
            rescueQueueOrderDOS.addAll(rescueQueueOrderManger.listBySO(queueOrderQueryDTO));
        }, 200);

        Map<Long, TeboRescueQueueOrderDO> rescueQueueOrderDOMap = DataUtils.listToMap(rescueQueueOrderDOS, TeboRescueQueueOrderDO::getId);

        Map<Long, TeboQueueOrderDO> queueOrderDOMap = DataUtils.listToMap(queueOrderDOS, TeboQueueOrderDO::getId);


        ArrayList<String> unionIds = new ArrayList<>(queueOrderDOS.stream().map(TeboQueueOrderDO::getUnionid).collect(Collectors.toSet()));
        List<RemoteConsumerVO> allConsumerList = new ArrayList<>();
        DataUtils.splitHandle(unionIds, splitList -> {
            RemoteConsumerQueryDTO remoteConsumerQueryDTO = new RemoteConsumerQueryDTO();
            remoteConsumerQueryDTO.setUnionIds(splitList);
            List<RemoteConsumerVO> consumerVOS = remoteTeboConsumerService.getConsumerList(remoteConsumerQueryDTO).getData();
            allConsumerList.addAll(consumerVOS);

        }, 200);
        //有重复数据
//        Map<String, RemoteConsumerVO> consumerVOMap = DataUtils.listToMap(allConsumerList, RemoteConsumerVO::getUnionid);
        Map<String, List<RemoteConsumerVO>> stringListMap = DataUtils.listToGroup(allConsumerList, RemoteConsumerVO::getUnionid);


        Map<Long, TeboShopListVO> finalShopMap = shopMap;
        resList.forEach(item -> {
            TeboShopListVO shopListVO = finalShopMap.get(item.getShopId());
            if (ObjectUtil.isNotEmpty(shopListVO)) {
                item.setShopPhoneNumber(shopListVO.getPhoneNumber());
            }
            Integer queueBusinessType = item.getQueueBusinessType();

            QueueBusinessTypeEnum anEnum = Objects.isNull(queueBusinessType) ? QueueBusinessTypeEnum.ARRIVED : QueueBusinessTypeEnum.getEnum(queueBusinessType);
            if (Objects.isNull(anEnum)) {
                return;
            }
            if (QueueBusinessTypeEnum.getArriveTypeList().contains(anEnum.getCode())) {
                TeboQueueOrderDO teboQueueOrderDO = queueOrderDOMap.get(item.getQueueOrderId());
                listServiceOrderVOArrived(item, teboQueueOrderDO, shopListVO, stringListMap);
            } else if (QueueBusinessTypeEnum.getUnArrivedTypeList().contains(queueBusinessType)) {
                TeboRescueQueueOrderDO rescueQueueOrderDO = rescueQueueOrderDOMap.get(item.getQueueOrderId());
                listServiceOrderVOUnArrived(item, rescueQueueOrderDO, shopListVO);
            }
            if (Objects.isNull(item.getQueueOrderType())) {
                item.setQueueOrderType(0);
            }

        });
        return resList;
    }

    @Override
    public void exportServiceOrder(HttpServletResponse response, ServiceOrderQueryDTO queryDTO) {
        long beforeQuery = System.currentTimeMillis();
        List<ServiceOrderListVO> orderListVOS = listServiceOrder(queryDTO);
        long beforeChange = System.currentTimeMillis();
        long queryTime = beforeChange - beforeQuery;
        log.info("查询list的耗时" + queryTime);

        List<ServiceOrderListExcelVO> listExcelVOS = BeanConvert.copyList(orderListVOS, ServiceOrderListExcelVO::new);
        for (ServiceOrderListExcelVO listExcelVO : listExcelVOS) {
            Integer orderType = listExcelVO.getOrderType();
            if (Objects.nonNull(orderType)) {
                QueueOrderTypeEnum anEnum = QueueOrderTypeEnum.getQueueOrderTypeEnum(orderType);
                if (Objects.nonNull(anEnum)) {
                    listExcelVO.setOrderTypeName(anEnum.getName());
                }
            }

            Integer orderStatus = listExcelVO.getOrderStatus();
            if (Objects.nonNull(orderStatus)) {
                ServiceOrderStatusEnum anEnum = ServiceOrderStatusEnum.getEnum(orderStatus);
                if (Objects.nonNull(anEnum)) {
                    listExcelVO.setOrderStatusName(anEnum.getMessage());
                }
            }

            Integer shopType = listExcelVO.getShopType();
            if (Objects.nonNull(shopType)) {
                ShopTypeEnum anEnum = ShopTypeEnum.getEnum(shopType);
                if (Objects.nonNull(anEnum)) {
                    listExcelVO.setShopTypeName(anEnum.getMsg());
                }
            }
            Boolean isQuickOrder = listExcelVO.getIsQuickOrder();
            if (Boolean.TRUE.equals(isQuickOrder)) {
                listExcelVO.setIsQuickOrderName("是");
            } else {
                listExcelVO.setIsQuickOrderName("否");
            }

            Integer queueBusinessType = listExcelVO.getQueueBusinessType();
            if (Objects.nonNull(queueBusinessType)) {
                QueueBusinessTypeEnum anEnum = QueueBusinessTypeEnum.getEnum(queueBusinessType);
                if (Objects.nonNull(anEnum)) {
                    listExcelVO.setQueueBusinessName(anEnum.getMessage());
                }
            }
            LocalDateTime cancelTime = listExcelVO.getCancelTime();
            listExcelVO.setCancel(Objects.isNull(cancelTime) ? "否" : "是");

        }
        long beforeExport = System.currentTimeMillis();
        long changeTime = beforeExport - beforeChange;

        log.info("查询后转换的耗时" + changeTime);


        ExcelUtil.exportExcelToResponse(response, listExcelVOS, ServiceOrderListExcelVO.class, "工单导出");
        long exportAfter = System.currentTimeMillis();
        long exportTime = exportAfter - beforeExport;
        log.info("easyexcel耗时" + exportTime);

    }

    @Override
    public ServiceOrderCountVO customerCount(String unionId) {
        if (StringUtils.isEmpty(unionId)) {
            throw new ServiceException("未指定用户，请联系管理员");
        }
        //todo 有空加多线程
        ServiceOrderCountVO countVO = new ServiceOrderCountVO();
        ServiceOrderQueryDTO queryDTO = new ServiceOrderQueryDTO();
        queryDTO.setUnionid(unionId);
        queryDTO.setOrderStatus(ServiceOrderStatusEnum.CANCEL.getCode());
        countVO.setCancel(serviceOrderManger.count(queryDTO));
        queryDTO.setOrderStatus(ServiceOrderStatusEnum.WAIT_PAY.getCode());
        countVO.setWaitPay(serviceOrderManger.count(queryDTO));
        queryDTO.setOrderStatus(ServiceOrderStatusEnum.IN_MAINTENANCE.getCode());
        countVO.setProcessing(serviceOrderManger.count(queryDTO));
        queryDTO.setOrderStatus(ServiceOrderStatusEnum.COMPLETED.getCode());
        countVO.setUnComment(serviceOrderManger.count(queryDTO));

        return countVO;
    }

    private void listServiceOrderVOUnArrived(ServiceOrderListVO item, TeboRescueQueueOrderDO rescueQueueOrderDO, TeboShopListVO shopListVO) {

        if (shopListVO != null) {
            if (StringUtils.isNotEmpty(shopListVO.getAreaName()) && StringUtils.isNotEmpty(shopListVO.getAddress())) {
                item.setShopAddress(shopListVO.getAreaName().replace("-", "") + shopListVO.getAddress());
                item.setShopType(shopListVO.getShopType());
            }
        }

        if (Objects.isNull(rescueQueueOrderDO)) {
            return;
        }
//        TeboShop shop = remoteShopService.getShopInfo(item.getShopId()).getData();


        item.setQueueOrderType(rescueQueueOrderDO.getOrderType());

        item.setQueueOrderNo(rescueQueueOrderDO.getOrderNo());
        item.setPreDoorTime(rescueQueueOrderDO.getPreDoorTime());
        item.setRequestLocation(rescueQueueOrderDO.getRequestLocation());
        item.setFaultMsg(rescueQueueOrderDO.getFaultMsg());
        item.setLgt(rescueQueueOrderDO.getLat());
        item.setLnt(rescueQueueOrderDO.getLon());
        item.setAccountPhone(rescueQueueOrderDO.getAccountPhone());
        item.setInitiationTime(rescueQueueOrderDO.getCreateTime());
    }

    private void listServiceOrderVOArrived(ServiceOrderListVO item, TeboQueueOrderDO queueOrderDO, TeboShopListVO shopListVO, Map<String, List<RemoteConsumerVO>> stringListMap) {

        if (shopListVO != null) {
            if (StringUtils.isNotEmpty(shopListVO.getAreaName()) && StringUtils.isNotEmpty(shopListVO.getAddress())) {
                item.setShopAddress(shopListVO.getAreaName().replace("-", "") + shopListVO.getAddress());
                item.setShopType(shopListVO.getShopType());
            }
        }
        if (Objects.isNull(queueOrderDO)) {
            return;
        }

        List<RemoteConsumerVO> remoteConsumerVOS = stringListMap.get(queueOrderDO.getUnionid());


        item.setQueueOrderType(queueOrderDO.getOrderType());
        if (CollectionUtil.isNotEmpty(remoteConsumerVOS)) {
            item.setNickName(remoteConsumerVOS.get(0).getNickName());
        }

        item.setQueueOrderTime(queueOrderDO.getCreateTime());
        item.setCallTime(queueOrderDO.getCallTime());
        //预约到店类型的，手动赋值预约到店 因为历史数据可能为空
        Integer pickupType = queueOrderDO.getPickupType();
        if (Objects.nonNull(pickupType)) {
            if (PickupTypeEnum.PICKUP_TYPE_ONSITE.getCode() == pickupType) {
                item.setQueueBusinessType(QueueBusinessTypeEnum.ARRIVED.getCode());
            } else {
                item.setQueueBusinessType(QueueBusinessTypeEnum.PRE_STORE.getCode());
            }
        }
        item.setQueueOrderId(queueOrderDO.getId());
        item.setQueueOrderNo(queueOrderDO.getOrderNo());
        item.setInitiationTime(queueOrderDO.getCreateTime());

    }

    @Transactional
    @Override
    public Boolean finishServiceOrder(Long queueOrderId) {
        if (queueOrderId == null) {
            throw new IllegalArgumentException("参数错误");
        }
        TeboQueueOrderDO queueOrderDO = queueOrderMapper.selectById(queueOrderId);
        if (queueOrderDO == null) {
            throw new GlobalException("订单不存在");
        }
        if (!Objects.equals(queueOrderDO.getOrderStatus(), QueueOrderStatusEnum.WAIT_PAY.getCode())) {
            throw new GlobalException("只有待付款订单可以关闭");
        }


        TeboServiceOrderDO teboServiceOrderDO = serviceOrderMapper.selectByQueueOrderId(queueOrderId);
        if (teboServiceOrderDO == null) {
            throw new GlobalException("订单不存在");
        }
        // if (ServiceItemEnum.WASH.getCode() != queueOrderDO.getOrderType()) {
        if (teboServiceOrderDO.getOrderAmount() > 0) {
            R<PayWechatVO> payResult = remotePayService.getPayWechatRecordByOrder(teboServiceOrderDO.getOrderNo());
            if (Objects.isNull(payResult.getData())) {
                throw new GlobalException("当前工单没有支付流水，无法关闭");
            }
        }

        // }


        // 更新消费者订单状态
        queueOrderDO.setActualPayAmount(queueOrderDO.getOrderAmount());
        queueOrderDO.setPayTime(LocalDateTime.now());
        queueOrderDO.setOrderStatus(QueueOrderStatusEnum.COMPLETED.getCode());
        queueOrderMapper.updateById(queueOrderDO);

        // 更新师傅端工单状态
        teboServiceOrderDO.setActualPayAmount(teboServiceOrderDO.getOrderAmount());
        teboServiceOrderDO.setPayTime(LocalDateTime.now());
        teboServiceOrderDO.setPayType(0);
        teboServiceOrderDO.setOrderStatus(ServiceOrderStatusEnum.COMPLETED.getCode());
        teboServiceOrderDO.setFinishTime(LocalDateTime.now());
        if (Objects.equals(teboServiceOrderDO.getIsAgentPay(), 1)) {
            teboServiceOrderDO.setPayBy(teboServiceOrderDO.getAccountName());
        } else {
            teboServiceOrderDO.setPayBy(teboServiceOrderDO.getNickName());
        }
        //手动关单，更新订单支付状态，并分佣
        remotePayService.updatePayementSuccessStatus(teboServiceOrderDO.getOrderNo());
        boolean res = serviceOrderMapper.updateById(teboServiceOrderDO) > 0;
        if (res) {
            // 使用券则把券设置为已使用
            List<TeboServiceOrderCouponDO> orderCouponDOList = serviceOrderCouponMapper.selectByServiceOrderId(teboServiceOrderDO.getId());
            if (orderCouponDOList.isEmpty()) {
                return true;
            }
            List<String> uniqueList = orderCouponDOList.stream().map(TeboServiceOrderCouponDO::getUniqueCode).collect(Collectors.toList());
            couponCustomerService.batchUpdateCouponStatus(uniqueList, 2);
            TeboCouponCustomerRecordDTO dto = new TeboCouponCustomerRecordDTO(uniqueList, 3, queueOrderDO.getShopId(), queueOrderDO.getShopName(), teboServiceOrderDO.getOrderNo());
            teboCouponManager.batchUpdateCouponRecordStatus(dto);
        }

        String phoneCall = getPhoneCall(teboServiceOrderDO.getPhoneNumber());
        String typeName = getTypeName(teboServiceOrderDO.getOrderType());
        String content = "请手机尾号 " + phoneCall + " 的客户到"
                + teboServiceOrderDO.getStationName() + "号" + typeName + "工位取车";
        disMqService.notifyAppMsg(teboServiceOrderDO.getShopId(), content);


        mathJifen(teboServiceOrderDO);
        return res;
    }

    @Transactional
    @Override
    public Boolean finishRescueQueueServiceOrder(Long queueOrderId) {
        if (queueOrderId == null) {
            throw new IllegalArgumentException("参数错误");
        }
        TeboRescueQueueOrderDO rescueQueueOrderDO = rescueQueueOrderManger.getById(queueOrderId);
        if (rescueQueueOrderDO == null) {
            throw new GlobalException("订单不存在");
        }
        if (!Objects.equals(rescueQueueOrderDO.getOrderStatus(), QueueOrderStatusEnum.WAIT_PAY.getCode())) {
            throw new GlobalException("只有待付款订单可以关闭");
        }
        TeboServiceOrderDO teboServiceOrderDO = serviceOrderMapper.selectByQueueOrderId(queueOrderId);
        if (teboServiceOrderDO == null) {
            throw new GlobalException("订单不存在");
        }
        // 更新消费者订单状态
        rescueQueueOrderDO.setActualPayAmount(rescueQueueOrderDO.getOrderAmount());
        rescueQueueOrderDO.setPayTime(LocalDateTime.now());
        rescueQueueOrderDO.setOrderStatus(QueueOrderStatusEnum.COMPLETED.getCode());
        rescueQueueOrderManger.updateById(rescueQueueOrderDO);

        // 更新师傅端工单状态
        teboServiceOrderDO.setActualPayAmount(teboServiceOrderDO.getOrderAmount());
        teboServiceOrderDO.setPayTime(LocalDateTime.now());
        teboServiceOrderDO.setPayType(0);
        teboServiceOrderDO.setOrderStatus(ServiceOrderStatusEnum.COMPLETED.getCode());
        teboServiceOrderDO.setFinishTime(LocalDateTime.now());
        teboServiceOrderDO.setMaintainProcess(ServiceOrderMaintainProcessEnum.COMPLETED.getCode());
        if (Objects.equals(teboServiceOrderDO.getIsAgentPay(), 1)) {
            teboServiceOrderDO.setPayBy(teboServiceOrderDO.getAccountName());
        } else {
            teboServiceOrderDO.setPayBy(teboServiceOrderDO.getNickName());
        }
        boolean res = serviceOrderMapper.updateById(teboServiceOrderDO) > 0;
        mathJifen(teboServiceOrderDO);
        if (res) {
            // 使用券则把券设置为已使用
            List<TeboServiceOrderCouponDO> orderCouponDOList = serviceOrderCouponMapper.selectByServiceOrderId(teboServiceOrderDO.getId());
            if (orderCouponDOList.isEmpty()) {
                return true;
            }
            List<String> uniqueList = orderCouponDOList.stream().map(TeboServiceOrderCouponDO::getUniqueCode).collect(Collectors.toList());
            couponCustomerService.batchUpdateCouponStatus(uniqueList, 2);
            TeboCouponCustomerRecordDTO dto = new TeboCouponCustomerRecordDTO(uniqueList, 3, teboServiceOrderDO.getShopId(), teboServiceOrderDO.getShopName(), teboServiceOrderDO.getOrderNo());
            teboCouponManager.batchUpdateCouponRecordStatus(dto);
        }
        return res;
    }

    @Override
    public Boolean serviceOrderArrived(Long id) {
        Assert.notNull(id, "未指定工单");
        TeboServiceOrderDO serviceOrderDO = getById(id);
        Assert.notNull(serviceOrderDO, "未匹配到工单");
        Integer orderStatus = serviceOrderDO.getOrderStatus();
        Integer maintainProcess = serviceOrderDO.getMaintainProcess();
        if (ServiceOrderStatusEnum.IN_MAINTENANCE.getCode() != orderStatus) {
            throw new ServiceException(
                    "当前工单状态无法到达，请联系管理员");
        }
        if (ServiceOrderMaintainProcessEnum.ARRIVED.getCode() <= maintainProcess) {
            throw new ServiceException(
                    "当前工单维修环节无法变更为到达，请联系管理员");
        }
        TeboServiceOrderDO update = new TeboServiceOrderDO();
        update.setId(id);
        update.setMaintainProcess(ServiceOrderMaintainProcessEnum.ARRIVED.getCode());
        update.setArriveTime(LocalDateTime.now());

        updateById(update);

        // 发送推送消息 已到达  允许微信接口挂了，但是状态正常更新

        pushArrivedMsg(serviceOrderDO);
        return true;
    }

    @Override
    public Boolean serviceOrderDeparted(Long id) {

        Assert.notNull(id, "未指定工单");
        TeboServiceOrderDO serviceOrderDO = getById(id);
        Assert.notNull(serviceOrderDO, "未匹配到工单");
        Integer orderStatus = serviceOrderDO.getOrderStatus();
        Integer maintainProcess = serviceOrderDO.getMaintainProcess();
        if (ServiceOrderStatusEnum.IN_MAINTENANCE.getCode() != orderStatus) {
            throw new ServiceException(
                    "当前工单状态无法出发，请联系管理员");
        }
        if (ServiceOrderMaintainProcessEnum.DEPARTED.getCode() <= maintainProcess) {
            throw new ServiceException(
                    "当前工单维修环节无法变更为出发，请联系管理员");
        }
        TeboServiceOrderDO update = new TeboServiceOrderDO();
        update.setId(id);
        update.setMaintainProcess(ServiceOrderMaintainProcessEnum.DEPARTED.getCode());
        update.setDepartTime(LocalDateTime.now());
        updateById(update);

        // 发送推送消息 已出发

        pushDepartedMsg(serviceOrderDO);
        return true;
    }

    /**
     * 推送师傅到达
     *
     * @param serviceOrderDO
     */
    private void pushArrivedMsg(TeboServiceOrderDO serviceOrderDO) {
        WxPushDTO wxPushDTO = new WxPushDTO();
        wxPushDTO.setAppid(appid);
        wxPushDTO.setUnionid(serviceOrderDO.getUnionid());
        HashMap<String, Object> pushData = new HashMap<>();

        JSONObject character_string2 = new JSONObject();
        //预约项目（服务类型）
        QueueOrderTypeEnum serviceTypeEnum = QueueOrderTypeEnum.getQueueOrderTypeEnum(serviceOrderDO.getOrderType());
        if (Objects.nonNull(serviceTypeEnum)) {
            character_string2.put("value", serviceTypeEnum.getName());
        }

        pushData.put("thing2", character_string2);
        JSONObject character_string6 = new JSONObject();
//        服务人员
        character_string6.put("value", serviceOrderDO.getAccountName());
        pushData.put("thing3", character_string6);
        JSONObject character_string7 = new JSONObject();
//        到达时间
        character_string7.put("value", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        pushData.put("time4", character_string7);
        wxPushDTO.setData(pushData);
        wxPushDTO.setTemplate_id(arriveTemplateId);
        wxPushDTO.setOfficialAccount(WeChatOfficialAccountEnum.teboTravel.getOfficialAccountName());

        remoteWxPushService.sendMessage(wxPushDTO);
    }


    /**
     * 推送师傅出发
     *
     * @param serviceOrderDO
     */
    private void pushDepartedMsg(TeboServiceOrderDO serviceOrderDO) {
        WxPushDTO wxPushDTO = new WxPushDTO();
        wxPushDTO.setAppid(appid);
        wxPushDTO.setUnionid(serviceOrderDO.getUnionid());
        HashMap<String, Object> pushData = new HashMap<>();

        JSONObject character_string2 = new JSONObject();
        //服务人员
        character_string2.put("value", serviceOrderDO.getAccountName());

        pushData.put("thing3", character_string2);
        JSONObject character_string6 = new JSONObject();
//        出发时间
        character_string6.put("value", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        pushData.put("time4", character_string6);
        JSONObject character_string7 = new JSONObject();
//        联系电话
        Long accountId = serviceOrderDO.getAccountId();
        R<TeboAccountInfoVO> accountResult = remoteAccountService.getAccountInfoById(accountId);
        TeboAccountInfoVO accountResultData = accountResult.getData();
        if (Objects.nonNull(accountResultData)) {
            character_string7.put("value", accountResultData.getPhoneNumber());
        }

        pushData.put("phone_number6", character_string7);
        wxPushDTO.setData(pushData);
        wxPushDTO.setTemplate_id(departTemplateId);
        wxPushDTO.setOfficialAccount(WeChatOfficialAccountEnum.teboTravel.getOfficialAccountName());

        remoteWxPushService.sendMessage(wxPushDTO);
    }

    @Override
    public ServiceOrderVO detailServiceOrder(Long serviceOrderId) {
        if (serviceOrderId == null) {
            throw new IllegalArgumentException("参数错误");
        }
        TeboServiceOrderDO serviceOrderDO = serviceOrderMapper.selectById(serviceOrderId);
        if (serviceOrderDO == null) {
            throw new GlobalException("订单不存在");
        }
        ServiceOrderVO serviceOrderVO = new ServiceOrderVO();
        BeanConvert.copy(serviceOrderDO, serviceOrderVO);
        serviceOrderVO.setServiceAmount(serviceOrderDO.getBaseAmount());
        Integer queueBusinessType = serviceOrderDO.getQueueBusinessType();

        QueueBusinessTypeEnum anEnum = Objects.isNull(queueBusinessType) ? QueueBusinessTypeEnum.ARRIVED : QueueBusinessTypeEnum.getEnum(queueBusinessType);
        if (Objects.isNull(anEnum)) {
            throw new ServiceException("工单类型异常，请联系管理员");
        }
        if (QueueBusinessTypeEnum.getArriveTypeList().contains(anEnum.getCode())) {
            serviceOrderDetailArrived(serviceOrderDO, serviceOrderVO);

        } else if (QueueBusinessTypeEnum.getUnArrivedTypeList().contains(queueBusinessType)) {
            serviceOrderDetailUnArrived(serviceOrderDO, serviceOrderVO, anEnum);
        }


        TeboShop shop = remoteShopService.getShopInfo(serviceOrderDO.getShopId()).getData();
        if (shop != null) {
            serviceOrderVO.setShopName(shop.getShopName());
            serviceOrderVO.setShopAddress(shop.getAreaName().replace("-", "") + shop.getAddress());
        }

        serviceOrderVO.setServiceAmount(serviceOrderDO.getBaseAmount());
        ServiceOrderGoodsQueryDTO queryDTO = new ServiceOrderGoodsQueryDTO();
        queryDTO.setServiceOrderId(serviceOrderDO.getId().toString());
        List<TeboServiceOrderGoodsDO> orderGoodsList = serviceOrderGoodsMapper.list(queryDTO);
        BigDecimal goodsAmount = BigDecimal.ZERO;
        if (!orderGoodsList.isEmpty()) {
            for (TeboServiceOrderGoodsDO orderGoodsDO : orderGoodsList) {
                goodsAmount = goodsAmount.add(new BigDecimal(orderGoodsDO.getGoodsAmount()));
            }
        }
        serviceOrderVO.setGoodsAmount(goodsAmount.intValue());
        serviceOrderVO.setOrderGoodsList(BeanConvert.copyList(orderGoodsList, ServiceOrderGoodsVO::new));

        // 优惠券信息
        List<TeboServiceOrderCouponDO> orderCouponList = serviceOrderCouponMapper.selectByServiceOrderId(serviceOrderId);
        BigDecimal couponAmount = BigDecimal.ZERO;
        if (!orderCouponList.isEmpty()) {
            for (TeboServiceOrderCouponDO orderCouponDO : orderCouponList) {
                couponAmount = couponAmount.add(new BigDecimal(orderCouponDO.getCouponPrice()));
            }
        }
        serviceOrderVO.setCouponAmount(couponAmount.intValue());


        serviceOrderVO.setCouponList(BeanConvert.copyList(orderCouponList, ServiceOrderCouponVO::new));

//        评价相关
        TeboCommentDO teboCommentDO = commentManger.getCommentsByOrderId(serviceOrderDO.getId());
        if (Objects.nonNull(teboCommentDO)) {
            serviceOrderVO.setCompetenceComment(teboCommentDO.getCompetenceComment());
            serviceOrderVO.setTotalComment(teboCommentDO.getTotalComment());
            serviceOrderVO.setAttitudeComment(teboCommentDO.getAttitudeComment());
            serviceOrderVO.setImageComment(teboCommentDO.getImageComment());
            serviceOrderVO.setServiceComment(teboCommentDO.getServiceComment());
            serviceOrderVO.setQuantityComment(teboCommentDO.getQuantityComment());
            serviceOrderVO.setCommentTime(teboCommentDO.getCreateTime());
        }

        /**
         * 保单相关
         */
        if (serviceOrderDO.getOrderType() == 4) {
            R<TeboServiceInsurancePolicyOrderVO> result = insurancePolicyOrderService.serviceInsurancePolicyOrder(serviceOrderId);
            if (ObjectUtil.isNotEmpty(result.getData())) {
                serviceOrderVO.setInsurancePolicyOrderVO(result.getData());
            } else {
                serviceOrderVO.setInsurancePolicyOrderVO(new TeboServiceInsurancePolicyOrderVO());
            }
        }
        return serviceOrderVO;
    }

    /**
     * 非到店的业务  工单详情
     *
     * @param serviceOrderDO
     * @param serviceOrderVO
     * @param anEnum
     */
    private void serviceOrderDetailUnArrived(TeboServiceOrderDO serviceOrderDO, ServiceOrderVO serviceOrderVO, QueueBusinessTypeEnum anEnum) {
        TeboRescueQueueOrderDO rescueQueueOrderDO = rescueQueueOrderManger.getById(serviceOrderDO.getQueueOrderId());
        if (Objects.isNull(rescueQueueOrderDO)) {
            return;
        }
        serviceOrderVO.setQueueOrderType(rescueQueueOrderDO.getOrderType());
        serviceOrderVO.setNickName(rescueQueueOrderDO.getNickName());
        serviceOrderVO.setPhoneNumber(rescueQueueOrderDO.getPhoneNumber());
        OrderFileQueryDTO fileQueryDTO = new OrderFileQueryDTO();
        fileQueryDTO.setOrderTable(anEnum.getTableName());
        fileQueryDTO.setOrderId(rescueQueueOrderDO.getId());
        List<TeboOrderFileDO> fileDOS = orderFileManger.listBySo(fileQueryDTO);
        if (CollectionUtil.isNotEmpty(fileDOS)) {
            serviceOrderVO.setUrls(fileDOS.stream().map(TeboOrderFileDO::getFileUrl).collect(Collectors.toList()));
        }
        serviceOrderVO.setFaultMsg(rescueQueueOrderDO.getFaultMsg());
        serviceOrderVO.setRequestLocation(rescueQueueOrderDO.getRequestLocation());
        serviceOrderVO.setPickupTime(serviceOrderVO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        serviceOrderVO.setInitiationTime(rescueQueueOrderDO.getCreateTime());
        serviceOrderVO.setBrand(rescueQueueOrderDO.getBrand());
        serviceOrderVO.setInitiationType(rescueQueueOrderDO.getInitiationType());
        serviceOrderVO.setPreDoorTime(rescueQueueOrderDO.getPreDoorTime());
        serviceOrderVO.setQueueOrderNo(rescueQueueOrderDO.getOrderNo());
        serviceOrderVO.setQueueOrderId(rescueQueueOrderDO.getId());
    }

    private void serviceOrderDetailArrived(TeboServiceOrderDO serviceOrderDO, ServiceOrderVO serviceOrderVO) {
        TeboQueueOrderDO queueOrderDO = queueOrderMapper.selectById(serviceOrderDO.getQueueOrderId());
        if (queueOrderDO == null) {
            return;
        }


        serviceOrderVO.setPickupType(queueOrderDO.getPickupType());
        serviceOrderVO.setQueueNumberStr(queueOrderDO.getQueueNumberStr());
        serviceOrderVO.setQueueOrderTime(queueOrderDO.getCreateTime());
        serviceOrderVO.setCallTime(queueOrderDO.getCallTime());
        serviceOrderVO.setQueueOrderType(queueOrderDO.getOrderType());
        serviceOrderVO.setInitiationTime(queueOrderDO.getCreateTime());
        serviceOrderVO.setQueueOrderNo(queueOrderDO.getOrderNo());
        serviceOrderVO.setQueueOrderId(queueOrderDO.getId());
        String unionid = queueOrderDO.getUnionid();
        TeboConsumer teboConsumer = remoteCustomerService.selectByUnionId(unionid).getData();
        if (teboConsumer != null) {
            serviceOrderVO.setNickName(teboConsumer.getNickName());
            serviceOrderVO.setPhoneNumber(teboConsumer.getPhoneNumber());
        }
    }

    @Transactional
    @Override
    public Boolean cancelServiceOrder(Long serviceOrderId) {
        if (serviceOrderId == null) {
            throw new IllegalArgumentException("参数错误");
        }
        TeboServiceOrderDO serviceOrderDO = serviceOrderMapper.selectById(serviceOrderId);
        if (serviceOrderDO == null) {
            throw new GlobalException("订单不存在");
        }
        Integer orderStatus = serviceOrderDO.getOrderStatus();
        if (CollectionUtil.newArrayList(ServiceOrderStatusEnum.COMMENTED.getCode(), ServiceOrderStatusEnum.COMPLETED.getCode()).contains(orderStatus)) {
            throw new ServiceException("当前工单状态不能取消");
        }

        serviceOrderDO.setOrderStatus(ServiceOrderStatusEnum.CANCEL.getCode());
        serviceOrderDO.setCancelBy(serviceOrderDO.getAccountName());
        serviceOrderDO.setCancelTime(LocalDateTime.now());
        serviceOrderDO.setUpdateTime(LocalDateTime.now());
        serviceOrderMapper.updateById(serviceOrderDO);

        //根据类型，更新对应的排队单表
        Integer queueBusinessType = serviceOrderDO.getQueueBusinessType();
        QueueBusinessTypeEnum anEnum = Objects.isNull(queueBusinessType) ? QueueBusinessTypeEnum.ARRIVED : QueueBusinessTypeEnum.getEnum(queueBusinessType);
        if (Objects.isNull(anEnum)) {
            throw new ServiceException("工单类型异常，请联系管理员");
        }
        if (QueueBusinessTypeEnum.getArriveTypeList().contains(anEnum.getCode())) {
            TeboQueueOrderDO teboQueueOrderDO = queueOrderMapper.selectById(serviceOrderDO.getQueueOrderId());
            teboQueueOrderDO.setOrderStatus(QueueOrderStatusEnum.CANCEL.getCode());
            queueOrderMapper.updateById(teboQueueOrderDO);
        }


        // 将券设置为可使用状态
        List<TeboServiceOrderCouponDO> orderCouponDOList = serviceOrderCouponMapper.selectByServiceOrderId(serviceOrderId);
        if (!orderCouponDOList.isEmpty()) {
            List<String> uniqueCodeList = orderCouponDOList.stream().map(TeboServiceOrderCouponDO::getUniqueCode).collect(Collectors.toList());
            couponCustomerService.batchUpdateCouponStatus(uniqueCodeList, 1);
        }

        return true;

    }

    @Transactional
    @Override
    public Boolean cancelServiceOrder(ServiceOrderDTO serviceOrderDTO) {
        Long id = serviceOrderDTO.getId();
        String cancelReason = serviceOrderDTO.getCancelReason();
        if (id == null) {
            throw new IllegalArgumentException("未指定工单");
        }

        TeboServiceOrderDO serviceOrderDO = serviceOrderMapper.selectById(id);
        if (serviceOrderDO == null) {
            throw new GlobalException("订单不存在");
        }

        Integer orderStatus = serviceOrderDO.getOrderStatus();
        if (CollectionUtil.newArrayList(ServiceOrderStatusEnum.COMMENTED.getCode(), ServiceOrderStatusEnum.COMPLETED.getCode()).contains(orderStatus)) {
            throw new ServiceException("当前工单状态不能取消");
        }

        LocalDateTime now = LocalDateTime.now();
        serviceOrderDO.setOrderStatus(ServiceOrderStatusEnum.CANCEL.getCode());
        serviceOrderDO.setCancelBy(serviceOrderDO.getAccountName());
        serviceOrderDO.setCancelTime(now);
        serviceOrderDO.setUpdateTime(now);
        serviceOrderDO.setCancelReason(cancelReason);
        serviceOrderMapper.updateById(serviceOrderDO);

        //根据类型，更新对应的排队单表
        Integer queueBusinessType = serviceOrderDO.getQueueBusinessType();
        QueueBusinessTypeEnum anEnum = Objects.isNull(queueBusinessType) ? QueueBusinessTypeEnum.ARRIVED : QueueBusinessTypeEnum.getEnum(queueBusinessType);
        if (Objects.isNull(anEnum)) {
            throw new ServiceException("工单类型异常，请联系管理员");
        }
        if (QueueBusinessTypeEnum.getArriveTypeList().contains(anEnum.getCode())) {
            TeboQueueOrderDO teboQueueOrderDO = queueOrderMapper.selectById(serviceOrderDO.getQueueOrderId());
            if (Objects.nonNull(teboQueueOrderDO)) {
                teboQueueOrderDO.setOrderStatus(QueueOrderStatusEnum.CANCEL.getCode());
                teboQueueOrderDO.setCancelTime(now);
                queueOrderMapper.updateById(teboQueueOrderDO);
            }

        } else if (QueueBusinessTypeEnum.getUnArrivedTypeList().contains(queueBusinessType)) {
            TeboRescueQueueOrderDO rescueQueueOrderDO = rescueQueueOrderManger.getById(serviceOrderDO.getQueueOrderId());
            if (Objects.nonNull(rescueQueueOrderDO)) {
                rescueQueueOrderDO.setOrderStatus(QueueOrderStatusEnum.CANCEL.getCode());
                rescueQueueOrderDO.setCancelBy(serviceOrderDO.getAccountName());
                rescueQueueOrderDO.setCancelTime(now);
                rescueQueueOrderDO.setCancelReason("师傅已取消订单");
                rescueQueueOrderManger.updateById(rescueQueueOrderDO);
            }

        }


        // 将券设置为可使用状态
        List<TeboServiceOrderCouponDO> orderCouponDOList = serviceOrderCouponMapper.selectByServiceOrderId(id);
        if (!orderCouponDOList.isEmpty()) {
            List<String> uniqueCodeList = orderCouponDOList.stream().map(TeboServiceOrderCouponDO::getUniqueCode).collect(Collectors.toList());
            couponCustomerService.batchUpdateCouponStatus(uniqueCodeList, 1);
        }
        // 通知消费者该订单已取消
        cancelOrderSendTemplateMessage(serviceOrderDO.getUnionid(), serviceOrderDO.getOrderNo());
        return true;
    }

    @Transactional
    @Override
    public Boolean afterPay(ServiceOrderAfterPayDTO afterPayDTO) {
        log.info("afterPay 支付回调，{}", com.alibaba.fastjson2.JSON.toJSONString(afterPayDTO));

        String orderNo = afterPayDTO.getOrderNo();

        TeboServiceOrderDO serviceOrderDO = serviceOrderMapper.selectByOrderNo(orderNo);
        Integer queueBusinessType = serviceOrderDO.getQueueBusinessType();


        QueueBusinessTypeEnum anEnum = Objects.isNull(queueBusinessType) ? QueueBusinessTypeEnum.ARRIVED : QueueBusinessTypeEnum.getEnum(queueBusinessType);
        if (Objects.isNull(anEnum)) {
            throw new ServiceException("工单类型异常，请联系管理员");
        }
        if (QueueBusinessTypeEnum.getArriveTypeList().contains(anEnum.getCode())) {
            afterPayArrive(serviceOrderDO);
        } else if (QueueBusinessTypeEnum.getUnArrivedTypeList().contains(queueBusinessType)) {
            afterPayUnArrive(serviceOrderDO);
        }

        Long shopId = serviceOrderDO.getShopId();


        mathJifen(serviceOrderDO);


        // 使用券则把券设置为已使用
        List<TeboServiceOrderCouponDO> orderCouponDOList = serviceOrderCouponMapper.selectByServiceOrderId(serviceOrderDO.getId());
        if (orderCouponDOList.isEmpty()) {
            return true;
        }
        List<String> uniqueList = orderCouponDOList.stream().map(TeboServiceOrderCouponDO::getUniqueCode).collect(Collectors.toList());
        couponCustomerService.batchUpdateCouponStatus(uniqueList, 2);
        // 更新领用流水
        TeboCouponCustomerRecordDTO dto = new TeboCouponCustomerRecordDTO(uniqueList, 3, shopId, serviceOrderDO.getShopName(), serviceOrderDO.getOrderNo());
        teboCouponManager.batchUpdateCouponRecordStatus(dto);
        String phoneCall = getPhoneCall(serviceOrderDO.getPhoneNumber());
        String typeName = getTypeName(serviceOrderDO.getOrderType());
        String content = "请手机尾号 " + phoneCall + " 的客户到"
                + serviceOrderDO.getStationName() + "号" + typeName + "工位取车";
        disMqService.notifyAppMsg(shopId, content);
        return true;
    }


    /**
     * 算积分
     *
     * @param serviceOrderDO
     */
    private void mathJifen(TeboServiceOrderDO serviceOrderDO) {
        TeboShop teboShop = remoteShopService.getShopInfo(serviceOrderDO.getShopId()).getData();
        if (Objects.isNull(teboShop)) {
            throw new ServiceException("未匹配到门店");
        }
        BigDecimal integrationCoefficient = teboShop.getIntegrationCoefficient();

        ServiceOrderGoodsQueryDTO queryDTO = new ServiceOrderGoodsQueryDTO();
        queryDTO.setServiceOrderId(String.valueOf(serviceOrderDO.getId()));
        List<TeboServiceOrderGoodsDO> orderGoodsDOS = serviceOrderGoodsMapper.list(queryDTO);
        if (CollectionUtil.isNotEmpty(orderGoodsDOS)) {
            for (TeboServiceOrderGoodsDO orderGoodsDO : orderGoodsDOS) {
                TeboGoodsQueryDTO remoteQuery = new TeboGoodsQueryDTO();
                remoteQuery.setGoodsIdList(Collections.singletonList(orderGoodsDO.getGoodsId()));
                List<TeboGoodsVO> goodsVOS = remoteGoodsService.list(remoteQuery).getData();
                if (CollectionUtil.isNotEmpty(goodsVOS)) {
                    //算积分
                    Integer basicIntegral = goodsVOS.get(0).getBasicIntegral();
                    if (Objects.nonNull(basicIntegral) && basicIntegral > 0) {
                        if (Objects.isNull(integrationCoefficient)) {
                            throw new ServiceException("门店积分系数为空");
                        }
                        BigDecimal multiply = new BigDecimal(basicIntegral).multiply(integrationCoefficient).multiply(new BigDecimal(orderGoodsDO.getGoodsNum()));
                        TeboUserIntegralDTO userIntegralDTO = new TeboUserIntegralDTO();
                        userIntegralDTO.setOrderId(serviceOrderDO.getId());
                        userIntegralDTO.setIntegral(multiply.intValue());
                        userIntegralDTO.setType(1);
                        userIntegralDTO.setUserAccount(serviceOrderDO.getShopId());
                        remoteIntegralOrderService.addIntegral(userIntegralDTO);
                    }
                }
            }
        }
    }

    @Transactional
    @Override
    public Boolean completeServiceOrder(Long id) {


        if (id == null) {
            throw new IllegalArgumentException("参数错误");
        }
        TeboServiceOrderDO teboServiceOrderDO = serviceOrderMapper.selectById(id);
        if (teboServiceOrderDO == null) {
            throw new GlobalException("订单不存在");
        }
        if (!Objects.equals(teboServiceOrderDO.getOrderStatus(), ServiceOrderStatusEnum.WAIT_PAY.getCode())) {
            throw new GlobalException("只有待付款订单可以关闭");
        }

        R<PayWechatVO> payResult = remotePayService.getPayWechatRecordByOrder(teboServiceOrderDO.getOrderNo());
        if (Objects.isNull(payResult.getData())) {
            throw new GlobalException("当前工单没有支付流水，无法关闭");
        }

        // 更新排队单状态
        Integer queueBusinessType = teboServiceOrderDO.getQueueBusinessType();
        QueueBusinessTypeEnum anEnum = Objects.isNull(queueBusinessType) ? QueueBusinessTypeEnum.ARRIVED : QueueBusinessTypeEnum.getEnum(queueBusinessType);
        if (Objects.isNull(anEnum)) {
            throw new ServiceException("工单类型异常，请联系管理员");
        }
        Long queueOrderId = teboServiceOrderDO.getQueueOrderId();
        if (Objects.nonNull(queueOrderId)) {
            if (QueueBusinessTypeEnum.getArriveTypeList().contains(anEnum.getCode())) {
                TeboQueueOrderDO update = new TeboQueueOrderDO();

                update.setId(queueOrderId);
                update.setActualPayAmount(teboServiceOrderDO.getOrderAmount());
                update.setPayTime(LocalDateTime.now());
                update.setOrderStatus(QueueOrderStatusEnum.COMPLETED.getCode());
                queueOrderMapper.updateById(update);

            } else if (QueueBusinessTypeEnum.getUnArrivedTypeList().contains(queueBusinessType)) {
                TeboRescueQueueOrderDO update = new TeboRescueQueueOrderDO();
                update.setId(queueOrderId);
                update.setActualPayAmount(teboServiceOrderDO.getOrderAmount());
                update.setPayTime(LocalDateTime.now());
                update.setOrderStatus(QueueOrderStatusEnum.COMPLETED.getCode());
                rescueQueueOrderManger.updateById(update);
            }
        }

        mathJifen(teboServiceOrderDO);

        // 更新师傅端工单状态
        teboServiceOrderDO.setActualPayAmount(teboServiceOrderDO.getOrderAmount());
        teboServiceOrderDO.setPayTime(LocalDateTime.now());
        teboServiceOrderDO.setPayType(0);
        teboServiceOrderDO.setOrderStatus(ServiceOrderStatusEnum.COMPLETED.getCode());
        teboServiceOrderDO.setFinishTime(LocalDateTime.now());
        teboServiceOrderDO.setMaintainProcess(ServiceOrderMaintainProcessEnum.COMPLETED.getCode());
        if (Objects.equals(teboServiceOrderDO.getIsAgentPay(), 1)) {
            teboServiceOrderDO.setPayBy(teboServiceOrderDO.getAccountName());
        } else {
            teboServiceOrderDO.setPayBy(teboServiceOrderDO.getNickName());
        }
        boolean res = serviceOrderMapper.updateById(teboServiceOrderDO) > 0;
        //手动关单，更新订单支付状态，并分佣
        remotePayService.updatePayementSuccessStatus(teboServiceOrderDO.getOrderNo());
        if (res) {
            // 使用券则把券设置为已使用
            List<TeboServiceOrderCouponDO> orderCouponDOList = serviceOrderCouponMapper.selectByServiceOrderId(teboServiceOrderDO.getId());
            if (orderCouponDOList.isEmpty()) {
                return true;
            }
            List<String> uniqueList = orderCouponDOList.stream().map(TeboServiceOrderCouponDO::getUniqueCode).collect(Collectors.toList());
            couponCustomerService.batchUpdateCouponStatus(uniqueList, 2);
            // 更新领用流水
            TeboCouponCustomerRecordDTO dto = new TeboCouponCustomerRecordDTO(uniqueList, 3, teboServiceOrderDO.getShopId(), teboServiceOrderDO.getShopName(), teboServiceOrderDO.getOrderNo());
            teboCouponManager.batchUpdateCouponRecordStatus(dto);
        }

        String phoneCall = getPhoneCall(teboServiceOrderDO.getPhoneNumber());
        String typeName = getTypeName(teboServiceOrderDO.getOrderType());
        String content = "请手机尾号 " + phoneCall + " 的客户到"
                + teboServiceOrderDO.getStationName() + "号" + typeName + "工位取车";
        disMqService.notifyAppMsg(teboServiceOrderDO.getShopId(), content);
        return res;
    }

    @Override
    public List<ServiceOrderDTO> getUnFinishServiceOrderByAccount(List<Long> accountIds) {

        if (CollectionUtil.isEmpty(accountIds)) {
            return new ArrayList<>();
        }
        ServiceOrderQueryDTO queryDTO = new ServiceOrderQueryDTO();
        queryDTO.setAccountIds(accountIds);
        queryDTO.setOrderStatusList(ServiceOrderStatusEnum.getUnFinishCodes());
        List<TeboServiceOrderDO> orderDOS = serviceOrderManger.list(queryDTO);
        List<ServiceOrderDTO> serviceOrderVOS = BeanConvert.copyList(orderDOS, ServiceOrderDTO::new);
        return serviceOrderVOS;

    }

    @Override
    public List<QueueOrderDTO> getUnFinishQueueOrderByAccount(List<Long> accountIds) {
        if (CollectionUtil.isEmpty(accountIds)) {
            return new ArrayList<>();
        }
        QueueOrderQueryDTO queryDTO = new QueueOrderQueryDTO();
        queryDTO.setAccountIds(accountIds);
        queryDTO.setOrderStatusList(Arrays.asList(QueueOrderStatusEnum.PROCESSING.getCode(), QueueOrderStatusEnum.WAIT_PAY.getCode()));
        List<TeboQueueOrderDO> list = queueOrderMapper.list(queryDTO);
        List<QueueOrderDTO> queueOrderDTOS = BeanConvert.copyList(list, QueueOrderDTO::new);
        return queueOrderDTOS;
    }


    @Transactional
    @Override
    public QuickOpenServiceOrderResultVO quickOpenOrder(OpenQuickServiceOrderDTO openQuickServiceOrderDTO) {
        Assert.notNull(openQuickServiceOrderDTO.getCategoryName(), "商品类目不能为空");
        Assert.notNull(openQuickServiceOrderDTO.getPrice(), "商品价格不能为空");
        Assert.notNull(openQuickServiceOrderDTO.getPhoneNumber(), "联系电话不能为空");


        //生成工单
        Long userId = MaintainerOnlineUserUtil.getUserId();
        TeboAccountInfoVO accountInfoVO = remoteAccountService.getAccountInfoById(userId).getData();
        if (Objects.isNull(accountInfoVO)) {
            throw new ServiceException("获取用户信息失败，请联系管理员");
        }
        Long shopId = accountInfoVO.getShopId();
        TeboShop teboShop = remoteShopService.getShopInfo(shopId).getData();
        if (Objects.isNull(teboShop)) {
            throw new ServiceException("获取门店失败，请联系管理员");
        }

        TeboServiceOrderDO serviceOrderDO = new TeboServiceOrderDO();
        serviceOrderDO.setId(SnowFlakeUtil.nextId());
        serviceOrderDO.setShopName(teboShop.getShopName());
        serviceOrderDO.setShopType(teboShop.getShopType());
        serviceOrderDO.setShopId(shopId);
//        serviceOrderDO.setshopty
        serviceOrderDO.setId(SnowFlakeUtil.nextId());
        serviceOrderDO.setOrderNo(TeboNumberGenerator.generateServiceOrderNo());
        serviceOrderDO.setAccountId(userId);
        serviceOrderDO.setTenantId(accountInfoVO.getTenantId());
        serviceOrderDO.setPhoneNumber(openQuickServiceOrderDTO.getPhoneNumber());
        serviceOrderDO.setAccountName(accountInfoVO.getAccountName());
        if (openQuickServiceOrderDTO.getPrice() == 0) {
            serviceOrderDO.setOrderStatus(ServiceOrderStatusEnum.COMPLETED.getCode());
        } else {
            serviceOrderDO.setOrderStatus(ServiceOrderStatusEnum.WAIT_PAY.getCode());
        }

        serviceOrderDO.setServiceType(ServiceTypeEnum.REPAIR_CAR.getCode());
        serviceOrderDO.setOrderAmount(openQuickServiceOrderDTO.getPrice());
        serviceOrderDO.setQueueBusinessType(QueueBusinessTypeEnum.ARRIVED.getCode());
        serviceOrderDO.setIsQuickOrder(true);
        serviceOrderDO.setOpenOrderTime(LocalDateTime.now());
        serviceOrderDO.setOrderType(QueueOrderTypeEnum.REPAIR.getCode());
        serviceOrderDO.setRemark(openQuickServiceOrderDTO.getRemark());
        serviceOrderDO.setCategoryName(openQuickServiceOrderDTO.getCategoryName());
        serviceOrderMapper.insert(serviceOrderDO);


        //生成跳转支付的二维码
        PayOrderDTO payOrderDTO = new PayOrderDTO();
        payOrderDTO.setOrderNo(serviceOrderDO.getOrderNo());
        payOrderDTO.setAmount(openQuickServiceOrderDTO.getPrice());
        payOrderDTO.setTenantId(teboShop.getTenantId());

        payOrderDTO.setBusinessType(30);
        payOrderDTO.setShopId(shopId);
        payOrderDTO.setDescription("快捷开单");
        payOrderDTO.setSource(2);

        String data = remoteWxPayService.generateOrderCode(serviceOrderDO.getId(), 1).getData();
        if (StringUtils.isEmpty(data)) {
            throw new ServiceException("生成支付二维码失败，请联系管理员");
        }
        QuickOpenServiceOrderResultVO quickOpenServiceOrderResultVO = new QuickOpenServiceOrderResultVO();
        quickOpenServiceOrderResultVO.setQrCode(data);
        quickOpenServiceOrderResultVO.setServiceOrderId(serviceOrderDO.getId());
        return quickOpenServiceOrderResultVO;

    }

    @Override
    public Boolean updateQuickServiceOrderStatus(String orderNo) {
        Assert.notNull(orderNo, "订单号不能为空");
        TeboServiceOrderDO serviceOrderDO = serviceOrderMapper.selectByOrderNo(orderNo);
        if (Objects.isNull(serviceOrderDO)) {
            throw new ServiceException("未匹配到工单");
        }
        TeboServiceOrderDO update = new TeboServiceOrderDO();
        update.setId(serviceOrderDO.getId());
        update.setFinishTime(LocalDateTime.now());
        update.setPayTime(LocalDateTime.now());
        update.setOrderStatus(ServiceOrderStatusEnum.COMPLETED.getCode());
        serviceOrderMapper.updateById(update);
        return Boolean.TRUE;
    }

    @Override
    public String getQuickOrderQrCode(Long orderId) {
        Assert.notNull(orderId, "工单不能为空");
        TeboServiceOrderDO serviceOrderDO = serviceOrderMapper.selectById(orderId);
        if (Objects.isNull(serviceOrderDO)) {
            throw new ServiceException("未找到工单");
        }
        Boolean isQuickOrder = serviceOrderDO.getIsQuickOrder();
        if (!isQuickOrder) {
            return null;
        }

        String data = remoteWxPayService.generateOrderCode(serviceOrderDO.getId(), 1).getData();
        if (StringUtils.isEmpty(data)) {
            throw new ServiceException("生成支付二维码失败，请联系管理员");
        }


        return data;
    }

    @Override
    public void initShopType() {
        List<TeboServiceOrderDO> list = serviceOrderMapper.list(new ServiceOrderQueryDTO());
        List<Long> shopIds = list.stream().map(TeboServiceOrderDO::getShopId).collect(Collectors.toSet()).stream().collect(Collectors.toList());
        List<TeboShopListVO> shopListVOS = new ArrayList<>();
        DataUtils.splitHandle(shopIds, splitList -> {
            TeboShopQueryDTO queryDTO = new TeboShopQueryDTO();
            queryDTO.setIdList(splitList);
            shopListVOS.addAll(remoteShopService.fullyQuery(queryDTO).getData());


        }, 200);
        Map<Long, TeboShopListVO> shopListVOMap = DataUtils.listToMap(shopListVOS, TeboShopListVO::getId);
        DataUtils.splitHandle(list, splitList -> {
            List<TeboServiceOrderDO> updateList = new ArrayList<>();

            for (TeboServiceOrderDO serviceOrderDO : splitList) {
                TeboServiceOrderDO update = new TeboServiceOrderDO();
                update.setId(serviceOrderDO.getId());
                Long shopId = serviceOrderDO.getShopId();
                if (Objects.nonNull(shopId)) {
                    TeboShopListVO shopListVO = shopListVOMap.get(shopId);
                    if (Objects.nonNull(shopListVO)) {
                        update.setShopType(shopListVO.getShopType());
                    }
                }
                updateList.add(update);
            }

            serviceOrderManger.updateBatchById(updateList);

        }, 200);

    }

    @Override
    public Boolean updateOrderType(String orderNo) {
        TeboServiceOrderDO serviceOrderDO = serviceOrderMapper.selectByOrderNo(orderNo);
        if (Objects.isNull(serviceOrderDO)) {
            throw new ServiceException("未匹配到工单");
        }
        TeboServiceOrderDO update = new TeboServiceOrderDO();
        update.setId(serviceOrderDO.getId());
        update.setOrderType(4);
        serviceOrderMapper.updateById(update);
        return Boolean.TRUE;
    }

    @Override
    public List<TeboOrderGoodsIdGroup> getOrderGoodsIdGroup(ServiceOrderQueryDTO queryDTO) {


        queryDTO.setOrderStatusList(ServiceOrderStatusEnum.getPayedStatusList());
        List<TeboServiceOrderDO> orderList = serviceOrderManger.list(queryDTO);
        if (CollectionUtil.isEmpty(orderList)) {
            return new ArrayList<>();
        }
        List<Long> orderIds = orderList.stream().map(TeboServiceOrderDO::getId).collect(Collectors.toList());
        Map<Long, List<TeboOrderGoodsIdGroup>> goodGroupAll = new HashMap<>();
        DataUtils.splitHandle(orderIds, (splitIds) -> {
            ServiceOrderGoodsQueryDTO goodsQueryDTO = new ServiceOrderGoodsQueryDTO();
            goodsQueryDTO.setServiceOrderIds(splitIds);
            List<TeboOrderGoodsIdGroup> codeGroupByOrderIds = serviceOrderGoodsMapper.getOrderGoodsIdGroup(goodsQueryDTO);
            if (CollectionUtil.isNotEmpty(codeGroupByOrderIds)) {
                codeGroupByOrderIds = codeGroupByOrderIds.stream().filter(e -> Objects.nonNull(e.getGoodsId())).collect(Collectors.toList());
                Map<Long, List<TeboOrderGoodsIdGroup>> goodGroup = DataUtils.listToGroup(codeGroupByOrderIds, TeboOrderGoodsIdGroup::getGoodsId);
                for (Long key : goodGroup.keySet()) {
                    if (!goodGroupAll.containsKey(key)) {
                        goodGroupAll.put(key, goodGroup.get(key));
                    } else {
                        List<TeboOrderGoodsIdGroup> goodsCodeGroups = goodGroupAll.get(key);
                        goodsCodeGroups.addAll(goodGroup.get(key));
                    }
                }
            }
        }, 400);

//        List<TeboOrderGoodsCodeGroup> codeGroupByOrderIds = mallOrderGoodsManager.getOrderGoodsCodeGroupByOrderIds(orderIds);
        List<TeboOrderGoodsIdGroup> resultList = new ArrayList<>();
        for (List<TeboOrderGoodsIdGroup> valueList : goodGroupAll.values()) {
            if (CollectionUtil.isEmpty(valueList)) {
                continue;
            }
            TeboOrderGoodsIdGroup item = new TeboOrderGoodsIdGroup();
            TeboOrderGoodsIdGroup first = valueList.get(0);
            item.setGoodsId(first.getGoodsId());
            item.setGoodsName(first.getGoodsName());
            long allCount = valueList.stream().mapToLong(TeboOrderGoodsIdGroup::getAllCount).reduce(0, Long::sum);
            item.setAllCount(allCount);
            int allAmount = valueList.stream().mapToInt(TeboOrderGoodsIdGroup::getAllAmount).reduce(0, Integer::sum);
            item.setAllAmount(allAmount);
            resultList.add(item);
        }

        return resultList.stream().sorted(Comparator.comparing(TeboOrderGoodsIdGroup::getAllAmount).reversed()).collect(Collectors.toList());
    }

    private String getTypeName(Integer orderType) {
        switch (orderType) {
            case 0:
            case 1:
            case 2:
                return "维修";
            case 3:
                return "洗车";
        }
        return null;
    }

    private String getPhoneCall(String phone) {
        Map<Character, String> map = new HashMap<>();
        map.put('0', "零");
        map.put('1', "一");
        map.put('2', "二");
        map.put('3', "三");
        map.put('4', "四");
        map.put('5', "五");
        map.put('6', "六");
        map.put('7', "七");
        map.put('8', "八");
        map.put('9', "九");
        String lastFour = phone.substring(phone.length() - 4);
        return map.get(lastFour.charAt(0)) + map.get(lastFour.charAt(1)) + map.get(lastFour.charAt(2)) + map.get(lastFour.charAt(3));

    }

    private void afterPayUnArrive(TeboServiceOrderDO serviceOrderDO) {
        TeboRescueQueueOrderDO rescueQueueOrderDO = rescueQueueOrderManger.getById(serviceOrderDO.getQueueOrderId());

        LocalDateTime now = LocalDateTime.now();
        serviceOrderDO.setPayTime(now);
        serviceOrderDO.setPayType(0);
        if (Objects.equals(serviceOrderDO.getIsAgentPay(), 1)) {
            serviceOrderDO.setPayBy(serviceOrderDO.getAccountName());
        } else {
            serviceOrderDO.setPayBy(rescueQueueOrderDO.getNickName());
        }
        serviceOrderDO.setActualPayAmount(serviceOrderDO.getOrderAmount());
        serviceOrderDO.setOrderStatus(ServiceOrderStatusEnum.COMPLETED.getCode());
        serviceOrderDO.setFinishTime(LocalDateTime.now());
        serviceOrderDO.setMaintainProcess(ServiceOrderMaintainProcessEnum.COMPLETED.getCode());
        serviceOrderMapper.updateById(serviceOrderDO);

        TeboRescueQueueOrderDO update = new TeboRescueQueueOrderDO();
        update.setId(serviceOrderDO.getQueueOrderId());
        update.setOrderStatus(QueueOrderStatusEnum.COMPLETED.getCode());
        update.setPayTime(now);
        update.setActualPayAmount(serviceOrderDO.getOrderAmount());
        rescueQueueOrderManger.updateById(update);
    }

    private void afterPayArrive(TeboServiceOrderDO serviceOrderDO) {
        TeboQueueOrderDO queueOrderDO = queueOrderMapper.selectById(serviceOrderDO.getQueueOrderId());

        LocalDateTime now = LocalDateTime.now();
        serviceOrderDO.setPayTime(now);
        serviceOrderDO.setPayType(0);
        if (Objects.equals(serviceOrderDO.getIsAgentPay(), 1)) {
            serviceOrderDO.setPayBy(serviceOrderDO.getAccountName());
        } else {
            serviceOrderDO.setPayBy(queueOrderDO.getNickName());
        }
        serviceOrderDO.setActualPayAmount(serviceOrderDO.getOrderAmount());
        serviceOrderDO.setOrderStatus(ServiceOrderStatusEnum.COMPLETED.getCode());
        serviceOrderDO.setFinishTime(LocalDateTime.now());
        serviceOrderMapper.updateById(serviceOrderDO);


        queueOrderDO.setOrderStatus(QueueOrderStatusEnum.COMPLETED.getCode());
        queueOrderDO.setPayTime(now);
        queueOrderDO.setActualPayAmount(serviceOrderDO.getOrderAmount());
        queueOrderMapper.updateById(queueOrderDO);
    }


    private void cancelOrderSendTemplateMessage(String unionId, String orderNo) {
        log.info("cancelOrderSendTemplateMessage {} orderNo {}", unionId, orderNo);
        WxPushDTO pushDTO = new WxPushDTO();
        pushDTO.setTemplate_id("wziWGkv20Cp4ureUwzpqrMf8cP_ejU5YT3zEV_H4Kbk");
        pushDTO.setAppid(appid);
        pushDTO.setUnionid(unionId);
        pushDTO.setOfficialAccount("泰博出行");
        HashMap<String, Object> data = new HashMap<>();

        JSONObject character_string11 = new JSONObject();
        character_string11.put("value", orderNo);
        data.put("character_string2", character_string11);
        JSONObject phrase5 = new JSONObject();
        phrase5.put("value", "已取消");
        data.put("phrase5", phrase5);
        pushDTO.setData(data);
        log.info("pushDTO={}", JSON.toJSONString(pushDTO));
        remoteWxPushService.sendMessage(pushDTO);
    }

}
