package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 师傅端服务订单优惠券表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-22
 */
@Getter
@Setter
@TableName("tebo_service_order_coupon")
public class TeboServiceOrderCouponDO extends Model<TeboServiceOrderCouponDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    @TableField("service_order_id")
    private Long serviceOrderId;

    /**
     * 礼包id
     */
    @TableField("pack_id")
    private Long packId;

    /**
     * 优惠券id
     */
    @TableField("coupon_id")
    private Long couponId;

    /**
     * 优惠券码
     */
    @TableField("coupon_code")
    private String couponCode;

    /**
     * 卡券编码
     */
    @TableField("unique_code")
    private String uniqueCode;

    /**
     * 优惠券名称
     */
    @TableField("coupon_name")
    private String couponName;

    /**
     * 优惠券金额,单位分
     */
    @TableField("coupon_price")
    private Integer couponPrice;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建者
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
