package com.tebo.rescue.mapper;

import com.tebo.rescue.entity.TeboRescueMsgDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.rescue.web.domain.msg.TeboRescueMsgQueryDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 工单超时消息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Mapper
public interface TeboRescueMsgMapper extends TeboBaseMapper<TeboRescueMsgDO> {

    /**
     * 未读消息列表
     */
    List<TeboRescueMsgDO> getMsgList(TeboRescueMsgQueryDTO queryDTO);

}
