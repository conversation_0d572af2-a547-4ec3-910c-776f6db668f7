package com.tebo.rescue.redisson.queue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024/3/9 14:11
 * @Desc : 延迟队列业务枚举
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum RedisDelayQueueEnum {
    TEBO_PACK_ORDER_PAY_TIMEOUT("TEBO_PACK_ORDER_PAYMENT_TIMEOUT","订单支付超时，自动取消订单", "teboPackOrderPayTimeout")
    ;
    /**
     * 延迟队列 Redis Key
     */
    private String code;

    /**
     * 中文描述
     */
    private String name;

    /**
     * 延迟队列具体业务实现的 Bean
     * 可通过 Spring 的上下文获取
     */
    private String beanId;

}
