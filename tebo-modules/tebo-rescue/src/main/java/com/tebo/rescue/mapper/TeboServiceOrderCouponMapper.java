package com.tebo.rescue.mapper;

import com.tebo.rescue.entity.TeboServiceOrderCouponDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 师傅端服务订单优惠券表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-22
 */
@Mapper
public interface TeboServiceOrderCouponMapper extends TeboBaseMapper<TeboServiceOrderCouponDO> {

    /**
     * 批量插入
     */
    void batchInsert(@Param("list") List<TeboServiceOrderCouponDO> serviceOrderCouponDOList);


    List<TeboServiceOrderCouponDO> selectByServiceOrderId(@Param("serviceOrderId") Long serviceOrderId);
}
