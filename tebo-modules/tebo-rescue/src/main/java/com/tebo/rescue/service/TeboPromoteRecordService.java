package com.tebo.rescue.service;

import com.tebo.rescue.api.domain.dto.TeboPromoteRecordDTO;
import com.tebo.rescue.api.domain.view.TeboPromoteRecordVO;
import com.tebo.rescue.web.domain.dto.promote.PromoteQueryDTO;

import java.util.List;

public interface TeboPromoteRecordService {
    /**
     * 添加记录
     */
    void activateBinding(TeboPromoteRecordDTO teboPromoteRecordDTO);

    /**
     *
     */
    TeboPromoteRecordVO selectByUnionId(String unionId);

    /**
     * 被推广人列表
     * @param promoteQueryDTO
     * @return
     */
    List<TeboPromoteRecordVO> getPromotedPersonList(PromoteQueryDTO promoteQueryDTO);

    /**
     * 通过被推广人手机号查询推广人
     */
    TeboPromoteRecordVO getByPromotedPhone(PromoteQueryDTO queryDTO);

    /**
     * 修改被推荐人备注
     */
    void updatePromotedPersonName(PromoteQueryDTO promoteQueryDTO);

    /**
     * 激活-车销渠道
     * @param teboPromoteRecordDTO
     */
    void activeCarSaleChannel(TeboPromoteRecordDTO teboPromoteRecordDTO);

    /**
     * 车销渠道列表
     */
    TeboPromoteRecordVO getCarSellOrderList(PromoteQueryDTO promoteQueryDTO);
}