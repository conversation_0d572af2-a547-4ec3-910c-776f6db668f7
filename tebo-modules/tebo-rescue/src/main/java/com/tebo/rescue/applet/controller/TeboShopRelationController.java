package com.tebo.rescue.applet.controller;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.domain.AjaxResult;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.DistanceUtil;
import com.tebo.rescue.applet.domain.dto.TeboCustomerShopQueryDTO;
import com.tebo.rescue.applet.domain.view.TeboShopConsumerVO;
import com.tebo.rescue.service.TeboShopRelationService;
import com.tebo.system.api.RemoteShopService;
import com.tebo.system.api.domain.dto.TeboShopQueryDTO;
import com.tebo.system.api.domain.view.TeboShopListVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ZhangFan
 * @date : 2025/03/29 10:00
 * @Desc : 管家门店相关
 */
@Slf4j
@RestController
@RequestMapping("/applet/shop")
public class TeboShopRelationController extends BaseController {

    @Resource
    private TeboShopRelationService teboShopRelationService;

    /**
     * 订单分配数据
     * @return
     */
    @GetMapping("/order/info")
    public AjaxResult orderInfo(@RequestParam("orderId") Long orderId) {
        if (ObjectUtils.isEmpty(orderId)) {
            throw new ServiceException("订单id不可为空");
        }
        return success(teboShopRelationService.orderInfo(orderId));
    }

    /**
     * 分配门店
     * @return
     */
    @PostMapping("/getList")
    public TableDataInfo getList(@RequestBody TeboCustomerShopQueryDTO query) {
        if (CollectionUtils.isEmpty(query.getUpperCodeList())) {
            throw new ServiceException("商CRMS账号不可为空");
        }
        if (ObjectUtils.isEmpty(query.getOrderId())) {
            throw new ServiceException("订单id不可为空");
        }
        Page page = PageHelper.startPage(query.getPageNum(), query.getPageSize());
        return getDataTable(teboShopRelationService.getList(query), page);
    }

    /**
     * 订单激活明细
     * @return
     */
    @PostMapping("/getDetailList")
    public TableDataInfo getDetailList(@RequestBody TeboCustomerShopQueryDTO query) {
        if (ObjectUtils.isEmpty(query.getOrderId())) {
            throw new ServiceException("订单id不可为空");
        }
        Page page = PageHelper.startPage(query.getPageNum(), query.getPageSize());
        return getDataTable(teboShopRelationService.getDetailList(query), page);
    }

    /**
     * 订单分配
     * @return
     */
    @PostMapping("/splitList")
    public AjaxResult splitList(@RequestBody TeboCustomerShopQueryDTO query) {
        if (CollectionUtils.isEmpty(query.getIdList())) {
            throw new ServiceException("主键id不可为空");
        }
        if (ObjectUtils.isEmpty(query.getOrderId())) {
            throw new ServiceException("订单id不可为空");
        }
        teboShopRelationService.splitList(query.getIdList(), query.getOrderId());
        return success();
    }

    /**
     * 订单分配-new
     * @return
     */
    @PostMapping("/splitSingle")
    public AjaxResult splitSingle(@RequestBody TeboCustomerShopQueryDTO query) {
        if (ObjectUtils.isEmpty(query.getId())) {
            throw new ServiceException("主键id不可为空");
        }
        if (ObjectUtils.isEmpty(query.getOrderId())) {
            throw new ServiceException("订单id不可为空");
        }
        if (ObjectUtils.isEmpty(query.getNumber()) || query.getNumber() > 5) {
            throw new ServiceException("分配数量为空或大于5");
        }
        teboShopRelationService.splitSingle(query.getId(), query.getOrderId(), query.getNumber());
        return success();
    }
    /**
     * 取消已分配
     * @return
     */
    @PostMapping("/cancelSplit")
    public AjaxResult cancelSplit(@RequestBody TeboCustomerShopQueryDTO query) {
        if (ObjectUtils.isEmpty(query.getId())) {
            throw new ServiceException("主键id不可为空");
        }
        if (ObjectUtils.isEmpty(query.getOrderId())) {
            throw new ServiceException("订单id不可为空");
        }
        teboShopRelationService.cancelSplitSingle(query.getId(), query.getOrderId());
        return success();
    }

}
