package com.tebo.rescue.remote;

import com.alibaba.fastjson.JSONObject;
import com.tebo.common.core.domain.R;
import com.tebo.rescue.api.domain.dto.OrderCouponDTO;
import com.tebo.rescue.service.TeboCouponCustomerService;
import com.tebo.rescue.web.domain.view.TeboCouponCustomerVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@Slf4j
@RequestMapping("/open/coupon")
public class RemoteCouponController {
    @Resource
    private TeboCouponCustomerService couponCustomerService;

    /**
     * 通过优惠券编码获取优惠券信息
     * @param couponCode
     * @return
     */
    @GetMapping("/getDetailByCouponCode/{couponCode}")
    public R<TeboCouponCustomerVO> getDetailByCouponCode(@PathVariable("couponCode") String couponCode){
        return R.ok(couponCustomerService.getDetailByCouponCode(couponCode));
    }

    /**
     * 更新优惠券为已使用
     */
    @PostMapping("/updateCouponStatus")
    public R<Integer> updateCouponStatus(@RequestBody OrderCouponDTO orderCouponDTO){
        log.info("orderCouponDTO param:{}", JSONObject.toJSONString(orderCouponDTO));
        return R.ok(couponCustomerService.updateCouponStatus(orderCouponDTO));
    }

    /**
     * 更新优惠券为已使用
     */
    @PostMapping("/updateCouponListStatus")
    public R<Integer> updateCouponListStatus(@RequestBody OrderCouponDTO orderCouponDTO){
        log.info("updateCouponListStatus param:{}", JSONObject.toJSONString(orderCouponDTO));
        return R.ok(couponCustomerService.updateCouponListStatus(orderCouponDTO));
    }

    /**
     * 更新优惠券占用状态
     */
    @PostMapping("/updateCouponOccStatus")
    public R<Integer> updateCouponOccStatus(@RequestBody OrderCouponDTO orderCouponDTO){
        log.info("updateCouponOccStatus param:{}", JSONObject.toJSONString(orderCouponDTO));
        return R.ok(couponCustomerService.updateCouponOccStatus(orderCouponDTO));
    }

    /**
     * 更新优惠券为未使用
     */
    @PostMapping("/updateCouponStatusNotUsed")
    public R<Integer> updateCouponStatusNotUsed(@RequestBody OrderCouponDTO orderCouponDTO){
        log.info("updateCouponStatusNotUsed param:{}", JSONObject.toJSONString(orderCouponDTO));
        return R.ok(couponCustomerService.updateCouponStatusNotUsed(orderCouponDTO));
    }

    /**
     * 给指定用户发放驴充充专属券
     * @param unionId
     * @return
     */
    @GetMapping("/grantLvccCoupon/{unionId}")
    public R<Boolean> grantLvccCoupon(@PathVariable("unionId") String unionId){
        return R.ok(couponCustomerService.grantLvccCoupon(unionId));
    }


    /**
     * 校验电池是否已在核销表中
     * @param batteryCode
     * @return
     */
    @GetMapping("/checkBatteryCode/{batteryCode}")
    public R<Boolean> checkBatteryCode(@PathVariable("batteryCode") String batteryCode){
        return R.ok(couponCustomerService.checkBatteryCode(batteryCode));
    }
}