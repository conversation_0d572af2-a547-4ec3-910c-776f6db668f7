package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 解冻资金记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-14
 */
@Getter
@Setter
@TableName("tebo_unfreeze_funds_record")
public class TeboUnfreezeFundsRecordDO extends Model<TeboUnfreezeFundsRecordDO> {

    private static final long serialVersionUID = 1L;

    @TableField("id")
    private Long id;

    /**
     * 会员卡订单编号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 类型 1:大米 2:电池 3:骑行险
     */
    @TableField("type")
    private Integer type;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    @Override
    public Serializable pkVal() {
        return null;
    }

}
