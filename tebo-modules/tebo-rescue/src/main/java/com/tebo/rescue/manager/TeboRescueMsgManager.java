package com.tebo.rescue.manager;

import com.tebo.rescue.entity.TeboRescueMsgDO;
import com.tebo.rescue.web.domain.msg.TeboRescueMsgQueryDTO;

import java.util.List;

public interface TeboRescueMsgManager {

    /**
     * 插入
     */
    void insert( TeboRescueMsgDO teboRescueMsgDO);

    /**
     * 未读消息列表
     * @return
     */
    List<TeboRescueMsgDO> getMsgList(TeboRescueMsgQueryDTO queryDTO);

    /**
     * 更新消息状态
     */
    public void updateMsgStatus(Long id);
}