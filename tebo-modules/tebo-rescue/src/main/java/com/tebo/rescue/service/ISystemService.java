package com.tebo.rescue.service;

import com.tebo.rescue.applet.domain.dto.ProductCheckDTO;
import com.tebo.rescue.api.domain.view.CheckBatteryVO;
import com.tebo.rescue.applet.domain.view.ProductCheckVO;

public interface ISystemService {

    /**
     * 防伪信息查询
     * @param checkDTO
     * @return
     */
    ProductCheckVO checkProduct( ProductCheckDTO checkDTO);

    CheckBatteryVO checkTianNengBattery(ProductCheckDTO checkDTO);
    /**
     * 检查是否可以买保险单
     * @param checkDTO
     * @return
     */
    Boolean byInsurance(ProductCheckDTO checkDTO);
}
