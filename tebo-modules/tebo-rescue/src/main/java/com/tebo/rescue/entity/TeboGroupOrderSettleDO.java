package com.tebo.rescue.entity;

import com.tebo.common.util.number.SnowFlakeUtil;
import lombok.Data;

@Data
public class TeboGroupOrderSettleDO {

    private Long id;

    private String orderNo;

    private String walletId;

    private Integer amount;

    private String remark;

    private Integer status;

    public TeboGroupOrderSettleDO(String orderNo, Integer amount, String remark) {
        this.id = SnowFlakeUtil.nextId();
        this.orderNo = orderNo;
        this.walletId = "1856202787096887295";
        this.amount = amount;
        this.remark = remark;
        this.status = 1;
    }
    public TeboGroupOrderSettleDO() {

    }
}
