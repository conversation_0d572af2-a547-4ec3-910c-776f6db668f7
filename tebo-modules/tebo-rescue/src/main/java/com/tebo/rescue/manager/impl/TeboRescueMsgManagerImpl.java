package com.tebo.rescue.manager.impl;

import com.tebo.rescue.entity.TeboRescueMsgDO;
import com.tebo.rescue.manager.TeboRescueMsgManager;
import com.tebo.rescue.mapper.TeboRescueMsgMapper;
import com.tebo.rescue.web.domain.msg.TeboRescueMsgQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class TeboRescueMsgManagerImpl implements TeboRescueMsgManager {

    @Resource
    private TeboRescueMsgMapper teboRescueMsgMapper;

    @Override
    public void insert(TeboRescueMsgDO teboRescueMsgDO) {
        teboRescueMsgMapper.insert(teboRescueMsgDO);
    }

    @Override
    public List<TeboRescueMsgDO> getMsgList(TeboRescueMsgQueryDTO queryDTO) {
        return teboRescueMsgMapper.getMsgList(queryDTO);
    }

    @Override
    public void updateMsgStatus(Long id) {
        TeboRescueMsgDO teboRescueMsgDO = new TeboRescueMsgDO();
        teboRescueMsgDO.setId(id);
        teboRescueMsgDO.setReader(1);
        teboRescueMsgMapper.updateById(teboRescueMsgDO);
    }

}