package com.tebo.rescue.service.impl;

import com.tebo.common.core.domain.R;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.CodeGenerator;
import com.tebo.common.util.number.MoneyUtil;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.common.util.time.LocalDateUtil;
import com.tebo.mall.api.RemoteGoodsService;
import com.tebo.mall.api.domain.dto.TeboGoodsQueryDTO;
import com.tebo.mall.api.domain.dto.TeboPlatGoodsQueryDTO;
import com.tebo.mall.api.domain.view.TeboGoodsVO;
import com.tebo.rescue.applet.domain.view.TeboShopConsumerVO;
import com.tebo.rescue.entity.TeboCouponDO;
import com.tebo.rescue.entity.TeboCouponGoodsDO;
import com.tebo.rescue.entity.TeboCouponShopDO;
import com.tebo.rescue.entity.TeboGiftPackCouponDO;
import com.tebo.rescue.enums.CouponIdEnum;
import com.tebo.rescue.manager.TeboCouponManager;
import com.tebo.rescue.mapper.TeboCouponGoodsMapper;
import com.tebo.rescue.mapper.TeboCouponMapper;
import com.tebo.rescue.mapper.TeboCouponShopMapper;
import com.tebo.rescue.mapper.TeboGiftPackCouponMapper;
import com.tebo.rescue.service.TeboCouponService;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponCreateDTO;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponGoodsDTO;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponQueryDTO;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponUpdateDTO;
import com.tebo.rescue.web.domain.view.TeboCouponCustomerRecordVO;
import com.tebo.rescue.web.domain.view.TeboCouponVO;
import com.tebo.rescue.web.domain.view.TeboGiftPackCouponVO;
import com.tebo.system.api.RemoteAreaService;
import com.tebo.system.api.RemoteCustomerService;
import com.tebo.system.api.RemoteShopService;
import com.tebo.system.api.domain.dto.TeboShopQueryDTO;
import com.tebo.system.api.domain.dto.TeboShopQueryParamDTO;
import com.tebo.system.api.domain.view.TeboShopListVO;
import com.tebo.system.api.model.TeboConsumer;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ZhangFan
 * @date : 2023/12/13 12:41
 * @Desc :
 */
@Slf4j
@Service
public class TeboCouponServiceImpl implements TeboCouponService {

    @Resource
    private TeboCouponMapper teboCouponMapper;
    @Resource
    private TeboCouponShopMapper teboCouponShopMapper;
    @Resource
    private TeboCouponGoodsMapper teboCouponGoodsMapper;
    @Resource
    private TeboGiftPackCouponMapper teboGiftPackCouponMapper;
    @Resource
    private TeboCouponManager teboCouponManager;
    @Resource
    private RemoteAreaService teboRemoteAreaService;
    @Resource
    private RemoteGoodsService remoteGoodsService;
    @Resource
    private RemoteShopService remoteShopService;
    @Resource
    private RemoteCustomerService remoteCustomerService;

    @Override
    public List<TeboCouponVO> getCouponList(TeboCouponQueryDTO query) {
        List<TeboCouponDO> couponDOList = teboCouponMapper.getCouponList(query);
        if (CollectionUtils.isEmpty(couponDOList)) {
            return java.util.Collections.emptyList();
        }
        List<TeboCouponVO> result = BeanConvert.copyList(couponDOList, TeboCouponVO::new);
        if (query.getDetail()) {
            List<TeboGiftPackCouponDO> giftPackCouponDOList = teboGiftPackCouponMapper.selectByCouponIds(result.stream().map(TeboCouponVO::getId).collect(Collectors.toList()));
            List<Long> couponIdList = giftPackCouponDOList.stream().map(TeboGiftPackCouponDO::getCouponId).collect(Collectors.toList());
            result.stream().forEach(item ->{
                // 卡券被礼包绑定，即不可编辑
                if (!CollectionUtils.isEmpty(couponIdList)) {
                    if (couponIdList.contains(item.getId())) {
                        item.setIsEdit(1);
                    }else {
                        item.setIsEdit(2);
                    }
                } else {
                    item.setIsEdit(2);
                }
            });
        }
        return result;
    }

    @Override
    public List<TeboGiftPackCouponVO> getCouponListByIds(List<Long> ids) {
        List<TeboCouponDO> couponDOList = teboCouponMapper.getCouponListByIds(ids);
        List<TeboGiftPackCouponVO> result = BeanConvert.copyList(couponDOList, TeboGiftPackCouponVO::new);
        return result;
    }

    @Override
    public TeboCouponVO getInfo(Long id) {
        TeboCouponVO couponVO = new TeboCouponVO();
        TeboCouponDO couponDO = teboCouponMapper.selectById(id);
        List<Long> shopIds = teboCouponShopMapper.selectByCouponId(id).stream().map(TeboCouponShopDO::getShopId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(shopIds)){
            TeboShopQueryDTO teboShopQueryDTO = new TeboShopQueryDTO();
            teboShopQueryDTO.setIdList(shopIds);
            R<List<TeboShopListVO>> shopList = remoteShopService.fullyQuery(teboShopQueryDTO);
            couponVO.setShopList(shopList.getData());
        }
        couponVO.setShopIds(shopIds);
        if (couponDO == null){
            return null;
        }
        /**
         * 指定非平台商品
         */
        if (couponDO.getGoodLimit() == 2 || couponDO.getGoodLimit() == 1) {
            List<Long> goodsIdList = teboCouponGoodsMapper.selectByCouponId(id).stream().map(TeboCouponGoodsDO::getGoodsId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(goodsIdList)){
                TeboGoodsQueryDTO goodsQueryDTO = new TeboGoodsQueryDTO();
                goodsQueryDTO.setGoodsIdList(goodsIdList);
                R<List<TeboGoodsVO>> goodsListR = remoteGoodsService.list(goodsQueryDTO);
                couponVO.setGoodsList(goodsListR.getData());
            }
        }
        BeanConvert.copy(couponDO, couponVO);
        return couponVO;
    }

    @Override
    public int create(TeboCouponCreateDTO create) {
        TeboCouponDO couponDO = buildTeboCouponDO(create);
        // todo 先循环插入，后面改为批量
         if (!Collections.isEmpty(create.getShopIds())) {
              List<TeboCouponShopDO> shopDOList = new ArrayList<>();
             create.getShopIds().stream().forEach(shopId -> {
             TeboCouponShopDO shopDO = new TeboCouponShopDO(couponDO.getId(), shopId);
             shopDOList.add(shopDO);
              // teboCouponShopMapper.insert(shopDO);
             });
             if (!CollectionUtils.isEmpty(shopDOList)){
                 teboCouponShopMapper.batchInsert(shopDOList);
             }
         }
        // // 约定 000 为全国
        // if ("000".equals(create.getArea())) {
        //     couponDO.setAreaCode("000");
        //     couponDO.setAreaName("全国");
        // }else {
        //     String[]  areaCodeArr=couponDO.getArea().split(",");
        //     couponDO.setAreaCode(areaCodeArr[2]);
        //     TeboAreaVO areaVO = teboRemoteAreaService.selectAreaByCode(create.getArea()).getData();
        //     couponDO.setAreaName(areaVO.getProvince() + "-" + areaVO.getCity() + "-"  + areaVO.getDistrict());
        // }
        if (create.getGoodLimit() != 0 && !Collections.isEmpty(create.getGoodsList())) {
            // 插入卡券商品关联
            List<TeboCouponGoodsDTO> goodsList = create.getGoodsList();
            List<TeboCouponGoodsDO> couponGoodsList = new ArrayList<>();
            goodsList.stream().forEach(goods -> {
                TeboCouponGoodsDO item = new TeboCouponGoodsDO(couponDO.getId(),goods.getGoodsId(),goods.getGoodsNo());
                item.setId(SnowFlakeUtil.nextId());
                couponGoodsList.add(item);
            });
            if (!CollectionUtils.isEmpty(couponGoodsList)){
                teboCouponGoodsMapper.batchInsert(couponGoodsList);
            }
        }
        teboCouponMapper.insert(couponDO);
        return 1;
    }

    @Override
    public int update(TeboCouponUpdateDTO update) {
        TeboCouponDO couponDO = teboCouponMapper.selectById(update.getId());
        if (ObjectUtils.isEmpty(couponDO)) {
            return 0;
        }
        BeanConvert.copy(update, couponDO);
        if (!StringUtils.isEmpty(update.getStartTimeStr())) {
            couponDO.setStartTime(LocalDateUtil.parseDate(update.getStartTimeStr().substring(0, 10)));
        }
        if (!StringUtils.isEmpty(update.getEndTimeStr())) {
            couponDO.setEndTime(LocalDateUtil.parseDate(update.getEndTimeStr().substring(0, 10)));
        }
        if (!StringUtils.isEmpty(update.getUseReduceStr())) {
            couponDO.setUseReduce(MoneyUtil.yuanToFen(update.getUseReduceStr()));
        }
        if (!StringUtils.isEmpty(update.getParValueStr())) {
            couponDO.setParValue(MoneyUtil.yuanToFen(update.getParValueStr()));
        }
         if (!Collections.isEmpty(update.getShopIds())) {
             teboCouponShopMapper.deleteByCouponId(couponDO.getId());
              List<TeboCouponShopDO> shopDOList = new ArrayList<>();
              update.getShopIds().stream().forEach(shopId -> {
                 TeboCouponShopDO shopDO = new TeboCouponShopDO(couponDO.getId(), shopId);
                  shopDOList.add(shopDO);
                // teboCouponShopMapper.insert(shopDO);
             });
             if (!CollectionUtils.isEmpty(shopDOList)){
                 teboCouponShopMapper.batchInsert(shopDOList);
             }
         }
        // if (!StringUtils.isEmpty(update.getArea())) {
        //     // 约定 000 为全国
        //     if ("000".equals(update.getArea())) {
        //         couponDO.setAreaCode("000");
        //         couponDO.setAreaName("全国");
        //     }else {
        //         String[]  areaCodeArr=update.getArea().split(",");
        //         couponDO.setAreaCode(areaCodeArr[2]);
        //         TeboAreaVO areaVO = teboRemoteAreaService.selectAreaByCode(update.getArea()).getData();
        //         couponDO.setAreaName(areaVO.getProvince() + "-" + areaVO.getCity() + "-"  + areaVO.getDistrict());
        //     }
        // }
        if (update.getGoodLimit() != 0 && !Collections.isEmpty(update.getGoodsList())) {
            teboCouponGoodsMapper.deleteByCouponId(couponDO.getId());
            // 插入卡券商品关联
            List<TeboCouponGoodsDTO> goodsList = update.getGoodsList();
            List<TeboCouponGoodsDO> couponGoodsList = new ArrayList<>();
            goodsList.stream().forEach(goods -> {
                TeboCouponGoodsDO item = new TeboCouponGoodsDO(couponDO.getId(),goods.getGoodsId(),goods.getGoodsNo());
                item.setId(SnowFlakeUtil.nextId());
                couponGoodsList.add(item);
            });
            if (!CollectionUtils.isEmpty(couponGoodsList)){
                teboCouponGoodsMapper.batchInsert(couponGoodsList);
            }
        }
        return teboCouponMapper.updateById(couponDO);
    }

    @Override
    public int batchCouponInUse(List<Long> ids) {
        ids.stream().forEach(id -> {
            TeboCouponDO couponDO = teboCouponMapper.selectById(id);
            couponDO.setInPack(1);
            teboCouponMapper.updateById(couponDO);
        });
        return ids.size();
    }

    @Override
    public int batchCouponNotInUse(List<Long> ids) {
        ids.stream().forEach(id -> {
            TeboCouponDO couponDO = teboCouponMapper.selectById(id);
            couponDO.setInPack(0);
            teboCouponMapper.updateById(couponDO);
        });
        return ids.size();
    }

    @Override
    public int updateCouponStatus(Long id, Integer status) {
        TeboCouponDO couponDO = teboCouponMapper.selectById(id);
        couponDO.setStatus(status);
        int num = teboCouponMapper.updateById(couponDO);
        if (num > 0) {
            // 更新成已作废
            if (status == 10) {
                // 删除用户卡券 + 领用数据
                Long couponId = couponDO.getId();
                teboCouponManager.batchDelCouponStatus(couponId);
            }

        }
        return 1;
    }

    @Override
    public List<TeboShopConsumerVO>  queryCouponShop(Long couponId) {
        TeboCouponDO couponDO = teboCouponMapper.selectById(couponId);
        if (Objects.isNull(couponDO)) {
            return java.util.Collections.emptyList();
        }
        // 全部门店
        TeboShopQueryParamDTO shopQueryDTO = new TeboShopQueryParamDTO();
         if (couponDO.getVest() == 1) {
             // 一期暂无 仅限指定门店
             List<Long> shopIds = teboCouponShopMapper.selectByCouponId(couponId).stream().map(TeboCouponShopDO::getShopId).collect(Collectors.toList());
             if (!CollectionUtils.isEmpty(shopIds)){
                 shopQueryDTO.setIdList(shopIds);
             }
         }else {
             if (couponDO.getTenantId() != 0) {
                 shopQueryDTO.setTenantId(couponDO.getTenantId());
             }
         }
         List<String> couponIdList = new ArrayList<>();
         couponIdList.add(CouponIdEnum.AN_XIN_BATTERY.getCode());
         couponIdList.add(CouponIdEnum.JU_HUI_BATTERY.getCode());
         if (couponIdList.contains(couponDO.getId().toString())){
             shopQueryDTO.setVip(1);
         }
         List<TeboShopListVO> shopListVOList = remoteShopService.getShopList(shopQueryDTO).getData();
        return BeanConvert.copyList(shopListVOList, TeboShopConsumerVO::new);
    }

    @Override
    public List<TeboCouponCustomerRecordVO> getCouponCustomerList(TeboCouponQueryDTO query) {
        return teboCouponManager.getCouponCustomerList(query);
    }

    @Override
    public void createCouponCustomerRecord(String uniqueCode, String unionId) {
        TeboConsumer consumer = remoteCustomerService.selectByUnionId(unionId).getData();
        teboCouponManager.createCouponCustomerRecord(uniqueCode,consumer);
    }

    private TeboCouponDO buildTeboCouponDO(TeboCouponCreateDTO create) {
        String couponCode = CodeGenerator.generateYHQCode();
        Long mainId = SnowFlakeUtil.nextId();
        TeboCouponDO couponDO = new TeboCouponDO(mainId, couponCode);
        BeanConvert.copy(create, couponDO);
        // // 默认为平台创建
        if (Objects.isNull(create.getTenantId())) {
            couponDO.setTenantId(0L);
        }
        couponDO.setResidueNum(create.getPublishNum());
        couponDO.setParValue(MoneyUtil.yuanToFen(create.getParValueStr()));
        // 默认购买即发放，即系统推送
        couponDO.setReceiveType(0);
        if (!StringUtils.isEmpty(create.getUseReduceStr())) {
            couponDO.setUseReduce(MoneyUtil.yuanToFen(create.getUseReduceStr()));
        }
        // 默认状态为 未开始
        couponDO.setStatus(1);
        if (!StringUtils.isEmpty(create.getStartTimeStr())) {
            couponDO.setStartTime(LocalDateUtil.parseDate(create.getStartTimeStr().substring(0, 10)));
            couponDO.setEndTime(LocalDateUtil.parseDate(create.getEndTimeStr().substring(0, 10)));
        }
        return couponDO;
    }
}
