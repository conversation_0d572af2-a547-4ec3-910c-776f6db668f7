package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 师傅端服务订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Getter
@Setter
@TableName("tebo_service_order")
public class TeboServiceOrderDO extends Model<TeboServiceOrderDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 维修师傅id
     */
    @TableField("account_id")
    private Long accountId;

    /**
     * 维修师傅名称
     */
    @TableField("account_name")
    private String accountName;

    /**
     * 门店id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 门店名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 0:旗舰店 1:标准店 2:社区店
     */
    @TableField("shop_type")
    private Integer shopType;

    /**
     * 合伙人id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 和c端选择的维修类型一样
     * 0:维修 1:安装 2:保养 3:洗车 4:保险单
     */
    @TableField("order_type")
    private Integer orderType;

    /**
     * 叫号订单类型 0:维修 1:安装 2:保养 3:洗车
     * 注：暂时没用
     */
    @TableField("queue_order_type")
    private Integer queueOrderType;

    /**
     * 服务类型
     * 0:维修-更换轮胎，1维修-更换电池，2维修-更换刹车片，3维修-车体维修，4维修-更换机油，5维修-更换充电器，6维修-修补轮胎，
     * 7安装-整车安装，8保养-更换机油，9保养-更换轮胎，10保养-更换电池，11保养-更换刹车片，12-更换充电器，13洗车-洗车
     */
    @TableField("service_type")
    private Integer serviceType;


    /**
     * 订单状态 0:维修中 1:待评价 2:已取消 3:待付款  4：已评价
     * @see ServiceOrderStatusEnum
     */
    @TableField("order_status")
    private Integer orderStatus;

    @TableField("maintain_process")
    private Integer maintainProcess;

    /**
     * C端叫号单id
     */
    @TableField("queue_order_id")
    private Long queueOrderId;

    /**
     * 用户名称
     */
    @TableField("nick_name")
    private String nickName;

    /**
     * 用户手机号
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 工位名称
     */
    @TableField("station_name")
    private String stationName;

    /**
     * 处理结果
     */
    @TableField("handle_result")
    private String handleResult;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 服务基础费用,单位分
     */
    @TableField("base_amount")
    private Integer baseAmount;

    /**
     * 订单金额,单位分
     */
    @TableField("order_amount")
    private Integer orderAmount;

    /**
     * 实际支付金额,单位分
     */
    @TableField("actual_pay_amount")
    private Integer actualPayAmount;

    @TableField("pay_time")
    private LocalDateTime payTime;

    /**
     * 支付方式 0:微信
     */
    @TableField("pay_type")
    private Integer payType;

    /**
     * 支付人
     */
    @TableField("pay_by")
    private String payBy;

    /**
     * 是否代付
     */
    @TableField("is_agent_pay")
    private Integer isAgentPay;

    /**
     * 是否快捷开单
     */
    @TableField("is_quick_order")
    private Boolean isQuickOrder;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 取消时间
     */
    @TableField(value = "cancel_time")
    private LocalDateTime cancelTime;

    /**
     * 取消人
     */
    @TableField(value = "cancel_by")
    private String cancelBy;


    /**
     * 业务类型 1 到店  2 救援服务 3 预约上门 4 预约到店
     * 对应枚举
     */
    @TableField(value = "queue_business_type")
    private Integer queueBusinessType;


    /**
     * 取消原因
     *
     * @return
     */
    @TableField("cancel_reason")
    private String cancelReason;

    /**
     * 是否换新
     */
    @TableField("change_new")
    private Boolean changeNew;


    @Override
    public Serializable pkVal() {
        return this.id;
    }


    /**
     * 折扣金额 元
     */
    @TableField("discount_amount")
    private Integer discountAmount;

    @TableField("unionid")
    private String unionid;


    /**
     * 出发时间
     */
    @TableField("depart_time")
    private LocalDateTime departTime;

    /**
     * 到达时间
     */
    @TableField("arrive_time")
    private LocalDateTime arriveTime;


    /**
     * 完成时间
     */
    @TableField("finish_time")
    private LocalDateTime finishTime;


    /**
     * 开单时间
     */
    @TableField("open_order_time")
    private LocalDateTime openOrderTime;


    /**
     * 类目名称
     */
    @TableField("category_name")
    private String categoryName;

}
