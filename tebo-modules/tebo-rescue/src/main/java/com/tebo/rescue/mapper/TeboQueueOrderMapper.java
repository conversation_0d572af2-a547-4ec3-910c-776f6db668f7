package com.tebo.rescue.mapper;

import com.tebo.rescue.applet.domain.dto.QueueOrderQueryDTO;
import com.tebo.rescue.applet.domain.dto.QueueOrderRankQueryDTO;
import com.tebo.rescue.applet.domain.view.QueueOrderStatusCountVO;
import com.tebo.rescue.applet.domain.view.ShopCustomerVO;
import com.tebo.rescue.entity.TeboQueueOrderDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * C端服务订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-13
 */
@Mapper
public interface TeboQueueOrderMapper extends TeboBaseMapper<TeboQueueOrderDO> {

    /**
     * 查询订单列表
     */
    List<TeboQueueOrderDO> list(QueueOrderQueryDTO queryDTO);

    /**
     * 查询当前订单前还有多少未处理订单
     * @param queryDTO
     * @return
     */
    Integer selectRank(QueueOrderRankQueryDTO queryDTO);

    /**
     * 获取下一个待叫号的订单
     * @param teboQueueOrderDO
     * @return
     */
    Long selectNextQueueId(TeboQueueOrderDO teboQueueOrderDO);

    /**
     * 查询今天之前未叫号的订单
     */
    List<TeboQueueOrderDO> listUnCallingbeforeToday(QueueOrderQueryDTO queryDTO);

    /**
     * 批量设置为取消
     * @param idList
     */
    void batchCancel(@Param("idList") List<Long> idList);

    /**
     * 订单状态统计
     */
    List<QueueOrderStatusCountVO> orderStatusCount(QueueOrderQueryDTO queryDTO);

    /**
     * 获取门店-客户数量映射
     */
    List<ShopCustomerVO> getShopCustomerNum(QueueOrderQueryDTO queryDTO);
}
