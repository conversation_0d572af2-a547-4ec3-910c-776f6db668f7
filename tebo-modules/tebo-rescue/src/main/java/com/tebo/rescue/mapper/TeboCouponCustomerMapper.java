package com.tebo.rescue.mapper;

import com.tebo.rescue.api.domain.dto.OrderCouponDTO;
import com.tebo.rescue.entity.TeboCouponCustomerDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.rescue.web.domain.dto.coupon.TeboCouponCustomerQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 消费者优惠券表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
@Mapper
public interface TeboCouponCustomerMapper extends TeboBaseMapper<TeboCouponCustomerDO> {

    List<TeboCouponCustomerDO> getExpireList();

    List<TeboCouponCustomerDO> getList(TeboCouponCustomerQueryDTO query);

    /**
     * 电池排序
     * @param query
     * @return
     */
    List<TeboCouponCustomerDO> getBatteryList(TeboCouponCustomerQueryDTO query);

    List<TeboCouponCustomerDO> getCouponCustomerList(@Param("unionId") String unionId);

    TeboCouponCustomerDO getDetailByCouponCode(String couponCode);

    /**
     * 根据唯一码列表获取列表
     */
    List<TeboCouponCustomerDO> getCouponListByUniqueList(@Param("list") List<String> uniqueCodeList);

    int insertBatch(List<TeboCouponCustomerDO> list);

    int batchUpdateByUniqueCode(@Param("list") List<String> uniqueCodeList, @Param("status") Integer status);

    int batchDelCouponStatus(@Param("couponId")Long couponId);

    int batchExpireCouponStatus(@Param("list")List<Long> couponIds);

    TeboCouponCustomerDO getCustomerCouponByCouponId(@Param("couponId")Long couponId,@Param("unionId")String unionId);

    /**
     * 查询用户大米券,电池券,骑行宝券
     */
    List<TeboCouponCustomerDO> getCustomerCouponList(TeboCouponCustomerQueryDTO query);

    /**
     * 未使用的电池券
     */
    List<TeboCouponCustomerDO> getUnUsedCustomerCouponList(TeboCouponCustomerQueryDTO query);

    /**
     * 大米券排序
     * @param unionId
     * @param orderNo
     * @return
     */
    List<TeboCouponCustomerDO> getCustomerRiceCouponList(TeboCouponCustomerQueryDTO query);
    TeboCouponCustomerDO getCustomerCycleCoupon(@Param("unionId") String unionId,@Param("orderNo") String orderNo);

    /**
     * 根据订单号查询卡券状态
     * @param orderNo
     * @return
     */
    public List<TeboCouponCustomerDO> getCustomerCouponByOrderNo(String orderNo);

    /**
     * 根据流水号查询订单号
     * @param id
     * @return
     */
    String getOrderNoByFlowId(@Param("id") Long id);

    /**
     * 更新卡券状态以及时间
     * @param orderCouponDTO
     * @return
     */
    Integer updateCouponStatusAndTime(OrderCouponDTO orderCouponDTO);

    /**
     * 删除电池券
     */
    void deleteBatteryCoupon(String orderNo);
}
