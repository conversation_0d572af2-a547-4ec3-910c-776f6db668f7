package com.tebo.rescue.applet.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.page.TableDataInfo;
import com.tebo.common.redis.constant.TeboRescueCacheConstant;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.security.util.MaintainerOnlineUserUtil;
import com.tebo.common.util.applet.AppletUtil;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.rescue.applet.domain.dto.*;
import com.tebo.rescue.applet.domain.view.*;
import com.tebo.rescue.enums.QueueOrderStatusEnum;
import com.tebo.rescue.service.IQueueOrderService;
import com.tebo.rescue.service.IServiceOrderService;
import com.tebo.system.api.RemoteAccountService;
import com.tebo.system.api.model.TeboAccountInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

/**
 * @Author：zhengmk
 * @Date 2023/12/14 15:27
 */
@RestController
@RequestMapping("/service/order")
public class ServiceOrderController extends BaseController {

    @Resource
    private IServiceOrderService serviceOrderService;

    @Resource
    private IQueueOrderService queueOrderService;

    @Resource
    private RedisService redisService;

    @Resource
    private HttpServletRequest request;

    @Autowired
    private RemoteAccountService accountService;

    /**
     * 工位列表
     */
    @PostMapping("/stationList")
    public R<List<StationListVO>> list(@RequestBody StationQueryDTO queryDTO) {
        List<StationListVO> list = new ArrayList<>();
        Long userId = MaintainerOnlineUserUtil.getUserId();
        R<TeboAccountInfoVO> accountInfoR = accountService.getAccountInfoById(userId);
        TeboAccountInfoVO accountInfoVO = accountInfoR.getData();
        if (Objects.isNull(accountInfoVO)) {
            throw new ServiceException("未获取到师傅信息");
        }
        Long shopId = accountInfoVO.getShopId();

        if (Objects.equals(queryDTO.getOrderType(), 0)) {
            // TODO 暂时写死工位，后续考虑管理后台配置
            StationListVO listVO1 = new StationListVO();
            listVO1.setStationId(1L);
            listVO1.setStationName("W1");
            setUsed(shopId, listVO1, "W1",userId);

            StationListVO listVO2 = new StationListVO();
            listVO2.setStationId(2L);
            listVO2.setStationName("W2");
            setUsed(shopId, listVO2, "W2",userId);

            StationListVO listVO3 = new StationListVO();
            listVO3.setStationId(3L);
            listVO3.setStationName("W3");
            setUsed(shopId, listVO3, "W3",userId);
            list.add(listVO1);
            list.add(listVO2);
            list.add(listVO3);
        } else {
            // TODO 暂时写死工位，后续考虑管理后台配置
            StationListVO listVO1 = new StationListVO();
            listVO1.setStationId(1L);
            listVO1.setStationName("X1");
            setUsed(shopId, listVO1, "X1",userId);

            StationListVO listVO2 = new StationListVO();
            listVO2.setStationId(2L);
            listVO2.setStationName("X2");
            setUsed(shopId, listVO2, "X2",userId);

            StationListVO listVO3 = new StationListVO();
            listVO3.setStationId(3L);
            listVO3.setStationName("X3");
            setUsed(shopId, listVO3, "X3",userId);

            list.add(listVO1);
            list.add(listVO2);
            list.add(listVO3);
        }

        return R.ok(list);
    }

    /**
     * 设值工位是否被占用
     *
     * @param shopId
     * @param listVO1
     */
    private void setUsed(Long shopId, StationListVO listVO1, String stationName, Long userId) {
        Object w1 = redisService.getCacheObject(TeboRescueCacheConstant.getAccountStationKey(shopId, stationName));
        if (Objects.isNull(w1)) {
            listVO1.setUsed(false);
            listVO1.setSelfUse(false);
        } else {
            listVO1.setUsed(true);
            if (w1.equals(userId)) {
                listVO1.setSelfUse(true);
            } else {
                listVO1.setSelfUse(false);
            }
        }
    }

    /**
     * 师傅端获取待修/已修列表
     */
    @PostMapping("/queueOrderList")
    public TableDataInfo queueOrderList(@RequestBody QueueOrderQueryDTO queryDTO) {

        if (queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            throw new GlobalException("分页参数不能为空");
        }
        if (queryDTO.getSearchType() != null && queryDTO.getSearchType() == 1) {
            queryDTO.setAccountId(MaintainerOnlineUserUtil.getUserId());
            queryDTO.setOrderStatus(QueueOrderStatusEnum.COMPLETED.getCode());
            queryDTO.setCreateTimeStartSecond(LocalDateTime.now().with(LocalTime.MIN));
            queryDTO.setCreateTimeEndSecond(LocalDateTime.now());
        }
        queryDTO.setOrderByColumn("queue_number asc");
        Page page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<QueueOrderListVO> queueOrderListVOList = queueOrderService.listQueueOrder(queryDTO);
        List<QueueOrderWaitVO> list = BeanConvert.copyList(queueOrderListVOList, QueueOrderWaitVO::new);
        return getDataTable(list, page);
    }

    /**
     * 师傅端工单列表
     */
    @PostMapping("/listServiceOrder")
    public R<List<ServiceOrderListVO>> listServiceOrder(@RequestBody ServiceOrderQueryDTO queryDTO) {
        if (queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            throw new GlobalException("分页参数不能为空");
        }
        Long accountId = MaintainerOnlineUserUtil.getUserId();
        queryDTO.setAccountId(accountId);
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        return R.ok(serviceOrderService.listServiceOrder(queryDTO));
    }

    /**
     * 工单各个状态数量
     */
    @PostMapping("customer/count")
    public R<ServiceOrderCountVO> customerCount() {
        String unionid = AppletUtil.getUnionIdByRequest(request);
        return R.ok(serviceOrderService.customerCount(unionid));
    }


    /**
     * c端工单列表
     */
    @PostMapping("/customer/listServiceOrder")
    public R<List<ServiceOrderListVO>> customerListServiceOrder(@RequestBody ServiceOrderQueryDTO queryDTO) {
        if (queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            throw new GlobalException("分页参数不能为空");
        }
        String unionId = AppletUtil.getUnionIdByRequest(request);
        queryDTO.setUnionid(unionId);
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        return R.ok(serviceOrderService.listServiceOrder(queryDTO));
    }

    /**
     * 工单详情
     */
    @GetMapping("/detailServiceOrder/{serviceOrderId}")
    public R<ServiceOrderVO> detailServiceOrder(@PathVariable("serviceOrderId") Long serviceOrderId) {
        return R.ok(serviceOrderService.detailServiceOrder(serviceOrderId));
    }

    /**
     * 工单师傅到达
     */
    @PostMapping("/arrived/{id}")
    public R orderArrived(@PathVariable("id") Long id) {
        serviceOrderService.serviceOrderArrived(id);
        return R.ok();
    }

    /**
     * 师傅出发
     */
    @PostMapping("/departed/{id}")
    public R orderDeparted(@PathVariable("id") Long id) {
        serviceOrderService.serviceOrderDeparted(id);
        return R.ok();
    }


    /**
     * 服务类型列表
     */
    @PostMapping("/listServiceType")
    public R<List<ServiceTypeListVO>> listServiceType(@RequestBody ServiceTypeQueryDTO serviceOrderDTO) {
        return R.ok(serviceOrderService.listServiceType(serviceOrderDTO));
    }

    // 创建服务工单
    @PostMapping("/addServiceOrder")
    public R<Map<String, Object>> addServiceOrder(@RequestBody ServiceOrderDTO serviceOrderDTO) {
        return R.ok(serviceOrderService.addServiceOrder(serviceOrderDTO));
    }

    // 救援服务 开单
    @PostMapping("/addRescueServiceOrder")
    public R<Map<String, Object>> addResuceServiceOrder(@RequestBody ServiceOrderDTO serviceOrderDTO) {
        return R.ok(serviceOrderService.rescueOpenOrder(serviceOrderDTO));
    }

    @GetMapping("/finishServiceOrder/{queueOrderId}")
    public R<Boolean> finishServiceOrder(@PathVariable("queueOrderId") Long queueOrderId) {
        return R.ok(serviceOrderService.finishServiceOrder(queueOrderId));
    }

    @GetMapping("/cancelServiceOrder/{serviceOrderId}")
    public R<Boolean> cancelServiceOrder(@PathVariable("serviceOrderId") Long serviceOrderId) {
        return R.ok(serviceOrderService.cancelServiceOrder(serviceOrderId));
    }

    /**
     * 新的取消接口，支持取消原因参数
     */
    @PostMapping("/cancelServiceOrder")
    public R<Boolean> cancelServiceOrder(@RequestBody ServiceOrderDTO serviceOrderDTO) {
        return R.ok(serviceOrderService.cancelServiceOrder(serviceOrderDTO));
    }


    /**
     * 师傅关单接口
     */
    @PostMapping("/complete/{id}")
    public R complete(@PathVariable("id") Long id) {
        serviceOrderService.completeServiceOrder(id);
        return R.ok();
    }


    /**
     * 获取师傅当前处理订单信息
     */
    @GetMapping("/getCurrentQueueOrder")
    public R<QueueOrderWaitVO> getQueueOrder() {
        Long accountId = MaintainerOnlineUserUtil.getUserId();
        QueueOrderQueryDTO queryDTO = new QueueOrderQueryDTO();
        queryDTO.setAccountId(accountId);
        queryDTO.setOrderStatusList(Arrays.asList(QueueOrderStatusEnum.PROCESSING.getCode(), QueueOrderStatusEnum.WAIT_PAY.getCode()));
        List<QueueOrderListVO> list = queueOrderService.listQueueOrder(queryDTO);
        if (list.isEmpty()) {
            return R.ok(null);
        }
        QueueOrderWaitVO queueOrderWaitVO = new QueueOrderWaitVO();
        BeanConvert.copy(list.get(0), queueOrderWaitVO);
        String key = TeboRescueCacheConstant.TEBO_RESCUE_PREFIX + TeboRescueCacheConstant.RECALL + queueOrderWaitVO.getId().toString() + "_times";
        Integer times = redisService.getCacheObject(key) == null ?
                1 : redisService.getCacheObject(key);
        if (times >= 3) {
            queueOrderWaitVO.setCanCall(1);
        } else {
            queueOrderWaitVO.setCanCall(0);
        }
        return R.ok(queueOrderWaitVO);
    }


    /**
     * TODO 删除
     * 支付成功后，更新订单状态
     */
    @PostMapping("/afterPaySuccess")
    public R<Boolean> afterPay(@RequestBody ServiceOrderAfterPayDTO afterPayDTO) {
        return R.ok(serviceOrderService.afterPay(afterPayDTO));
    }



    /**
     * 快捷开单
     *
     * @return
     */
    @PostMapping("/quick/openOrder")
    public R<QuickOpenServiceOrderResultVO> quickOpenOrder(@RequestBody OpenQuickServiceOrderDTO openQuickServiceOrderDTO) {
        QuickOpenServiceOrderResultVO quickOpenServiceOrderResultVO = serviceOrderService.quickOpenOrder(openQuickServiceOrderDTO);
        return R.ok(quickOpenServiceOrderResultVO);
    }


    /**
     * 快捷开单获取二维码
     * @param serviceOrderId
     * @return
     */
    @GetMapping("/getQuickOrderQrCode/{serviceOrderId}")
    public R<String> getQuickOrderQrCode(@PathVariable("serviceOrderId") Long serviceOrderId) {
        String quickOrderQrCode = serviceOrderService.getQuickOrderQrCode(serviceOrderId);
        return R.ok(quickOrderQrCode);
    }


    @GetMapping("/initShopType")
    public R init() {
        serviceOrderService.initShopType();
        return R.ok();
    }

}
