package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 救援上门服务订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Getter
@Setter
@TableName("tebo_rescue_queue_order")
public class TeboRescueQueueOrderDO extends Model<TeboRescueQueueOrderDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * unionid
     */
    @TableField("unionid")
    private String unionid;

    /**
     * 用户昵称
     */
    @TableField("nick_name")
    private String nickName;

    /**
     * 用户手机号
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 门店id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 门店名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 门店类型
     * 0:旗舰店 1:标准店 2:社区店 3：服务体验中心
     */
    @TableField("shop_type")
    private Integer shopType;

    /**
     * 合伙人id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 维修师傅id
     */
    @TableField("account_id")
    private Long accountId;

    /**
     * 维修师傅名称
     */
    @TableField("account_name")
    private String accountName;

    /**
     * 工位名称
     */
    @TableField("station_name")
    private String stationName;

    /**
     * 订单类型 1 到店  2 救援服务 3 预约上门 4 预约到店
     */
    @TableField("order_type")
    private Integer orderType;

    /**
     * 订单状态 //0, "未叫号",1, "处理中",2, "待付款",3, "已完成",4, "已取消",8, "已失效",9, "已超时"
     * @see QueueOrderStatusEnum
     */
    @TableField("order_status")
    private Integer orderStatus;




    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 订单金额,单位分
     */
    @TableField("order_amount")
    private Integer orderAmount;

    /**
     * 实际支付金额,单位分
     */
    @TableField("actual_pay_amount")
    private Integer actualPayAmount;

    /**
     * 发起时间
     */
    @TableField("initiation_time")
    private LocalDateTime initiationTime;

    /**
     * 发起方式，请求来源
     */
    @TableField("initiation_Type")
    private Integer initiationType;


    /**
     * 预约上门时间
     */
    @TableField("pre_door_time")
    private LocalDateTime preDoorTime;



    /**
     * 支付时间
     */
    @TableField("pay_time")
    private LocalDateTime payTime;



    /**
     * 取消时间
     */
    @TableField("cancel_time")
    private LocalDateTime cancelTime;


    /**
     * 取消者
     */
    @TableField(value = "cancel_by")
    private String cancelBy;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField("del_flag")
    private Boolean delFlag;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;


    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    /**
     * 故障
     */
    @TableField("fault_msg")
    private String faultMsg;


    /**
     * 请求位置
     */
    @TableField("request_location")
    private String requestLocation;


    /**
     * 服务项目
     */
    @TableField("service_item")
    private Integer serviceItem;


    /**
     * 品牌
     */
    @TableField("brand")
    private String brand;

    /**
     * 纬度
     */
    @TableField("lon")
    private String lon;

    /**
     * 经度
     */
    @TableField("lat")
    private String lat;


    /**
     * 直线距离
     */
    @TableField("distance")
    private String distance;


    /**
     * 取消原因
     * @return
     */
    @TableField("cancel_reason")
    private String cancelReason;

    /**
     * 接单时间
     */
    @TableField("receive_time")
    private LocalDateTime receiveTime;

    /**
     * 维修师傅电话
     */
    @TableField("account_phone")
    private String accountPhone;



    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
