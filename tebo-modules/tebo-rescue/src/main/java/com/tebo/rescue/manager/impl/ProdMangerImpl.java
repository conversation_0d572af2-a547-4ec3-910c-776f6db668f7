package com.tebo.rescue.manager.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.rescue.applet.domain.view.CheckBatteryRemoteVO;
import com.tebo.rescue.api.domain.view.CheckBatteryVO;
import com.tebo.rescue.manager.ProdManger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class ProdMangerImpl implements ProdManger {


    @Value("${prod.url}")
    private String prodUrl;

    @Value("${prod.checkTianNengBatteryUrl}")
    private String checkTianNengBatteryUrl;

    @Value("${prod.token}")
    private String prodToken;

    @Override
    public CheckBatteryRemoteVO checkBattery(String code) {
        String url = prodUrl + "/prod-api/tbyOrder/big_customer/checkBattery";
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("battery_code", code);
//        log.info("调用远程防伪接口，参数:{}", JSON.toJSONString(paramMap));
        HttpResponse httpResponse = HttpRequest.get(url).header("token", prodToken).form(paramMap).execute();
//        String res = HttpUtil.get(url, paramMap);

        if (StringUtils.isNotEmpty(httpResponse.body())) {
            JSONObject resJson = JSON.parseObject(httpResponse.body());
//            log.info("调用远程防伪接口，返回:{}", JSON.toJSONString(resJson));
            String resCode = resJson.getString("code");
            if (!"200".equals(resCode)) {
                String msg = resJson.getString("msg");
                if (StringUtils.isNotEmpty(msg)) {
                    throw new ServiceException(msg);
                } else {
                    throw new ServiceException("调用远程防伪接口失败，请稍后再试");
                }

            }
            String dataStr = resJson.getString("data");
            CheckBatteryRemoteVO checkBatteryRemoteVO = JSON.parseObject(dataStr, CheckBatteryRemoteVO.class);
            return checkBatteryRemoteVO;
        }
        return null;
    }

    @Override
    public CheckBatteryVO checkTianNengBattery(String code) {
        String url = checkTianNengBatteryUrl + "/prod-api/tbyOrder/big_customer/checkBatteryNew";
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("battery_code", code);
//        log.info("调用远程防伪接口，参数:{}", JSON.toJSONString(paramMap));
        HttpResponse httpResponse = HttpRequest.get(url).header("token", prodToken).form(paramMap).execute();
//        String res = HttpUtil.get(url, paramMap);

        if (StringUtils.isNotEmpty(httpResponse.body())) {
            JSONObject resJson = JSON.parseObject(httpResponse.body());
//            log.info("调用远程防伪接口，返回:{}", JSON.toJSONString(resJson));
            String resCode = resJson.getString("code");
            String dataStr = resJson.getString("data");
            JSONObject jsonObject  =  JSONObject.parseObject(dataStr);
            String batteryExist = jsonObject.getString("batteryExist");
            if (!"200".equals(resCode)) {
                String msg = resJson.getString("msg");
                if (StringUtils.isNotEmpty(msg)) {
                    throw new ServiceException(msg);
                } else {
                    throw new ServiceException("调用远程防伪接口失败，请稍后再试");
                }

            }
            if (!batteryExist.equals("Y")) {
                throw new ServiceException("非天能电池");
            }
            CheckBatteryVO checkBatteryRemoteVO = JSON.parseObject(dataStr, CheckBatteryVO.class);
            return checkBatteryRemoteVO;
        }
        return null;
    }
}
