package com.tebo.rescue.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.enums.WeChatOfficialAccountEnum;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.core.utils.StringUtils;
import com.tebo.common.core.utils.bean.BeanUtils;
import com.tebo.common.core.utils.excel.ExcelUtil;
import com.tebo.common.security.util.MaintainerOnlineUserUtil;
import com.tebo.common.util.DataUtils;
import com.tebo.common.util.applet.AppletUtil;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.rescue.applet.domain.dto.*;
import com.tebo.rescue.applet.domain.view.excel.RescuePreOrderExportVO;
import com.tebo.rescue.applet.domain.view.CustomerQueueStatusVO;
import com.tebo.rescue.applet.domain.view.MaintenanceQueueOrderNumberVO;
import com.tebo.rescue.applet.domain.view.excel.RescueRescueOrderExportVO;
import com.tebo.rescue.entity.TeboOrderFileDO;
import com.tebo.rescue.entity.TeboRescueMsgDO;
import com.tebo.rescue.entity.TeboRescueQueueOrderDO;
import com.tebo.rescue.entity.TeboServiceOrderDO;
import com.tebo.rescue.enums.*;
import com.tebo.rescue.manager.TeboOrderFileManger;
import com.tebo.rescue.manager.TeboRescueMsgManager;
import com.tebo.rescue.manager.TeboRescueQueueOrderManger;
import com.tebo.rescue.manager.TeboServiceOrderManger;
import com.tebo.rescue.mapper.TeboRescueQueueOrderMapper;
import com.tebo.rescue.service.ITeboRescueQueueOrderService;
import com.tebo.rescue.service.mq.DisMqService;
import com.tebo.rescue.service.pay.TeboWechatPayService;
import com.tebo.rescue.thread.GeTuiPushThreadPool;
import com.tebo.rescue.util.TeboNumberGenerator;
import com.tebo.system.api.*;
import com.tebo.system.api.domain.dto.*;
import com.tebo.system.api.domain.view.TeboShopListVO;
import com.tebo.system.api.domain.view.WechatPrepayResponse;
import com.tebo.system.api.model.TeboAccountInfoVO;
import com.tebo.system.api.model.TeboConsumer;
import com.tebo.system.api.model.TeboShop;
import com.tebo.system.api.remote.RemoteSmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TeboRescueQueueOrderServiceImpl extends ServiceImpl<TeboRescueQueueOrderMapper, TeboRescueQueueOrderDO> implements ITeboRescueQueueOrderService {

    @Autowired
    private TeboRescueQueueOrderManger rescueQueueOrderManger;

    @Autowired
    private RemoteAccountService remoteAccountService;

    @Autowired
    private RemoteFileService remoteFileService;


    @Autowired
    private RemoteShopService remoteShopService;


    @Autowired
    private TeboServiceOrderManger serviceOrderManger;

    @Resource
    private HttpServletRequest request;

    @Autowired
    private TeboOrderFileManger orderFileManger;


    @Autowired
    private RemotePartnerService remotePartnerService;

    @Autowired
    private RemoteWxPushService remoteWxPushService;

    @Autowired
    private RemoteCustomerService remoteCustomerService;

    @Autowired
    private DisMqService disMqService;

    @Autowired
    private RemoteSmsService remoteSmsService;
    @Resource
    private TeboWechatPayService teboWechatPayService;

    @Autowired
    private RemoteALiService remoteALiService;

    @Resource
    private TeboRescueMsgManager teboRescueMsgManager;


    @Value("${applet.appId_c}")
    private String appid;

    @Value("${applet.rescue.receive_template_id}")
    private String receiveTemplateId;

     @Value("${ali.push.voiceCode}")
     private String voiceCode;


    @Transactional
    @Override
    public RescueQueueOrderSaveDTO createRescueOrder(RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO) {
        //校验
        verifyCreateRescueOrder(rescueQueueOrderSaveDTO);

        //判断是否有未完结的排队单
        RescueQueueOrderQueryDTO queueOrderQueryDTO = new RescueQueueOrderQueryDTO();
        if (ObjectUtils.isEmpty(rescueQueueOrderSaveDTO.getUnionid())) {
            queueOrderQueryDTO.setUnionid(AppletUtil.getUnionIdByRequest(request));
        }else {
            queueOrderQueryDTO.setUnionid(rescueQueueOrderSaveDTO.getUnionid());
        }
        queueOrderQueryDTO.setOrderType(QueueBusinessTypeEnum.RESCUE.getCode());
        queueOrderQueryDTO.setOrderStatusList(QueueOrderStatusEnum.getUnFinishStatusList());
        List<TeboRescueQueueOrderDO> dos = rescueQueueOrderManger.listBySO(queueOrderQueryDTO);
        if (CollectionUtil.isNotEmpty(dos)) {
            throw new ServiceException("当前存在未完成的救援单");
        }
        RescueQueueOrderSaveDTO saveDTO = new RescueQueueOrderSaveDTO();
        TeboRescueQueueOrderDO orderDO = createRescueOrderByBusinessType(rescueQueueOrderSaveDTO, QueueBusinessTypeEnum.RESCUE.getCode());

        //推送大屏
        CompletableFuture.runAsync(() -> {
            geTuiPushRescue(orderDO);
        }, GeTuiPushThreadPool.thisPool());


        return BeanConvert.copy(orderDO, saveDTO);
    }

    @Transactional
    @Override
    public WechatPrepayResponse createRescueOrderNew(RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO) {
        //校验
        verifyCreateRescueOrder(rescueQueueOrderSaveDTO);

        //判断是否有未完结的排队单
        RescueQueueOrderQueryDTO queueOrderQueryDTO = new RescueQueueOrderQueryDTO();
        queueOrderQueryDTO.setUnionid(AppletUtil.getUnionIdByRequest(request));
        queueOrderQueryDTO.setOrderType(QueueBusinessTypeEnum.RESCUE.getCode());
        queueOrderQueryDTO.setOrderStatusList(QueueOrderStatusEnum.getUnFinishStatusList());
        List<TeboRescueQueueOrderDO> dos = rescueQueueOrderManger.listBySO(queueOrderQueryDTO);
        if (CollectionUtil.isNotEmpty(dos)) {
            throw new ServiceException("当前存在未完成的救援单");
        }
        rescueQueueOrderSaveDTO.setUnionid(AppletUtil.getUnionIdByRequest(request));
        rescueQueueOrderSaveDTO.setId(SnowFlakeUtil.nextId());
        //根据经纬度，获取合伙人
        TeboPositionQueryDTO queryDTO = new TeboPositionQueryDTO();
        queryDTO.setLgt(rescueQueueOrderSaveDTO.getLon());
        queryDTO.setLnt(rescueQueueOrderSaveDTO.getLat());
        R<TeboShopListVO> shopResult = remotePartnerService.getShopByDistrictCode(queryDTO);
        TeboShopListVO shop = shopResult.getData();
        rescueQueueOrderSaveDTO.setTenantId(shop.getTenantId());
        // 发起预支付
        return teboWechatPayService.rescueOrderBehalfPrePay(rescueQueueOrderSaveDTO).getData();
    }

    private void geTuiPushRescue(TeboRescueQueueOrderDO orderDO) {
        Long shopId = orderDO.getShopId();
        R<TeboShop> shopInfoR = remoteShopService.getShopInfo(shopId);
        TeboShop shop = shopInfoR.getData();
        if (Objects.isNull(shop)) {
            return;
        }
        String cid = shop.getCid();
        GeTuiPushDTO pushDTO = new GeTuiPushDTO();
        pushDTO.setCid(cid);
        pushDTO.setContent("有新的救援单，请接单");
        disMqService.notifyAppMsg(shopId, "有新的救援单，请接单");

    }


    private void geTuiPushDoor(TeboRescueQueueOrderDO orderDO) {
        Long shopId = orderDO.getShopId();
        R<TeboShop> shopInfoR = remoteShopService.getShopInfo(shopId);
        TeboShop shop = shopInfoR.getData();
        if (Objects.isNull(shop)) {
            return;
        }
        String cid = shop.getCid();
        GeTuiPushDTO pushDTO = new GeTuiPushDTO();
        pushDTO.setCid(cid);
        pushDTO.setContent("有新的上门单，请接单");
        disMqService.notifyAppMsg(shopId, "有新的上门单，请接单");
    }

    @Override
    public void handleTimeOutRescueQueueOrder() {

        LocalDateTime now = LocalDateTime.now();
        handleTimeOutOrder(now);

        //超时后10min后，取消
        cancelTimeOutRescueOrder(now);


    }

    /**
     * 处理超时订单
     *
     * @param now
     */
    private void handleTimeOutOrder(LocalDateTime now) {
        RescueQueueOrderQueryDTO queueOrderQueryDTO = new RescueQueueOrderQueryDTO();
        //查询创建近1天到近5分钟的
        queueOrderQueryDTO.setOrderStatus(QueueOrderStatusEnum.UN_CALL.getCode());

        LocalDateTime nowB5 = now.plusMinutes(-5);
        queueOrderQueryDTO.setInitiationTimeStart(now.minusDays(1));
        queueOrderQueryDTO.setInitiationTimeEnd(nowB5);
        queueOrderQueryDTO.setOrderType(QueueBusinessTypeEnum.RESCUE.getCode());
        List<TeboRescueQueueOrderDO> dos = rescueQueueOrderManger.listBySO(queueOrderQueryDTO);
        if (CollectionUtil.isNotEmpty(dos)) {
            List<List<TeboRescueQueueOrderDO>> split = CollectionUtil.split(dos, 50);
            for (List<TeboRescueQueueOrderDO> teboRescueQueueOrderDOS : split) {
                List<TeboRescueQueueOrderDO> updateList = new ArrayList<>();
                for (TeboRescueQueueOrderDO teboRescueQueueOrderDO : teboRescueQueueOrderDOS) {
                    TeboRescueQueueOrderDO update = new TeboRescueQueueOrderDO();
                    update.setOrderStatus(QueueOrderStatusEnum.TIMEOUT.getCode());
                    update.setId(teboRescueQueueOrderDO.getId());
                    updateList.add(update);

                    //推送
                    CompletableFuture.runAsync(() -> {
                        pushTimeOut(teboRescueQueueOrderDO);
                    }, GeTuiPushThreadPool.thisPool());

                }
                updateBatchById(updateList);

            }

        }
    }

    private void pushTimeOut(TeboRescueQueueOrderDO teboRescueQueueOrderDO) {
        Long tenantId = teboRescueQueueOrderDO.getTenantId();
        if (Objects.isNull(tenantId)) {
            return;
        }
        String content = "门店：%s 出现超时未接单，请关注";
        new Thread(new Runnable() {
            @Override
            public void run() {
                TeboRescueMsgDO teboRescueMsgDO = new TeboRescueMsgDO();
                teboRescueMsgDO.setId(SnowFlakeUtil.nextId());
                teboRescueMsgDO.setType(1);
                teboRescueMsgDO.setMsg(String.format(content, teboRescueQueueOrderDO.getShopName()));
                teboRescueMsgDO.setTenantId(tenantId);
                teboRescueMsgDO.setOrderId(teboRescueQueueOrderDO.getId());
                teboRescueMsgManager.insert(teboRescueMsgDO);
            }
        }).start();
        disMqService.notifyOrderTimeout(tenantId, String.format(content, teboRescueQueueOrderDO.getShopName()));
        disMqService.notifyOrderTimeout(0L, String.format(content, teboRescueQueueOrderDO.getShopName()));


    }


    /**
     * 自动取消超时十分钟的救援单
     *
     * @param now
     */
    private void cancelTimeOutRescueOrder(LocalDateTime now) {
        RescueQueueOrderQueryDTO queueOrderQueryDTOTimeOut = new RescueQueueOrderQueryDTO();
        //查询创建近五天到近15分钟的
        queueOrderQueryDTOTimeOut.setOrderStatus(QueueOrderStatusEnum.TIMEOUT.getCode());
        queueOrderQueryDTOTimeOut.setOrderType(QueueBusinessTypeEnum.RESCUE.getCode());
        LocalDateTime nowB15 = now.plusMinutes(-15);
        //取消的时间判断，用第一次创建时间
        queueOrderQueryDTOTimeOut.setCreateTimeStartSecond(now.minusDays(5));
        queueOrderQueryDTOTimeOut.setCreateTimeEndSecond(nowB15);
        List<TeboRescueQueueOrderDO> timeOutDos = rescueQueueOrderManger.listBySO(queueOrderQueryDTOTimeOut);
        if (CollectionUtil.isEmpty(timeOutDos)) {
            return;
        }
        cancelRescueQueueOrderInTask(timeOutDos, LocalDateTime.now());
    }

    /**
     * 预约上门的单子，到预约时间 失效
     */
    public void cancelPreDoorOrder() {
        RescueQueueOrderQueryDTO queueOrderQueryDTOTimeOut = new RescueQueueOrderQueryDTO();
        queueOrderQueryDTOTimeOut.setOrderStatus(QueueOrderStatusEnum.UN_CALL.getCode());
        queueOrderQueryDTOTimeOut.setOrderType(QueueBusinessTypeEnum.PRE_DOOR.getCode());
        // 查询范围 查预约预约时间在现在一小时前到现在的单子
        LocalDateTime now = LocalDateTime.now();
        //今天起始
        queueOrderQueryDTOTimeOut.setPreDoorTimeStart(now.minusHours(1));
        queueOrderQueryDTOTimeOut.setPreDoorTimeEnd(now);
        List<TeboRescueQueueOrderDO> timeOutDos = rescueQueueOrderManger.listBySO(queueOrderQueryDTOTimeOut);
        if (CollectionUtil.isEmpty(timeOutDos)) {
            return;
        }
        cancelPreQueueOrderInTask(timeOutDos, now);
    }

    /**
     * 预约到店的单子，到预约时间取消
     */
    public void cancelPreStoreOrder() {
        RescueQueueOrderQueryDTO queueOrderQueryDTOTimeOut = new RescueQueueOrderQueryDTO();
        queueOrderQueryDTOTimeOut.setOrderStatus(QueueOrderStatusEnum.PROCESSING.getCode());
        queueOrderQueryDTOTimeOut.setOrderType(QueueBusinessTypeEnum.PRE_STORE.getCode());
        // 查询范围 查预约预约时间在现在一小时前到现在的单子
        LocalDateTime now = LocalDateTime.now();
        //今天起始
        queueOrderQueryDTOTimeOut.setPreDoorTimeStart(now.minusHours(1));
        queueOrderQueryDTOTimeOut.setPreDoorTimeEnd(now);
        List<TeboRescueQueueOrderDO> timeOutDos = rescueQueueOrderManger.listBySO(queueOrderQueryDTOTimeOut);
        if (CollectionUtil.isEmpty(timeOutDos)) {
            return;
        }
        cancelPreQueueOrderInTask(timeOutDos, now);
    }


    /**
     * job中取消救援排队单
     *
     * @param timeOutDos
     * @param now
     */
    private void cancelRescueQueueOrderInTask(List<TeboRescueQueueOrderDO> timeOutDos, LocalDateTime now) {
        List<List<TeboRescueQueueOrderDO>> splitTimeOut = CollectionUtil.split(timeOutDos, 50);
        for (List<TeboRescueQueueOrderDO> teboRescueQueueOrderDOS : splitTimeOut) {
            List<TeboRescueQueueOrderDO> updateList = new ArrayList<>();
            for (TeboRescueQueueOrderDO teboRescueQueueOrderDO : teboRescueQueueOrderDOS) {
                //如果现在是预约时间15分钟后，取消
                LocalDateTime createTime = teboRescueQueueOrderDO.getCreateTime();
                if (Objects.isNull(createTime)) {
                    continue;
                }
                //现在是预约15min后，取消掉
                LocalDateTime after15 = createTime.plusMinutes(15);
                if (now.isAfter(after15)) {
                    TeboRescueQueueOrderDO update = new TeboRescueQueueOrderDO();
                    update.setOrderStatus(QueueOrderStatusEnum.CANCEL.getCode());
                    update.setCancelTime(now);
                    update.setCancelBy("system");
                    update.setId(teboRescueQueueOrderDO.getId());
                    updateList.add(update);
                }
            }
            updateBatchById(updateList);
        }
    }

    /**
     * job中取消预约上门排队单
     *
     * @param timeOutDos
     * @param now
     */
    private void cancelPreQueueOrderInTask(List<TeboRescueQueueOrderDO> timeOutDos, LocalDateTime now) {
        List<List<TeboRescueQueueOrderDO>> splitTimeOut = CollectionUtil.split(timeOutDos, 50);
        for (List<TeboRescueQueueOrderDO> teboRescueQueueOrderDOS : splitTimeOut) {
            List<TeboRescueQueueOrderDO> updateList = new ArrayList<>();
            for (TeboRescueQueueOrderDO teboRescueQueueOrderDO : teboRescueQueueOrderDOS) {
                //如果现在是预约时间15分钟后，取消
                LocalDateTime preDoorTime = teboRescueQueueOrderDO.getPreDoorTime();
                if (Objects.isNull(preDoorTime)) {
                    continue;
                }
                LocalDateTime after15 = preDoorTime.plusMinutes(15);
                if (now.isAfter(after15)) {
                    TeboRescueQueueOrderDO update = new TeboRescueQueueOrderDO();
                    update.setOrderStatus(QueueOrderStatusEnum.UNEFFICACY.getCode());
                    update.setId(teboRescueQueueOrderDO.getId());
                    updateList.add(update);
                }


            }
            updateBatchById(updateList);

        }
    }

    @Override
    public CustomerQueueStatusVO customerQueueStatus() {
        CustomerQueueStatusVO result = new CustomerQueueStatusVO();
        String unionId = AppletUtil.getUnionIdByRequest(request);
        //拿到这个用户当前未完结的救援单
        RescueQueueOrderQueryDTO queueOrderQueryDTO = new RescueQueueOrderQueryDTO();
        queueOrderQueryDTO.setUnionid(unionId);
        queueOrderQueryDTO.setOrderType(QueueBusinessTypeEnum.RESCUE.getCode());
        queueOrderQueryDTO.setOrderStatusList(QueueOrderStatusEnum.getUnFinishStatusList());
        List<TeboRescueQueueOrderDO> rescueQueueOrderDOS = rescueQueueOrderManger.listBySO(queueOrderQueryDTO);
        if (CollectionUtil.isNotEmpty(rescueQueueOrderDOS)) {
            TeboRescueQueueOrderDO rescueQueueOrderDO = rescueQueueOrderDOS.get(0);
            result.setRescueOrderId(rescueQueueOrderDO.getId());
            result.setRescueOrderStatus(rescueQueueOrderDO.getOrderStatus());
        }

        //预约上门
        RescueQueueOrderQueryDTO preDoorOrderQueryDTO = new RescueQueueOrderQueryDTO();
        preDoorOrderQueryDTO.setUnionid(unionId);
        preDoorOrderQueryDTO.setOrderType(QueueBusinessTypeEnum.PRE_DOOR.getCode());
        preDoorOrderQueryDTO.setOrderStatusList(QueueOrderStatusEnum.getUnFinishStatusList());
        List<TeboRescueQueueOrderDO> preDoorQueueOrderDOS = rescueQueueOrderManger.listBySO(preDoorOrderQueryDTO);
        if (CollectionUtil.isNotEmpty(preDoorQueueOrderDOS)) {
            TeboRescueQueueOrderDO rescueQueueOrderDO = preDoorQueueOrderDOS.get(0);
            result.setPreDoorOrderId(rescueQueueOrderDO.getId());
            result.setPreDoorOrderStatus(rescueQueueOrderDO.getOrderStatus());
        }

        /**
         * 预约到店
         */
        RescueQueueOrderQueryDTO preDoorStoreQueryDTO = new RescueQueueOrderQueryDTO();
        preDoorStoreQueryDTO.setUnionid(unionId);
        preDoorStoreQueryDTO.setOrderType(QueueBusinessTypeEnum.PRE_STORE.getCode());
        preDoorStoreQueryDTO.setOrderStatusList(QueueOrderStatusEnum.getUnFinishStatusList());
        List<TeboRescueQueueOrderDO> preDoorStoreOrderDOS = rescueQueueOrderManger.listBySO(preDoorStoreQueryDTO);
        if (CollectionUtil.isNotEmpty(preDoorStoreOrderDOS)) {
            TeboRescueQueueOrderDO rescueQueueOrderDO = preDoorStoreOrderDOS.get(0);
            result.setPreStoreOrderId(rescueQueueOrderDO.getId());
            result.setPreStoreOrderStatus(rescueQueueOrderDO.getOrderStatus());
        }


        return result;

    }

    @Override
    public void cancelRescueOrder(RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO) {
        Long id = rescueQueueOrderSaveDTO.getId();
        Assert.notNull(id, "未指定排队单");
        String cancelReason = rescueQueueOrderSaveDTO.getCancelReason();
//        Assert.notNull(cancelReason, "取消原因不能为空");
        TeboRescueQueueOrderDO rescueQueueOrderDO = getById(id);
        if (Objects.isNull(rescueQueueOrderDO)) {
            throw new ServiceException("未匹配到排队单");
        }
        Integer orderStatus = rescueQueueOrderDO.getOrderStatus();
        Integer orderType = rescueQueueOrderDO.getOrderType();
        //预约到店的单子，接单，待接单，可以取消
        if (QueueBusinessTypeEnum.PRE_STORE.getCode().equals(orderType)) {
            if (!CollectionUtil.newArrayList(QueueOrderStatusEnum.UN_CALL.getCode(), QueueOrderStatusEnum.PROCESSING.getCode()).contains(orderStatus)) {
                throw new ServiceException("当前状态不允许取消");
            }
        } else {
            //接单后无法取消
            if (QueueOrderStatusEnum.PROCESSING.getCode() <= orderStatus && QueueOrderStatusEnum.TIMEOUT.getCode() != orderStatus) {
                throw new ServiceException("当前状态不允许取消");
            }
        }


        TeboRescueQueueOrderDO update = new TeboRescueQueueOrderDO();
        update.setId(id);
        update.setOrderStatus(QueueOrderStatusEnum.CANCEL.getCode());
        String unionIdByRequest = AppletUtil.getUnionIdByRequest(request);
        R<TeboConsumer> teboConsumerR = remoteCustomerService.selectByUnionId(unionIdByRequest);
        TeboConsumer consumer = teboConsumerR.getData();
        if (Objects.nonNull(consumer)) {
            update.setCancelBy(consumer.getNickName());
        }

        update.setCancelTime(LocalDateTime.now());
        update.setCancelReason(cancelReason);
        updateById(update);
    }


    /**
     * 根据订单类型构建救援排队单
     *
     * @param rescueQueueOrderSaveDTO
     * @param orderTypeCode
     * @return
     */
    private TeboRescueQueueOrderDO buildRescueQueueByOrderType(RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO, int orderTypeCode) {
        TeboRescueQueueOrderDO save = new TeboRescueQueueOrderDO();
        String unionId = rescueQueueOrderSaveDTO.getUnionid();
        if (ObjectUtils.isEmpty(unionId)) {
            unionId = AppletUtil.getUnionIdByRequest(request);;
        }
        BeanUtils.copyProperties(rescueQueueOrderSaveDTO, save);
        save.setUnionid(unionId);

        //根据经纬度，获取合伙人
        TeboPositionQueryDTO queryDTO = new TeboPositionQueryDTO();
        queryDTO.setLgt(rescueQueueOrderSaveDTO.getLon());
        queryDTO.setLnt(rescueQueueOrderSaveDTO.getLat());
        R<TeboShopListVO> shopResult = remotePartnerService.getRescueShopByDistrictCode(queryDTO);
        TeboShopListVO shop = shopResult.getData();
        if (Objects.isNull(shop)) {
            throw new ServiceException("当前区域无经营门店");
        }

        if (QueueBusinessTypeEnum.RESCUE.getCode().equals(orderTypeCode)) {
            save.setOrderNo(TeboNumberGenerator.generateRescueQueueOrderJyNo());
        } else if (QueueBusinessTypeEnum.getPreTypeList().contains(orderTypeCode)) {
            save.setOrderNo(TeboNumberGenerator.generatePreQueueOrderNo());
        }
        save.setOrderType(orderTypeCode);
        if (QueueBusinessTypeEnum.PRE_STORE.getCode().equals(orderTypeCode)) {
            //预约到店的场景，默认处理中
            save.setOrderStatus(QueueOrderStatusEnum.PROCESSING.getCode());
        } else {
            save.setOrderStatus(QueueOrderStatusEnum.UN_CALL.getCode());
        }
        save.setBrand(rescueQueueOrderSaveDTO.getBrand());
        List<String> faultMsg = rescueQueueOrderSaveDTO.getFaultMsg();
        if (CollectionUtil.isNotEmpty(faultMsg)) {
            save.setFaultMsg(String.join(",", faultMsg));
        }
        if (ObjectUtils.isEmpty(rescueQueueOrderSaveDTO.getId())) {
            save.setId(SnowFlakeUtil.nextId());
        }else {
            save.setId(rescueQueueOrderSaveDTO.getId());
        }
//        if (Objects.isNull(rescueQueueOrderSaveDTO.getServiceItem())) {
//            save.setServiceItem(ServiceItemEnum.getRescueServiceItem().get(0));
//        }


        save.setInitiationType(InitiationTypeEnum.APPLET.getCode());
        save.setInitiationTime(LocalDateTime.now());
        //不是预约到店的，店铺由经纬度带出
        if (!QueueBusinessTypeEnum.PRE_STORE.getCode().equals(orderTypeCode)) {
            save.setTenantId(shop.getTenantId());
            save.setShopName(shop.getShopName());
            save.setShopId(shop.getId());
            save.setShopType(shop.getShopType());
            save.setDistance(Objects.nonNull(shop.getDistance()) ? String.valueOf(shop.getDistance()) : "");
        }

        if (Objects.isNull(save.getServiceItem())) {
            save.setServiceItem(ServiceItemEnum.REPAIR.getCode());
        }

        return save;
    }


    /**
     * 校验创建救援单
     *
     * @param rescueQueueOrderSaveDTO
     */
    private static void verifyCreateRescueOrder(RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO) {
        Assert.notEmpty(rescueQueueOrderSaveDTO.getNickName(), "必须填写姓名");
        Assert.notEmpty(rescueQueueOrderSaveDTO.getNickName(), "必须填写手机号");
        Assert.notEmpty(rescueQueueOrderSaveDTO.getLon(), "必须传经度");
        Assert.notEmpty(rescueQueueOrderSaveDTO.getLat(), "必须传纬度");


    }

    private static void verifyPreStoreRescueOrder(RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO) {

        Assert.notEmpty(rescueQueueOrderSaveDTO.getLon(), "必须传经度");
        Assert.notEmpty(rescueQueueOrderSaveDTO.getLat(), "必须传纬度");
        Assert.notNull(rescueQueueOrderSaveDTO.getShopName(), "门店名不能为空");
        Assert.notNull(rescueQueueOrderSaveDTO.getShopId(), "门店不能为空");
        Assert.notNull(rescueQueueOrderSaveDTO.getShopType(), "门店不能为空");
        Assert.notNull(rescueQueueOrderSaveDTO.getTenantId(), "合伙人不能为空");

        Assert.notNull(rescueQueueOrderSaveDTO.getPreDoorTime(), "预约时间不能为空");

        //预约时间不能超过今天的五天后
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nowPlus5 = now.plusDays(5);
        if (nowPlus5.isBefore(rescueQueueOrderSaveDTO.getPreDoorTime())) {
            throw new ServiceException("预约时间不能超过今天的五天后");
        }


    }

    @Override
    public RescueQueueOrderSaveDTO createPreDoorOrder(RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO) {
        int code = QueueBusinessTypeEnum.PRE_DOOR.getCode();
        verifyCreateRescueOrder(rescueQueueOrderSaveDTO);
        //期望时间
        Assert.notNull(rescueQueueOrderSaveDTO.getPreDoorTime(), "必须填写期望时间");
//        Assert.notNull(rescueQueueOrderSaveDTO.getServiceItem(), "必须填写服务项目");
        //预约时间不能超过今天的五天后
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nowPlus5 = now.plusDays(5);
        if (nowPlus5.isBefore(rescueQueueOrderSaveDTO.getPreDoorTime())) {
            throw new ServiceException("预约时间不能超过今天的五天后");
        }
        //此用户不能存在未完成的预约单
        RescueQueueOrderQueryDTO queueOrderQueryDTO = new RescueQueueOrderQueryDTO();
        queueOrderQueryDTO.setOrderStatusList(QueueOrderStatusEnum.getUnFinishStatusList());
        queueOrderQueryDTO.setOrderType(QueueBusinessTypeEnum.PRE_DOOR.getCode());
        queueOrderQueryDTO.setUnionid(AppletUtil.getUnionIdByRequest(request));
        List<TeboRescueQueueOrderDO> dos = rescueQueueOrderManger.listBySO(queueOrderQueryDTO);
        if (CollectionUtil.isNotEmpty(dos)) {
            throw new ServiceException("存在未完结的预约上门单，请先完成上一单");
        }
        RescueQueueOrderSaveDTO saveDTO = new RescueQueueOrderSaveDTO();


        TeboRescueQueueOrderDO orderDO = createRescueOrderByBusinessType(rescueQueueOrderSaveDTO, code);
        //推送大屏
        CompletableFuture.runAsync(() -> {
            geTuiPushDoor(orderDO);
        }, GeTuiPushThreadPool.thisPool());
        return BeanConvert.copy(orderDO, saveDTO);
    }


    private TeboRescueQueueOrderDO createRescueOrderByBusinessType(RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO, Integer orderType) {
        TeboRescueQueueOrderDO save = buildRescueQueueByOrderType(rescueQueueOrderSaveDTO, orderType);
        if (orderType.equals(QueueBusinessTypeEnum.PRE_DOOR.getCode()) || orderType.equals(QueueBusinessTypeEnum.RESCUE.getCode())) {
            //给维修师傅发短信
            Long shopId = save.getShopId();
            List<TeboAccountInfoVO> list = getTeboAccountInfoVOSByShop(shopId);
            if (CollectionUtil.isNotEmpty(list)){
                List<String> phoneNumberList = list.stream().map(item -> item.getPhoneNumber()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(phoneNumberList)) {
                    String phoneNumbers = String.join(",", phoneNumberList);
                    SmsSendDTO smsSendDTO = new SmsSendDTO();
                    smsSendDTO.setPhoneNumber(phoneNumbers);
                    remoteSmsService.storeMasterPendingOrder(smsSendDTO);
                }
            }
        }
        //朝柱确认，只有救援类型的发语音通知
        if (orderType.equals(QueueBusinessTypeEnum.RESCUE.getCode())) {
            Long shopId = save.getShopId();
            yuYinNotify(shopId);

        }

        this.save(save);

        List<TeboOrderFileDO> fileSaves = new ArrayList<>();

        for (String fileUrl : rescueQueueOrderSaveDTO.getFileUrlList()) {
            TeboOrderFileDO fileSave = new TeboOrderFileDO();
            fileSave.setFileUrl(fileUrl);
            fileSave.setOrderId(save.getId());
            fileSave.setId(SnowFlakeUtil.nextId());
            fileSave.setOrderTable(OrderTableEnum.TEBO_RESCUE_QUEUE_ORDER.getTableName());
            fileSave.setOrderId(save.getId());
            fileSaves.add(fileSave);
        }


        orderFileManger.insertBatch(fileSaves);
        return save;
    }

    /**
     * 语音通知
     *
     * @param shopId
     */
    private void yuYinNotify(Long shopId) {
        if (Objects.nonNull(shopId)) {
            TeboShop teboShop = remoteShopService.getShopInfo(shopId).getData();
            if (Objects.nonNull(teboShop)) {
                String phoneNumber = teboShop.getPhoneNumber();
                if (StringUtils.isNotEmpty(phoneNumber)) {
                    ALiYuYinPushRemoteDTO aLiYuYinPushRemoteDTO = new ALiYuYinPushRemoteDTO();
                    aLiYuYinPushRemoteDTO.setCalledNumber(phoneNumber);
                     aLiYuYinPushRemoteDTO.setVoiceCode(voiceCode);
                    remoteALiService.push(aLiYuYinPushRemoteDTO);
                    return;
                }
                //没有老板电话的话，随便找个师傅的
                List<TeboAccountInfoVO> list = getTeboAccountInfoVOSByShop(shopId);
                if (CollectionUtil.isEmpty(list)){
                    return;
                }
                TeboAccountInfoVO accountInfoVO = list.get(0);
                String accountPhoneNumber = accountInfoVO.getPhoneNumber();
                if (StringUtils.isNotEmpty(accountPhoneNumber)) {
                    ALiYuYinPushRemoteDTO aLiYuYinPushRemoteDTO = new ALiYuYinPushRemoteDTO();
                    aLiYuYinPushRemoteDTO.setCalledNumber(accountPhoneNumber);
                     aLiYuYinPushRemoteDTO.setVoiceCode(voiceCode);
                    remoteALiService.push(aLiYuYinPushRemoteDTO);
                }

            }
        }
    }

    private List<TeboAccountInfoVO> getTeboAccountInfoVOSByShop(Long shopId) {
        R<List<TeboAccountInfoVO>> result = remoteAccountService.getAccountInfoListByShopId(shopId);
        if (result.getCode() != 200) {
            throw new GlobalException("获取维修师傅信息异常");
        }
        List<TeboAccountInfoVO> list = result.getData();
        return list;
    }

    @Override
    public RescueQueueOrderSaveDTO createPreStoreOrder(RescueQueueOrderSaveDTO rescueQueueOrderSaveDTO) {
        int code = QueueBusinessTypeEnum.PRE_STORE.getCode();
        verifyPreStoreRescueOrder(rescueQueueOrderSaveDTO);


        RescueQueueOrderQueryDTO queueOrderQueryDTO = new RescueQueueOrderQueryDTO();
        queueOrderQueryDTO.setOrderStatusList(QueueOrderStatusEnum.getUnFinishStatusList());
        queueOrderQueryDTO.setOrderType(QueueBusinessTypeEnum.PRE_STORE.getCode());
        queueOrderQueryDTO.setUnionid(AppletUtil.getUnionIdByRequest(request));
        List<TeboRescueQueueOrderDO> dos = rescueQueueOrderManger.listBySO(queueOrderQueryDTO);
        if (CollectionUtil.isNotEmpty(dos)) {
            throw new ServiceException("存在未完结的预约到店单，请先完成上一单");
        }
        RescueQueueOrderSaveDTO saveDTO = new RescueQueueOrderSaveDTO();
        TeboRescueQueueOrderDO orderDO = createRescueOrderByBusinessType(rescueQueueOrderSaveDTO, code);
        return BeanConvert.copy(orderDO, saveDTO);
    }


    @Override
    public List<RescueQueueOrderItemDTO> getPreOrderListByUser(RescueQueueOrderQueryDTO queryDTO) {
        RescueQueueOrderQueryDTO rescueQueueOrderQueryDTO = new RescueQueueOrderQueryDTO();
        String unionId = AppletUtil.getUnionIdByRequest(request);
        rescueQueueOrderQueryDTO.setUnionid(unionId);
        rescueQueueOrderQueryDTO.setOrderTypeList(QueueBusinessTypeEnum.getPreTypeList());
        rescueQueueOrderQueryDTO.setOrderStatusList(queryDTO.getOrderStatusList());
        List<TeboRescueQueueOrderDO> dos = rescueQueueOrderManger.listBySOOrderCreateTimeDesc(rescueQueueOrderQueryDTO);
        List<RescueQueueOrderItemDTO> rescueQueueOrderItemDTOS = convertRescueOrderDoToDto(dos);
        return rescueQueueOrderItemDTOS;
    }

    @Override
    public List<RescueQueueOrderItemDTO> getUnReceivedPreDoorOrderList() {
        Long shopId = getShopIdByAccount();
        RescueQueueOrderQueryDTO rescueQueueOrderQueryDTO = new RescueQueueOrderQueryDTO();
        rescueQueueOrderQueryDTO.setShopId(shopId);
        rescueQueueOrderQueryDTO.setOrderStatus(QueueOrderStatusEnum.UN_CALL.getCode());
        rescueQueueOrderQueryDTO.setOrderType(QueueBusinessTypeEnum.PRE_DOOR.getCode());
        List<TeboRescueQueueOrderDO> dos = rescueQueueOrderManger.listBySO(rescueQueueOrderQueryDTO);

        return convertRescueOrderDoToDto(dos);
    }

    @Override
    public List<RescueQueueOrderItemDTO> getRescueOrderListByUser() {
        RescueQueueOrderQueryDTO rescueQueueOrderQueryDTO = new RescueQueueOrderQueryDTO();
        String unionId = AppletUtil.getUnionIdByRequest(request);
        rescueQueueOrderQueryDTO.setUnionid(unionId);
        rescueQueueOrderQueryDTO.setOrderType(QueueBusinessTypeEnum.RESCUE.getCode());
        List<TeboRescueQueueOrderDO> dos = rescueQueueOrderManger.listBySOOrderCreateTimeDesc(rescueQueueOrderQueryDTO);
        List<RescueQueueOrderItemDTO> rescueQueueOrderItemDTOS = convertRescueOrderDoToDto(dos);
        return rescueQueueOrderItemDTOS;
    }

    @Override
    public List<RescueQueueOrderItemDTO> shopStoreRescueOrderList(RescueQueueOrderQueryDTO queryDTO) {
        Long shopId = getShopIdByAccount();
        RescueQueueOrderQueryDTO rescueQueueOrderQueryDTO = new RescueQueueOrderQueryDTO();
        rescueQueueOrderQueryDTO.setShopId(shopId);
        rescueQueueOrderQueryDTO.setOrderStatus(queryDTO.getOrderStatus());
        rescueQueueOrderQueryDTO.setOrderType(QueueBusinessTypeEnum.RESCUE.getCode());
        rescueQueueOrderQueryDTO.setOrderStatusList(queryDTO.getOrderStatusList());
        List<TeboRescueQueueOrderDO> dos = rescueQueueOrderManger.listBySOOrderCreateTimeDesc(rescueQueueOrderQueryDTO);

        return convertRescueOrderDoToDto(dos);
    }

    @Override
    public List<RescueQueueOrderItemDTO> shopPreDoorOrderList(RescueQueueOrderQueryDTO queryDTO) {
        Long shopId = getShopIdByAccount();
        RescueQueueOrderQueryDTO rescueQueueOrderQueryDTO = new RescueQueueOrderQueryDTO();
        rescueQueueOrderQueryDTO.setShopId(shopId);
        rescueQueueOrderQueryDTO.setOrderStatus(queryDTO.getOrderStatus());
        rescueQueueOrderQueryDTO.setOrderType(QueueBusinessTypeEnum.PRE_DOOR.getCode());
        rescueQueueOrderQueryDTO.setOrderStatusList(queryDTO.getOrderStatusList());
        List<TeboRescueQueueOrderDO> dos = rescueQueueOrderManger.listBySOOrderCreateTimeDesc(rescueQueueOrderQueryDTO);

        return convertRescueOrderDoToDto(dos);
    }


    /**
     * 获取师傅的店铺
     *
     * @return
     */
    private Long getShopIdByAccount() {
        Long userId = MaintainerOnlineUserUtil.getUserId();
        R<TeboAccountInfoVO> result = remoteAccountService.getAccountInfoById(userId);
        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            throw new ServiceException("未获取到师傅信息");
        }
        TeboAccountInfoVO accountInfoVO = result.getData();
        Long shopId = accountInfoVO.getShopId();
        return shopId;
    }

    @Transactional
    @Override
    public Boolean receivingOrder(RescueQueueOrderReceiveDTO rescueQueueOrderReceiveDTO) {
        Assert.notNull(rescueQueueOrderReceiveDTO.getId(), "未指定接单单号");
        //当前师傅是否接救援单
        Long userId = MaintainerOnlineUserUtil.getUserId();

        R<TeboAccountInfoVO> accountResult = remoteAccountService.getAccountInfoById(userId);
        TeboAccountInfoVO account = accountResult.getData();
        if (Objects.isNull(account)) {
            throw new ServiceException("获取师傅信息失败");
        }
        Boolean openRescue = account.getOpenRescue();
        if (!Boolean.TRUE.equals(openRescue)) {
            throw new ServiceException("请在‘我的’中开启接单按钮再接单");
        }

        //修改排队单状态
        Long id = rescueQueueOrderReceiveDTO.getId();
        TeboRescueQueueOrderDO rescueQueueOrderDO = getById(id);
        if (Objects.isNull(rescueQueueOrderDO)) {
            throw new ServiceException("未找到排队单");
        }
        Integer orderStatus = rescueQueueOrderDO.getOrderStatus();
        if (QueueOrderStatusEnum.UN_CALL.getCode() != orderStatus) {
            throw new ServiceException("当前单已被接单");
        }

        //如果当前是救援类型的接单 当前师傅有没完成的工单（救援+正常到店），不允许接单
        if (QueueBusinessTypeEnum.RESCUE.getCode().equals(rescueQueueOrderDO.getOrderType())) {
            Boolean hasOrder = serviceOrderManger.hasInMaintainProgressOrder(userId);
            if (Boolean.TRUE.equals(hasOrder)) {
                throw new ServiceException("当前存在没完成的工单");
            }
        }
        //师傅门店
        Long accountShopId = getShopIdByAccount();
        R<TeboShop> result = remoteShopService.getShopInfo(accountShopId);
        TeboShop shop = result.getData();
        if (Objects.isNull(shop)) {
            throw new ServiceException("未匹配到店铺");
        }
        if (!accountShopId.equals(rescueQueueOrderDO.getShopId())) {
            throw new ServiceException("该单据已不属于当前门店");
        }

        TeboRescueQueueOrderDO updateQueueOrder = new TeboRescueQueueOrderDO();
        updateQueueOrder.setId(rescueQueueOrderReceiveDTO.getId());

        String shopName = shop.getShopName();
        updateQueueOrder.setAccountId(userId);
        updateQueueOrder.setAccountName(account.getAccountName());
        updateQueueOrder.setOrderStatus(QueueOrderStatusEnum.PROCESSING.getCode());
        updateQueueOrder.setReceiveTime(LocalDateTime.now());
        updateQueueOrder.setAccountPhone(account.getPhoneNumber());
        //更新排队单
        updateById(updateQueueOrder);

        // 创建工单
        //根据最新的排队单生成工单
        TeboRescueQueueOrderDO queueOrderDO = getById(id);
        serviceOrderManger.createServiceOrderByRescueQueueOrder(queueOrderDO);

        //发送消息
        pushReceiveWxData(shopName, queueOrderDO);
        //记录行为
        RemoteConsumerRecordDTO recordDTO = new RemoteConsumerRecordDTO();
        Integer orderType = queueOrderDO.getOrderType();
        QueueBusinessTypeEnum anEnum = QueueBusinessTypeEnum.getEnum(orderType);
        recordDTO.setLastType(anEnum.getConsumerRecordEnum().getType());
        recordDTO.setShopId(accountShopId);
        recordDTO.setUnionId(rescueQueueOrderDO.getUnionid());
        disMqService.recordConsumerRecord(recordDTO);
        return true;


    }

    private void pushReceiveWxData(String shopName, TeboRescueQueueOrderDO queueOrderDO) {
        WxPushDTO wxPushDTO = new WxPushDTO();
        wxPushDTO.setAppid(appid);
        wxPushDTO.setUnionid(queueOrderDO.getUnionid());
        HashMap<String, Object> pushData = new HashMap<>();

        JSONObject character_string2 = new JSONObject();
        //店名
        character_string2.put("value", shopName);
        pushData.put("thing4", character_string2);
        JSONObject character_string6 = new JSONObject();
//        订单类型
        character_string6.put("value", QueueBusinessTypeEnum.getEnum(queueOrderDO.getOrderType()).getPushOrderType());
        pushData.put("const2", character_string6);
        JSONObject character_string7 = new JSONObject();
//        下单时间
        character_string7.put("value", queueOrderDO.getCreateTime());
        pushData.put("time3", character_string7);
        wxPushDTO.setData(pushData);
        wxPushDTO.setTemplate_id(receiveTemplateId);
        wxPushDTO.setOfficialAccount(WeChatOfficialAccountEnum.teboTravel.getOfficialAccountName());

        remoteWxPushService.sendMessage(wxPushDTO);
    }


    @Override
    public List<RescueQueueOrderItemDTO> rescueOrderList(RescueQueueOrderQueryDTO queryDTO) {
        queryDTO.setOrderType(QueueBusinessTypeEnum.RESCUE.getCode());

        List<TeboRescueQueueOrderDO> list = rescueQueueOrderManger.listBySOOrderCreateTimeDesc(queryDTO);
        return convertRescueOrderDoToDto(list);
    }

    @Override
    public void rescueOrderExport(HttpServletResponse response, RescueQueueOrderQueryDTO queryDTO) {
        List<RescueQueueOrderItemDTO> list = rescueOrderList(queryDTO);
        List<RescueRescueOrderExportVO> rescueOrderExportVOS = convertItemVoToRescueExcelVo(list);
        ExcelUtil.exportExcelToResponse(response, rescueOrderExportVOS, RescueRescueOrderExportVO.class, "救援列表");

    }

    @Override
    public List<RescueQueueOrderItemDTO> listByCondition(RescueQueueOrderQueryDTO queryDTO) {
        List<TeboRescueQueueOrderDO> list = rescueQueueOrderManger.listBySOOrderCreateTimeDesc(queryDTO);
        return convertRescueOrderDoToDto(list);
    }


    /**
     * 转化救援excel
     *
     * @param list
     * @return
     */
    private static List<RescueRescueOrderExportVO> convertItemVoToRescueExcelVo(List<RescueQueueOrderItemDTO> list) {
        Map<Long, RescueQueueOrderItemDTO> queueOrderMap = DataUtils.listToMap(list, RescueQueueOrderItemDTO::getId);
        List<RescueRescueOrderExportVO> exportVOS = BeanConvert.copyList(list, RescueRescueOrderExportVO::new);
        for (RescueRescueOrderExportVO exportVO : exportVOS) {
            Long id = exportVO.getId();
            RescueQueueOrderItemDTO orderItemDTO = queueOrderMap.get(id);

            //门店类型
            Integer shopType = orderItemDTO.getShopType();
            if (Objects.nonNull(shopType)) {
                ShopTypeEnum anEnum = ShopTypeEnum.getEnum(shopType);
                if (Objects.nonNull(anEnum)) {
                    String msg = anEnum.getMsg();
                    exportVO.setShopTypeName(msg);
                }
            }

            Integer orderStatus = orderItemDTO.getOrderStatus();
            if (Objects.nonNull(orderStatus)) {
                QueueOrderStatusRescueFrontEnum orderTypeEnum = QueueOrderStatusRescueFrontEnum.getQueueOrderTypeEnum(orderStatus);
                if (Objects.nonNull(orderTypeEnum)) {
                    exportVO.setOrderStatusName(orderTypeEnum.getMessage());
                }
            }
            Integer initiationType = orderItemDTO.getInitiationType();
            if (Objects.nonNull(initiationType)) {
                InitiationTypeEnum initiationTypeEnum = InitiationTypeEnum.getInitiationTypeEnum(initiationType);
                if (Objects.nonNull(initiationTypeEnum)) {
                    exportVO.setInitiationTypeName(initiationTypeEnum.getMessage());
                }
            }
//
        }
        return exportVOS;
    }


    /**
     * 转化预约excel
     *
     * @param list
     * @return
     */
    private static List<RescuePreOrderExportVO> convertItemVoToPreExcelVo(List<RescueQueueOrderItemDTO> list) {
        Map<Long, RescueQueueOrderItemDTO> queueOrderMap = DataUtils.listToMap(list, RescueQueueOrderItemDTO::getId);
        List<RescuePreOrderExportVO> exportVOS = BeanConvert.copyList(list, RescuePreOrderExportVO::new);
        for (RescuePreOrderExportVO exportVO : exportVOS) {
            Long id = exportVO.getId();
            RescueQueueOrderItemDTO orderItemDTO = queueOrderMap.get(id);
            //服务项目
            Integer serviceItem = orderItemDTO.getServiceItem();
            if (Objects.nonNull(serviceItem)) {
                ServiceItemEnum anEnum = ServiceItemEnum.getEnum(serviceItem);
                if (Objects.nonNull(anEnum)) {
                    exportVO.setServiceItemName(anEnum.getMsg());
                }
            }
            //门店类型
            Integer shopType = orderItemDTO.getShopType();
            if (Objects.nonNull(shopType)) {
                ShopTypeEnum anEnum = ShopTypeEnum.getEnum(shopType);
                if (Objects.nonNull(anEnum)) {
                    String msg = anEnum.getMsg();
                    exportVO.setShopTypeName(msg);
                }
            }
            //预约类型
            Integer orderType = orderItemDTO.getOrderType();
            if (Objects.nonNull(orderType)) {
                QueueBusinessTypeEnum anEnum = QueueBusinessTypeEnum.getEnum(orderType);
                if (Objects.nonNull(anEnum)) {
                    String message = anEnum.getMessage();
                    exportVO.setOrderTypeName(message);
                }
            }

            Integer orderStatus = orderItemDTO.getOrderStatus();
            if (Objects.nonNull(orderStatus)) {
                QueueOrderStatusPreFrontEnum orderTypeEnum = QueueOrderStatusPreFrontEnum.getQueueOrderTypeEnum(orderStatus);
                if (Objects.nonNull(orderTypeEnum)) {
                    exportVO.setOrderStatusName(orderTypeEnum.getMessage());
                }
            }
        }
        return exportVOS;
    }


    @Override
    public List<RescueQueueOrderItemDTO> preOrderList(RescueQueueOrderQueryDTO queryDTO) {
        queryDTO.setOrderTypeList(CollectionUtil.newArrayList(QueueBusinessTypeEnum.getPreTypeList()));
        //如果已经指定了
        if (Objects.nonNull(queryDTO.getPreType())) {
            queryDTO.setOrderType(queryDTO.getPreType());
        }
        List<TeboRescueQueueOrderDO> list = rescueQueueOrderManger.listBySOOrderCreateTimeDesc(queryDTO);

        return convertRescueOrderDoToDto(list);
    }

    @Override
    public void preDoorExport(HttpServletResponse response, RescueQueueOrderQueryDTO queryDTO) {
        List<RescueQueueOrderItemDTO> rescueQueueOrderItemDTOS = preOrderList(queryDTO);
        List<RescuePreOrderExportVO> preExports = convertItemVoToPreExcelVo(rescueQueueOrderItemDTOS);

        ExcelUtil.exportExcelToResponse(response, preExports, RescuePreOrderExportVO.class, "预约列表");
    }


    @Override
    public RescueQueueOrderDetailDTO orderDetail(RescueQueueOrderQueryDTO queryDTO) {
        Long id = queryDTO.getId();
        Assert.notNull(id, "必须指定服务单");
        TeboRescueQueueOrderDO rescueQueueOrderDO = getById(id);
        RescueQueueOrderDetailDTO dto = new RescueQueueOrderDetailDTO();
        BeanConvert.copy(rescueQueueOrderDO, dto);
        OrderFileQueryDTO fileQueryDTO = new OrderFileQueryDTO();
        fileQueryDTO.setOrderId(id);
        fileQueryDTO.setOrderTable(OrderTableEnum.TEBO_RESCUE_QUEUE_ORDER.getTableName());
        List<TeboOrderFileDO> fileDOS = orderFileManger.listBySo(fileQueryDTO);
        if (CollectionUtil.isNotEmpty(fileDOS)) {
            dto.setUrls(fileDOS.stream().map(TeboOrderFileDO::getFileUrl).collect(Collectors.toList()));
        }
        Long shopId = rescueQueueOrderDO.getShopId();
        if (Objects.nonNull(shopId)) {
            R<TeboShop> shopInfoResult = remoteShopService.getShopInfo(shopId);
            if (Objects.nonNull(shopInfoResult.getData())) {
                dto.setShopAddress(shopInfoResult.getData().getAddress());
            }

        }
        Integer orderStatus = dto.getOrderStatus();
        if (QueueOrderStatusEnum.getHasServiceOrderStatusList().contains(orderStatus)){
            ServiceOrderQueryDTO serviceOrderQueryDTO = new ServiceOrderQueryDTO();
            serviceOrderQueryDTO.setQueueOrderId(id);
            List<TeboServiceOrderDO> serviceOrderDOS = serviceOrderManger.list(serviceOrderQueryDTO);
            if (CollectionUtil.isNotEmpty(serviceOrderDOS)){
                dto.setMaintainProcess(serviceOrderDOS.get(0).getMaintainProcess());
            }
        }
        return dto;
    }

    @Override
    public void shopAllocation(AllocationShopDTO allocationShopDTO) {
        Assert.notNull(allocationShopDTO.getQueueOrderId(), "未指定排队单");
        Assert.notNull(allocationShopDTO.getShopId(), "未指定门店");
        R<TeboShop> shopInfoResult = remoteShopService.getShopInfo(allocationShopDTO.getShopId());
        TeboShop shop = shopInfoResult.getData();
        if (Objects.isNull(shop)) {
            throw new ServiceException("未匹配到门店");
        }
        Boolean openRescue = shop.getOpenRescue();
        if (!Boolean.TRUE.equals(openRescue)){
            throw new ServiceException("该门店关闭了救援和预约服务，无法派单");
        }
        if (0 == shop.getStatus()) {
            throw new ServiceException("该门店为禁用状态，无法派单");
        }
        TeboRescueQueueOrderDO rescueQueueOrderDO = getById(allocationShopDTO.getQueueOrderId());
        if (Objects.isNull(rescueQueueOrderDO)) {
            throw new ServiceException("未匹配到服务单");
        }
        Integer orderStatus = rescueQueueOrderDO.getOrderStatus();
        if (QueueOrderStatusEnum.TIMEOUT.getCode() != orderStatus && QueueOrderStatusEnum.UN_CALL.getCode() != orderStatus) {
            throw new ServiceException("只有待受理和已超时状态可派单");
        }
        TeboRescueQueueOrderDO update = new TeboRescueQueueOrderDO();
        update.setId(allocationShopDTO.getQueueOrderId());
        update.setOrderStatus(QueueOrderStatusEnum.UN_CALL.getCode());
        update.setShopName(shop.getShopName());
        update.setShopId(Long.parseLong(shop.getId()));
        update.setInitiationTime(LocalDateTime.now());
        updateById(update);
    }

    @Override
    public List<MaintenanceQueueOrderNumberVO> getMaintenanceServiceOrderCount() {
        Long shopId = getShopIdByAccount();
        return rescueQueueOrderManger.getMaintenanceServiceOrderCount(shopId);
    }

    List<RescueQueueOrderItemDTO> convertRescueOrderDoToDto(List<TeboRescueQueueOrderDO> doList) {

        List<RescueQueueOrderItemDTO> rescueQueueOrderItemDTOS = new ArrayList<>();
        if (CollectionUtil.isEmpty(doList)) {
            return rescueQueueOrderItemDTOS;
        }
        for (TeboRescueQueueOrderDO rescueQueueOrderDO : doList) {
            RescueQueueOrderItemDTO orderItemDTO = new RescueQueueOrderItemDTO();
            BeanConvert.copy(rescueQueueOrderDO, orderItemDTO);

            rescueQueueOrderItemDTOS.add(orderItemDTO);
        }
        return rescueQueueOrderItemDTOS;
    }
}




