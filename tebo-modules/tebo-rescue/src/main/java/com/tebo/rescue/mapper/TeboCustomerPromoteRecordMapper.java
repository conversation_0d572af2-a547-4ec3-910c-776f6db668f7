package com.tebo.rescue.mapper;

import com.tebo.rescue.entity.TeboCustomerPromoteRecordDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import com.tebo.rescue.entity.TeboPromoteRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 用户推广记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Mapper
public interface TeboCustomerPromoteRecordMapper extends TeboBaseMapper<TeboCustomerPromoteRecordDO> {
    TeboCustomerPromoteRecordDO selectByUnionId(String unionId);

}
