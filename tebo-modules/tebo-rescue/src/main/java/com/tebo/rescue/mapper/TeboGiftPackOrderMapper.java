package com.tebo.rescue.mapper;

import com.tebo.rescue.applet.domain.dto.GiftPackOrderQueryDTO;
import com.tebo.rescue.entity.TeboGiftPackOrderDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 礼包订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-24
 */
@Mapper
public interface TeboGiftPackOrderMapper extends TeboBaseMapper<TeboGiftPackOrderDO> {


    /**
     *根据订单号查询订单信息
     */
    TeboGiftPackOrderDO selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 查询数量
     */
    int count(GiftPackOrderQueryDTO queryDTO);

    int getTeboGiftPackOrder(GiftPackOrderQueryDTO queryDTO);


    void cancelExpiredPackOrder(GiftPackOrderQueryDTO queryDTO);

    /**
     * 会员权益专属
     * @param unionId
     * @return
     */
    List<TeboGiftPackOrderDO> selectByUnionId(String unionId);

    /**
     * 会员权益专属2
     * @param unionId
     * @return
     */
    List<TeboGiftPackOrderDO> selectByUnionIdAndStatus(String unionId);

    /**
     * 查询一个人推广了多少个卡
     */
    List<TeboGiftPackOrderDO> selectByReferencePhone(GiftPackOrderQueryDTO giftPackOrderQueryDTO);

    /**
     * 查询核销的历史卡券
     */
    List<String> getOldCouponSign();

    /**
     * 查询最新核销的卡券
     */
    List<String> getNewCouponSign();
}
