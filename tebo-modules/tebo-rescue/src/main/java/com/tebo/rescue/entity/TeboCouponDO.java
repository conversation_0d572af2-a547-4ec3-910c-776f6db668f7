package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 卡券表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-13
 */
@Getter
@Setter
@TableName("tebo_coupon")
public class TeboCouponDO extends Model<TeboCouponDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;


    /**
     * 0 单独卡券 1礼包卡券
     */
    @TableField(value = "single")
    private Integer single;

    /**
     * 0未被礼包占用 1被礼包占用
     */
    @TableField(value = "in_pack")
    private Integer inPack;

    /**
     * 卡券编号
     */
    @TableField("coupon_code")
    private String couponCode;

    /**
     * 卡券名称
     */
    @TableField("coupon_name")
    private String couponName;

    /**
     * 卡券归属（0 天能 1合伙人 2天能+合伙人）
     */
    @TableField("vest")
    private Integer vest;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 发行主体
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 卡券类型（11 电池 12维修）
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 适用区域
     */
    @TableField("area")
    private String area;

    /**
     * 适用区域编码
     */
    @TableField("area_code")
    private String areaCode;

    /**
     * 适用区域名称
     */
    @TableField("area_name")
    private String areaName;

    /**
     * 总发行量
     */
    @TableField("publish_num")
    private Integer publishNum;

    /**
     * 已使用
     */
    @TableField("use_num")
    private Integer useNum;

    /**
     * 剩余量
     */
    @TableField("residue_num")
    private Integer residueNum;

    /**
     * 面额（分）
     */
    @TableField("par_value")
    private Integer parValue;

    /**
     * 每人限领
     */
    @TableField("par_receive")
    private Integer parReceive;

    /**
     * 领取方式 0 系统推送 1 主动领取
     */
    @TableField("receive_type")
    private Integer receiveType;

    /**
     * 使用方式 0 无门槛 1 满额使用
     */
    @TableField("use_type")
    private Integer useType;

    /**
     * 满 xx 可用(分)
     */
    @TableField("use_reduce")
    private Integer useReduce;

    /**
     * 有效期开始
     */
    @TableField("start_time")
    private LocalDate startTime;

    /**
     * 有效期结束
     */
    @TableField("end_time")
    private LocalDate endTime;

    /**
     * 状态 1 未开始 2已开始 3已过期 10已作废
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 限制商品（0全场通用 1指定平台商品 2 指定非平台商品）
     */
    @TableField("good_limit")
    private Integer goodLimit;

    /**
     * 可用商品分类id
     */
    @TableField("type_id")
    private String typeId;

    /**
     * 参与门店 0 全部门店 1 指定门店 2 指定区域门店
     */
    @TableField("use_shop")
    private Integer useShop;

    /**
     * 卡券描述
     */
    @TableField("`desc`")
    private String desc;

    /**
     * 主图
     */
    @TableField("main_image")
    private String mainImage;

    /**
     * 使用须知
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by",fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by",fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标识
     */
    @TableField("del_flag")
    private Integer delFlag;


    @Override
    public Serializable pkVal() {
        return this.id;
    }


    public TeboCouponDO(Long id, String couponCode) {
        this.id =id;
        this.couponCode = couponCode;
    }

    public TeboCouponDO() {

    }

}
