package com.tebo.rescue.applet.controller;

import com.tebo.common.core.web.controller.BaseController;
import com.tebo.common.core.web.domain.AjaxResult;
import com.tebo.common.util.applet.AppletUtil;
import com.tebo.rescue.service.TeboAppletIndexService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * C小程序主页统计
 */
@RestController
@RequestMapping("/applet/index")
public class TeboAppletIndexController extends BaseController {

    @Resource
    private TeboAppletIndexService teboAppletIndexService;
    @Resource
    private HttpServletRequest request;

    /**
     * 小程序首页数量统计
     * @return
     */
    @GetMapping("/countNumber")
    public AjaxResult countNumber() {
        String unionid = AppletUtil.getUnionIdByRequest(request);
//        String unionid = "ozRHL6ROIzuAS_XwHI8gcB7dF_Ok";
        return AjaxResult.success(teboAppletIndexService.countNumber(unionid));
    }


    /**
     * 商家端判断是否有领券资格
     * @return
     */
    @GetMapping("/countNumberByPhoneNumber")
    public AjaxResult hasReceiveTime(@RequestParam String phoneNumber) {
        return AjaxResult.success(teboAppletIndexService.countNumberByPhoneNumber(phoneNumber));
    }

}
