package com.tebo.rescue.manager.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.util.number.CodeGenerator;
import com.tebo.rescue.applet.domain.dto.ServiceOrderQueryDTO;
import com.tebo.rescue.entity.TeboRescueQueueOrderDO;
import com.tebo.rescue.entity.TeboServiceOrderDO;
import com.tebo.rescue.enums.QueueBusinessTypeEnum;
import com.tebo.rescue.enums.ServiceOrderMaintainProcessEnum;
import com.tebo.rescue.enums.ServiceOrderStatusEnum;
import com.tebo.rescue.manager.TeboServiceOrderManger;
import com.tebo.rescue.mapper.TeboServiceOrderMapper;
import com.tebo.rescue.util.TeboNumberGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;


@Service
@Slf4j
public class TeboServiceOrderMangerImpl extends ServiceImpl<TeboServiceOrderMapper,TeboServiceOrderDO> implements TeboServiceOrderManger {
    @Autowired
    private TeboServiceOrderMapper teboServiceOrderMapper;

    @Autowired
    private RedisService redisService;

    @Override
    public Boolean createServiceOrderByRescueQueueOrder(TeboRescueQueueOrderDO teboRescueQueueOrderDO ) {
        TeboServiceOrderDO save = new TeboServiceOrderDO();
        save.setAccountId(teboRescueQueueOrderDO.getAccountId());
        save.setAccountName(teboRescueQueueOrderDO.getAccountName());
        save.setShopId(teboRescueQueueOrderDO.getShopId());
        save.setShopName(teboRescueQueueOrderDO.getShopName());
        save.setShopType(teboRescueQueueOrderDO.getShopType());

        save.setTenantId(teboRescueQueueOrderDO.getTenantId());
        save.setOrderNo(TeboNumberGenerator.generateServiceOrderNo());
        //todo 服务项目和排队单统一，再确认下
        save.setServiceType(teboRescueQueueOrderDO.getServiceItem());
        save.setOrderStatus(ServiceOrderStatusEnum.IN_MAINTENANCE.getCode());
        save.setMaintainProcess(ServiceOrderMaintainProcessEnum.RECEIVED.getCode());
        save.setQueueOrderId(teboRescueQueueOrderDO.getId());
        save.setNickName(teboRescueQueueOrderDO.getNickName());
        save.setPhoneNumber(teboRescueQueueOrderDO.getPhoneNumber());
        save.setStationName(teboRescueQueueOrderDO.getStationName());
        save.setQueueBusinessType(teboRescueQueueOrderDO.getOrderType());
        save.setUnionid(teboRescueQueueOrderDO.getUnionid());
        save.setOrderType(teboRescueQueueOrderDO.getServiceItem());
        teboServiceOrderMapper.insert(save);
        return true;
    }

    @Override
    public String buildServiceOrderNo() {
        String prefix = CodeGenerator.generateServiceOrderCode();
        Integer num = redisService.getCacheObject("service_" + prefix);
        // 表示是今天第一个订单
        if (num == null) {
            num = 1;
            redisService.setCacheObject("service_" + prefix, num, 1L, TimeUnit.DAYS);
        } else {
            AtomicInteger atomicInteger = new AtomicInteger(num);
            num = atomicInteger.incrementAndGet();
            redisService.setCacheObject("service_" + prefix, num, 1L, TimeUnit.DAYS);
        }
        return prefix + String.format("%06d", num);
    }

    @Override
    public TeboServiceOrderDO getById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return teboServiceOrderMapper.selectById(id);
    }

//    @Override
//    public Integer updateById(TeboServiceOrderDO update) {
//
//        return teboServiceOrderMapper.updateById(update);
//    }

    @Override
    public Boolean hasInMaintainProgressOrder(Long accountId) {
        if (Objects.isNull(accountId)) {
            throw new ServiceException("未指定师傅");
        }
        ServiceOrderQueryDTO queryDTO = new ServiceOrderQueryDTO();
        queryDTO.setAccountId(accountId);
        queryDTO.setOrderStatusList(CollectionUtil.newArrayList(ServiceOrderStatusEnum.IN_MAINTENANCE.getCode(),ServiceOrderStatusEnum.WAIT_PAY.getCode()));
        queryDTO.setQueueBusinessTypeList(CollectionUtil.newArrayList(QueueBusinessTypeEnum.ARRIVED.getCode(), QueueBusinessTypeEnum.RESCUE.getCode()));
        List<TeboServiceOrderDO> list = teboServiceOrderMapper.list(queryDTO);
        return CollectionUtil.isNotEmpty(list);

    }

    @Override
    public List<TeboServiceOrderDO> list(ServiceOrderQueryDTO queryDTO) {
        return teboServiceOrderMapper.list(queryDTO);
    }

    @Override
    public Integer count(ServiceOrderQueryDTO queryDTO) {
        return teboServiceOrderMapper.count(queryDTO);
    }
}
