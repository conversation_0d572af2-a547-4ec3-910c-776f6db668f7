package com.tebo.rescue.mapper;

import com.tebo.rescue.entity.TeboCouponShopDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 卡券可用门店表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-13
 */
@Mapper
public interface TeboCouponShopMapper extends TeboBaseMapper<TeboCouponShopDO> {

    int deleteByCouponId(Long couponId);

    List<TeboCouponShopDO> selectByCouponId(Long couponId);

    /**
     * 批量插入
     * @param list
     */
    void batchInsert(@Param("list") List<TeboCouponShopDO> list);
}
