package com.tebo.rescue.mapper;

import com.tebo.rescue.applet.domain.dto.group.TeboGroupPurchaseOrderDTO;
import com.tebo.rescue.entity.TeboGroupPurchaseCustomerDO;
import com.tebo.common.datasource.base.TeboBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 团购订单消费者表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
@Mapper
public interface TeboGroupPurchaseCustomerMapper extends TeboBaseMapper<TeboGroupPurchaseCustomerDO> {
    List<TeboGroupPurchaseCustomerDO> getOrder(TeboGroupPurchaseOrderDTO teboGroupPurchaseOrderDTO);

}
