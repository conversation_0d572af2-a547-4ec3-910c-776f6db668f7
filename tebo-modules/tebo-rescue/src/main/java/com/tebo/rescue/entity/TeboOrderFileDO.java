package com.tebo.rescue.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 订单图片关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-08
 */
@Getter
@Setter
@TableName("tebo_order_file")
public class TeboOrderFileDO extends Model<TeboOrderFileDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 文件url
     */
    @TableField("file_url")
    private String fileUrl;




    /**
     * 订单id
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 对应的订单表
     */
    @TableField("order_table")
    private String orderTable;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField("del_flag")
    private Boolean delFlag;
    /**
     * 取消时间
     */
    @TableField("cancel_time")
    private LocalDateTime cancelTime;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;


    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
