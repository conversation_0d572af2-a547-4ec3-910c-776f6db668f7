package com.tebo.rescue.remote;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.tebo.common.core.domain.R;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.rescue.api.domain.dto.UnfreezeFundsQueryDTO;
import com.tebo.rescue.entity.TeboGiftPackOrderDO;
import com.tebo.rescue.entity.TeboGroupPurchaseOrderDO;
import com.tebo.rescue.entity.TeboUnfreezeFundsRecordDO;
import com.tebo.rescue.mapper.TeboCouponCustomerMapper;
import com.tebo.rescue.mapper.TeboGiftPackOrderMapper;
import com.tebo.rescue.mapper.TeboGroupPurchaseOrderMapper;
import com.tebo.rescue.mapper.TeboUnfreezeFundsRecordMapper;
import com.tebo.rescue.util.TeboCommissionUtil;
import com.tebo.system.api.RemoteWalletService;
import com.tebo.system.api.domain.dto.wallet.TeboWalletPlanDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Slf4j
@RequestMapping("/open/commission")
public class RemoteFundUnfreezeController {
    @Resource
    private TeboUnfreezeFundsRecordMapper fundsRecordMapper;

    @Resource
    private TeboGiftPackOrderMapper teboGiftPackOrderMapper;

    @Resource
    private TeboGroupPurchaseOrderMapper teboGroupPurchaseOrderMapper;

    @Resource
    private TeboCommissionUtil teboCommissionUtil;

    @Resource
    private RemoteWalletService remoteWalletService;
    @PostMapping("/cycleCommission")
    public R cycleCommission(@RequestBody UnfreezeFundsQueryDTO unfreezeFundsQueryDTO){
        String orderNo = unfreezeFundsQueryDTO.getOrderNo();
        UnfreezeFundsQueryDTO queryParam = new UnfreezeFundsQueryDTO();
        queryParam.setOrderNo(orderNo);
        List<TeboUnfreezeFundsRecordDO> list = fundsRecordMapper.getByOrderNo(queryParam);
        TeboGiftPackOrderDO teboGiftPackOrderDO = teboGiftPackOrderMapper.selectByOrderNo(orderNo);
        if (ObjectUtil.isEmpty(list)){
            if (teboGiftPackOrderDO.getOrderType() == 1){
                /**
                 * 佣金解冻
                 */
                TeboWalletPlanDTO teboWalletPlanDTO = teboCommissionUtil.buildCommissionParam(orderNo);
                log.info("signRecord signRecord commission param:{}", JSONObject.toJSONString(teboWalletPlanDTO));
                remoteWalletService.startSplitPlan(teboWalletPlanDTO);
                /**
                 * 超服店分佣
                 */
                teboCommissionUtil.buildVipShopCommissionParam(orderNo);
            }
        }
        /**
         * 骑行险解冻
         */
        TeboWalletPlanDTO cycle = new TeboWalletPlanDTO();
        if (teboGiftPackOrderDO.getOrderType() == 1){
            cycle = teboCommissionUtil.buildCycleParam(orderNo);
            remoteWalletService.startSplitPlan(cycle);
        }else if(teboGiftPackOrderDO.getOrderType() == 2) {
            TeboGroupPurchaseOrderDO teboGroupPurchaseOrderDO = teboGroupPurchaseOrderMapper.selectById(teboGiftPackOrderDO.getGroupOrderId());
            cycle = teboCommissionUtil.buildGroupCycleParam(teboGroupPurchaseOrderDO.getOrderNo(),teboGiftPackOrderDO.getUnionId(),orderNo);
            remoteWalletService.startSplitPlanForGroup(cycle);
        }
        /**
         * 插入核销记录
         */
        TeboUnfreezeFundsRecordDO teboUnfreezeFundsRecordDO = new TeboUnfreezeFundsRecordDO();
        BeanConvert.copy(unfreezeFundsQueryDTO,teboUnfreezeFundsRecordDO);
        fundsRecordMapper.insert(teboUnfreezeFundsRecordDO);
        return R.ok();
    }
}