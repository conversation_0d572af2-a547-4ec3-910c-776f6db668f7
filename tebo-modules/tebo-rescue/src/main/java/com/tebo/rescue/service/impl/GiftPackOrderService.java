package com.tebo.rescue.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.tebo.common.core.constant.GiftPackUrlConstants;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.exception.GlobalException;
import com.tebo.common.core.exception.ServiceException;
import com.tebo.common.redis.constant.TeboRescueCacheConstant;
import com.tebo.common.redis.service.RedisService;
import com.tebo.common.util.DataUtils;
import com.tebo.common.util.applet.AppletUtil;
import com.tebo.common.util.bean.BeanConvert;
import com.tebo.common.util.http.HttpTool;
import com.tebo.common.util.number.CodeGenerator;
import com.tebo.common.util.number.SnowFlakeUtil;
import com.tebo.common.util.position.AutoNaviPositionUtil;
import com.tebo.common.util.time.DateUtil;
import com.tebo.common.util.time.LocalDateUtil;
import com.tebo.mall.api.RemoteCommissionService;
import com.tebo.mall.api.RemoteIntegralOrderService;
import com.tebo.mall.api.domain.dto.CloudShopIntegralVo;
import com.tebo.mall.api.domain.dto.CouponCommissionShareDTO;
import com.tebo.mall.api.domain.dto.TeboCloudShopUserIntegralQueryDTO;
import com.tebo.mall.api.domain.dto.TeboIntegralDTO;
import com.tebo.mall.api.domain.dto.cycling.TeboCyclingOrderDTO;
import com.tebo.rescue.api.domain.dto.TeboGiftPackOrderDTO;
import com.tebo.rescue.api.domain.dto.TeboRemoteGiftOrderDTO;
import com.tebo.rescue.applet.domain.dto.GiftPackOrderDTO;
import com.tebo.rescue.applet.domain.dto.GiftPackOrderQueryDTO;
import com.tebo.rescue.entity.TeboGiftPackDO;
import com.tebo.rescue.entity.TeboGiftPackOrderDO;
import com.tebo.rescue.enums.PackIdEnum;
import com.tebo.rescue.mapper.TeboGiftPackMapper;
import com.tebo.rescue.mapper.TeboGiftPackOrderMapper;
import com.tebo.rescue.mapper.TeboPackOpenDistrictMapper;
import com.tebo.rescue.service.IGiftPackOrderService;
import com.tebo.rescue.service.TeboCouponCustomerService;
import com.tebo.rescue.service.TeboCustomerPromoteRecordService;
import com.tebo.rescue.service.TeboPromoteRecordService;
import com.tebo.rescue.service.mq.DisMqService;
import com.tebo.rescue.util.TeboCommissionUtil;
import com.tebo.rescue.web.domain.dto.promote.PromoteQueryDTO;
import com.tebo.rescue.web.domain.view.TeboCouponCustomerVO;
import com.tebo.rescue.web.domain.view.TeboGiftPackOrderVO;
import com.tebo.rescue.web.domain.view.TeboJiFenAccountVO;
import com.tebo.system.api.*;
import com.tebo.rescue.api.domain.view.TeboPromoteRecordVO;
import com.tebo.system.api.domain.dto.wallet.TeboWalletPlanDTO;
import com.tebo.system.api.domain.dto.wallet.TeboWalletPlanDetailDTO;
import com.tebo.system.api.domain.view.TeboPartnerInfoVO;
import com.tebo.system.api.model.TeboConsumer;
import com.tebo.system.api.model.TeboShop;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @Author：zhengmk
 * @Date 2023/12/24 9:32
 */

@Slf4j
@Service
public class GiftPackOrderService implements IGiftPackOrderService {
    @Resource
    private HttpServletRequest request;

    @Resource
    private TeboGiftPackOrderMapper giftPackOrderMapper;

    @Resource
    private TeboGiftPackMapper giftPackMapper;
    @Resource
    private TeboCouponCustomerService teboCouponCustomerService;
    @Resource
    private TeboPackOpenDistrictMapper teboPackOpenDistrictMapper;
    @Resource
    private RemotePayService remotePayService;
    @Resource
    private RemoteIntegralOrderService remoteIntegralOrderService;
    @Resource
    private TeboCustomerPromoteRecordService customerPromoteRecordService;
    @Resource
    private TeboPromoteRecordService promoteRecordService;
    @Resource
    private RemoteShopService remoteShopService;
    @Resource
    private RemotePartnerService remotePartnerService;
    @Resource
    private RemoteWalletService remoteWalletService;

    @Value("${giftPack.cloudShopUrl}")
    private String url;

    @Autowired
    private RedisService redisService;
    @Resource
    private DisMqService disMqService;
    @Resource
    private TeboCommissionUtil teboCommissionUtil;
    @Resource
    private RemoteCommissionService remoteCommissionService;
    @Resource
    private RemoteCustomerService remoteCustomerService;

    @Transactional
    @Override
    public Long createGiftOrder(GiftPackOrderDTO packOrderDTO) {
        TeboGiftPackDO giftPackDO = giftPackMapper.selectById(packOrderDTO.getPackId());
        String unionid = AppletUtil.getUnionIdByRequest(request);
        TeboGiftPackOrderDO giftPackOrderDO = new TeboGiftPackOrderDO();
        BeanConvert.copy(packOrderDTO, giftPackOrderDO);
        giftPackOrderDO.setCreateBy(unionid);
        giftPackOrderDO.setOrderNo(CodeGenerator.generateGiftPackOrderCode());
        giftPackOrderDO.setId(SnowFlakeUtil.nextId());
        giftPackOrderDO.setOrderNo("GPD" + giftPackOrderDO.getId());
        giftPackOrderDO.setUnionId(unionid);
        giftPackOrderDO.setOrderStatus(0);
        giftPackOrderDO.setType(0);
        TeboPromoteRecordVO promoteRecordVO = customerPromoteRecordService.selectByUnionId(unionid);
        if (ObjectUtil.isNotEmpty(promoteRecordVO)){
            giftPackOrderDO.setReferencePhone(promoteRecordVO.getReferencePhone());
            giftPackOrderDO.setType(promoteRecordVO.getType());
            try {
                if(promoteRecordVO.getType() == 3){
                    R<TeboShop> shopR = remoteShopService.getShopByPhoneNumber(promoteRecordVO.getReferencePhone());
                    if (ObjectUtil.isNotEmpty(shopR) && ObjectUtil.isNotEmpty(shopR.getData()) ){
                        TeboShop teboShop = shopR.getData();
                        Long tenantId = teboShop.getTenantId();
                        if (ObjectUtil.isNotEmpty(tenantId)){
                            R<TeboPartnerInfoVO> partnerInfoVOR = remotePartnerService.getById(tenantId);
                            TeboPartnerInfoVO teboPartnerInfoVO = partnerInfoVOR.getData();
                            if (ObjectUtil.isNotEmpty(teboPartnerInfoVO)){
                                giftPackOrderDO.setPartnerPhone(teboPartnerInfoVO.getLinkPhone());
                            }
                        }
                    }
                }else if (promoteRecordVO.getType() == 5 || promoteRecordVO.getType() == 6){
                    PromoteQueryDTO queryDTO = new PromoteQueryDTO();
                    queryDTO.setReferencePhone(giftPackOrderDO.getReferencePhone());
                    TeboPromoteRecordVO teboPromoteRecordVO = promoteRecordService.getByPromotedPhone(queryDTO);
                    if (ObjectUtil.isNotEmpty(teboPromoteRecordVO)){
                        giftPackOrderDO.setPartnerPhone(teboPromoteRecordVO.getReferencePhone());
                    }
                }
            }catch (Exception e){
               log.error("获取合伙人信息失败",e);
            }
            giftPackOrderDO.setReferenceWalletId(promoteRecordVO.getReferenceWalletId());
        }
        if (!StringUtils.isEmpty(packOrderDTO.getLgt()) && !StringUtils.isEmpty(packOrderDTO.getLnt()) ){
            // 如果新字段为空，则根据经纬度获取
            if (StringUtils.isEmpty(packOrderDTO.getDistantStr())) {
                String provinceCityDistrict = AutoNaviPositionUtil.getProvinceCityDistrict(packOrderDTO.getLgt(), packOrderDTO.getLnt());
                giftPackOrderDO.setDistrict(provinceCityDistrict);
            }else {
                giftPackOrderDO.setDistrict(packOrderDTO.getDistantStr());
            }
          /**
           List<String> allDistrictList = teboPackOpenDistrictMapper.getAllDistrictList();
           if (!StringUtils.isEmpty(provinceCityDistrict)){
               String[] strArray = provinceCityDistrict.split("-");
               String province = strArray[0];
               String city = strArray[1];
               String district = strArray[2];
               String detail = province+"-"+city+"-"+district;
               Boolean open = false;
               for (String str : allDistrictList ){
                   if (detail.contains(str)){
                       open = true;
                       break;
                   }
               }
              if (!open){
                 throw new ServiceException("当前区域未开通，敬请期待.....");
              }

           }
           */


        }
        if (giftPackDO.getStatus() != 10) {
            throw new ServiceException("礼包未到活动时间，暂不可购买");
        }

        checkBuyLimit(giftPackDO,unionid);
        giftPackOrderDO.setOrderAmount(giftPackDO.getPrice());
        giftPackOrderMapper.insert(giftPackOrderDO);
        return giftPackOrderDO.getId();
    }

    @Override
    public Long createMergeOrder(TeboRemoteGiftOrderDTO orderDTO) {
        TeboGiftPackOrderDO giftPackOrderDO = new TeboGiftPackOrderDO();
        giftPackOrderDO.setId(SnowFlakeUtil.nextId());
        giftPackOrderDO.setOrderNo("GPD" + giftPackOrderDO.getId() + "A");
        giftPackOrderDO.setUnionId(orderDTO.getUnionId());
        giftPackOrderDO.setOrderStatus(1);
        giftPackOrderDO.setType(3);
        giftPackOrderDO.setPackId(1839262600894873600L);
        R<TeboShop> shopR = remoteShopService.getShopInfo(orderDTO.getShopId());
        if (ObjectUtil.isNotEmpty(shopR) && ObjectUtil.isNotEmpty(shopR.getData())) {
            TeboShop teboShop = shopR.getData();
            giftPackOrderDO.setReferencePhone(teboShop.getPhoneNumber());
            Long tenantId = teboShop.getTenantId();
            if (ObjectUtil.isNotEmpty(tenantId)) {
                R<TeboPartnerInfoVO> partnerInfoVOR = remotePartnerService.getById(tenantId);
                TeboPartnerInfoVO teboPartnerInfoVO = partnerInfoVOR.getData();
                if (ObjectUtil.isNotEmpty(teboPartnerInfoVO)) {
                    giftPackOrderDO.setPartnerPhone(teboPartnerInfoVO.getLinkPhone());
                }
            }
        }
        giftPackOrderDO.setDistrict(orderDTO.getDistrict());
        giftPackOrderDO.setOrderAmount(4990);
        giftPackOrderDO.setOrderType(3);
        giftPackOrderDO.setPayTime(LocalDateTime.now());
        giftPackOrderDO.setCreateBy(orderDTO.getBatteryOrderNo());
        // 插入久久券订单
        giftPackOrderMapper.insert(giftPackOrderDO);
        // 发放已占用的卡电池券券
        // 发放未使用的大米券
        // 发放积分
        // 异步发券
        disMqService.grantPackCoupon(giftPackOrderDO.getPackId(), giftPackOrderDO.getUnionId(),giftPackOrderDO.getOrderNo());
        // 发放200积分
        log.info("createMergeOrder addUserIntegral {}", giftPackOrderDO.getUnionId());
//        remoteIntegralOrderService.addUserIntegral(new TeboIntegralDTO(200, giftPackOrderDO.getUnionId(),giftPackOrderDO.getId()));
        /*
         * 创建分账计划 供应商付款&推广人佣金
         */
        TeboWalletPlanDTO teboWalletPlanDTO = new TeboWalletPlanDTO();
        teboWalletPlanDTO.setOrderNo(giftPackOrderDO.getOrderNo());
        List<TeboWalletPlanDetailDTO> detailList = new ArrayList<>();
        /**
         * 大米供应商打款
         */
        TeboWalletPlanDTO riceWallet = teboCommissionUtil.buildRiceParam(giftPackOrderDO.getOrderNo());
        if (ObjectUtil.isNotEmpty(riceWallet)){
            detailList.addAll(riceWallet.getDetails());
        }
        remoteWalletService.splitPlan(teboWalletPlanDTO);
        /**
         * 超服店分佣
         */
        R<TeboConsumer> customerR = remoteCustomerService.selectByUnionId(giftPackOrderDO.getUnionId());
        CouponCommissionShareDTO commissionShareDTO = new CouponCommissionShareDTO();
        commissionShareDTO.setUnionId(giftPackOrderDO.getUnionId());
        commissionShareDTO.setOrderNo(giftPackOrderDO.getOrderNo());
        commissionShareDTO.setOrderId(giftPackOrderDO.getId());
        commissionShareDTO.setPhoneNumber(customerR.getData().getPhoneNumber());
        commissionShareDTO.setPacketId(giftPackOrderDO.getPackId());
        /**
         * 根据推广人手机号查询推广人门店id
         */
        if (ObjectUtil.isNotEmpty(shopR.getData())){
            commissionShareDTO.setCloudShopId(shopR.getData().getCloudShopId());
        }
        log.info("createMergeOrder saleCardAmount param :{}", com.alibaba.fastjson2.JSONObject.toJSONString(commissionShareDTO));
        remoteCommissionService.saleCardAmount(commissionShareDTO);
        return giftPackOrderDO.getId();
    }


    @Transactional
    @Override
    public Long createGroupGiftOrder(GiftPackOrderDTO packOrderDTO) {
        TeboGiftPackDO giftPackDO = giftPackMapper.selectById(packOrderDTO.getPackId());
        TeboGiftPackOrderDO giftPackOrderDO = new TeboGiftPackOrderDO();
        BeanConvert.copy(packOrderDTO, giftPackOrderDO);
        giftPackOrderDO.setCreateBy(packOrderDTO.getUnionId());
        giftPackOrderDO.setId(packOrderDTO.getId());
        giftPackOrderDO.setOrderNo(packOrderDTO.getOrderNo());
        giftPackOrderDO.setUnionId(packOrderDTO.getUnionId());
        giftPackOrderDO.setType(7);
        if (!StringUtils.isEmpty(packOrderDTO.getLgt()) && !StringUtils.isEmpty(packOrderDTO.getLnt()) ){
            String provinceCityDistrict = AutoNaviPositionUtil.getProvinceCityDistrict(packOrderDTO.getLgt(),packOrderDTO.getLnt());
            giftPackOrderDO.setDistrict(provinceCityDistrict);
        }
        giftPackOrderDO.setOrderAmount(giftPackDO.getPrice());
        giftPackOrderMapper.insert(giftPackOrderDO);
        return giftPackOrderDO.getId();
    }
    @Override
    public Boolean getGiftPackOrderList(String unionId) {
        List<TeboGiftPackOrderDO> orderDOList = giftPackOrderMapper.selectByUnionId(unionId);
        return CollectionUtils.isEmpty(orderDOList) ? false : true;
    }

    @Override
    public TeboJiFenAccountVO isVip(String unionId) {
        List<TeboGiftPackOrderDO> orderDOList = giftPackOrderMapper.selectByUnionId(unionId);
        TeboJiFenAccountVO teboJiFenAccountVO = new TeboJiFenAccountVO();
        String key = TeboRescueCacheConstant.NON_VIP_SEND_INTEGRAL +unionId;
        /**
         * 是否发放过非会员积分
         */
        String cacheVip = redisService.getCacheObject(key);
        Boolean vip = false;
        if (!CollectionUtils.isEmpty(orderDOList) ||"1".equals(cacheVip)){
            vip = true;
        }
        teboJiFenAccountVO.setVip(vip);
        if (!vip){
            TeboIntegralDTO teboIntegralDTO = new TeboIntegralDTO();
            teboIntegralDTO.setIntegral(50);
            teboIntegralDTO.setUnionId(unionId);
            redisService.setCacheObject(key,"1");
            try {
                remoteIntegralOrderService.addNonVipUserIntegral(teboIntegralDTO);
            }catch (Exception e){
               log.error("addNonVipUserIntegral error",e);
            }
        }
        return teboJiFenAccountVO;
    }

    @Override
    public List<TeboGiftPackOrderVO> getList(String unionId) {
        List<TeboGiftPackOrderDO> orderDOList = giftPackOrderMapper.selectByUnionIdAndStatus(unionId);
        if (CollectionUtils.isEmpty(orderDOList)) {
            return Collections.emptyList();
        }
        List<TeboGiftPackOrderVO> orderDOListVO = BeanConvert.copyList(orderDOList, TeboGiftPackOrderVO::new);
        orderDOListVO.forEach(orderDO -> {
            orderDO.setPackName("天能久久券");
        });
        return orderDOListVO;
    }

    @Override
    public Boolean refundGiftPack(String orderNo, String unionId) {
        log.info("refundGiftPack orderNo {} unionId {}", orderNo, unionId);
        TeboGiftPackOrderDO giftPackOrderDO = giftPackOrderMapper.selectByOrderNo(orderNo);
        if (ObjectUtil.isEmpty(giftPackOrderDO)) {
            log.info("refundGiftPack fail orderNo {} unionId {}", orderNo, unionId);
            throw new GlobalException("订单不存在");
        }
        if (!ObjectUtils.nullSafeEquals(giftPackOrderDO.getOrderStatus(),1)) {
            log.info("refundGiftPack fail orderNo {} unionId {}", orderNo, unionId);
            throw new GlobalException("该订单已处理，请勿重复提交");
        }
        if (giftPackOrderDO.getOrderType() == 3){
            throw new GlobalException("合并下单不支持退款");
        }
        // 如果订单是之前微信支付的，则提醒找在线客服处理
        if (giftPackOrderDO.getPayTime().isBefore(LocalDateUtil.parseTime("2024-11-15 00:00:00"))) {
            throw new GlobalException("请联系在线客服处理退款");
        }
        // 取消卡券
        teboCouponCustomerService.refundGiftPackCoupon(orderNo, unionId);
        // 调用退款接口
        R<Boolean> res = remoteWalletService.orderPaymentRefund(orderNo);
        log.info("refundGiftPack orderPaymentRefund res {}", res);
        if (res.getData()) {
            try {
                giftPackOrderDO.setOrderStatus(3);
                giftPackOrderDO.setVerificationStatus(2);
                giftPackOrderDO.setRiceVerificationStatus(2);
                giftPackOrderDO.setRiceVerificationTime(LocalDateTime.now());
                giftPackOrderDO.setBatteryVerificationStatus(2);
                giftPackOrderDO.setBatteryVerificationTime(LocalDateTime.now());
                giftPackOrderDO.setCycleVerificationStatus(2);
                giftPackOrderDO.setCycleVerificationTime(LocalDateTime.now());
                giftPackOrderDO.setRefundTime(LocalDateTime.now());
                giftPackOrderMapper.updateById(giftPackOrderDO);
                log.info("refundGiftPack end orderNo {} unionId {}", orderNo, unionId);
                // 退积分（根据两个礼包分别退200、400积分）
                Integer integralNum = 200;
                if (!giftPackOrderDO.getPackId().equals(1839262600894873600L)) {
                    integralNum = 400;
                }
                R<Boolean> IntegralRes = remoteIntegralOrderService.reduceUserIntegral(new TeboIntegralDTO(integralNum, unionId, giftPackOrderDO.getId()));
                log.info("refundGiftPack reduceUserIntegral IntegralRes {}", IntegralRes);
                // 退B端计划
                String apiUrl = url + GiftPackUrlConstants.clearSaleCardAmount;
                Map<String, String> headers = new HashMap();
                headers.put("SystemChannel", "PC_BGSHOP");
                Map<String, Object> apiParams = new TreeMap<>();//接口参数
                apiParams.put("saleCardOrderCode", orderNo);
                String resultJson = HttpTool.sendPost(apiUrl, JSONObject.toJSONString(apiParams), headers);
                log.info("refundGiftPack clearSaleCardAmount resultJson {}", resultJson);
                return true;
            } catch (Exception e) {
                return true;
            }
        }else {
            return false;
        }
    }

    @Override
    public void updateOrderStatus(TeboGiftPackOrderDTO giftPackOrderDTO) {
        TeboGiftPackOrderDO teboGiftPackOrderDO = giftPackOrderMapper.selectByOrderNo(giftPackOrderDTO.getOrderNo());
        if (ObjectUtil.isEmpty(teboGiftPackOrderDO)){
            return ;
        }
        teboGiftPackOrderDO.setVerificationStatus(1);
        if (ObjectUtil.isEmpty(teboGiftPackOrderDO.getVerificationTime())){
          teboGiftPackOrderDO.setVerificationTime(LocalDateTime.now());
        }
        teboGiftPackOrderDO.setCycleVerificationTime(LocalDateTime.now());
        teboGiftPackOrderDO.setCycleVerificationStatus(1);
        giftPackOrderMapper.updateById(teboGiftPackOrderDO);
    }

    private void checkBuyLimit(TeboGiftPackDO giftPackDO, String unionid) {

        GiftPackOrderQueryDTO queryDTO = new GiftPackOrderQueryDTO();
        queryDTO.setPackId(giftPackDO.getId());
        queryDTO.setUnionid(unionid);
        queryDTO.setOrderStatus(1);
        queryDTO.setOrderType(1);
        // 当礼包限制购买次数时间
        if (giftPackDO.getBuyLimit() == 1) {
            // 统计已成功购买次数
            int count = giftPackOrderMapper.count(queryDTO);
            if (count >= giftPackDO.getBuyLimitNum()) {
                throw new GlobalException("购买次数已达上限");
            }
        }
//        if(giftPackDO.getResidueNum() <= 0) {
//            throw new GlobalException("礼包已售罄");
//        }
        /**
         * 会员权益礼包 一个月只能买2次
         */
        if (giftPackDO.getType() == 3 && giftPackDO.getId().toString().equals(PackIdEnum.JU_HUI.getCode()) ){
            int count = giftPackOrderMapper.getTeboGiftPackOrder(queryDTO);
            if (count >= 3){
              throw new ServiceException("本月购买已达上限3次");
            }
        }
    }

}
