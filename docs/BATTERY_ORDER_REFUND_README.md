# 电池订单退款系统

## 📋 项目概述

电池订单退款系统是一个完整的退款处理解决方案，支持电池订单与久久券的合并退款，包含权益收回、异步回调处理、定时状态查询等功能。

## 🎯 核心功能

### 1. 退款申请处理
- **订单校验**：格式校验（TBCXA开头A结尾）、状态校验、重复退款检查
- **权益收回**：物理删除2张电池抵用券（20元+30元）和1张大米兑换券
- **状态管理**：更新电池订单和久久券订单状态为已退款
- **异步退款**：调用第三方退款接口，支持异步回调

### 2. 异步回调处理
- **回调接口**：接收第三方退款结果回调
- **状态更新**：更新订单回调状态和退款记录状态
- **容错处理**：回调失败不影响主流程

### 3. 定时状态查询
- **主动查询**：定时查询长时间未回调的退款订单
- **状态同步**：主动从第三方查询退款状态并同步本地
- **数据清理**：定期清理过期的退款记录

## 🏗️ 系统架构

### 数据关联关系
```
电池订单表 (tebo_mall_order)
    ↓ order_no ↔ creater_by
久久券订单表 (tebo_gift_pack_order)
    ↓ order_no ↔ order_no
券表 (tebo_coupon_customer)
    ├── 大米券: coupon_id = 1839262235357085696
    └── 电池券: 其他券ID (2张)
```

### 核心组件
- **TeboBatteryOrderService**: 退款业务核心服务
- **RefundStatusQueryTask**: 定时任务处理器
- **RemoteCouponService**: 券服务远程接口
- **RemoteGiftPackOrderService**: 久久券订单远程接口

## 🔄 业务流程

### 退款申请流程
1. **分布式锁控制** - 防止并发退款
2. **订单格式校验** - TBCXA开头，A结尾
3. **电池订单校验** - 存在性、状态、重复退款、核销状态
4. **久久券查询** - 通过`creater_by`字段关联查找久久券订单
5. **大米券校验** - 检查`coupon_id=1839262235357085696`的券状态是否为0
6. **权益收回** - 物理删除2张电池券+1张大米券记录
7. **订单状态更新** - 久久券订单和电池订单都更新为已退款
8. **异步退款调用** - 执行实际退款，不等待回调
9. **退款记录保存** - 完整记录退款信息

### 异步回调处理
1. **接收回调** - 第三方支付平台回调退款结果
2. **状态更新** - 更新订单回调状态和退款记录状态
3. **日志记录** - 记录回调处理结果

### 定时状态查询
1. **查询待处理订单** - 每5分钟查询10分钟前未回调的订单
2. **主动查询状态** - 调用第三方接口查询退款状态
3. **状态同步** - 更新本地订单和退款记录状态
4. **数据清理** - 每天清理30天前的退款记录

## 📊 数据库设计

### 核心表结构

#### 电池订单表 (tebo_mall_order)
```sql
ALTER TABLE `tebo_mall_order` ADD COLUMN `refund_callback_status` tinyint(4) DEFAULT 0 
COMMENT '退款回调是否成功 0:未回调 1:回调成功 2:回调失败';
```

#### 退款记录表 (tebo_battery_order_refund)
```sql
CREATE TABLE `tebo_battery_order_refund` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `battery_order_no` varchar(64) NOT NULL COMMENT '电池订单号',
  `gift_pack_order_no` varchar(64) DEFAULT NULL COMMENT '久久券订单号',
  `refund_amount` int(11) NOT NULL COMMENT '退款金额(分)',
  `refund_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '退款状态 1:退款中 2:退款成功 3:退款失败',
  `refund_reason` varchar(255) DEFAULT NULL COMMENT '退款原因',
  `wechat_refund_no` varchar(64) DEFAULT NULL COMMENT '微信退款单号',
  `operator` varchar(64) DEFAULT NULL COMMENT '操作人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_battery_order_no` (`battery_order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电池订单退款记录表';
```

#### 久久券订单表 (tebo_gift_pack_order)
```sql
ALTER TABLE `tebo_gift_pack_order` ADD COLUMN `refund_time` datetime DEFAULT NULL COMMENT '退款时间';
```

## 🚀 API接口

### 退款申请
```http
POST /applet/order/refundBatteryOrder
Content-Type: application/json

{
    "orderNo": "TBCXA123456789A",
    "refundReason": "用户申请退款",
    "operator": "admin"
}
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "退款申请提交成功",
    "data": true
}
```

### 退款回调
```http
POST /applet/order/refundCallback
Content-Type: application/x-www-form-urlencoded

orderNo=TBCXA123456789A&refundNo=RF123456&callbackStatus=1
```

**参数说明**:
- `orderNo`: 电池订单号
- `refundNo`: 第三方退款单号
- `callbackStatus`: 回调状态 (1:成功 2:失败)

## ⚙️ 配置说明

### 定时任务配置
```java
// 每5分钟执行一次状态查询
@Scheduled(fixedRate = 5 * 60 * 1000)
public void queryPendingRefunds()

// 每天凌晨2点清理数据
@Scheduled(cron = "0 0 2 * * ?")
public void cleanupOldRefunds()
```

### 券类型配置
```java
// 大米券ID
public static final Long RICE_COUPON_ID = 1839262235357085696L;

// 券状态
public static final Integer COUPON_STATUS_UNUSED = 0;  // 未使用
public static final Integer COUPON_STATUS_USED = 1;    // 已使用
```

### 订单状态配置
```java
// 订单状态枚举
REFUNDED(11, "已退款")

// 回调状态枚举
NOT_CALLBACK(0, "未回调")
CALLBACK_SUCCESS(1, "回调成功")
CALLBACK_FAILED(2, "回调失败")
```

## 🛡️ 安全特性

### 并发控制
- **分布式锁**: 使用Redis分布式锁防止同一订单并发退款
- **事务管理**: 使用`@Transactional`确保数据一致性
- **幂等性**: 支持重复调用，防止重复退款

### 异常处理
```java
// 分层异常处理策略
try {
    // 业务逻辑
} catch (ServiceException e) {
    log.error("业务异常：{}", e.getMessage());
    throw e; // 重新抛出业务异常
} catch (Exception e) {
    log.error("系统异常", e);
    throw new ServiceException("系统异常，请联系客服");
}
```

### 数据安全
- **物理删除**: 券记录物理删除，不可恢复
- **状态追踪**: 完整的状态变更记录
- **操作审计**: 记录操作人和操作时间

## 📈 监控指标

### 业务指标
- 退款申请量
- 退款成功率
- 退款处理时长
- 权益收回成功率
- 回调成功率

### 技术指标
- 接口响应时间
- 定时任务执行情况
- 异常告警统计
- 数据库性能指标

## 🔧 部署指南

### 环境要求
- JDK 8+
- MySQL 5.7+
- Redis 3.0+
- Spring Boot 2.x

### 部署步骤
1. **数据库初始化**: 执行 `sql/tebo_battery_order_refund.sql`
2. **配置检查**: 检查分布式锁和定时任务配置
3. **服务部署**: 先部署rescue服务，再部署mall服务
4. **接口测试**: 验证退款和回调接口
5. **监控配置**: 配置业务和技术监控

## 🧪 测试指南

### 测试用例
```java
@Test
public void testRefundBatteryOrder() {
    TeboBatteryOrderRefundDTO refundDTO = new TeboBatteryOrderRefundDTO();
    refundDTO.setOrderNo("TBCXA123456789A");
    refundDTO.setRefundReason("用户申请退款");
    refundDTO.setOperator("test_user");
    
    Boolean result = teboBatteryOrderService.refundBatteryOrder(refundDTO);
    assertTrue(result);
}
```

### 验证要点
1. **关联查询**: 确认通过`creater_by`字段能正确查询到久久券订单
2. **大米券识别**: 确认能正确识别`coupon_id=1839262235357085696`的券
3. **状态判断**: 确认`status=0`为未使用，`status=1`为已使用
4. **权益收回**: 确认能删除2张电池券+1张大米券
5. **异步处理**: 确认退款调用后不等待回调直接返回

## 🆘 故障排查

### 常见问题
1. **退款失败**: 检查订单状态和券状态
2. **回调超时**: 检查网络和第三方服务状态
3. **定时任务异常**: 检查数据库连接和任务配置
4. **权益收回失败**: 检查券记录是否存在

### 日志分析
- 查看退款流程日志
- 分析异常堆栈信息
- 监控业务指标变化
- 检查定时任务执行日志

## 📞 技术支持

### 文档资源
- `sql/tebo_battery_order_refund.sql` - 数据库脚本

### 联系方式
如有问题，请联系开发团队或查看相关技术文档。
