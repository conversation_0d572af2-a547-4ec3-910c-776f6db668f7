# 电池订单退款功能

## 功能概述

电池订单退款功能用于处理电池订单和久久券的合并退款，确保在满足退款条件的情况下，能够安全地退款并处理相关的券状态。

## 功能特点

1. **并发安全**：使用分布式锁防止并发退款
2. **事务保证**：使用数据库事务确保数据一致性
3. **完整校验**：多重校验确保退款条件满足
4. **状态管理**：正确处理订单和券的状态变更
5. **记录追踪**：完整的退款记录便于追踪

## 退款条件校验

### 1. 订单号格式校验
- 订单号必须以 `TBCXA` 开头
- 订单号必须以 `A` 结尾
- 示例：`TBCXA123456789A`

### 2. 大米券状态校验
- 大米券必须未使用
- 通过积分校验接口确认

### 3. 电池订单状态校验
- 电池订单必须未核销
- 订单状态必须为"待提货"

## API接口

### 退款接口

**请求地址：** `POST /battery/order/refund`

**请求参数：**
```json
{
    "orderNo": "TBCXA123456789A",
    "refundReason": "用户申请退款",
    "operator": "admin"
}
```

**参数说明：**
- `orderNo`：电池订单号（必填）
- `refundReason`：退款原因（可选）
- `operator`：操作人（可选）

**响应示例：**
```json
{
    "code": 200,
    "msg": "退款申请提交成功",
    "data": true
}
```

## 退款处理流程

1. **分布式锁控制**：防止同一订单并发退款
2. **订单号格式校验**：确保订单号符合规范
3. **订单存在性校验**：确认电池订单存在
4. **订单状态校验**：确认订单可以退款
5. **重复退款检查**：防止重复退款
6. **核销状态校验**：确认订单未核销
7. **久久券查询**：查找关联的久久券订单
8. **大米券状态校验**：确认大米券未使用
9. **执行退款处理**：
   - 更新电池订单状态为已取消
   - 处理久久券订单和券状态
   - 调用退款接口
   - 记录退款记录

## 数据库表结构

### 退款记录表 (tebo_battery_order_refund)

```sql
CREATE TABLE `tebo_battery_order_refund` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `battery_order_no` varchar(64) NOT NULL COMMENT '电池订单号',
  `gift_pack_order_no` varchar(64) DEFAULT NULL COMMENT '久久券订单号',
  `refund_amount` int(11) NOT NULL COMMENT '退款金额(分)',
  `refund_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '退款状态 1:退款中 2:退款成功 3:退款失败',
  `refund_reason` varchar(255) DEFAULT NULL COMMENT '退款原因',
  `wechat_refund_no` varchar(64) DEFAULT NULL COMMENT '微信退款单号',
  `operator` varchar(64) DEFAULT NULL COMMENT '操作人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_battery_order_no` (`battery_order_no`),
  KEY `idx_gift_pack_order_no` (`gift_pack_order_no`),
  KEY `idx_refund_status` (`refund_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电池订单退款记录表';
```

## 涉及的表

1. **券订单表**：`rescue.tebo_gift_pack_order` - 久久券订单信息
2. **大米券表**：`tebo_coupon_customer` - 大米券和电池券信息
3. **电池订单表**：`mall.tebo_mall_order` - 电池订单信息
4. **退款记录表**：`tebo_battery_order_refund` - 退款记录

## 错误处理

常见错误及处理：

1. **订单号格式不正确**：返回格式校验错误
2. **订单不存在**：返回订单不存在错误
3. **订单状态不允许退款**：返回状态错误
4. **订单已退款**：返回重复退款错误
5. **订单已核销**：返回核销状态错误
6. **大米券已使用**：返回券状态错误
7. **并发处理**：返回处理中提示

## 注意事项

1. **并发控制**：使用分布式锁确保同一订单不会并发退款
2. **事务管理**：整个退款过程在一个事务中执行
3. **状态一致性**：确保电池订单、久久券订单、券状态的一致性
4. **记录完整性**：完整记录退款过程便于后续追踪
5. **异常处理**：任何步骤失败都会回滚整个事务

## 测试

运行测试用例：
```bash
mvn test -Dtest=TeboBatteryOrderServiceTest
```

测试覆盖：
- 正常退款流程
- 订单号格式校验
- 各种异常情况处理
