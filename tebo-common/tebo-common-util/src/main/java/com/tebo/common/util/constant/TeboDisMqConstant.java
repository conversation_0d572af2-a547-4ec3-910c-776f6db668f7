package com.tebo.common.util.constant;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024/1/15 14:23
 * @Desc : DisMq 常量池
 */
public class TeboDisMqConstant {

    // Topic
    public static final String DIS_MQ_TOPIC_RESCUE = "dis_mq_topic_rescue";
    public static final String DIS_MQ_TOPIC_MALL = "dis_mq_topic_mall";
    public static final String DIS_MQ_TOPIC_LST = "dis_mq_topic_lst";



    // msgType
    /**
     * 礼包卡券-发放
     */
    public static final Integer COUPON_GRANT_PACK_COUPON = 100001;
    /**
     * 消费者行为记录
     */
    public static final Integer CONSUMER_SERVICE_RECORD = 100002;

    /**
     * 通知安卓应用消息
     */
    public static final Integer NOTIFY_APP_MSG = 100003;

    /**
     * 工单超时消息
     */
    public static final Integer NOTIFY_ORDER_TIMEOUT = 100004;

    /**
     * 单独卡券-发放
     */
    public static final Integer COUPON_GRANT_SINGLE_COUPON = 100005;


    /**
     * 商城核销
     */
    public static final Integer MALL_WRITE_OFF = 100005;

    /**
     * 公众号推送给门店
     */
    public static final Integer NOTIFY_SHOP_MSG = 200001;


}
