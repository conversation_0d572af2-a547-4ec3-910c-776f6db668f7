# Tomcat
server:
  port: 8400

# Spring
spring:
  application:
    # 应用名称
    name: tebo-gateway
  profiles:
    # 环境配置
    active: @env@
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: @nacos.server.address@
        namespace: @nacos.server.namespace@
        group: tebo
        username: nacos
        password: Tn!@#$1234
      config:
        # 配置中心地址
        server-addr: @nacos.server.address@
        namespace: @nacos.server.namespace@
        group: tebo
        username: nacos
        password: Tn!@#$1234
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: 127.0.0.1:8718
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: 127.0.0.1:8848
            dataId: sentinel-tebo-gateway
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: gw-flow
