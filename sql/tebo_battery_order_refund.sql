-- 电池订单退款记录表
CREATE TABLE `tebo_battery_order_refund` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `battery_order_no` varchar(64) NOT NULL COMMENT '电池订单号',
  `gift_pack_order_no` varchar(64) DEFAULT NULL COMMENT '久久券订单号',
  `refund_amount` int(11) NOT NULL COMMENT '退款金额(分)',
  `refund_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '退款状态 1:退款中 2:退款成功 3:退款失败',
  `refund_reason` varchar(255) DEFAULT NULL COMMENT '退款原因',
  `wechat_refund_no` varchar(64) DEFAULT NULL COMMENT '微信退款单号',
  `operator` varchar(64) DEFAULT NULL COMMENT '操作人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_battery_order_no` (`battery_order_no`),
  KEY `idx_gift_pack_order_no` (`gift_pack_order_no`),
  KEY `idx_refund_status` (`refund_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电池订单退款记录表';

-- 为电池订单表添加退款回调状态字段
ALTER TABLE `tebo_mall_order` ADD COLUMN `refund_callback_status` tinyint(4) DEFAULT 0 COMMENT '退款回调是否成功 0:未回调 1:回调成功 2:回调失败';

-- 为久久券订单表添加退款时间字段（如果不存在）
ALTER TABLE `tebo_gift_pack_order` ADD COLUMN `refund_time` datetime DEFAULT NULL COMMENT '退款时间';
