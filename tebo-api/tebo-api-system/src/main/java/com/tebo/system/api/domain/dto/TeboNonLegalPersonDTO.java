package com.tebo.system.api.domain.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class TeboNonLegalPersonDTO implements Serializable {
    /**
     * 泰博出行门店id,
     */
    private String tbShopId;

    /**
     * 云门店id
     */
    private String cloudShopId;

    /**
     * 非法人姓名
     */
    private String unincorporatedName;

    /**
     * 非法人手机号
     */
    private String phoneNumber;

    /**
     * 非法人身份证正面
     */
    private String idCardFront;

    /**
     * 非法人身份证反面
     */
    private String idCardBack;

    /**
     * 非法人身份证号码
     */
    private String idCard;

    /**
     * 开户银行
     */
   private String accountBank;

    /**
     * 银行卡号
     */
    private String accountNumber;

    /**
     * 身份证开始时间
     */
    private String idCardPeriodBegin;

    /**
     * 身份证结束时间
     */
    private String idCardPeriodEnd;

    /**
     * 开户银行省市编码
     */
    private String bankAddressCode;
}