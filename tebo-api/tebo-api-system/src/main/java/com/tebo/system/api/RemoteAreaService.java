package com.tebo.system.api;

import com.tebo.common.core.constant.ServiceNameConstants;
import com.tebo.common.core.domain.R;
import com.tebo.system.api.domain.view.TeboAreaVO;
import com.tebo.system.api.factory.RemoteAreaFallbackFactory;
import com.tebo.system.api.factory.RemoteUserFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2023/12/16 16:34
 * @Desc :
 */
@FeignClient(contextId = "remoteAreaService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteAreaFallbackFactory.class)
public interface RemoteAreaService {
    /**
     * 根据区域编码查询区域信息
     *
     * @param areaCode 区域编码
     * @return 区域信息
     */
    @GetMapping("/area/{areaCode}")
    R<TeboAreaVO> selectAreaByCode(@PathVariable("areaCode") String areaCode);


    /**
     * 获取三级地址对应的省市信息
     * @param districtCode
     * @return
     */
    @GetMapping("/area/provinceCityInfo/{districtName}")
    R<TeboAreaVO> provinceCityInfo(@PathVariable("districtName") String districtName);

}
