package com.tebo.system.api.domain.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.tebo.common.core.web.page.PageDomain;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * @Author：zhengmk
 * @Date 2023/12/12 10:25
 */
@Data
public class TeboLstShopQueryDTO extends PageDomain implements Serializable {

    /**
     * 审核状态 0:待审核 1:审核通过 2:审核驳回
     */
    private Integer reviewStatus;

    /**
     * 零售通门店名称
     */
    private String lstShopName;

    /**
     * 区域
     */
    private String districtName;

    /***
     * 泰博出行门店名称
     */
    private String shopName;

    /**
     * 缴纳保证金  1:缴纳 2：未缴纳
     */
    private Integer payDeposit;

    /**
     * 门店手机号
     */
    private String phoneNumber;

    /**
     * 启用状态
     */
    private Integer lstStatus;

    /**
     * 申请时间(开始时间)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startTime;

    /**
     * 申请时间(结束时间)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endTime;

    /**
     * 缴纳时间(开始时间)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startPayTime;

    /**
     * 缴纳时间(结束时间)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endPayTime;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTimeStartSecond;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTimeEndSecond;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startPayTimeSecond;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endPayTimeSecond;
}

