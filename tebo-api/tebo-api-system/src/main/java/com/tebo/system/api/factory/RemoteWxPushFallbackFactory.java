package com.tebo.system.api.factory;

import com.tebo.common.core.domain.R;
import com.tebo.system.api.RemoteWxPushService;
import com.tebo.system.api.domain.dto.WechatUserSubscribeDTO;
import com.tebo.system.api.domain.dto.WxPushDTO;
import com.tebo.system.api.domain.dto.wechat.TeboWechatNotifyForShopDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 微信推送服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteWxPushFallbackFactory implements FallbackFactory<RemoteWxPushService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteWxPushFallbackFactory.class);

    @Override
    public RemoteWxPushService create(Throwable cause) {
        log.error("推送微信失败，", cause.getMessage());
        return new RemoteWxPushService() {
            @Override
            public void sendMessage(WxPushDTO wxPushDTO) {
                log.error("推送微信公众号失败", cause.getMessage());
            }

            @Override
            public void sendNotifyForShop(TeboWechatNotifyForShopDTO wxPushDTO) {
                log.error("sendNotifyForShop 推送微信公众号失败", cause.getMessage());
            }


//            @Override
//            public R<Boolean> hasSubscribe(WechatUserSubscribeDTO wechatUserSubscribeDTO) {
//                log.error("判断用户服务号关注失败", cause.getMessage());
//                return R.fail("判断用户服务号关注失败");
//            }


            ;
        };

    }
}
