package com.tebo.system.api;

import com.tebo.common.core.constant.ServiceNameConstants;
import com.tebo.common.core.domain.R;
import com.tebo.system.api.domain.dto.WechatUserSubscribeDTO;
import com.tebo.system.api.domain.dto.WxPushDTO;
import com.tebo.system.api.domain.dto.wechat.TeboWechatNotifyForShopDTO;
import com.tebo.system.api.factory.RemoteUserFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(contextId = "RemoteWxPushService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteUserFallbackFactory.class)
public interface RemoteWxPushService {
    @PostMapping("wechat/sendMessage")
    public void sendMessage(@RequestBody WxPushDTO wxPushDTO);

    @PostMapping("wechat/sendNotifyForShop")
    public void sendNotifyForShop(@RequestBody TeboWechatNotifyForShopDTO wxPushDTO);


}
