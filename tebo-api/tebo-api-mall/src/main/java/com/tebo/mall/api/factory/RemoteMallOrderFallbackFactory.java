package com.tebo.mall.api.factory;

import com.tebo.common.core.domain.R;
import com.tebo.mall.api.RemoteGoodsService;
import com.tebo.mall.api.RemoteMallOrderWechatPayBackService;

import com.tebo.mall.api.domain.dto.TeboMallOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2023/12/12 13:26
 * @Desc : 账号服务降级处理
 */
@Component
@Slf4j
public class RemoteMallOrderFallbackFactory implements FallbackFactory<RemoteMallOrderWechatPayBackService> {
    @Override
    public RemoteMallOrderWechatPayBackService create(Throwable cause) {
        log.error("订单服务调用失败:{}", cause.getMessage());
        return new RemoteMallOrderWechatPayBackService() {
            @Override
            public R<Boolean> payMallOrderAfterNotify(TeboMallOrderDTO orderDTO) {
                return R.fail("商城订单支付回调失败" + cause.getMessage());
            }

            @Override
            public R<Boolean> payWarrantyExtensionOrderAfterNotify(TeboMallOrderDTO afterPayDTO) {
                return R.fail("保费订单支付回调失败" + cause.getMessage());
            }

            @Override
            public R<Boolean> payBatteryOrderAfterNotify(TeboMallOrderDTO afterPayDTO) {
                return R.fail("商城订单支付回调失败" + cause.getMessage());
            }
        };
    }
}
