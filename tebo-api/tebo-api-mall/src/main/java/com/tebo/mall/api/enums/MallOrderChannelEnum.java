package com.tebo.mall.api.enums;

/**
 * 订单状态
 */
public enum MallOrderChannelEnum {
    NORMAL(1, "普通订单", ""),
    WARCRAFT(2, "魔兽订单","1681858774"),
    DONKEY_CHARGING(3, "驴充充",""),
    WARRANTY_EXTENSION(4, "延保","1683136991"),
    CLOUD_SHOP(5, "云店",""),
    SPECIAL_GOODS(6, "直营商品","");
    private Integer code;
    private String msg;

    private String subMchid;

    MallOrderChannelEnum(int code, String msg,String subMchid) {
        this.code = code;
        this.msg = msg;
        this.subMchid = subMchid;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public String getSubMchid() {
        return subMchid;
    }

    public static MallOrderChannelEnum getMallOrderStatus(Integer code) {
        for (MallOrderChannelEnum orderStatusEnum : MallOrderChannelEnum.values()) {
            if (orderStatusEnum.getCode().equals(code)) {
                return orderStatusEnum;
            }
        }
        return null;
    }

}
