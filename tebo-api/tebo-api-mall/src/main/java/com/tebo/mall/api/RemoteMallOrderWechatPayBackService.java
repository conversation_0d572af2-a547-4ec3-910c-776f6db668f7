package com.tebo.mall.api;

import com.tebo.common.core.constant.ServiceNameConstants;
import com.tebo.common.core.domain.R;
import com.tebo.mall.api.domain.dto.TeboMallOrderDTO;
import com.tebo.mall.api.factory.RemoteMallOrderFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(contextId = "remoteMallOrderWechatPayBackService", value = ServiceNameConstants.MALL_SERVICE, fallbackFactory = RemoteMallOrderFallbackFactory.class)
public interface RemoteMallOrderWechatPayBackService {

    /**
     * 商城订单支付成功回调
     * @param afterPayDTO
     * @return
     */
    @PostMapping("/order/wechat/pay/payMallOrderAfterNotify")
    R<Boolean> payMallOrderAfterNotify(@RequestBody TeboMallOrderDTO afterPayDTO);

    /**
     * 保费订单支付成功回调
     */
    @PostMapping("/order/wechat/pay/payWarrantyExtensionOrderAfterNotify")
    R<Boolean> payWarrantyExtensionOrderAfterNotify(@RequestBody TeboMallOrderDTO afterPayDTO);

    /**
     * 电池合并下单支付成功回调-带久久券
     * @param afterPayDTO
     * @return
     */
    @PostMapping("/order/wechat/pay/payBatteryOrderAfterNotify")
    R<Boolean> payBatteryOrderAfterNotify(@RequestBody TeboMallOrderDTO afterPayDTO);

}
