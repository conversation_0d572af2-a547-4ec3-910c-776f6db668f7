package com.tebo.mall.api.util;


import java.util.Random;

/**
 * <AUTHOR>
 * @since 2024-12-27
 * @Desc 抽奖工具类
 */
public class DrawLotteryUtil {


    private static final Random random = new Random();

    //具有抽奖资格的用户
    public static String lotteryEligibilityKey = "lottery:permission:";

    //弹框
    public static String bulletFrame = "lottery:popUp:";

    //已经抽奖的用户
    public static String prizeDraw = "lottery:draw:";

    //奖品库存
    public static String prizeInventory = "lottery:inventory:";

    /**
     * 抽象兜底奖品id
     */
    public static String prizeDefaultId = "lottery:default:prize";

    // 活动信息
    public static String activityInfo = "lottery:activityInfo:";

    // 活动奖品
    public static String activityPrize = "lottery:activityInfo:prize:";

    // 活动奖品
    public static String activityDrawLock = "lottery:draw:lock:";

    // 落点方法(暂不考虑概率一致情况)
    public static Integer drawLottery(Integer one,Integer two, Integer three, Integer four,Integer five,Integer six) {
        Integer number = random.nextInt(10000); // 生成一个0到100之间的随机数
        // 根据概率判断中奖情况
        if (number < one) {
            return one;
        } else if (number < one + two) {
            return two;
        } else if (number < one + two + three) {
            return three;
        } else if (number < one + two + three + four) {
            return four;
        }else if (number < one + two + three + four + five) {
            return five;
        }else if (number < one + two + three + four + five + six) {
            return six;
        } else {
            return six;
        }
    }

    public static void main(String[] args) {
        System.out.println(drawLottery(1000,2000,3000,4000,0,0));
    }


}
