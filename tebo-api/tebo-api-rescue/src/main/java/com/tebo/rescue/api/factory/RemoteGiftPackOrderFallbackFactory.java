package com.tebo.rescue.api.factory;

import com.tebo.common.core.domain.R;
import com.tebo.rescue.api.RemoteGiftPackOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 久久券订单服务降级处理
 */
@Component
@Slf4j
public class RemoteGiftPackOrderFallbackFactory implements FallbackFactory<RemoteGiftPackOrderService> {

    @Override
    public RemoteGiftPackOrderService create(Throwable cause) {
        log.error("调用久久券订单服务失败:{}", cause.getMessage());
        return new RemoteGiftPackOrderService() {
            @Override
            public R<Boolean> updateOrderStatusToRefunded(String giftPackOrderNo) {
                return R.fail("更新久久券订单状态为已退款失败" + cause.getMessage());
            }
        };
    }
}
