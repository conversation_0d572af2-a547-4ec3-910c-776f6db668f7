package com.tebo.rescue.api.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class OrderCouponDTO implements Serializable {
    /**
     * 优惠券编码
     */
    private String couponCode;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 核销时间
     */
    private LocalDateTime writeOffTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 占用状态 1 未占用 2已占用
     */
    private Integer occStatus;
    /**
     * 占用订单号
     */
    private String occNumber;

}