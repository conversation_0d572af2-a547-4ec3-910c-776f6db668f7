package com.tebo.rescue.api.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> ZhangFan
 * @date : 2023/12/21 18:41
 * @Desc :
 */
@Data
public class TeboCouponCustomerDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 合伙人id
     */
    private Long tenantId;

    /**
     * 卡券编码
     */
    private String uniqueCode;

    /**
     * 卡券id
     */
    private Long couponId;

    /**
     * 卡券名称
     */
    private String couponName;

    /**
     * 面额（元）
     */
    private String parValue;

    /**
     * 来源 0 购买 1转赠
     */
    private Integer source;

    /**
     * 礼包id
     */
    private Long packId;

    /**
     * 卡券码
     */
    private String qrCode;

    /**
     * 领取优惠卷的会员id
     */
    private String unionId;

    /**
     * 状态0未领取1未使用2已使用3已失效 10已分享
     */
    private Integer status;

    /**
     * 领取时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiveTime;

    /**
     * 有效期开始日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 有效期结束日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 使用须知
     */
    private String remark;

    /**
     * 主图
     */
    private String mainImage;

    /**
     * 限制商品（0全场通用 1指定商品 2 指定分类）
     */
    private Integer goodLimit;

    /**
     * 使用方式 0 无门槛 1 满额使用
     */
    private Integer useType;

    /**
     * 满 xx 可用(分)
     */
    private Integer useReduce;

    /**
     * 卡券对应的订单编码
     */
    private String orderNo;
    /**
     * 占用状态 1 未占用 2已占用
     */
    private Integer occStatus;
    /**
     * 占用订单号
     */
    private String occNumber;
}
