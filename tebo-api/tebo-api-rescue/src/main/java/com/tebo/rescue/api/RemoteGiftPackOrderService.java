package com.tebo.rescue.api;

import com.tebo.common.core.constant.ServiceNameConstants;
import com.tebo.common.core.domain.R;
import com.tebo.rescue.api.factory.RemoteGiftPackOrderFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 久久券订单远程服务
 */
@FeignClient(contextId = "remoteGiftPackOrderService", value = ServiceNameConstants.RESCUE_SERVICE, fallbackFactory = RemoteGiftPackOrderFallbackFactory.class)
public interface RemoteGiftPackOrderService {

    /**
     * 更新久久券订单状态为已退款
     * @param giftPackOrderNo 久久券订单号
     * @return
     */
    @PostMapping("/giftPackOrder/updateOrderStatusToRefunded/{giftPackOrderNo}")
    R<Boolean> updateOrderStatusToRefunded(@PathVariable("giftPackOrderNo") String giftPackOrderNo);
}
