package com.tebo.rescue.api.factory;

import com.tebo.common.core.domain.R;
import com.tebo.rescue.api.RemoteQueueOrderService;
import com.tebo.rescue.api.RemoteWechatPayBackService;
import com.tebo.rescue.api.domain.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 叫号订单服务
 */
@Component
@Slf4j
public class RemoteQueueOrderFallbackFactory implements FallbackFactory<RemoteQueueOrderService> {

    @Override
    public RemoteQueueOrderService create(Throwable throwable) {

        log.error("叫号订单服务调用失败:{}", throwable.getMessage());
        return new RemoteQueueOrderService() {
            @Override
            public R<Map<Long, Integer>> getShopCustomerNum(ShopCustomerQueryDTO queryDTO) {
                return R.fail("门店顾客数量查询失败" + throwable.getMessage());
            }


            @Override
            public R<List<ServiceOrderDTO>> getUnFinishServiceOrderByAccount(@RequestParam("ids") List<Long> ids) {
                return R.fail("查询师傅进行中工单失败" + throwable.getMessage());
            }

            @Override
            public R<ServiceOrderDTO> getServiceOrderById(Long id) {
                return R.fail("查询工单详情" + throwable.getMessage());
            }


            @GetMapping("/getUnFinishQueueOrderByAccount")
            public R<List<QueueOrderDTO>> getUnFinishQueueOrderByAccount(@RequestParam("ids") List<Long> ids) {
                return R.fail("查询师傅进行中排队单失败" + throwable.getMessage());
            }

            @Override
            public R<Boolean> updateOrderType(String orderNo) {
                return R.fail("更新订单类型为保险订单" + throwable.getMessage());
            }

            @Override
            public R<List<TeboOrderGoodsIdGroup>> orderGoodsIdGroup(ServiceOrderQueryDTO serviceOrderQueryDTO) {

                return R.fail("查询工单商品聚合失败" + throwable.getMessage());
            }

            @PostMapping("/open/cloud/coupon/writeOff/signRecordNew")
            @Override
            public R<Boolean> signRecordNew(TeboCouponMallOrderDTO dto) {
                return R.fail("核销失败" + throwable.getMessage());
            }

        };
    }


}
