package com.tebo.rescue.api.factory;

import com.tebo.common.core.domain.R;
import com.tebo.common.core.web.domain.AjaxResult;
import com.tebo.rescue.api.RemoteCouponService;
import com.tebo.rescue.api.domain.dto.OrderCouponDTO;
import com.tebo.rescue.api.domain.dto.TeboCouponCustomerDTO;
import com.tebo.rescue.api.domain.dto.TeboRemoteGiftOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class RemoteCouponFallbackFactory implements FallbackFactory<RemoteCouponService> {
    @Override
    public RemoteCouponService create(Throwable cause) {
        log.error("调用优惠券失败:{}", cause.getMessage());
        return new RemoteCouponService(){
            @Override
            public AjaxResult writeOff(String couponCode) {
                return AjaxResult.error("核销卡券失败"+cause.getMessage());
            }

            @Override
            public R<TeboCouponCustomerDTO> grantLvccCoupon(String unionId) {
                return  R.fail("给指定用户发放驴充充专属券失败"+cause.getMessage());
            }

            @Override
            public R updateOrderStatus(String orderNo) {
                return  R.fail("更新订单核销状态失败"+cause.getMessage());
            }

            @Override
            public R<Boolean> checkBatteryCode(String batteryCode) {
                return  R.fail("获取优惠券详情失败"+cause.getMessage());
            }

            @Override
            public R<Long> createMergeOrder(TeboRemoteGiftOrderDTO orderDTO) {
                return R.fail("创建久久券订单失败" + cause.getMessage());
            }

            @Override
            public  R<TeboCouponCustomerDTO> getDetailByCouponCode(String couponCode){
                return  R.fail("获取优惠券详情失败"+cause.getMessage());
            }
            @Override
            public R updateCouponStatus(OrderCouponDTO orderCouponDTO) {
                return  R.fail("更新优惠券状态异常"+cause.getMessage());
            }

            @Override
            public R<Integer> updateCouponListStatus(OrderCouponDTO orderCouponDTO) {
                return  R.fail("更新优惠券状态异常"+cause.getMessage());
            }

            @Override
            public R<Integer> updateCouponOccStatus(OrderCouponDTO orderCouponDTO) {
                return  R.fail("批量更新优惠券占用状态异常"+cause.getMessage());
            }

            @Override
            public R<Integer> updateCouponStatusNotUsed(OrderCouponDTO orderCouponDTO) {
                return  R.fail("更新优惠券状态未使用异常"+cause.getMessage());
            }

            @Override
            public R<Boolean> reclaimUserBenefits(String giftPackOrderNo) {
                return R.fail("收回用户权益失败" + cause.getMessage());
            }

        };

    }
}