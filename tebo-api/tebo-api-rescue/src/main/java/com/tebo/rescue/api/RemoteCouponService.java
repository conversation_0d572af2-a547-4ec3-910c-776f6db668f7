package com.tebo.rescue.api;

import com.tebo.common.core.constant.ServiceNameConstants;
import com.tebo.common.core.domain.R;
import com.tebo.common.core.web.domain.AjaxResult;
import com.tebo.rescue.api.domain.dto.OrderCouponDTO;
import com.tebo.rescue.api.domain.dto.TeboCouponCustomerDTO;
import com.tebo.rescue.api.domain.dto.TeboRemoteGiftOrderDTO;
import com.tebo.rescue.api.factory.RemoteQueueOrderFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(contextId = "remoteCouponService", value = ServiceNameConstants.RESCUE_SERVICE, fallbackFactory = RemoteQueueOrderFallbackFactory.class)
public interface RemoteCouponService {

    /**
     * 通过优惠券编码获取优惠券信息
     * @param
     * @return
     */
    @GetMapping("/open/coupon/getDetailByCouponCode/{couponCode}")
    R<TeboCouponCustomerDTO> getDetailByCouponCode(@PathVariable("couponCode")String couponCode);

    /**
     * 批量更新优惠券状态为已使用
     */
    @PostMapping("/open/coupon/updateCouponStatus")
    R<Integer> updateCouponStatus(@RequestBody OrderCouponDTO orderCouponDTO);

    /**
     * 批量更新优惠券状态为已使用
     */
    @PostMapping("/open/coupon/updateCouponListStatus")
    R<Integer> updateCouponListStatus(@RequestBody OrderCouponDTO orderCouponDTO);


    /**
     * 批量更新优惠券占用状态
     */
    @PostMapping("/open/coupon/updateCouponOccStatus")
    R<Integer> updateCouponOccStatus(@RequestBody OrderCouponDTO orderCouponDTO);


    /**
     * 批量更新优惠券状态为未使用
     */
    @PostMapping("/open/coupon/updateCouponStatusNotUsed")
    R<Integer> updateCouponStatusNotUsed(@RequestBody OrderCouponDTO orderCouponDTO);
    /**
     * 卡券核销
     */
    @GetMapping("/open/cloud/coupon/writeOff/{couponCode}")
    AjaxResult writeOff(@PathVariable("couponCode")String couponCode);


    /**
     * 给指定用户发放驴充充专属券
     * @param
     * @return
     */
    @GetMapping("/open/coupon/grantLvccCoupon/{unionId}")
    R<TeboCouponCustomerDTO> grantLvccCoupon(@PathVariable("unionId")String unionId);

    /**
     * 更改订单核销时间和核销状态
     */
    @GetMapping("/giftPackOrder/updateOrderStatus/{orderNo}")
    R updateOrderStatus(@PathVariable("orderNo")String orderNo);


    /**
     * 校验电池是否已在核销表中
     * @param
     * @return
     */
    @GetMapping("/open/coupon/checkBatteryCode/{batteryCode}")
    R<Boolean> checkBatteryCode(@PathVariable("batteryCode")String batteryCode);

    /**
     * 合并下单创建久久订单
     * @param orderDTO
     * @return
     */
    @PostMapping("/giftPackOrder/createMergeOrder")
    R<Long> createMergeOrder(@RequestBody TeboRemoteGiftOrderDTO orderDTO);
}